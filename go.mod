module git.haier.net/devops/hcms-task-center

go 1.22.0

toolchain go1.22.7

require (
	git.haier.net/devops/ops-golang-common/batch v0.0.1-202308181412
	git.haier.net/devops/ops-golang-common/common v0.0.3-202405170921
	git.haier.net/devops/ops-golang-common/haierapi v0.1.2-202502251452
	git.haier.net/devops/ops-golang-common/hbc v0.1.2-202508251433
	git.haier.net/devops/ops-golang-common/hcam v0.2.3-202508251640
	git.haier.net/devops/ops-golang-common/log v0.0.5-202308181028
	git.haier.net/devops/ops-golang-common/models v0.1.0-202508291525
	git.haier.net/devops/ops-golang-common/sdk/aliyun v0.2.2-202508251627
	git.haier.net/devops/ops-golang-common/sdk/aws v0.2.1-202508121400
	git.haier.net/devops/ops-golang-common/sdk/azure v0.2.1-202506241523
	git.haier.net/devops/ops-golang-common/sdk/client v0.0.7-202501021442
	git.haier.net/devops/ops-golang-common/sdk/feishu v0.2.1-201310251122
	git.haier.net/devops/ops-golang-common/sdk/googlecloud v0.0.1-202508051349
	git.haier.net/devops/ops-golang-common/sdk/huaweicloud v0.2.6-202508141312
	git.haier.net/devops/ops-golang-common/sdk/oraclecloud v0.0.1-202505271419
	git.haier.net/devops/ops-golang-common/sdk/private v0.2.2-202502181104
	git.haier.net/devops/ops-golang-common/sdk/tencentcloud v0.2.1-202501091421
	git.haier.net/devops/ops-golang-common/sdk/util v0.2.1-202504241337
	git.haier.net/devops/ops-golang-common/sdk/zabbix v0.0.1-202405211336
	git.haier.net/devops/ops-golang-common/utils v0.1.3-202407221148
	github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute/v6 v6.2.0
	github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/mysql/armmysqlflexibleservers v1.1.1
	github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v2 v2.2.1
	github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/redis/armredis/v2 v2.3.0
	github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/sql/armsql v1.1.0
	github.com/LPX3F8/orderedmap v1.0.0
	github.com/alibabacloud-go/adb-20190315/v5 v5.2.1
	github.com/alibabacloud-go/alikafka-20190916/v3 v3.0.3
	github.com/alibabacloud-go/amqp-open-20191212/v2 v2.0.0
	github.com/alibabacloud-go/clickhouse-20191111/v3 v3.2.8
	github.com/alibabacloud-go/dds-20151201/v4 v4.2.0
	github.com/alibabacloud-go/dms-enterprise-20181101 v1.55.0
	github.com/alibabacloud-go/elasticsearch-20170613/v2 v2.3.0
	github.com/alibabacloud-go/gpdb-20160503/v4 v4.2.0
	github.com/alibabacloud-go/hbase-20190101/v3 v3.1.2
	github.com/alibabacloud-go/hitsdb-20200615/v5 v5.7.0
	github.com/alibabacloud-go/oceanbasepro-20190901 v1.0.9
	github.com/alibabacloud-go/ons-20190214/v2 v2.0.2
	github.com/alibabacloud-go/polardb-20170801/v6 v6.3.2
	github.com/alibabacloud-go/polardbx-20200202 v1.0.9
	github.com/alibabacloud-go/rocketmq-20220801 v1.5.3
	github.com/alibabacloud-go/starrocks-20221019 v1.1.0
	github.com/alibabacloud-go/tea v1.3.10
	github.com/aliyun/alibaba-cloud-sdk-go v1.62.514
	github.com/aliyun/alibabacloud-oss-go-sdk-v2 v1.2.3
	github.com/ansrivas/fiberprometheus/v2 v2.7.0
	github.com/aws/aws-sdk-go v1.51.30
	github.com/aws/aws-sdk-go-v2/service/ec2 v1.164.1
	github.com/aws/aws-sdk-go-v2/service/elasticache v1.38.8
	github.com/aws/aws-sdk-go-v2/service/iam v1.37.3
	github.com/aws/aws-sdk-go-v2/service/pricing v1.28.1
	github.com/aws/aws-sdk-go-v2/service/rds v1.79.6
	github.com/aws/smithy-go v1.22.2
	github.com/chyroc/lark v0.0.111
	github.com/gin-gonic/gin v1.10.0
	github.com/go-redsync/redsync/v4 v4.13.0
	github.com/goccy/go-json v0.10.2
	github.com/gofiber/fiber/v2 v2.52.5
	github.com/gofiber/utils v1.1.0
	github.com/google/uuid v1.6.0
	github.com/gorhill/cronexpr v0.0.0-20180427100037-88b0669f7d75
	github.com/hashicorp/go-retryablehttp v0.7.7
	github.com/hashicorp/raft v1.7.1
	github.com/huaweicloud/huaweicloud-sdk-go-v3 v0.1.108
	github.com/iancoleman/strcase v0.3.0
	github.com/imroc/req/v3 v3.48.0
	github.com/joho/godotenv v1.5.1
	github.com/kr/pretty v0.3.1
	github.com/mcuadros/go-defaults v1.2.0
	github.com/nacos-group/nacos-sdk-go v1.1.5
	github.com/panjf2000/ants/v2 v2.8.1
	github.com/patrickmn/go-cache v2.1.0+incompatible
	github.com/pkg/errors v0.9.1
	github.com/redis/go-redis/v9 v9.6.1
	github.com/shopspring/decimal v1.3.1
	github.com/sirupsen/logrus v1.9.3
	github.com/stretchr/testify v1.9.0
	github.com/swaggo/swag v1.8.12
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/billing v1.0.870
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cam v1.0.726
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cbs v1.0.877
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cdb v1.0.726
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.877
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm v1.0.726
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/postgres v1.0.726
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/redis v1.0.726
	github.com/vmware/govmomi v0.30.7
	github.com/xuri/excelize/v2 v2.8.1
	go.uber.org/atomic v1.11.0
	golang.org/x/sync v0.10.0
	golang.org/x/time v0.5.0
	gopkg.in/yaml.v3 v3.0.1
	gorm.io/gorm v1.25.5
)

require (
	github.com/alibabacloud-go/alibabacloud-gateway-sls v0.3.0 // indirect
	github.com/alibabacloud-go/alibabacloud-gateway-sls-util v0.3.0 // indirect
	github.com/alibabacloud-go/darabonba-array v0.1.0 // indirect
	github.com/alibabacloud-go/darabonba-encode-util v0.0.2 // indirect
	github.com/alibabacloud-go/darabonba-map v0.0.2 // indirect
	github.com/alibabacloud-go/darabonba-signature-util v0.0.7 // indirect
	github.com/alibabacloud-go/darabonba-string v1.0.2 // indirect
	github.com/alibabacloud-go/sls-20201230/v6 v6.9.2 // indirect
	github.com/alibabacloud-go/yundun-bastionhost-20191209/v2 v2.3.0 // indirect
	github.com/aws/aws-sdk-go-v2/service/savingsplans v1.24.3 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
)

require (
	cloud.google.com/go v0.112.0 // indirect
	cloud.google.com/go/bigquery v1.59.1 // indirect
	cloud.google.com/go/compute v1.23.3 // indirect
	cloud.google.com/go/compute/metadata v0.2.3 // indirect
	cloud.google.com/go/iam v1.1.6 // indirect
	git.haier.net/devops/ops-golang-common/gogin v0.0.3-202308111802 // indirect
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/Azure/azure-sdk-for-go/sdk/azcore v1.16.0 // indirect
	github.com/Azure/azure-sdk-for-go/sdk/azidentity v1.7.0 // indirect
	github.com/Azure/azure-sdk-for-go/sdk/internal v1.10.0 // indirect
	github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/costmanagement/armcostmanagement/v2 v2.1.0 // indirect
	github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/mysql/armmysql v1.1.1 // indirect
	github.com/AzureAD/microsoft-authentication-library-for-go v1.2.2 // indirect
	github.com/KyleBanks/depth v1.2.1 // indirect
	github.com/LPX3F8/glist v0.0.0-**************-2befc00a67c2 // indirect
	github.com/PuerkitoBio/purell v1.1.1 // indirect
	github.com/PuerkitoBio/urlesc v0.0.0-**************-de5bf2ad4578 // indirect
	github.com/alibabacloud-go/alibabacloud-gateway-spi v0.0.5 // indirect
	github.com/alibabacloud-go/darabonba-openapi v0.2.1 // indirect
	github.com/alibabacloud-go/darabonba-openapi/v2 v2.1.9 // indirect
	github.com/alibabacloud-go/das-********/v3 v3.1.1 // indirect
	github.com/alibabacloud-go/debug v1.0.1 // indirect
	github.com/alibabacloud-go/endpoint-util v1.1.1 // indirect
	github.com/alibabacloud-go/hbr-********/v2 v2.0.1 // indirect
	github.com/alibabacloud-go/imm-********/v2 v2.1.15 // indirect
	github.com/alibabacloud-go/openapi-util v0.1.1 // indirect
	github.com/alibabacloud-go/openplatform-********/v2 v2.0.1 // indirect
	github.com/alibabacloud-go/tea-fileform v1.1.1 // indirect
	github.com/alibabacloud-go/tea-oss-sdk v1.1.3 // indirect
	github.com/alibabacloud-go/tea-oss-utils v1.1.0 // indirect
	github.com/alibabacloud-go/tea-utils v1.4.5 // indirect
	github.com/alibabacloud-go/tea-utils/v2 v2.0.7 // indirect
	github.com/alibabacloud-go/tea-xml v1.1.3 // indirect
	github.com/aliyun/credentials-go v1.4.5 // indirect
	github.com/andybalholm/brotli v1.1.0 // indirect
	github.com/antonfisher/nested-logrus-formatter v1.3.1 // indirect
	github.com/apache/arrow/go/v14 v14.0.2 // indirect
	github.com/armon/go-metrics v0.4.1 // indirect
	github.com/armon/go-radix v1.0.0 // indirect
	github.com/aws/aws-sdk-go-v2 v1.36.3 // indirect
	github.com/aws/aws-sdk-go-v2/config v1.18.34 // indirect
	github.com/aws/aws-sdk-go-v2/credentials v1.13.33 // indirect
	github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.13.9 // indirect
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.34 // indirect
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.34 // indirect
	github.com/aws/aws-sdk-go-v2/internal/ini v1.3.40 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.11.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.11.11 // indirect
	github.com/aws/aws-sdk-go-v2/service/sso v1.13.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/ssooidc v1.15.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/sts v1.21.3 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/buger/jsonparser v1.1.1 // indirect
	github.com/bytedance/sonic v1.11.6 // indirect
	github.com/bytedance/sonic/loader v0.1.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/clbanning/mxj/v2 v2.7.0 // indirect
	github.com/cloudflare/circl v1.4.0 // indirect
	github.com/cloudwego/base64x v0.1.4 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/fatih/color v1.16.0 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-errors/errors v1.0.1 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-openapi/jsonpointer v0.19.5 // indirect
	github.com/go-openapi/jsonreference v0.19.6 // indirect
	github.com/go-openapi/spec v0.20.4 // indirect
	github.com/go-openapi/swag v0.19.15 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.20.0 // indirect
	github.com/go-task/slim-sprig/v3 v3.0.0 // indirect
	github.com/gocarina/gocsv v0.0.0-20231116093920-b87c2d0e983a // indirect
	github.com/golang-jwt/jwt/v5 v5.2.1 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/google/flatbuffers v23.5.26+incompatible // indirect
	github.com/google/pprof v0.0.0-20240910150728-a0b0bb1d4134 // indirect
	github.com/google/s2a-go v0.1.7 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.3.2 // indirect
	github.com/googleapis/gax-go/v2 v2.12.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-hclog v1.6.3 // indirect
	github.com/hashicorp/go-immutable-radix v1.0.0 // indirect
	github.com/hashicorp/go-msgpack/v2 v2.1.2 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/golang-lru v0.5.0 // indirect
	github.com/huaweicloud/huaweicloud-sdk-go-obs v3.24.6+incompatible
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/kylelemons/godebug v1.1.0 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/lpx3f8/gorm-mysql v1.0.6 // indirect
	github.com/lpx3f8/mysql v1.0.6 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.15 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/onsi/ginkgo/v2 v2.20.2 // indirect
	github.com/opentracing/opentracing-go v1.2.1-0.20220228012449-10b1cf09e00b // indirect
	github.com/oracle/oci-go-sdk v24.3.0+incompatible
	github.com/pelletier/go-toml/v2 v2.2.2 // indirect
	github.com/philhofer/fwd v1.1.2 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pkg/browser v0.0.0-20240102092130-5ac0b6a4141c // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.19.1 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.53.0 // indirect
	github.com/prometheus/procfs v0.14.0 // indirect
	github.com/quic-go/qpack v0.5.1 // indirect
	github.com/quic-go/quic-go v0.47.0 // indirect
	github.com/refraction-networking/utls v1.6.7 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/rogpeppe/go-internal v1.12.0 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc v1.0.726 // indirect
	github.com/tinylib/msgp v1.1.9 // indirect
	github.com/tjfoc/gmsm v1.4.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.52.0 // indirect
	github.com/valyala/tcplisten v1.0.0 // indirect
	github.com/xuri/efp v0.0.0-20231025114914-d1ff6096ae53 // indirect
	github.com/xuri/nfp v0.0.0-20230919160717-d98342af3f05 // indirect
	github.com/zeebo/xxh3 v1.0.2 // indirect
	go.mongodb.org/mongo-driver v1.12.1 // indirect
	go.opencensus.io v0.24.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.47.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.47.0 // indirect
	go.opentelemetry.io/otel v1.22.0 // indirect
	go.opentelemetry.io/otel/metric v1.22.0 // indirect
	go.opentelemetry.io/otel/trace v1.22.0 // indirect
	go.uber.org/mock v0.4.0 // indirect
	go.uber.org/multierr v1.5.0 // indirect
	go.uber.org/zap v1.15.0 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/crypto v0.31.0 // indirect
	golang.org/x/exp v0.0.0-20240909161429-701f63a606c0 // indirect
	golang.org/x/mod v0.21.0 // indirect
	golang.org/x/net v0.33.0 // indirect
	golang.org/x/oauth2 v0.18.0 // indirect
	golang.org/x/sys v0.28.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	golang.org/x/tools v0.25.0 // indirect
	golang.org/x/xerrors v0.0.0-20231012003039-104605ab7028 // indirect
	google.golang.org/api v0.162.0 // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto v0.0.0-20240125205218-1f4bbc51befe // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240205150955-31a09d347014 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240205150955-31a09d347014 // indirect
	google.golang.org/grpc v1.61.0 // indirect
	google.golang.org/protobuf v1.34.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)
