package main

import (
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobCleanupData"
	"git.haier.net/devops/hcms-task-center/scripts/jobCleanupTask"
	"git.haier.net/devops/hcms-task-center/scripts/jobForDBMS/DBMSSyncBaseMeta"
	"git.haier.net/devops/hcms-task-center/scripts/jobForDBMS/DBMSSyncDBMeta"
	"git.haier.net/devops/hcms-task-center/scripts/jobPreAllocation"
	"git.haier.net/devops/hcms-task-center/scripts/jobScanAccount"
	"git.haier.net/devops/hcms-task-center/scripts/jobScanMonitoring"
	"git.haier.net/devops/hcms-task-center/scripts/jobSendDataToCMDB"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/pullBill"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/refineBill"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/retrieveBill"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBucket"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBucketDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseAliyun"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQL"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQL/jobSyncGeminiDB"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQL/jobSyncMemcache"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQL/jobSyncStarRocks"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQLDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabasePrivate"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds/jobSyncGaussDB"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRdsDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncHbr"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncHostEcs"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncHostEcsDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncHostPrivate"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncMiddleware"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncMiddlewareDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncNetworkDevice"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncResource"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncSubnet"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncSubnetDedicated"
)

const (
	EnvLocal      = "local"
	EnvDev        = "dev"
	EnvProd       = "prod"
	EnvVcenter    = "vcenter"
	EnvSingapore  = "singapore"
	EnvHuaweiProd = "hwprod"
)

var (
	// formalTasks 正式环境任务
	formalTasks = tools.MergeData(
		hotsTasks,       // 主机相关任务
		databaseTasks,   // 数据库相关任务
		subnetTasks,     // 子网相关任务
		cmdbTasks,       // 技术架构CMDB相关任务
		middlewareTasks, // 中间件相关任务
		cleanTasks,      // 清理任务
		dbmsTasks,       // DBMS元数据任务
		billTasks,       // 账单任务

		networkDeviceTasks, // 网络设备任务
	)

	subnetTasks = []taskcenter.ITask{
		jobSyncSubnet.New(),          // 同步子网信息
		jobSyncSubnetDedicated.New(), // 同步专属云子网信息
	}

	hotsTasks = []taskcenter.ITask{
		jobSyncHostEcs.New(),                  // 同步云上主机信息
		jobSyncHostEcsDedicated.New(),         // 同步专属云主机信息
		jobSyncHostPrivate.NewSyncJXJG(),      // 同步JXJG主机信息
		jobSyncHostPrivate.NewSyncWikiCloud(), // 同步wiki中的物理机信息
		jobSyncHostPrivate.NewSyncFactory(),   // 同步工厂主机信息
	}

	databaseTasks = []taskcenter.ITask{
		jobSyncDatabaseRds.New(),                       // 同步云上RDS
		jobSyncDatabaseRdsDedicated.New(),              // 同步专属云上RDS
		jobSyncMemcache.NewSyncMemcache(),              // 同步云上Memcache
		jobSyncDatabaseNoSQL.NewSyncKvStore(),          // 同步云上Redis
		jobSyncDatabaseNoSQLDedicated.NewSyncKvStore(), // 同步专属云上Redis
		jobSyncDatabaseNoSQL.NewSyncMongoDB(),          // 同步云上MongoDB
		jobSyncDatabaseNoSQL.NewSyncElasticSearch(),    // 同步云上ElasticSearch
		jobSyncDatabaseAliyun.NewSyncPolarDB(),         // 同步云上PolarDB
		jobSyncDatabaseAliyun.NewSyncPolarDBX(),        // 同步云上PolarDB
		jobSyncDatabaseAliyun.NewSyncOceanBase(),       // 同步云上OceanBase
		jobSyncDatabasePrivate.New(),                   // 同步私有云数据库
		jobSyncGaussDB.New(),                           // 同步gaussDB数据库
		jobSyncGeminiDB.New(),                          // 同步geminiDB数据库
		jobSyncBucket.New(),                            // 同步云上bucket
		jobSyncBucketDedicated.New(),                   // 同步专属云bucket
		jobSyncDatabaseAliyun.NewSyncClickhouse(),      // 同步Clickhouse
		jobSyncDatabaseAliyun.NewSyncAdb(),             // 同步Adb MySQL
		jobSyncDatabaseAliyun.NewSyncAdbPgSQL(),        // 同步Adb PgSQL
		jobSyncDatabaseAliyun.NewSyncHbase(),           // 同步Hbase
		jobSyncDatabaseAliyun.NewSyncHitsdb(),          // 同步Hitsdb
		jobSyncStarRocks.New(),                         // 同步starrocks
		jobSyncHbr.NewSyncHbr(),                        // 数据库备份
	}

	middlewareTasks = []taskcenter.ITask{
		jobSyncMiddleware.NewSyncMiddleware(),                   // 同步中间件任务
		jobSyncMiddlewareDedicated.NewSyncMiddlewareDedicated(), // 同步专属云中间件任务
	}

	cmdbTasks = []taskcenter.ITask{
		jobSendDataToCMDB.New(*bizutils.DataSource()), // 同步数据到CMDB
		jobScanAccount.New(),                          // 扫描过期账号
		jobScanMonitoring.NewScanZabbixMonitoring(),   // 同步zabbix监控
	}

	dbmsTasks = []taskcenter.ITask{
		DBMSSyncDBMeta.New(),   // DBMS元数据任务
		DBMSSyncBaseMeta.New(), // DBMS元数据任务
	}

	cleanTasks = []taskcenter.ITask{
		jobCleanupData.New(), // 清理数据
		jobCleanupTask.New(), // 清理过期异常任务
	}

	billTasks = []taskcenter.ITask{
		jobPreAllocation.New(),                     // 预分账
		pullBill.NewGoogleCloudSyncBill(),          // 同步谷歌云账单
		pullBill.NewAliyunSyncBill(),               // 同步阿里云账单
		pullBill.NewHuaweicloudSyncBill(),          // 同步华为云账单
		pullBill.NewHuaweicloudDedicatedSyncBill(), // 同步华为专属云账单
		pullBill.NewTencentCloudSyncBill(),         // 同步腾讯云账单
		pullBill.NewOracleDownloadBill(),           // 1.甲骨文账单下载
		pullBill.NewOracleCloudSyncBill(),          // 2.甲骨文账单同步
		pullBill.NewAzureSyncBill(),                // 同步微软云账单
		pullBill.NewAwsDownloadBill(),              // 1.亚马逊账单下载
		pullBill.NewAwsPricingBill(),               // 2.亚马逊价目更新
		// pullBill.NewAwsCompareBill(),         // 3.亚马逊账单比对
		pullBill.NewAwsSyncBill(),             // 4.同步亚马逊账单
		refineBill.NewRefineBillAliyun(),      // 阿里云账单精细化
		refineBill.NewRefineBillHuawei(),      // 华为云账单精细化
		refineBill.NewRefineBillTencent(),     // 腾讯云账单精细化
		refineBill.NewRefineBillAzure(),       // 微软云账单精细化
		refineBill.NewRefineBillAws(),         // 亚马逊账单精细化
		refineBill.NewRefineBillGcloud(),      // 谷歌云账单精细化
		refineBill.NewRefineBillOracle(),      // 甲骨文云账单精细化
		retrieveBill.NewAliyunRetrieveBill(),  // 阿里云稳定账单日期重置
		retrieveBill.NewHuaweiRetrieveBill(),  // 华为云稳定账单日期重置
		retrieveBill.NewTencentRetrieveBill(), // 腾讯云稳定账单日期重置
		retrieveBill.NewAwsRetrieveBill(),     // 亚马逊稳定账单日期重置
		jobSyncResource.New(),                 // 同步资源信息（用以查询资源创建时间）
	}

	vcenterTasks = []taskcenter.ITask{
		jobSyncHostPrivate.NewSyncPrivateCloud(), // 同步私有云虚拟机
	}

	singaporeTasks = []taskcenter.ITask{
		pullBill.NewAwsDownloadBill(),             // 下载亚马逊账单
		pullBill.NewAwsSyncBill(),                 // 同步亚马逊账单
		pullBill.NewAwsPricingBill(),              // 同步亚马逊价目
		pullBill.NewGoogleCloudSyncBill(),         // 同步谷歌云账单
		pullBill.NewOracleDownloadBill(),          // 甲骨文账单下载
		pullBill.NewOracleCloudSyncBill(),         // 同步甲骨文云账单
		refineBill.NewRefineBillOracle(),          // 甲骨文云账单精细化
		retrieveBill.NewAwsRetrieveBill(),         // 亚马逊稳定账单日期重置
		retrieveBill.NewGoogleCloudRetrieveBill(), // 谷歌云稳定账单日期重置
	}

	networkDeviceTasks = []taskcenter.ITask{
		jobSyncNetworkDevice.NewSyncNetworkDevice(), // 同步网络设备信息
	}
)

// localTestTasks 本地测试任务
var localTestTasks = []taskcenter.ITask{
	pullBill.NewHuaweicloudSyncBill(),  // 同步华为云账单
	pullBill.NewTencentCloudSyncBill(), // 同步腾讯云账单
	pullBill.NewGoogleCloudSyncBill(),  // 同步谷歌云账单
	// jobTest.NewTestPanic(),        // 测试panic
	// jobTest.NewTest("TEST_JOB_1"), // 测试并发
	// jobTest.NewTest("TEST_JOB_2"), // 测试并发
	// jobTest.NewTest("TEST_JOB_3"), // 测试并发
}
