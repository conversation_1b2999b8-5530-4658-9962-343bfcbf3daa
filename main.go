package main

import (
	"flag"
	"fmt"
	"runtime/debug"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"

	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/pool"
	"git.haier.net/devops/hcms-task-center/core/taskcenter"
	"git.haier.net/devops/hcms-task-center/scripts/jobTest"
)

var (
	httpPort         int
	raftPort         int
	secondsEastOfUTC = int((8 * time.Hour).Seconds())
	beijing          = time.FixedZone("Beijing Time", secondsEastOfUTC)
	cnf              = config.Global()
	quit             bool
)

func init() {
	// 设置全局时区为北京时间
	time.Local = beijing
	// 初始化参数
	flag.IntVar(&httpPort, "hp", cnf.ServerPort, "http listen addr")
	flag.IntVar(&raftPort, "rp", cnf.RaftPort, "raft listen addr")
}

func main() {
	flag.Parse()
	defer func() {
		time.Sleep(time.Second)
	}()
	defer func() {
		if err := recover(); err != nil {
			notice.SendErrorMessage("任务中心异常退出",
				fmt.Sprintf("任务中心异常退出！\n\n异常信息：%s\n\n堆栈信息：%s", err, string(debug.Stack())))
		} else {
			if !quit {
				notice.SendErrorMessage("任务中心异常退出", "任务中心节点非预期内退出!\n\n堆栈信息："+string(debug.Stack()))
			}
		}
	}()
	defer closeDBConn()

	// 配置端口
	cnf.ServerPort = httpPort
	cnf.RaftPort = raftPort

	// 根据环境注册任务
	var tasks []taskcenter.ITask
	switch cnf.Env {
	case EnvLocal:
		tasks = localTestTasks
	case EnvDev:
		tasks = append(
			formalTasks,
			jobTest.NewTestPanic(),
			jobTest.NewTestSendMessage(),
		)
	case EnvProd:
		tasks = append(
			formalTasks,
			jobTest.NewTestSendMessage(),
		)
	case EnvHuaweiProd:
		tasks = append(
			formalTasks,
			jobTest.NewTestSendMessage(),
		)
	case EnvVcenter:
		tasks = vcenterTasks
	case EnvSingapore:
		tasks = singaporeTasks
	}

	// 启动
	taskCent := taskcenter.NewTaskCenter(cnf, taskcenter.WithTasks(tasks...))
	taskCent.Run()
	taskCent.Wait()
	quit = true
	notice.SendInfoMessage("任务中心节点退出", fmt.Sprintf("任务中心节点退出: **%s**", taskCent.NodeName()))
}

func closeDBConn() {
	// 释放协程池
	pool.Pool().Release()

	// 关闭连接
	for _, v := range models.Items() {
		if db, _ := v.DB(); db != nil {
			_ = db.Close()
		}
	}
}
