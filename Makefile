BINARY=tc
GO111MODULE:=on
export GO111MODULE

TRIM_PATH := $(shell pwd)
GCFLAGS=-gcflags "all=-l=2 -trimpath=${TRIM_PATH}"
LDFLAGS=-ldflags "-w -s"

BUILD_TIME=`date +%Y%m%d%H%M`
COMMIT_VERSION=`git rev-parse HEAD`

# colors compatible setting
CRED:=$(shell tput setaf 1 2>/dev/null)
CGREEN:=$(shell tput setaf 2 2>/dev/null)
CYELLOW:=$(shell tput setaf 3 2>/dev/null)
CEND:=$(shell tput sgr0 2>/dev/null)

.PHONY: go_mod_tidy
go_mod_tidy:
	@echo "$(CGREEN)Run go mod tidy -v ...$(CEND)"
	@go mod tidy -v

.PHONY: fmt
fmt: go_mod_tidy
	@echo "$(CGREEN)Run gofmt on all source files ...$(CEND)"
	@echo "gofmt -l -s -w ..."
	@ret=0 && for d in $$(go list -f '{{.Dir}}' ./... | grep -v /vendor/); do \
		gofmt -l -s -w $$d/*.go || ret=$$? ; \
	done ; exit $$ret

.PHONY: dev
dev: swag
	@echo "$(CGREEN)Run gofmt on all source files ...$(CEND)"
	@echo "gofmt -l -s -w ..."
	@ret=0 && for d in $$(go list -f '{{.Dir}}' ./... | grep -v /vendor/); do \
		gofmt -l -s -w $$d/*.go || ret=$$? ; \
	done ; exit $$ret

.PHONY: swag
swag:
	@echo "$(CGREEN)Run swag init & swag format ...$(CEND)"
	@swag i --parseDependency  --parseDepth=6 && swag f
	@make fmt
