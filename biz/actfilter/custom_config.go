package actfilter

import (
	"git.haier.net/devops/ops-golang-common/hbc"
)

type CustomConfig struct {
	CustomProduct  map[hbc.CloudVendor]hbc.CloudProduct
	CustomPurposes map[hbc.CloudVendor][]string
	IgnoreVendor   map[hbc.CloudVendor]struct{}
	CustomRegion   map[hbc.CloudVendor]string
}

func NewCustomConfig(opts ...CustomConfigOption) *CustomConfig {
	c := &CustomConfig{
		CustomProduct:  map[hbc.CloudVendor]hbc.CloudProduct{},
		CustomPurposes: map[hbc.CloudVendor][]string{},
		IgnoreVendor:   map[hbc.CloudVendor]struct{}{},
		CustomRegion:   map[hbc.CloudVendor]string{},
	}
	for _, o := range opts {
		o(c)
	}
	return c
}
