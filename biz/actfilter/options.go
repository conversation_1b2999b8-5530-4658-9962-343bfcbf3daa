package actfilter

import (
	"git.haier.net/devops/ops-golang-common/hbc"
)

type CustomConfigOption func(config *CustomConfig)

func SetCustomProduct(vendor hbc.CloudVendor, product hbc.CloudProduct) CustomConfigOption {
	return func(config *CustomConfig) {
		config.CustomProduct[vendor] = product
	}
}

func SetCustomPurpose(vendor hbc.CloudVendor, purpose ...string) CustomConfigOption {
	return func(config *CustomConfig) {
		config.CustomPurposes[vendor] = purpose
	}
}

func SetIgnoreVendor(ignoreVendors ...hbc.CloudVendor) CustomConfigOption {
	return func(config *CustomConfig) {
		for _, v := range ignoreVendors {
			config.IgnoreVendor[v] = struct{}{}
		}
	}
}

func SetRegion(vendor hbc.CloudVendor, region string) CustomConfigOption {
	return func(config *CustomConfig) {
		config.CustomRegion[vendor] = region
	}
}
