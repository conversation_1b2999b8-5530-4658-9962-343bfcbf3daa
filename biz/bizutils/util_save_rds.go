package bizutils

import (
	"context"
	"errors"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/uuid"
	"gorm.io/gorm"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func CreateOrUpdateRds(ctx context.Context, databaseInfos ...*models.DatabaseInfo) error {
	logger.Infof(ctx, "got %d rds records, ready to save ...", len(databaseInfos))

	b := tools.NewBatch[*models.DatabaseInfo, error](ctx,
		batch.WithBatchSize(10),
		batch.WithShowLog(true),
		batch.WithLogPrintStep(500))
	b.Run(databaseInfos, func(ctx context.Context, rdsInfo *models.DatabaseInfo) (error, error) {
		var isExcludeEnv bool // 更新时是否排除env字段的更新
		// get env from resource_applications
		scode, env, err := ApplicationApplySCodeEnv(ctx, rdsInfo.InstanceId)
		if err != nil {
			return err, nil
		}
		rdsInfo.Env = UnifyEnv(rdsInfo.Env)
		if scode != empty {
			rdsInfo.Scode = scode
		}
		if env != empty {
			rdsInfo.Env = env
		}
		if rdsInfo.Env == empty {
			isExcludeEnv = true
		}
		rdsInfo.EngineType = UnifyDBType(rdsInfo.EngineType)
		if rdsInfo.UniRegionId == "" {
			rdsInfo.UniRegionId = GetUnionRegion(ctx, rdsInfo.Vendor.String(), rdsInfo.Region)
		}
		if rdsInfo.Scode != "" && IsSCode(strings.TrimSpace(rdsInfo.Scode)) {
			project, _ := api.HdsClient().QueryAlmProject(strings.TrimSpace(rdsInfo.Scode))
			if project != nil {
				rdsInfo.Project = project.Id
				rdsInfo.Domain = project.Domain
				rdsInfo.Team = project.OrgName
				rdsInfo.OwnerId = project.Owner
				rdsInfo.OwnerName = project.OwnerName
			}
		}
		rdsInfo.ResourceId = uuid.DatabaseResourceId(rdsInfo)
		if rdsInfo.IsDeleted == nil {
			isDeleted := false
			rdsInfo.IsDeleted = &isDeleted
		}
		if rdsInfo.Scode == empty && rdsInfo.Vendor == hbc.HuaweiCloud && rdsInfo.AccountName == "hr690f" {
			rdsInfo.Scode = "S04076"
		}
		// S码为空发送通知
		if rdsInfo.Scode == empty {
			notice.SendWarnMessage("同步RDS资源S码为空", "实例ID: "+rdsInfo.InstanceId)
		}

		m := DataSource().Model(ctx)
		dbFoundRdsInfo := models.DatabaseInfo{}
		if err := m.Orm().Where(models.DatabaseInfo{InstanceId: rdsInfo.InstanceId}).Take(&dbFoundRdsInfo).Error; err == nil {
			// 退订下线，跳过更新
			if dbFoundRdsInfo.SubscriptionStatus == 0 {
				return nil, nil
			}
			// 记录变更历史
			if isRdsInfoDifferent(&dbFoundRdsInfo, rdsInfo) {
				if err = SaveDatabaseInfoHistory(ctx, nil, &dbFoundRdsInfo, DelOperaTypeUpdate); err != nil {
					return err, nil
				}
			}
			rdsInfo.SubscriptionStatus = dbFoundRdsInfo.SubscriptionStatus // 重新赋值，避免零值
			rdsInfo.CreateTime = dbFoundRdsInfo.CreateTime
			rdsInfo.ID = dbFoundRdsInfo.ID
			// 资源申请和云厂商环境标签都为空的情况使用数据库原有信息
			if isExcludeEnv {
				// update exclude:env
				rdsInfo.Env = dbFoundRdsInfo.Env
			}
			return m.Orm().Save(rdsInfo).Error, nil
		} else {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return err, nil
			}
			// notfound: create
			return m.Orm().Transaction(func(tx *gorm.DB) error {
				// host empty create
				if rdsInfo.Host == "" {
					return tx.Create(rdsInfo).Error
				}
				// 根据host和port查询实例
				list, err := findRdsListByHostPort(tx, rdsInfo.Host, rdsInfo.Port)
				if err != nil {
					return err
				}
				if err := tx.Create(rdsInfo).Error; err != nil {
					return err
				}
				// 下线已存在的数据
				for _, rds := range list {
					if err := updateRdsOffline(tx, rds.ID); err != nil {
						return err
					}
					// 忽略rds记录到历史表
					SaveDatabaseInfoHistory(ctx, nil, rds, DelOperaTypeIgnore)
				}
				return nil
			}), nil
		}
	})

	return tools.MergeErrors(b.Outs())
}

// isRdsInfoDifferent true:不同
func isRdsInfoDifferent(dbFoundRdsInfo, rdsInfo *models.DatabaseInfo) bool {
	if dbFoundRdsInfo == nil || rdsInfo == nil {
		return true
	}

	return !(dbFoundRdsInfo.Scode == rdsInfo.Scode &&
		dbFoundRdsInfo.OwnerId == rdsInfo.OwnerId &&
		dbFoundRdsInfo.ResourceGroup == rdsInfo.ResourceGroup &&
		dbFoundRdsInfo.IsSelfBuild == rdsInfo.IsSelfBuild &&
		dbFoundRdsInfo.Status == rdsInfo.Status)
}

func findRdsListByHostPort(tx *gorm.DB, host string, port int) ([]*models.DatabaseInfo, error) {
	var data []*models.DatabaseInfo
	if err := tx.Model(&models.DatabaseInfo{}).
		Where("host = ? AND port = ? AND is_deleted = 0", host, port).Find(&data).Error; err != nil {
		return nil, err
	}
	return data, nil
}

func updateRdsOffline(tx *gorm.DB, id int64) error {
	now := time.Now()
	return tx.Model(&models.DatabaseInfo{}).Select("is_deleted", "delete_time", "update_time").Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_deleted":  true,
			"delete_time": now,
			"update_time": now,
		}).Error
}

func GetRdsDetailByInstanceName(ctx context.Context, name string) (*models.DatabaseInfo, error) {
	m := DataSource().Model(ctx)
	rdsInfo := new(models.DatabaseInfo)
	if err := m.Orm().Where(models.DatabaseInfo{InstanceName: name}).Take(&rdsInfo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return rdsInfo, nil
}
