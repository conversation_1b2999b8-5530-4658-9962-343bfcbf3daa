package bizutils

import (
	"context"
	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/patrickmn/go-cache"
	"sync"
	"time"
)

var (
	instanceIdInsMapCache = cache.New(60*time.Minute, 120*time.Minute)
	instanceIdInsMapLock  = new(sync.Mutex)
)

// GetInstanceByInstanceId 根据instanceId获取数据库信息
func GetInstanceByInstanceId(ctx context.Context, instanceId string) (*models.DatabaseInfo, error) {
	instanceIdInsMapLock.Lock()
	defer instanceIdInsMapLock.Unlock()
	if v, ok := instanceIdInsMapCache.Get(instanceId); ok {
		return v.(*models.DatabaseInfo), nil
	}

	m := DataSource().Model(ctx)
	databaseInfo := new(models.DatabaseInfo)
	if err := m.Orm().Model(&models.DatabaseInfo{}).Where("instance_id = ?", instanceId).Order("is_deleted ASC").First(databaseInfo).Error; err != nil {
		return nil, err
	}

	instanceIdInsMapCache.Set(instanceId, databaseInfo, cache.DefaultExpiration)
	return databaseInfo, nil
}
