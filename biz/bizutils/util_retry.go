package bizutils

import (
	"math/rand"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
)

func WithRetry[T any](maxTimes int, f func() (T, error)) (T, error) {
	var err error
	var data T

	for i := 0; i < maxTimes; i++ {
		if data, err = f(); err == nil {
			return data, nil
		}
		time.Sleep(time.Duration(rand.Intn(3)) * time.Second)
		log.SystemErrorf("retry %d times, err: %v", i+1, err)
	}

	return *new(T), err
}
