package bizutils

import (
	"context"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/patrickmn/go-cache"
)

var (
	aggregatedIdInsMapCache       = cache.New(60*time.Minute, 120*time.Minute)
	aggregatedIdInsMapLock        = new(sync.Mutex)
	instanceIdOverViewInsMapCache = cache.New(60*time.Minute, 120*time.Minute)
	instanceIdOverViewInsMapLock  = new(sync.Mutex)
)

// GetInstanceByAggregatedId 根据聚合ID获取实例信息
func GetInstanceByAggregatedId(ctx context.Context, aggregatedId string) (*models.CmdbProductOverview, error) {
	aggregatedIdInsMapLock.Lock()
	defer aggregatedIdInsMapLock.Unlock()
	if v, ok := aggregatedIdInsMapCache.Get(aggregatedId); ok {
		return v.(*models.CmdbProductOverview), nil
	}

	m := DataSource().Model(ctx)
	cmdbProductOverview := new(models.CmdbProductOverview)
	if err := m.Orm().Model(&models.CmdbProductOverview{}).Where("aggregated_id = ?", aggregatedId).First(cmdbProductOverview).Error; err != nil {
		return nil, err
	}

	aggregatedIdInsMapCache.Set(aggregatedId, cmdbProductOverview, cache.DefaultExpiration)
	return cmdbProductOverview, nil
}

// GetCmdbProductOverviewInstanceByInstanceId 根据实例ID获取cmdb总表实例信息
func GetCmdbProductOverviewInstanceByInstanceId(ctx context.Context, instanceId string) (*models.CmdbProductOverview, error) {
	instanceIdOverViewInsMapLock.Lock()
	defer instanceIdOverViewInsMapLock.Unlock()
	if v, ok := instanceIdOverViewInsMapCache.Get(instanceId); ok {
		return v.(*models.CmdbProductOverview), nil
	}

	m := DataSource().Model(ctx)
	cmdbProductOverview := new(models.CmdbProductOverview)
	if err := m.Orm().Model(&models.CmdbProductOverview{}).Where("instance_id = ? and supplement_id = ''", instanceId).First(cmdbProductOverview).Error; err != nil {
		return nil, err
	}

	instanceIdOverViewInsMapCache.Set(instanceId, cmdbProductOverview, cache.DefaultExpiration)
	return cmdbProductOverview, nil
}

// GetCmdbProductOverviewByProductInfo 根据厂商-产品编码-账户ID-实例ID获取cmdb总表实例信息,分拆项都不为空的情况获取第一个,有空的分拆项取分拆项空的实例.
func GetCmdbProductOverviewByProductInfo(ctx context.Context, vendor, accountId, instanceId string, productCodes []string) models.CmdbProductOverview {
	m := DataSource().Model(ctx)
	cmdb := make([]*models.CmdbProductOverview, 0)
	m.Orm().Model(&models.CmdbProductOverview{}).
		Where("vendor= ? and product_code in ? and account_id = ? and instance_id = ?", vendor, productCodes, accountId, instanceId).Find(&cmdb)

	if len(cmdb) == 0 {
		return models.CmdbProductOverview{}
	}
	for _, c := range cmdb {
		if c.SupplementID == "" {
			return *c
		}
	}
	return *cmdb[0]
}
