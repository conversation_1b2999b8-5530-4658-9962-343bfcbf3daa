package bizutils

import (
	"context"

	"git.haier.net/devops/ops-golang-common/models/models"
)

func SaveTag(ctx context.Context, tag *models.TagInfo) error {
	m := DataSource().Model(ctx)

	res := m.Orm().Where(models.TagInfo{
		TagName:    tag.TagName,
		HrUser:     tag.HrUser,
		ResourceID: tag.ResourceID,
	}).Assign(models.TagInfo{
		Vendor:       tag.Vendor,
		Account:      tag.Account,
		ResourceID:   tag.ResourceID,
		ResourceType: tag.ResourceType,
		TagName:      tag.TagName,
		TagValue:     tag.TagValue,
		Scope:        tag.Scope,
		HrUser:       tag.HrUser,
		Comment:      tag.Comment,
	}).FirstOrCreate(tag)
	if res.Error != nil {
		return res.Error
	}
	return nil
}
