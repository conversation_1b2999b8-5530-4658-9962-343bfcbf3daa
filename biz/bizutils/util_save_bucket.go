package bizutils

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/LPX3F8/orderedmap"

	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func CreateOrUpdateBucket(ctx context.Context, bucketInfos ...*models.BucketInfo) error {
	data := orderedmap.New[string, *models.BucketInfo]()
	for _, b := range bucketInfos {
		data.Store(b.Vendor+b.AccountName+b.Bucket<PERSON>ame+b.Location+b.StorageClass, b)
	}
	bucketInfos = data.Slice()
	logger.Infof(ctx, "got %d bucket records, ready to save ...", len(bucketInfos))

	// m := config.Global().GetDefaultStore().Model(ctx)
	// cmdb := make([]models.CmdbProductOverview, 0)
	// if err := m.Orm().Model(&models.CmdbProductOverview{}).
	// 	Where("vendor='aliyun' and product_code ='oss' and account_name =? and supplement_id !='' ", "hr690n").
	// 	Find(&cmdb).Error; err != nil {
	// }

	// dict := make(map[string]int, 0)
	// for _, c := range cmdb {
	// 	var e bool
	// 	for _, b := range bucketInfos {
	// 		if c.SupplementID == b.Name {
	// 			e = true
	// 			break
	// 		}
	// 	}

	// 	if !e {
	// 		dict[c.SupplementID] = 1
	// 	}
	// }

	// fmt.Println(utils.JsonString(dict))

	// for _, b := range bucketInfos {
	// 	if _, ok := dict[b.AccountName]; ok {
	// 		dict[b.AccountName] = dict[b.AccountName] + 1
	// 	} else {
	// 		dict[b.AccountName] = 1
	// 	}
	// }
	// fmt.Println(utils.JsonString(bucketInfos))

	b := tools.NewBatch[*models.BucketInfo, error](ctx,
		batch.WithBatchSize(10),
		batch.WithShowLog(true),
		batch.WithLogPrintStep(500))
	b.Run(bucketInfos, func(ctx context.Context, bucket *models.BucketInfo) (err error, _ error) {
		defer func() {
			if e := recover(); e != nil {
				notice.SendErrorMessage("存储对象bucket同步方法异常", fmt.Sprintf("堆栈信息：\n%s", debug.Stack()))
				err = fmt.Errorf("存储对象bucket同步方法异常：%s", e)
			}
		}()
		err = saveBucket(ctx, bucket)
		return
	})

	return b.Error()
}

func saveBucket(ctx context.Context, bucket *models.BucketInfo) error {
	m := DataSource().Model(ctx)
	res := m.Orm().Where(models.BucketInfo{
		Vendor:      bucket.Vendor,
		AccountName: bucket.AccountName,
		BucketName:  bucket.BucketName,
		Location:    bucket.Location,
	}).Assign(models.BucketInfo{
		Scode:                       bucket.Scode,
		AggregatedId:                bucket.AggregatedId,
		CreationDate:                bucket.CreationDate,
		StorageClass:                bucket.StorageClass,
		AccessMonitor:               bucket.AccessMonitor,
		ExtranetEndpoint:            bucket.ExtranetEndpoint,
		IntranetEndpoint:            bucket.IntranetEndpoint,
		DataRedundancyType:          bucket.DataRedundancyType,
		Versioning:                  bucket.Versioning,
		TransferAcceleration:        bucket.TransferAcceleration,
		CrossRegionReplication:      bucket.CrossRegionReplication,
		Storage:                     bucket.Storage,
		ObjectCount:                 bucket.ObjectCount,
		MultipartUploadCount:        bucket.MultipartUploadCount,
		LiveChannelCount:            bucket.LiveChannelCount,
		MultipartPartCount:          bucket.MultipartPartCount,
		LastModifiedTime:            bucket.LastModifiedTime,
		InfrequentAccessStorage:     bucket.InfrequentAccessStorage,
		InfrequentAccessRealStorage: bucket.InfrequentAccessRealStorage,
		InfrequentAccessObjectCount: bucket.InfrequentAccessObjectCount,
		ArchiveStorage:              bucket.ArchiveStorage,
		ArchiveRealStorage:          bucket.ArchiveRealStorage,
		ArchiveObjectCount:          bucket.ArchiveObjectCount,
		ColdArchiveStorage:          bucket.ColdArchiveStorage,
		ColdArchiveRealStorage:      bucket.ColdArchiveRealStorage,
		ColdArchiveObjectCount:      bucket.ColdArchiveObjectCount,
	}).FirstOrCreate(bucket)
	if res.Error != nil {
		return res.Error
	}
	return nil
}

// func ossRegion(location string) string {
// 	var region string
// 	parts := strings.Split(location, "-")
// 	for index, p := range parts {
// 		if index == 0 {
// 			continue
// 		}
// 		region = region + "-" + p
// 	}
// 	if len(region) > 0 {
// 		return region[1:]
// 	} else {
// 		return region
// 	}
// }
