package bizutils

import (
	"strings"

	"git.haier.net/devops/ops-golang-common/common"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ecs"
)

func TryParseEnvFromResourceGroup(name string) (env string) {
	name = strings.TrimSpace(strings.ToLower(name))
	switch name {
	case "测试":
		return common.EnvTest
	case "开发":
		return common.EnvDev
	case "生产":
		return common.EnvProd
	case "验证", "验收":
		return common.EnvUat
	default:
		return ""
	}
}

func TryParseEnvFromInstanceTag(tags ...ecs.Tag) (env string) {
	for _, tag := range tags {
		switch strings.ToLower(strings.TrimSpace(tag.TagKey)) {
		case "env", "环境":
			env = UnifyEnv(strings.TrimSpace(strings.ToLower(tag.TagValue)))
			return
		}
	}
	return
}

func TryParseSCode(name string) string {
	name = strings.TrimSpace(name)
	for _, str := range strings.Split(name, "-") {
		if IsSCode(str) {
			return str
		}
	}
	return ""
}

func TryParseEnv(name string) (env string) {
	switch {
	case strings.HasPrefix(name, "prod-"), strings.HasPrefix(name, "prod_"),
		strings.HasSuffix(name, "-prod"), strings.HasSuffix(name, "_prod"),
		strings.HasPrefix(name, "prd-"), strings.HasPrefix(name, "prd_"),
		strings.HasSuffix(name, "-prd"), strings.HasSuffix(name, "_prd"):
		env = common.EnvProd
	case strings.HasPrefix(name, "test-"), strings.HasPrefix(name, "test_"),
		strings.HasSuffix(name, "-test"), strings.HasSuffix(name, "_test"),
		strings.HasPrefix(name, "cs-"), strings.HasPrefix(name, "cs_"),
		strings.HasSuffix(name, "-cs"), strings.HasSuffix(name, "_cs"):
		env = common.EnvTest
	case strings.HasPrefix(name, "dev-"), strings.HasPrefix(name, "dev_"),
		strings.HasSuffix(name, "-dev"), strings.HasSuffix(name, "_dev"):
		env = common.EnvDev
	case strings.EqualFold(name, "yanshou"),
		strings.HasPrefix(name, "ys-"), strings.HasPrefix(name, "ys_"),
		strings.HasSuffix(name, "-ys"), strings.HasSuffix(name, "_ys"),
		strings.HasPrefix(name, "yz-"), strings.HasPrefix(name, "yz_"),
		strings.HasSuffix(name, "-yz"), strings.HasSuffix(name, "_yz"):
		env = common.EnvUat
	case
		strings.HasPrefix(name, "pre-"), strings.HasPrefix(name, "pre_"),
		strings.HasSuffix(name, "-pre"), strings.HasSuffix(name, "_pre"):
		env = common.EnvPre
	}
	return env
}
