package bizutils

import (
	"fmt"
	"testing"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/stretchr/testify/assert"
)

func TestUnifyNAString(t *testing.T) {
	a := assert.New(t)
	ctx := context.NewContext()
	m := DataSource().Model(ctx)
	obj := new(models.HostInfo)
	err := m.Orm().Table("rc_host_info").Where("private_ip = ?", "************").Find(&obj).Error
	a.NoError(err)
	fmt.Println(UnifyNAString(obj.Region))
	fmt.Println(obj.Region)
	_ = SaveHost(ctx, obj)
}

func TestRdsSave(t *testing.T) {
	a := assert.New(t)
	ctx := context.NewContext()
	m := DataSource().Model(ctx)
	obj := new(models.DatabaseInfo)
	err := m.Orm().Table("rc_database_info").Where("instance_id = ?", "2e56d50fac3341e7ab1a1b53a251b752in01").Find(&obj).Error
	a.NoError(err)
	// err = SaveDatabaseInfoHistory(ctx, nil, obj, DelOperaTypeUpdate)
	err = CreateOrUpdateRds(ctx, obj)
	fmt.Println(err)
}
