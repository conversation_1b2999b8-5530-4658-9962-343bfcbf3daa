package bizutils

import (
	"context"
	"time"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/uuid"
	"github.com/aws/smithy-go/ptr"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func CreateOrUpdateMiddleware(ctx context.Context, MiddlewareInfos ...*models.MiddlewareInfo) error {
	logger.Infof(ctx, "got %d rds records, ready to save ...", len(MiddlewareInfos))

	b := tools.NewBatch[*models.MiddlewareInfo, error](ctx,
		batch.WithBatchSize(10),
		batch.WithShowLog(true),
		batch.WithLogPrintStep(500))
	b.Run(MiddlewareInfos, func(ctx context.Context, info *models.MiddlewareInfo) (error, error) {
		var isExcludeEnv bool // 更新时是否排除env字段的更新
		// get env from resource_applications
		scode, env, err := ApplicationApplySCodeEnv(ctx, info.InstanceId)
		if err != nil {
			return err, nil
		}
		if scode != empty {
			info.SCode = scode
		}
		info.Env = UnifyEnv(info.Env)
		if env != empty {
			info.Env = env
		}
		// 资源申请和云厂商环境标签数据不为空
		if info.Env == empty {
			isExcludeEnv = true
		}
		if info.UniRegionId == "" {
			info.UniRegionId = GetUnionRegion(ctx, info.Vendor.String(), info.Region)
		}
		if info.SCode != "" && IsSCode(info.SCode) {
			project, _ := api.HdsClient().QueryAlmProject(info.SCode)
			if project != nil {
				info.Project = project.Id
				info.Domain = project.Domain
				info.Team = project.OrgName
				info.OwnerId = project.Owner
				info.OwnerName = project.OwnerName
			}
		}
		info.ResourceId = uuid.MiddlewareResourceId(info)
		m := DataSource().Model(ctx)

		// 记录变更历史
		middlewareFoundInfo := models.MiddlewareInfo{}
		if err := m.Orm().Where(models.HostInfo{Vendor: info.Vendor, InstanceId: info.InstanceId}).Take(&middlewareFoundInfo).Error; err == nil {
			// 资源申请和云厂商环境标签都为空的情况使用数据库原有环境信息
			if isExcludeEnv {
				info.Env = middlewareFoundInfo.Env
			}
			if isMiddlewareInfoDifferent(&middlewareFoundInfo, info) {
				err = saveMiddlewareHistory(ctx, &middlewareFoundInfo)
				if err != nil {
					return err, nil
				}
			}
		}

		err = m.Orm().Where(models.MiddlewareInfo{
			Vendor:     info.Vendor,
			InstanceId: info.InstanceId,
		}).Assign(models.MiddlewareInfo{
			Model:            m,
			Vendor:           info.Vendor,
			AccountName:      info.AccountName,
			InstanceId:       info.InstanceId,
			InstanceName:     info.InstanceName,
			CreationTime:     info.CreationTime,
			ExpiredTime:      info.ExpiredTime,
			IsDeleted:        ptr.Bool(false),
			InsType:          info.InsType,
			InsVersion:       info.InsVersion,
			InsCategory:      info.InsCategory,
			InsStatus:        info.InsStatus,
			ClassCode:        info.ClassCode,
			ResourceGroup:    info.ResourceGroup,
			ResourceId:       info.ResourceId,
			Region:           info.Region,
			Zone:             info.Zone,
			UniRegionId:      info.UniRegionId,
			PrivateEndpoints: info.PrivateEndpoints,
			PublicEndpoints:  info.PublicEndpoints,
			PayType:          info.PayType,
			Description:      info.Description,
			DiskSize:         info.DiskSize,
			DiskType:         info.DiskType,
			TopicNum:         info.TopicNum,
			TopicQuota:       info.TopicQuota,
			GroupNum:         info.GroupNum,
			GroupQuota:       info.GroupQuota,
			MaxTPS:           info.MaxTPS,
			MaxBandwidth:     info.MaxBandwidth,
			VpcId:            info.VpcId,
			SubnetId:         info.SubnetId,
			Env:              info.Env,
			Project:          info.Project,
			SCode:            info.SCode,
			AggregatedId:     info.AggregatedId,
		}).FirstOrCreate(info).Error
		return err, nil
	})

	return tools.MergeErrors(b.Outs())
}

// isHostInfoDifferent true:不同
func isMiddlewareInfoDifferent(foundInfo, middlewareInfo *models.MiddlewareInfo) bool {
	if foundInfo == nil || middlewareInfo == nil {
		return true
	}

	return !(foundInfo.SCode == middlewareInfo.SCode &&
		foundInfo.OwnerId == middlewareInfo.OwnerId &&
		foundInfo.ResourceGroup == middlewareInfo.ResourceGroup &&
		foundInfo.InsStatus == middlewareInfo.InsStatus)
}

func saveMiddlewareHistory(ctx context.Context, middlewareInfo *models.MiddlewareInfo) error {
	m := DataSource().Model(ctx)
	history := &models.MiddlewareInfoDelHistory{
		Vendor:           middlewareInfo.Vendor,
		AccountName:      middlewareInfo.AccountName,
		InstanceId:       middlewareInfo.InstanceId,
		InstanceName:     middlewareInfo.InstanceName,
		InstanceType:     middlewareInfo.InsType,
		InstanceVersion:  middlewareInfo.InsVersion,
		InstanceCategory: middlewareInfo.InsCategory,
		InstanceStatus:   middlewareInfo.InsStatus,
		IsDeleted:        middlewareInfo.IsDeleted,
		ClassCode:        middlewareInfo.ClassCode,
		CreationTime:     middlewareInfo.CreationTime,
		ExpiredTime:      middlewareInfo.ExpiredTime,
		Scode:            middlewareInfo.SCode,
		Project:          middlewareInfo.Project,
		Env:              middlewareInfo.Env,
		ResourceGroup:    middlewareInfo.ResourceGroup,
		DiskSize:         float64(middlewareInfo.DiskSize),
		DiskType:         middlewareInfo.DiskType,
		VpcId:            middlewareInfo.VpcId,
		SubnetId:         middlewareInfo.SubnetId,
		Region:           middlewareInfo.Region,
		UniRegionId:      middlewareInfo.UniRegionId,
		PrivateEndpoints: middlewareInfo.PrivateEndpoints.String(),
		PublicEndpoints:  middlewareInfo.PublicEndpoints.String(),
		PayType:          middlewareInfo.PayType,
		Zone:             middlewareInfo.Zone,
		Description:      middlewareInfo.Description,
		ResourceId:       middlewareInfo.ResourceId,
		Domain:           middlewareInfo.Domain,
		Team:             middlewareInfo.Team,
		OwnerId:          middlewareInfo.OwnerId,
		OwnerName:        middlewareInfo.OwnerName,
		TopicNum:         middlewareInfo.TopicNum,
		TopicQuota:       middlewareInfo.TopicQuota,
		GroupNum:         middlewareInfo.GroupNum,
		GroupQuota:       middlewareInfo.GroupQuota,
		//MaxTps:           middlewareInfo.MaxTps,
		MaxBandwidth:  middlewareInfo.MaxBandwidth,
		OperationType: "update",
		CreateTime:    time.Now(),
		UpdateTime:    time.Now(),
	}
	return m.Orm().Create(history).Error
}
