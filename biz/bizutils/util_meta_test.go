package bizutils

import (
	"fmt"
	"testing"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/stretchr/testify/assert"
)

func TestGetDBConfig(t *testing.T) {
	var c = config.Global()
	m := c.GetStore(c.DefaultStoreName).Model(context.NewContext())
	a := assert.New(t)
	val, err := GetDBConfig(m, "bill_start_time")
	a.NoError(err)
	fmt.Println(val)
}
