package bizutils

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/aws/smithy-go/ptr"
)

func CreateOrUpdateNode(ctx context.Context, nodes ...*models.DatabaseNode) error {
	if len(nodes) == 0 {
		return nil
	}
	instanceId := nodes[0].InstanceId
	if instanceId == "" {
		return fmt.Errorf("instanceId is empty")
	}

	// 先删除
	m := DataSource().Model(ctx)
	err := m.Orm().Table(nodes[0].TableName()).Where(models.DatabaseNode{InstanceId: instanceId}).Update("is_deleted", "1").Error
	if err != nil {
		return err
	}

	if len(nodes) == 1 {
		err := SaveDatabaseNode(ctx, instanceId, nodes[0])
		return err
	}

	logger.Infof(ctx, "got %d database node records, ready to save ...", len(nodes))
	b := tools.NewBatch[*models.DatabaseNode, error](ctx,
		batch.WithShowLog(true),
		batch.WithLogPrintStep(100))
	b.Run(nodes, func(ctx context.Context, node *models.DatabaseNode) (err error, _ error) {
		defer func() {
			if e := recover(); e != nil {
				notice.SendErrorMessage("数据库节点同步方法异常", fmt.Sprintf("堆栈信息：\n%s", debug.Stack()))
				err = fmt.Errorf("数据库节点同步方法异常：%s", e)
			}
		}()
		err = SaveDatabaseNode(ctx, instanceId, node)
		return
	})

	return b.Error()
}

func SaveDatabaseNode(ctx context.Context, instanceId string, node *models.DatabaseNode) error {
	m := DataSource().Model(ctx)

	res := m.Orm().Where(models.DatabaseNode{
		InstanceId: instanceId,
		NodeId:     node.NodeId,
	}).Assign(models.DatabaseNode{
		Cpu:             node.Cpu,
		Memory:          node.Memory,
		NodeClass:       node.NodeClass,
		NodeDescription: node.NodeDescription,
		NodeRole:        node.NodeRole,
		NodeStatus:      node.NodeStatus,
		Zone:            node.Zone,
		IsDeleted:       ptr.Bool(false),
	}).FirstOrCreate(node)
	if res.Error != nil {
		return res.Error
	}
	return nil
}
