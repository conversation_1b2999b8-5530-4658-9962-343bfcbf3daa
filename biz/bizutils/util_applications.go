package bizutils

import (
	"context"
	"fmt"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"
)

func ApplicationApplySCodeEnv(ctx context.Context, instanceId string) (scode string, env string, err error) {
	instance := new(models.ResourceApplicationInstance)
	m := DataSource().Model(ctx)
	if err = m.Orm().Model(&models.ResourceApplicationInstance{}).
		Where("type = 1 and instance_id =? ", instanceId).Having("MIN(detail_id)").First(instance).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return empty, empty, nil
		}
		return empty, empty, err
	}
	// 创建资源申请的时间是在两周内使用资源申请的S码
	now := time.Now()
	diff := now.Sub(instance.CreateTime)
	if diff < 0 {
		diff = -diff
	}
	if diff <= 14*24*time.Hour {
		scode = instance.SCode
	}

	if instance.DetailType == empty {
		return scode, empty, nil
	}

	tableName := fmt.Sprintf("console_resource_application_details_%s", instance.DetailType)
	switch instance.DetailType {
	case "db":
		tableName = "console_resource_application_details_rds"
	case "fuzzy":
		tableName = "console_resource_application_fuzzy"
	}
	sql := fmt.Sprintf("select env from %s where id =? ", tableName)
	if err = m.Orm().Raw(sql, instance.DetailId).First(&env).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return scode, empty, nil
		}
	}

	return scode, env, nil
}
