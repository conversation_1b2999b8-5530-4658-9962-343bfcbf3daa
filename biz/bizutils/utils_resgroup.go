package bizutils

import (
	"context"
	"strings"
	"unicode/utf8"

	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	epsModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

type EnterpriseProjectGetter interface {
	GetHuaweiEnterpriseProject(client *huaweicloud.EpsClient, ctx context.Context, projectId string) *epsModel.EpDetail
}

func ParseHuaweiEnvProject(ctx context.Context, instanceName, epsId string, epsClient *huaweicloud.EpsClient, epsGetter EnterpriseProjectGetter) (env, code, project string) {
	if epsId == "" {
		return
	}
	enterpriseProject := epsGetter.GetHuaweiEnterpriseProject(epsClient, ctx, epsId)
	if enterpriseProject == nil {
		env = TryParseEnv(instanceName)
		return
	}
	env = enterpriseProject.Type.Value()
	for _, tmp := range strings.Split(enterpriseProject.Name, "-") {
		if IsSCode(tmp) {
			code = tmp
		}
		if match := TryParseEnvFromResourceGroup(tmp); match != "" {
			env = match
		}
	}
	if code != "" {
		if projectInfo, _ := api.HdsClient().QueryAlmProject(code); projectInfo != nil {
			project = projectInfo.Id
		}
	}
	if project == "" {
		project = "default"
	}
	return
}

type ResourceGroupGetter interface {
	GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup
}

func ParseAliyunEnvProject(resourceManagerClient *aliyun.ResourceClient, getter ResourceGroupGetter, resGroupId string) (env, code, project string) {
	// 尝试从资源组中解析环境和代码
	resGroup := getter.GetAliyunResourceGroup(resourceManagerClient, resGroupId)
	if resGroup != nil {
		for _, tmp := range strings.Split(resGroup.DisplayName, "-") {
			if IsSCode(tmp) {
				code = tmp
			}
			if match := TryParseEnvFromResourceGroup(tmp); match != "" {
				env = match
			}
		}
	}

	// 尝试从硬编码中查找项目名称和代码
	if projectName, ok := config.Global().AliyunAccountMapping[resourceManagerClient.Name()]; code == "" && ok {
		code = projectName
	}
	// 截取后六位做为关联的S码
	if resGroup != nil && resGroupId != "default" && code == "" && utf8.RuneCount([]byte(resGroup.DisplayName)) >= 6 {
		code = tools.SubstringLastN(resGroup.DisplayName, 6)
	}
	// 尝试从 ALMS 中查找项目 ID
	if code != "" {
		if projectInfo, _ := api.HdsClient().QueryAlmProject(code); projectInfo != nil {
			project = projectInfo.Id
		}
	}
	// 如果没有找到项目名称，则使用默认值
	if project == "" {
		project = "default"
	}
	return
}
