package bizutils

import (
	"fmt"
	"testing"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/stretchr/testify/assert"
)

func TestGetCostUnitMoveRules(t *testing.T) {
	a := assert.New(t)
	rules, err := GetCostUnitMoveRules(context.NewContext(), "<EMAIL>", []string{string(hbc.AliCloud)})
	a.NoError(err)
	a.NotEmpty(rules)
	fmt.Println(utils.JsonString(rules))
}
