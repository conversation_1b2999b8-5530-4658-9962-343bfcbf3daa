package bizutils

import (
	"context"
	"fmt"
	"runtime/debug"
	"strings"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/ormtype"
	"git.haier.net/devops/ops-golang-common/models/uuid"
	"github.com/aws/smithy-go/ptr"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var hostMaintain = map[hbc.CloudVendor]*ormtype.StringSlice{
	hbc.AliCloud:             {"王炳焜|20011063", "胡家欣|22025559"},
	hbc.AliCloudDedicated:    {"兰惠|21045395", "王炳焜|20011063"},
	hbc.HuaweiCloud:          {"胡家欣|22025559"},
	hbc.HuaweiCloudDedicated: {"杜雷|22054388"},
	hbc.TencentCloud:         {"王伟诚|01516780"},
	hbc.AWS:                  {"孙瑞|19033291"},
	hbc.Azure:                {"孙瑞|19033291"},
	hbc.Private:              {"杜雷|22054388", "兰惠|21045395"},
	hbc.JXJG:                 {"HOC服务台"},
}

func CreateOrUpdateHost(ctx context.Context, hosts ...*models.HostInfo) error {
	if len(hosts) == 1 {
		err := SaveHost(ctx, hosts[0])
		return err
	}

	logger.Infof(ctx, "got %d host records, ready to save ...", len(hosts))
	b := tools.NewBatch[*models.HostInfo, error](ctx,
		batch.WithShowLog(true),
		batch.WithLogPrintStep(100))
	b.Run(hosts, func(ctx context.Context, host *models.HostInfo) (err error, _ error) {
		defer func() {
			if e := recover(); e != nil {
				notice.SendErrorMessage("主机同步方法异常", fmt.Sprintf("堆栈信息：\n%s", debug.Stack()))
				err = fmt.Errorf("主机同步方法异常：%s", e)
			}
		}()
		err = SaveHost(ctx, host)
		return
	})

	return b.Error()
}

func SaveHost(ctx context.Context, host *models.HostInfo) error {
	m := DataSource().Model(ctx)

	// 退订下线，跳过更新
	var record models.HostInfo
	result := m.Orm().Where(models.HostInfo{
		Vendor:      host.Vendor,
		AccountName: host.AccountName,
		InstanceId:  host.InstanceId,
	}).First(&record)
	if result.Error == nil {
		if record.SubscriptionStatus == 0 {
			fmt.Printf("host %s is deleted, skip\n", host.InstanceId)
			return nil
		}
	}
	host.Scode = strings.TrimSpace(host.Scode)
	if IsSCode(host.Scode) {
		if host.ResourceGroup == "" {
			host.ResourceGroup = host.Scode
		}
		projectInfo, err := api.HdsClient().QueryAlmProject(host.Scode)
		if err != nil {
			logger.Warnf(ctx, "query scode project failed: %s", err)
			return nil
		}
		if projectInfo != nil {
			host.Project = projectInfo.Id
			host.Domain = projectInfo.Domain
			host.Team = projectInfo.OrgName
			host.OwnerName = projectInfo.OwnerName
			host.OwnerId = projectInfo.Owner
		}
	}
	host.ResourceId = uuid.HostResourceId(host)
	host.OsType = UnifyOsType(host.OsType)
	host.Region = UnifyNAString(host.Region)
	host.Zone = UnifyNAString(host.Zone)
	host.InstanceName = UnifyNAString(host.InstanceName)
	host.UniRegionId = GetUnionRegion(ctx, host.Vendor.String(), host.Region)
	host.HostType = UnifyHostType(host.HostType)
	if host.IsDeleted == nil {
		host.IsDeleted = ptr.Bool(false)
	}
	if host.Numa == nil {
		host.Numa = ptr.Bool(false)
	}
	if host.Maintainer == nil || len(host.Maintainer.Slice()) == 0 {
		host.Maintainer = hostMaintain[host.Vendor]
	}
	var isExcludeEnv bool // 更新时是否排除env字段的更新
	// get env from resource_applications
	scode, env, err := ApplicationApplySCodeEnv(ctx, host.InstanceId)
	if err != nil {
		return err
	}
	if scode != empty {
		host.Scode = scode
	}
	if env != empty {
		host.Env = env
	}
	// get env from host instance_name
	if host.Env == empty {
		host.Env = TryParseEnv(host.InstanceName)
		if host.Env == empty {
			isExcludeEnv = true
		}
	}

	if host.Scode == empty && host.Vendor == hbc.HuaweiCloud && host.AccountName == "hr690f" {
		host.Scode = "S04076"
	}
	// S码为空发送通知
	if host.Scode == empty {
		notice.SendWarnMessage("同步ECS资源S码为空", "实例ID: "+host.InstanceId)
	}

	// 记录变更历史
	hostFoundInfo := models.HostInfo{}
	if err := m.Orm().Where(models.HostInfo{Vendor: host.Vendor, AccountName: host.AccountName, InstanceId: host.InstanceId}).Take(&hostFoundInfo).Error; err == nil {
		// 资源申请和云厂商环境标签都为空的情况使用数据库原有环境信息
		if isExcludeEnv {
			host.Env = hostFoundInfo.Env
		}
		if isHostInfoDifferent(&hostFoundInfo, host) {
			if err = SaveHostInfoHistory(ctx, nil, &hostFoundInfo, DelOperaTypeUpdate); err != nil {
				return err
			}
		}
		if host.MonitoringMode != MonitoringModeIsNotNecessary && host.MonitoringMode != MonitoringModeIsHOC { // monitor: false 标签是优先级更高;接入了rrs新资源临时设置hoc监控时不重置.
			host.MonitoringMode = hostFoundInfo.MonitoringMode
		}
	}

	res := m.Orm().Where(models.HostInfo{
		Vendor:      host.Vendor,
		AccountName: host.AccountName,
		InstanceId:  host.InstanceId,
	}).Assign(models.HostInfo{
		AccountName:     host.AccountName,
		InstanceName:    host.InstanceName,
		CreationTime:    host.CreationTime,
		ExpiredTime:     host.ExpiredTime,
		IsDeleted:       host.IsDeleted,
		PrivateIp:       host.PrivateIp,
		PublicIp:        host.PublicIp,
		NetworkType:     host.NetworkType,
		VpcId:           host.VpcId,
		SubnetId:        host.SubnetId,
		Scode:           host.Scode,
		Project:         host.Project,
		Env:             host.Env,
		ResourceGroup:   host.ResourceGroup,
		OsType:          host.OsType,
		OsName:          host.OsName,
		OsArch:          host.OsArch,
		Numa:            host.Numa,
		ImageId:         host.ImageId,
		Cpu:             host.Cpu,
		Memory:          host.Memory,
		DiskSize:        host.DiskSize,
		DiskType:        host.DiskType,
		HostStatus:      host.HostStatus,
		UniHostStatus:   host.UniHostStatus,
		HostType:        host.HostType,
		Region:          host.Region,
		Zone:            host.Zone,
		ClassCode:       host.ClassCode,
		ChargeType:      host.ChargeType,
		Rack:            host.Rack,
		ProviderName:    host.ProviderName,
		BrandName:       host.BrandName,
		HostModel:       host.HostModel,
		Sn:              host.Sn,
		Description:     host.Description,
		ResourceId:      host.ResourceId,
		UniRegionId:     host.UniRegionId,
		Domain:          host.Domain,
		Team:            host.Team,
		OwnerId:         host.OwnerId,
		OwnerName:       host.OwnerName,
		Maintainer:      host.Maintainer,
		GpuInfo:         host.GpuInfo,
		GpuAmount:       host.GpuAmount,
		GpuModel:        host.GpuModel,
		GpuMemory:       host.GpuMemory,
		AggregatedId:    host.AggregatedId,
		MonitoringMode:  host.MonitoringMode,
		MaintainingTeam: host.MaintainingTeam,
		SDKClientName:   host.SDKClientName,
	}).FirstOrCreate(host)
	if res.Error != nil {
		return res.Error
	}

	if host.MonitoringMode == MonitoringModeIsNotNecessary {
		if host.Vendor == hbc.AliCloud || host.Vendor == hbc.AliCloudDedicated || host.Vendor == hbc.HuaweiCloud || host.Vendor == hbc.TencentCloud || host.Vendor == hbc.AWS {
			_ = SaveTag(ctx, &models.TagInfo{
				Vendor:       host.Vendor,
				Account:      host.AccountName,
				ResourceID:   host.ResourceId,
				ResourceType: "ecs",
				TagName:      "monitoring",
				TagValue:     "false",
				Scope:        "all",
				HrUser:       "CMDB",
				Comment:      "EMR主机不监控",
			})
		}
	}

	return nil
}

// isHostInfoDifferent true:不同
func isHostInfoDifferent(foundInfo, hostInfo *models.HostInfo) bool {
	if foundInfo == nil || hostInfo == nil {
		return true
	}

	return !(foundInfo.Scode == hostInfo.Scode &&
		foundInfo.OwnerId == hostInfo.OwnerId &&
		foundInfo.ResourceGroup == hostInfo.ResourceGroup &&
		foundInfo.HostStatus == hostInfo.HostStatus)
}

func RemoveDuplicates(strs []string) []string {
	seen := make(map[string]struct{})
	result := []string{}

	for _, s := range strs {
		if _, exists := seen[s]; !exists {
			seen[s] = struct{}{}
			result = append(result, s)
		}
	}
	return result
}
