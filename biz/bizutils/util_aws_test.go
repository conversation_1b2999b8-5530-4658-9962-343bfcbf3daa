package bizutils

import (
	"fmt"
	"testing"
)

func TestAWS(t *testing.T) {
	scode, _, err := GetResourceSupplement("a3e803d1cec5b0a02bd3e4b0338c7dc9", "a3e803d1cec5b0a02bd3e4b0338c7dc9", "a3e803d1cec5b0a02bd3e4b0338c7dc9")
	if err != nil {
		fmt.Println("err: " + err.<PERSON>rror())
	}
	fmt.Println(scode)
}

func TestAWSUsage(t *testing.T) {
	rate, err := GetResourceUsageAgreement("ecs", "i-0c5835a5bd88ba45d1111")
	if err != nil {
		fmt.Println("err: " + err.Error())
	}
	fmt.Println(rate)
}
