package bizutils

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"
)

const (
	key_aws   = "bill:aws:resource"
	None      = "none"
	semicolon = ";"
)

// get scode resourceId
func GetResourceSupplement(account, productCode, resourceId string) (scode string, rawInstanceId string, err error) {
	conn, err := c.RedisConn()
	if err != nil {
		return empty, empty, err
	}
	key := fmt.Sprintf("%s:%s:%s:%s", key_aws, account, productCode, resourceId)
	// 根据aggregatedId获取scode
	value, err := conn.Get(key)
	if err != nil {
		return empty, empty, err
	}
	if value != empty {
		rawInstanceId, scode, found := strings.Cut(value, semicolon)
		if found {
			return scode, rawInstanceId, nil
		}
	}
	// search db
	m := DataSource().Model(context.Background())
	resource := new(models.ResourceSupplement)
	if err := m.Orm().Model(&models.ResourceSupplement{}).Where("vendor ='aws' AND account = ? AND code = ? AND resource_id = ?",
		account, productCode, resourceId).First(resource).Error; err != nil {
		return empty, empty, err
	}

	// 设置过期时间
	baseDuration := 1 * time.Hour
	randomDuration := time.Duration(rand.Intn(60)) * time.Minute
	expiration := baseDuration + randomDuration

	v := fmt.Sprintf("%s"+semicolon+"%s", resource.RawResourceId, resource.Scode)
	if _, err := conn.SetNX(key, v, expiration); err != nil {
		return empty, empty, err
	}
	return resource.Scode, resource.RawResourceId, err
}

// aws SP RI
func GetResourceUsageAgreement(productCode, resourceId string) (string, error) {
	conn, err := c.RedisConn()
	if err != nil {
		return empty, err
	}
	key := fmt.Sprintf("%s:%s:%s", key_aws, "usage", resourceId)
	// 根据resourceId获取commit_rate承诺后单价
	value, err := conn.Get(key)
	if err != nil {
		return empty, err
	}
	if value == None {
		return None, nil
	}
	if value != empty {
		return value, nil
	}

	// search db
	m := DataSource().Model(context.Background())
	resource := new(models.ResourceUsageAgreement)
	err = m.Orm().Model(&models.ResourceUsageAgreement{}).Where("vendor ='aws' AND  resource_id = ? AND enable_time <= ?",
		resourceId, time.Now().Format(time.DateTime)).First(resource).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// cache empty resource_id commit_rate. set 30 min expiration.
			if _, err := conn.SetNX(key, None, time.Duration(rand.Intn(30))*time.Minute); err != nil {
				return empty, err
			}
			return None, nil
		}
		return empty, err
	}

	// 设置过期时间
	expiration := 1*time.Hour + time.Duration(rand.Intn(60))*time.Minute
	commitRate := fmt.Sprint(resource.CommitRate)
	if _, err := conn.SetNX(key, commitRate, expiration); err != nil {
		return empty, err
	}

	return commitRate, nil
}
