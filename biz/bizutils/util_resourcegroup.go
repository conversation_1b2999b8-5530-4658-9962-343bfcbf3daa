package bizutils

import (
	"sync"

	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
)

var (
	alyMtx      = new(sync.Mutex)
	alyResGroup = orderedmap.New[string, *resourcemanager.ResourceGroup]()
)

func GetAliyunResGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return LoadFromMap[string, *resourcemanager.ResourceGroup](alyMtx, alyResGroup, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}
