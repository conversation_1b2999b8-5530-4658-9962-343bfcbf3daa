package bizutils

import (
	"context"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/bssopenapi"

	"git.haier.net/devops/hcms-task-center/core/tools"
)

type ResourceInstance struct {
	models.Model

	CommodityCode    string `gorm:"column:commodity_code" json:"commodity_code"`         // 资源的商品code。
	ResourceUserName string `gorm:"column:resource_user_name" json:"resource_user_name"` // 资源属主的用户名。
	CommodityName    string `gorm:"column:commodity_name" json:"commodity_name"`         // 资源的商品名。
	ResourceUserId   int64  `gorm:"column:resource_user_id" json:"resource_user_id"`     // 资源的属主用户ID。
	ApportionName    string `gorm:"column:apportion_name" json:"apportion_name"`         // 资源分拆名。
	ApportionCode    string `gorm:"column:apportion_code" json:"apportion_code"`         // 资源分拆code。
	ResourceType     string `gorm:"column:resource_type" json:"resource_type"`           // 资源类型。
	ResourceNick     string `gorm:"column:resource_nick" json:"resource_nick"`           // 资源的自定义昵称。
	ResourceTag      string `gorm:"column:resource_tag" json:"resource_tag"`             // 资源的Tag标签。
	ResourceId       string `gorm:"column:resource_id" json:"resource_id"`               // 资源的实例ID。
	ResourceGroup    string `gorm:"column:resource_group" json:"resource_group"`         // 资源所属的资源组。
	RelatedResources string `gorm:"column:related_resources" json:"related_resources"`   // 资源实例相关的资源。
	ResourceStatus   string `gorm:"column:resource_status" json:"resource_status"`       // 资源状态。
}

func (r *ResourceInstance) TableName() string {
	return "resource_instance"
}

func CreateOrUpdateResourceInstance(ctx context.Context, resourceInstances ...bssopenapi.ResourceInstanceList) error {
	logger.Infof(ctx, "got %d instance records, ready to save ...", len(resourceInstances))

	b := tools.NewBatch[bssopenapi.ResourceInstanceList, error](ctx,
		batch.WithBatchSize(100),
		batch.WithShowLog(true),
		batch.WithLogPrintStep(100),
	)

	m := DataSource().Model(ctx)
	b.Run(resourceInstances, func(ctx context.Context, input bssopenapi.ResourceInstanceList) (error, error) {
		dto := ResourceInstance{
			CommodityCode:    input.CommodityCode,
			ResourceUserName: input.ResourceUserName,
			CommodityName:    input.CommodityName,
			ResourceUserId:   input.ResourceUserId,
			ApportionName:    input.ApportionName,
			ApportionCode:    input.ApportionCode,
			ResourceType:     input.ResourceType,
			ResourceNick:     input.ResourceNick,
			ResourceTag:      input.ResourceTag,
			ResourceId:       input.ResourceId,
			ResourceGroup:    input.ResourceGroup,
			RelatedResources: input.RelatedResources,
			ResourceStatus:   input.ResourceStatus,
		}

		err := m.Orm().Where(ResourceInstance{
			ResourceId:   input.ResourceId,
			ResourceType: input.ResourceType,
		}).Assign(ResourceInstance{
			CommodityCode:    input.CommodityCode,
			ResourceUserName: input.ResourceUserName,
			CommodityName:    input.CommodityName,
			ResourceUserId:   input.ResourceUserId,
			ApportionName:    input.ApportionName,
			ApportionCode:    input.ApportionCode,
			ResourceType:     input.ResourceType,
			ResourceNick:     input.ResourceNick,
			ResourceTag:      input.ResourceTag,
			ResourceId:       input.ResourceId,
			ResourceGroup:    input.ResourceGroup,
			RelatedResources: input.RelatedResources,
			ResourceStatus:   input.ResourceStatus,
		}).FirstOrCreate(&dto).Error

		return err, nil
	})

	return tools.MergeErrors(b.Outs())
}
