package bizutils

import (
	"context"
	"fmt"
	"time"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/ops-golang-common/models/models"
)

const (
	empty    = ""
	key_cmdb = "cmdb"
)

var (
	c = config.Global()
)

func SCodeFromCMDB(aggregatedId string) (string, error) {
	conn, err := c.RedisConn()
	if err != nil {
		return empty, err
	}
	key := fmt.Sprintf("%s:%s", key_cmdb, aggregatedId)

	// 根据aggregatedId获取scode
	scode, err := conn.Get(key)
	if err != nil {
		return empty, err
	}
	if scode != empty {
		return scode, nil
	}
	// search db
	m := DataSource().Model(context.Background())
	var product models.CmdbProductOverview
	if err = m.Orm().Where("aggregated_id = ?", aggregatedId).First(&product).Error; err != nil {
		return empty, err
	}
	if _, err = conn.SetNX(key, product.Scode, time.Hour*1); err != nil {
		return empty, err
	}
	return product.Scode, nil
}
