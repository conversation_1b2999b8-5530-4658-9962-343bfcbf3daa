package bizutils

import (
	"context"
	"sync"

	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"
)

var (
	unionRegionCache = make(map[string]*models.UnionRegionInfo)
	unionRegionLock  = new(sync.Mutex)
)

func GetUnionRegion(ctx context.Context, vendor, regionId string) string {
	unionRegionLock.Lock()
	defer unionRegionLock.Unlock()
	if region, ok := unionRegionCache[regionId]; ok {
		return region.UniRegionId
	}
	m := DataSource().Model(ctx)
	unionRegion := new(models.UnionRegionInfo)
	if err := m.Orm().Model(&models.UnionRegionInfo{}).Where("region_id = ? and vendor = ?", regionId, vendor).First(unionRegion).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return regionId
		}
		return ""
	}
	unionRegionCache[regionId] = unionRegion
	return unionRegion.UniRegionId
}
