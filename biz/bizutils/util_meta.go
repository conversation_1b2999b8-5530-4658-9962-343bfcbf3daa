package bizutils

import "git.haier.net/devops/ops-golang-common/models/models"

func GetDBConfig(model models.Model, name string) (string, error) {
	var param models.ConfigParam
	err := model.Orm().Where("name = ?", name).First(&param).Error
	return param.Value, err
}

func SaveDBConfig(model models.Model, name, value, creator string) error {
	var param models.ConfigParam

	// 根据name查询，如果存在则更新，不存在则插入
	model.Orm().Where("name = ?", name).First(&param)
	param.Name = name
	param.Value = value
	param.Creator = creator
	param.Modifier = creator
	// 保存
	return model.Orm().Save(&param).Error
}
