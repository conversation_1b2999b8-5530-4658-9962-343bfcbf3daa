package bizutils

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"
)

var txOpt = &sql.TxOptions{Isolation: sql.LevelRepeatableRead, ReadOnly: false}

func SaveDatabaseInfoHistory(ctx context.Context, tx *gorm.DB, rdsInfo *models.DatabaseInfo, operationType DelOperaType) (err error) {
	if tx == nil {
		m := config.Global().GetDefaultStore().Model(ctx)
		tx = m.Orm().Begin(txOpt)
		if err := tx.Error; err != nil {
			return fmt.Errorf("failed to start transaction: %v", err)
		}

		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			} else if err != nil {
				tx.Rollback()
			} else {
				err = tx.Commit().Error
			}
		}()
	}

	history := &models.DatabaseInfoDelHistory{
		Vendor:             rdsInfo.Vendor,
		AccountName:        rdsInfo.AccountName,
		InstanceId:         rdsInfo.InstanceId,
		InstanceName:       rdsInfo.InstanceName,
		InstanceType:       rdsInfo.InstanceType,
		InstanceRole:       rdsInfo.InstanceRole,
		IsDeleted:          rdsInfo.IsDeleted,
		Category:           rdsInfo.Category,
		PrimaryInstanceId:  rdsInfo.PrimaryInstanceId,
		HostInsId:          rdsInfo.HostInsId,
		Status:             rdsInfo.Status,
		ClassCode:          rdsInfo.ClassCode,
		ChargeType:         rdsInfo.ChargeType,
		CreationTime:       rdsInfo.CreationTime,
		ExpiredTime:        rdsInfo.ExpiredTime,
		Host:               rdsInfo.Host,
		Port:               rdsInfo.Port,
		EngineType:         rdsInfo.EngineType,
		EngineVersion:      rdsInfo.EngineVersion,
		Scode:              rdsInfo.Scode,
		Project:            rdsInfo.Project,
		Env:                rdsInfo.Env,
		ResourceGroup:      rdsInfo.ResourceGroup,
		Cpu:                rdsInfo.Cpu,
		Memory:             rdsInfo.Memory,
		DiskSize:           rdsInfo.DiskSize,
		DiskType:           rdsInfo.DiskType,
		VpcId:              rdsInfo.VpcId,
		SubnetId:           rdsInfo.SubnetId,
		DgDomain:           rdsInfo.DgDomain,
		DgId:               rdsInfo.DgId,
		Region:             rdsInfo.Region,
		UniRegionId:        rdsInfo.UniRegionId,
		Zone:               rdsInfo.Zone,
		Description:        rdsInfo.Description,
		ResourceId:         rdsInfo.ResourceId,
		Domain:             rdsInfo.Domain,
		Team:               rdsInfo.Team,
		OwnerId:            rdsInfo.OwnerId,
		OwnerName:          rdsInfo.OwnerName,
		MonitoringMode:     rdsInfo.MonitoringMode,
		DbmsMode:           rdsInfo.DbmsMode,
		IsSelfBuild:        rdsInfo.IsSelfBuild,
		SubscriptionStatus: rdsInfo.SubscriptionStatus,
		OperationType:      operationType.String(),
		CreateTime:         time.Now(),
		UpdateTime:         time.Now(),
	}
	return tx.Create(history).Error
}
