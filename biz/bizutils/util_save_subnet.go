package bizutils

import (
	"context"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/aws/smithy-go/ptr"

	"git.haier.net/devops/hcms-task-center/core/tools"
)

func CreateOrUpdateSubnet(ctx context.Context, infos ...*models.SubnetInfo) error {
	logger.Infof(ctx, "got %d subnet records, ready to save ...", len(infos))
	b := tools.NewBatch[*models.SubnetInfo, error](ctx,
		batch.WithBatchSize(10),
		batch.WithShowLog(true),
		batch.WithLogPrintStep(500),
	)

	m := DataSource().Model(ctx)
	b.Run(infos, func(ctx context.Context, info *models.SubnetInfo) (error, error) {
		err := m.Orm().Where(models.SubnetInfo{
			Vendor:   info.Vendor,
			SubnetId: info.SubnetId,
		}).Assign(models.SubnetInfo{
			AccountName:   info.AccountName,
			CreationTime:  info.CreationTime,
			VpcId:         info.VpcId,
			SubnetId:      info.SubnetId,
			ResourceGroup: info.ResourceGroup,
			Region:        info.Region,
			Zone:          info.Zone,
			State:         info.State,
			Cidr:          info.Cidr,
			Description:   info.Description,
			IsDeleted:     ptr.Bool(false),
		}).FirstOrCreate(info).Error
		return err, nil
	})

	return tools.MergeErrors(b.Outs())
}
