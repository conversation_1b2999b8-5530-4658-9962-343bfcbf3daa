package bizutils

import (
	"strings"

	"git.haier.net/devops/ops-golang-common/common"
	"github.com/iancoleman/strcase"

	"git.haier.net/devops/hcms-task-center/biz/notice"
)

func UnifyNAString(naString string) string {
	naString = strings.ToLower(strings.TrimSpace(naString))
	switch naString {
	case "n/a", "n/a_n/a", "_", "":
		return "N/A"
	default:
		return naString
	}
}

	func UnifyEnv(env string) string {
	env = strings.ToLower(strings.TrimSpace(env))
	switch env {
	case EnvDev, EnvTest, EnvPre, EnvProd, EnvUat:
		return env
	case "prd", "生产", "production":
		return EnvProd
	case "测试":
		return EnvTest
	case "预发":
		return EnvPre
	case "":
		return ""
	case "n/a", "u+大数据密码机":
		return EnvProd
	default:
		switch {
		case strings.Contains(env, "测试"), strings.Contains(env, "test"), strings.Contains(env, "cs"):
			return EnvTest
		case strings.Contains(env, "预发"):
			return EnvPre
		case strings.Contains(env, "生产"), strings.Contains(env, "prod"):
			return EnvProd
		case strings.Contains(env, "开发"):
			return EnvDev
		case strings.Contains(env, "验收"), strings.Contains(env, "ys"),
			strings.Contains(env, "验证"), strings.Contains(env, "yz"):
			return EnvUat
		case strings.Contains(env, "已退订"), strings.Contains(env, "物理机退订"):
			return EnvTest
		case strings.Contains(env, "未退订"):
			return EnvProd
		case strings.Contains(env, "其他"), strings.Contains(env, "系统备注"):
			return EnvTest
		case strings.Contains(env, "虚拟机退订"), strings.Contains(env, "退订服务器"):
			return EnvTest
		default:
			logger.Warningf("未知的环境类型：%s，强制设置为线上服务", env)
			return EnvProd
		}
	}
}

func UnifyHostType(hostType string) string {
	typ := strings.ToLower(strings.TrimSpace(hostType))
	switch typ {
	case "vm", "virtual", "virtualmachine":
		return common.HostTypeVirtual
	case "pm", "physical", "hostsystem", "物理机":
		return common.HostTypePhysical
	case "ecs":
		return "ECS"
	default:
		notice.SendWarnMessage("UnifyHostType解析异常", "未知的主机类型: "+hostType)
		return hostType
	}
}

func UnifyHostStatus(status string) string {
	status = strings.TrimSpace(strings.ToLower(status))
	switch status {
	case "running", "stopping", "stopped", "deploying", "maintain", "unallocated":
		return strcase.ToCamel(status)
	case "active", "poweredon":
		return common.StatusRunning
	case "shutdown", "poweredoff":
		return common.StatusStopped
	}
	return strcase.ToCamel(status)
}

func UnifyOsType(osType string) string {
	switch osType {
	case common.OsTypeLinux, common.OsTypeWindows:
		return osType
	default:
		osType = strings.ToLower(strings.TrimSpace(osType))
		switch {
		case osType == "":
			return "N/A"
		case strings.Contains(osType, "用户自运维"):
			return "N/A"
		case strings.Contains(osType, "centos"):
			return common.OsTypeLinux
		case strings.Contains(osType, "ubuntu"):
			return common.OsTypeLinux
		case strings.Contains(osType, "redhat"):
			return common.OsTypeLinux
		case strings.Contains(osType, "debian"):
			return common.OsTypeLinux
		case strings.Contains(osType, "suse"):
			return common.OsTypeLinux
		case strings.Contains(osType, "fedora"):
			return common.OsTypeLinux
		case strings.Contains(osType, "aix"):
			return common.OsTypeLinux
		case strings.Contains(osType, "esxi"):
			return common.OsTypeLinux
		case strings.Contains(osType, "vsca"):
			return common.OsTypeLinux
		case strings.Contains(osType, "vmnix"):
			return common.OsTypeLinux
		case strings.Contains(osType, "macos"):
			return common.OsTypeLinux
		case strings.Contains(osType, "coreos"):
			return common.OsTypeLinux
		case strings.Contains(osType, "alpine"):
			return common.OsTypeLinux
		case strings.Contains(osType, "vmnix"):
			return common.OsTypeLinux
		case strings.Contains(osType, "vsphere"):
			return common.OsTypeLinux
		case strings.Contains(osType, "vcsa"):
			return common.OsTypeLinux
		case strings.Contains(osType, "linux"):
			return common.OsTypeLinux
		case strings.Contains(osType, "windows"):
			return common.OsTypeWindows
		default:
			return osType
		}
	}
}

func UnifyDBType(dbType string) string {
	dbType = strings.ToLower(strings.TrimSpace(dbType))
	switch dbType {
	case "mysql":
		return DBTypeMySQL
	case "mssql", "sqlserver":
		return DBTypeSQLServer
	case "oracle", "oracle-ee":
		return DBTypeOracle
	case "redis":
		return DBTypeRedis
	case "mongodb", "mongo":
		return DBTypeMongoDB
	case "postgresql", "postgres":
		return DBTypePostgres
	case "mariadb":
		return DBTypeMariaDB
	case "docdb":
		return DBTypeDocDB
	case "elasticsearch", "es":
		return DBTypeElasticSearch
	default:
		return strcase.ToCamel(dbType)
	}
}
