package bizutils

import (
	"context"
	"fmt"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"

	"git.haier.net/devops/hcms-task-center/core/tools"
)

func CreateCostUnitMoveLog(ctx context.Context, logs ...*models.CostUnitMoveLog) error {
	if len(logs) == 1 {
		return SaveLog(ctx, logs[0])
	}

	logger.Infof(ctx, "got %d cost unit move logs, ready to save ...", len(logs))
	b := tools.NewBatch[*models.CostUnitMoveLog, error](ctx,
		batch.WithShowLog(true),
		batch.WithLogPrintStep(50))
	b.Run(logs, func(ctx context.Context, log *models.CostUnitMoveLog) (err error, _ error) {
		defer func() {
			if e := recover(); e != nil {
				err = fmt.Errorf("cost unit move log save error: %v", e)
			}
		}()
		err = SaveLog(ctx, log)
		return
	})

	return b.Error()
}

func SaveLog(ctx context.Context, log *models.CostUnitMoveLog) error {
	m := DataSource().Model(ctx)
	res := m.Orm().Save(log)
	if res.Error != nil {
		logger.Errorf(ctx, "save cost unit move log error: %v", res.Error)
		return res.Error
	}

	return nil
}
