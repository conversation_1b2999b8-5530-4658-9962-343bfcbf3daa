package bizutils

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/hbc/account"
	"git.haier.net/devops/ops-golang-common/models/models"

	"git.haier.net/devops/hcms-task-center/core/tools"

	"github.com/patrickmn/go-cache"
)

var (
	scodeMapCache           = cache.New(60*time.Minute, 120*time.Minute)
	scodeMapLock            = new(sync.Mutex)
	cloudAccountCache       = cache.New(60*time.Minute, 120*time.Minute)
	cloudAccountLock        = new(sync.Mutex)
	resourceSupplementCache = cache.New(60*time.Minute, 120*time.Minute)
	resourceSupplementLock  = new(sync.Mutex)
	serviceFreeCache        = cache.New(60*time.Minute, 120*time.Minute)
	serviceFreeLock         = new(sync.Mutex)
)

func GetServiceFree(ctx context.Context, vendor string) map[string][]*models.ServiceFree {
	serviceFreeLock.Lock()
	defer serviceFreeLock.Unlock()
	if serviceFree, ok := serviceFreeCache.Get(vendor); ok {
		return serviceFree.(map[string][]*models.ServiceFree)
	}
	m := DataSource().Model(ctx)
	serviceFree := []*models.ServiceFree{}
	if err := m.Orm().Model(&models.ServiceFree{}).Where("vendor = ? and del_flag  = '0' and type in (1,2) ", vendor).
		Find(&serviceFree).Order("type, start_date asc").Error; err != nil {
		return nil
	}
	serviceFreeMap := map[string][]*models.ServiceFree{}
	for _, s := range serviceFree {
		if _, ok := serviceFreeMap[fmt.Sprintf("%s%d", vendor, s.Type)]; ok {
			serviceFreeMap[fmt.Sprintf("%s%d", vendor, s.Type)] = append(serviceFreeMap[fmt.Sprintf("%s%d", vendor, s.Type)], s)
		} else {
			serviceFreeMap[fmt.Sprintf("%s%d%s", vendor, s.Type, strings.ToUpper(s.Currency))] = []*models.ServiceFree{s}
		}
	}

	scodeMapCache.Set(vendor, serviceFreeMap, cache.DefaultExpiration)
	return serviceFreeMap
}

// GetDefaultScode 从缓存中获取账户默认的S码
func GetDefaultScode(ctx context.Context, vendor, account string) *models.DefaultScode {
	scodeMapLock.Lock()
	defer scodeMapLock.Unlock()
	if defaultScode, ok := scodeMapCache.Get(account); ok {
		return defaultScode.(*models.DefaultScode)
	}
	m := DataSource().Model(ctx)
	defaultScode := new(models.DefaultScode)
	if err := m.Orm().Model(&models.DefaultScode{}).Where("vendor = ? and account = ?", vendor, account).First(defaultScode).Error; err != nil {
		return nil
	}
	scodeMapCache.Set(account, defaultScode, cache.DefaultExpiration)
	return defaultScode
}

// GetCloudAccount 根据vendor和accountId获取账户数据
func GetCloudAccount(ctx context.Context, vendor, accountId string) *account.CloudAccount {
	cloudAccountLock.Lock()
	defer cloudAccountLock.Unlock()
	key := vendor + accountId
	if cloudAccount, ok := cloudAccountCache.Get(key); ok {
		return cloudAccount.(*account.CloudAccount)
	}
	m := DataSource().Model(ctx)
	cloudAccount := new(account.CloudAccount)
	if err := m.Orm().Model(&account.CloudAccount{}).Where("vendor = ? and account_identify = ?", vendor, accountId).First(cloudAccount).Error; err != nil {
		return nil
	}
	cloudAccountCache.Set(key, cloudAccount, cache.DefaultExpiration)
	return cloudAccount
}

// GetResourceSupplement 从bc_resource_supplement中获取数据
// func GetResourceSupplement(ctx context.Context, account, productCode, resourceId string) *models.ResourceSupplement {
// 	resourceSupplementLock.Lock()
// 	defer resourceSupplementLock.Unlock()
// 	key := account + productCode + resourceId
// 	if subProduct, ok := resourceSupplementCache.Get(key); ok {
// 		return subProduct.(*models.ResourceSupplement)
// 	}
// 	m := DataSource().Model(ctx)
// 	resource := new(models.ResourceSupplement)
// 	if err := m.Orm().Model(&models.ResourceSupplement{}).Where("vendor ='aws' AND account = ? AND code = ? AND resource_id = ?", account, productCode, resourceId).First(resource).Error; err != nil {
// 		return nil
// 	}
// 	resourceSupplementCache.Set(key, resource, cache.DefaultExpiration)
// 	return resource
// }

// GetResourceInfosByInstanceId 根据实例ID获取资源信息
func GetResourceInfosByInstanceId(ctx context.Context, instanceIds []string) ([]*models.ResourceInfo, error) {
	resourceInfos := make([]*models.ResourceInfo, len(instanceIds))
	m := DataSource().Model(ctx)
	m.Orm().Model(&models.ResourceInfo{}).Where("resource_id IN ?", instanceIds).Find(&resourceInfos)

	return resourceInfos, nil
}

func CreateOrUpdateResource(ctx context.Context, infos ...*models.ResourceInfo) error {
	logger.Infof(ctx, "got %d resource records, ready to save ...", len(infos))

	b := tools.NewBatch[*models.ResourceInfo, error](ctx,
		batch.WithBatchSize(10),
		batch.WithShowLog(true),
		batch.WithLogPrintStep(500),
	)

	m := DataSource().Model(ctx)
	b.Run(infos, func(ctx context.Context, info *models.ResourceInfo) (error, error) {
		err := m.Orm().Where(models.ResourceInfo{
			ResourceId:   info.ResourceId,
			ResourceType: info.ResourceType,
		}).Assign(models.ResourceInfo{
			Service:         info.Service,
			ResourceType:    info.ResourceType,
			ResourceGroupId: info.ResourceGroupId,
			ResourceId:      info.ResourceId,
			CreateDate:      info.CreateDate,
			RegionId:        info.RegionId,
		}).FirstOrCreate(info).Error

		return err, nil
	})

	return tools.MergeErrors(b.Outs())
}
