package bizutils

import (
	"context"

	"git.haier.net/devops/ops-golang-common/models/models"

	"git.haier.net/devops/hcms-task-center/core/config"
)

// GetCostUnitMoveRules 查询预分账规则
func GetCostUnitMoveRules(ctx context.Context, account string, clouds []string) ([]models.CloudCostUnitMoveRule, error) {
	m := config.Global().GetStore("hcms").Model(ctx)
	rules := make([]models.CloudCostUnitMoveRule, 0)
	err := m.Orm().Model(&models.CloudCostUnitMoveRule{}).
		Where("status='on' and account = ? and cloud in ?", account, clouds).Find(&rules).Error
	if err != nil {
		logger.Errorf(ctx, "get cost unit move rules error: %v", err)
		return rules, err
	}
	return rules, nil
}
