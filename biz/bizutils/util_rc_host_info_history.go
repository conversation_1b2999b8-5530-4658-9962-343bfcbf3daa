package bizutils

import (
	"context"
	"fmt"
	"time"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"
)

func SaveHostInfoHistory(ctx context.Context, tx *gorm.DB, hostInfo *models.HostInfo, operationType DelOperaType) (err error) {
	if tx == nil {
		m := config.Global().GetDefaultStore().Model(ctx)
		tx = m.Orm().Begin(txOpt)
		if err := tx.Error; err != nil {
			return fmt.Errorf("failed to start transaction: %v", err)
		}

		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			} else if err != nil {
				tx.Rollback()
			} else {
				err = tx.Commit().Error
			}
		}()
	}

	history := &models.HostInfoDelHistory{
		Vendor:             hostInfo.Vendor,
		AccountName:        hostInfo.AccountName,
		InstanceId:         hostInfo.InstanceId,
		InstanceName:       hostInfo.InstanceName,
		CreationTime:       hostInfo.CreationTime,
		ExpiredTime:        hostInfo.ExpiredTime,
		IsDeleted:          hostInfo.IsDeleted,
		PrivateIp:          hostInfo.PrivateIp,
		PublicIp:           hostInfo.PublicIp,
		IDRACIp:            hostInfo.IDRACIp,
		NetworkType:        hostInfo.NetworkType,
		VpcId:              hostInfo.VpcId,
		SubnetId:           hostInfo.SubnetId,
		Scode:              hostInfo.Scode,
		Project:            hostInfo.Project,
		Env:                hostInfo.Env,
		ResourceGroup:      hostInfo.ResourceGroup,
		OsType:             hostInfo.OsType,
		OsName:             hostInfo.OsName,
		OsArch:             hostInfo.OsArch,
		Numa:               hostInfo.Numa,
		ImageId:            hostInfo.ImageId,
		Cpu:                hostInfo.Cpu,
		Memory:             hostInfo.Memory,
		DiskSize:           hostInfo.DiskSize,
		DiskType:           hostInfo.DiskType,
		HostStatus:         hostInfo.HostStatus,
		HostType:           hostInfo.HostType,
		HostInsId:          hostInfo.HostInsId,
		Region:             hostInfo.Region,
		UniRegionId:        hostInfo.UniRegionId,
		Zone:               hostInfo.Zone,
		ClassCode:          hostInfo.ClassCode,
		ChargeType:         hostInfo.ChargeType,
		Rack:               hostInfo.Rack,
		ProviderName:       hostInfo.ProviderName,
		BrandName:          hostInfo.BrandName,
		HostModel:          hostInfo.HostModel,
		Sn:                 hostInfo.Sn,
		Description:        hostInfo.Description,
		ResourceId:         hostInfo.ResourceId,
		Domain:             hostInfo.Domain,
		Team:               hostInfo.Team,
		OwnerId:            hostInfo.OwnerId,
		OwnerName:          hostInfo.OwnerName,
		Maintainer:         hostInfo.Maintainer,
		MonitoringMode:     hostInfo.MonitoringMode,
		IpAddr2:            hostInfo.IpAddr2,
		GpuInfo:            hostInfo.GpuInfo,
		GpuAmount:          hostInfo.GpuAmount,
		GpuModel:           hostInfo.GpuModel,
		GpuMemory:          hostInfo.GpuMemory,
		UniHostStatus:      hostInfo.UniHostStatus,
		SubscriptionStatus: hostInfo.SubscriptionStatus,
		OperationType:      operationType.String(),
		CreateTime:         time.Now(),
		UpdateTime:         time.Now(),
	}
	return tx.Create(history).Error
}
