package bizutils

import (
	"fmt"
	"log"
	"sort"
	"testing"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/stretchr/testify/assert"
)

func TestGetDefaultScode(t *testing.T) {
	a := assert.New(t)
	scode := GetDefaultScode(context.NewContext(), "haierhealth", "hr690n")
	a.Equal("S02058", scode.Scode)
}

// func TestGetHdsSubProducts(t *testing.T) {
// 	scode := GetHdsSubProducts(context.NewContext(), "S01440")
// 	fmt.Println(scode)
// }

// func TestGetResourceSupplement(t *testing.T) {
// 	scode := GetResourceSupplement(context.NewContext(), "wms-znzz", "", "cluster-wms-prod-v1")
// 	fmt.Println(scode)
// }

// 比对Excel中的实例环境信息
func TestExcel(t *testing.T) {
	hostInfos := []models.HostInfo{}
	hostInfoMap := make(map[string]models.HostInfo, len(hostInfos))
	model := DataSource().Model(context.NewContext())
	if err := model.Orm().Where("vendor='private' and account_name = 'rrs' and  host_type ='Physical' ").Find(&hostInfos).Error; err != nil {
		fmt.Println(err)
	}
	fmt.Println("hostInfos total: ", len(hostInfos))
	for _, r := range hostInfos {
		hostInfoMap[r.PrivateIp] = r
	}

	// 打开Excel文件
	f, err := excelize.OpenFile("/Users/<USER>/Downloads/cmp导出物流服务器list_标红蓝_20250318.xlsx")
	if err != nil {
		log.Fatalf("打开Excel文件失败: %v", err)
	}

	// 获取所有Sheet的名称
	physical_sheet := f.GetSheetName(1)
	// vm_sheet := f.GetSheetName(2)

	// 遍历所有Sheet并读取内容
	var not_match_count, maintenance_team_count int
	rows := f.GetRows(physical_sheet)
	for index, row := range rows {
		if index == 0 {
			continue
		}
		scode := row[3]
		ip := row[8]
		// vm_maintenance_team := row[28]
		physical_maintenance_team := row[34]

		// 跳过没有准备好的scode
		if scode == "S04242" {
			continue
		}

		if v, ok := hostInfoMap[ip]; !ok {
			fmt.Println("not match: " + ip)
			not_match_count = not_match_count + 1
		} else {
			if v.MaintainingTeam != physical_maintenance_team {
				maintenance_team_count = maintenance_team_count + 1
				fmt.Println("maintenance_team not match: " + ip)
			}
			delete(hostInfoMap, ip)
		}
	}
	fmt.Println("not match total:", not_match_count)

	var sortIpList []string
	for k := range hostInfoMap {
		sortIpList = append(sortIpList, k)
	}
	sort.Strings(sortIpList)
	fmt.Println("more db than excel total:", len(sortIpList))
	// fmt.Println(utils.JsonString(sortIpList))
	fmt.Println("maintenance_team not match: ", maintenance_team_count)
}
