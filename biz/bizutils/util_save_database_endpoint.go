package bizutils

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/aws/smithy-go/ptr"
)

func CreateOrUpdateEndpoint(ctx context.Context, endpoints ...*models.DatabaseEndpoint) error {
	if len(endpoints) == 0 {
		return nil
	}
	instanceId := endpoints[0].InstanceId
	if instanceId == "" {
		return fmt.Errorf("instanceId is empty")
	}

	//  先删除
	m := DataSource().Model(ctx)
	err := m.Orm().Table(endpoints[0].TableName()).Where(models.DatabaseEndpoint{InstanceId: instanceId}).Update("is_deleted", "1").Error
	if err != nil {
		return err
	}

	if len(endpoints) == 1 {
		err := SaveDatabaseEndpoint(ctx, instanceId, endpoints[0])
		return err
	}

	logger.Infof(ctx, "got %d database endpoint records, ready to save ...", len(endpoints))
	b := tools.NewBatch[*models.DatabaseEndpoint, error](ctx,
		batch.WithShowLog(true),
		batch.WithLogPrintStep(100))
	b.Run(endpoints, func(ctx context.Context, endpoint *models.DatabaseEndpoint) (err error, _ error) {
		defer func() {
			if e := recover(); e != nil {
				notice.SendErrorMessage("数据库连接同步方法异常", fmt.Sprintf("堆栈信息：\n%s", debug.Stack()))
				err = fmt.Errorf("数据库连接同步方法异常：%s", e)
			}
		}()
		err = SaveDatabaseEndpoint(ctx, instanceId, endpoint)
		return
	})

	return b.Error()
}

func SaveDatabaseEndpoint(ctx context.Context, instanceId string, endpoint *models.DatabaseEndpoint) error {
	m := DataSource().Model(ctx)

	res := m.Orm().Where(models.DatabaseEndpoint{
		InstanceId:  instanceId,
		Host:        endpoint.Host,
		Port:        endpoint.Port,
		Description: endpoint.Description,
		PublicIp:    endpoint.PublicIp,
		PrivateIp:   endpoint.PrivateIp,
		NodeId:      endpoint.NodeId,
		NetType:     endpoint.NetType,
	}).Assign(models.DatabaseEndpoint{
		Description: endpoint.Description,
		IsDeleted:   ptr.Bool(false),
	}).FirstOrCreate(endpoint)
	if res.Error != nil {
		return res.Error
	}
	return nil
}
