package bizutils

import (
	"strconv"
	"strings"
)

func ParseCPUFromHuaweiSpec(spec string) (int, int) {
	tmp := strings.Split(strings.ToLower(strings.TrimSpace(spec)), "-")
	if len(tmp) <= 1 {
		return 0, 0
	}
	tmp2 := strings.Split(tmp[1], "u")
	cpuInt, err := strconv.Atoi(tmp2[0])
	if err != nil {
		return 0, 0
	}
	if len(tmp2) <= 1 {
		return cpuInt, 0
	}
	memInt, err := strconv.Atoi(tmp2[1])
	if err == nil {
		return cpuInt, memInt
	}
	return cpuInt, 0
}
