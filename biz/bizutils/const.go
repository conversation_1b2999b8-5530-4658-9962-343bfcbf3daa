package bizutils

import (
	"git.haier.net/devops/ops-golang-common/common"
)

const (
	DefaultTimeFormatter     = "2006-01-02 15:04:05"
	AliyunEcsTimeFormat      = "2006-01-02T15:04Z"
	NormalDatetimeFmt        = "2006-01-02T15:04:05Z"
	AliyunElasticsearch      = "2006-01-02T15:04:05.000Z"
	AliyunPolarDBXTimeFormat = "2006-01-02T15:04:05.000+0700"
	HuaweiEcsTimeFormat      = "2006-01-02T15:04:05+07:00"
	HuaweiRdsTimeFormat      = "2006-01-02T15:04:05Z0700"
	HuaweiRdsNoSqlTimeFormat = "2006-01-02T15:04:05.999Z"
	TencentEcsTimeFormat     = NormalDatetimeFmt
	AwsEcsTimeFormat         = NormalDatetimeFmt
)

const (
	PurposeAccountCheck = "ACCOUNT_CHECK"
	PurposeAdmin        = "MAIN"
	PurposeBill         = "BILL"      // 账单账号
	PurposeDedicated    = "DEDICATED" // 专属云账号
	PurposeOSS          = "OSS"
)

const (
	EnvDev  = common.EnvDev
	EnvTest = common.EnvTest
	EnvPre  = common.EnvPre
	EnvProd = common.EnvProd
	EnvUat  = common.EnvUat
)

const (
	DBTypeMySQL         = "MySQL"
	DBTypeMariaDB       = "MariaDB"
	DBTypePostgres      = "PostgreSQL"
	DBTypeSQLServer     = "SQLServer"
	DBTypeOracle        = "Oracle"
	DBTypeRedis         = "Redis"
	DBTypeMongoDB       = "MongoDB"
	DBTypeDocDB         = "DocDB"
	DBTypeElasticSearch = "ElasticSearch"
	DBTypeAdb           = "AnalyticDB" // 云原生数据仓库 AnalyticDB MySQL版
	DBTypeAdbPGSQL      = "Gpdb"       // 云原生数据仓库 AnalyticDB PostgreSQL版
	DBTypeClickhouse    = "Clickhouse"
	DBTypeHbase         = "Hbase"
	DBTypeHitsdb        = "Hitsdb"
	DBTypeOceanBase     = "OceanBase"
	DBTypeGeminiDB      = "geminiDB"
)

// 监控模式
const (
	MonitoringModeIsNone         = 0 // 未接监控
	MonitoringModeIsZabbix       = 1 // zabbix
	MonitoringModeIsPrometheus   = 2 // prometheus
	MonitoringModeIsNotNecessary = 3 // 不接监控
	MonitoringModeIsHOC          = 4 // hoc监控
)

const (
	NetTypePublic  = "public"
	NetTypePrivate = "private"
)

const (
	TimeoutErr = "timeout"
)

const (
	DelOperaTypeUpdate DelOperaType = "update"
	DelOperaTypeDelete DelOperaType = "delete"
	DelOperaTypeIgnore DelOperaType = "ignore"
)

type DelOperaType string

func (typ DelOperaType) String() string {
	return string(typ)
}
