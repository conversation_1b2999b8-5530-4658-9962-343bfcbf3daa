package hybrid

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/hbc/account"
	"git.haier.net/devops/ops-golang-common/hcam"
	mhbc "git.haier.net/devops/ops-golang-common/models/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils"

	"git.haier.net/devops/hcms-task-center/core/config"
)

var accountManager *hcam.HybridAccountManager

func init() {
	c := config.Global()
	accountManager = hcam.NewAccountManager(*c.GetDefaultStore())
}

func AccountManager() *hcam.HybridAccountManager {
	return accountManager
}

func GetVendors(model models.Model, ignoredVendors ...hbc.CloudVendor) ([]hbc.CloudVendor, error) {
	vendorInfoList, err := mhbc.AvailableVendors(model)
	if err != nil {
		return nil, utils.NewError("get cloud vendor failed: %s", err.Error())
	}

	vendors := make([]hbc.CloudVendor, 0)
	for _, info := range vendorInfoList {
		isIgnored := false
		for _, ignoredVendor := range ignoredVendors {
			if ignoredVendor == info.VendorCode {
				isIgnored = true
				break
			}
		}
		if !isIgnored {
			vendors = append(vendors, info.VendorCode)
		}
	}

	return vendors, nil
}

func GetClients(ctx context.Context, vendor hbc.CloudVendor, prod hbc.CloudProduct, purpose []string, region string) (clients []client.IClient, err error) {
	accounts, err := accountManager.GetAccounts(ctx, account.WithPurposeTypes(purpose...), account.WithVendors(vendor))
	if err != nil {
		return nil, err
	}

	if len(region) == 0 {
		region = defaultRegions[vendor]
	}

	for _, cloudAccount := range accounts {
		var c client.IClient
		var clientErr error
		switch cloudAccount.Vendor {
		case hbc.Private:
			c, clientErr = accountManager.GetClient(cloudAccount, prod, cloudAccount.PurposeType, cloudAccount.AccountName)
		case hbc.Azure:
			c, clientErr = accountManager.GetClient(cloudAccount, prod, cloudAccount.PurposeType, cloudAccount.AuthRegion)
		default:
			c, clientErr = accountManager.GetClient(cloudAccount, prod, cloudAccount.PurposeType, region)
		}
		if clientErr != nil {
			return nil, clientErr
		}
		clients = append(clients, c)
	}
	return clients, nil
}

func GetClient(ctx context.Context, vendor hbc.CloudVendor, accountName string, prod hbc.CloudProduct,
	purpose, region string) (client.IClient, error) {
	account, err := accountManager.GetAccount(ctx, vendor, accountName, purpose)
	if err != nil {
		return nil, err
	}
	return accountManager.GetClient(account, prod, purpose, region)
}
