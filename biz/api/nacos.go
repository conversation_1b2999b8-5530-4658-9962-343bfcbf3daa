package api

import (
	"git.haier.net/devops/hcms-task-center/core/config"
	"github.com/nacos-group/nacos-sdk-go/clients"
	"github.com/nacos-group/nacos-sdk-go/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/common/constant"
	"github.com/nacos-group/nacos-sdk-go/vo"
)

func newNacosClient() (config_client.IConfigClient, error) {
	clientConfig := constant.ClientConfig{
		NamespaceId: config.Global().GetNameSpaceId(), // 如果需要支持多namespace，我们可以创建多个client,它们有不同的NamespaceId。当namespace是public时，此处填空字符串。
		TimeoutMs:   5000,
		LogLevel:    "warn",
	}

	serverConfigs := []constant.ServerConfig{
		{
			IpAddr:      config.Global().GetHost(),
			ContextPath: "/nacos",
			Port:        config.Global().GetPort(),
			Scheme:      config.Global().GetSchema(),
		},
	}

	return clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  &clientConfig,
			ServerConfigs: serverConfigs,
		},
	)
}
