package api

import (
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/ops-golang-common/haierapi"
	"github.com/nacos-group/nacos-sdk-go/clients/config_client"
)

var (
	hcmsConf      = config.Global().GetApp("hcms")
	hdsConf       = config.Global().GetApp("hds")
	hworkAuthConf = config.Global().GetApp("hwork-auth")
	hcmsClient    = haierapi.NewHcmsClient("PROD", "TASK_CENTER", hcmsConf.GetToken())
	hdsClient     = haierapi.NewHdsClient("TASK_CENTER", hdsConf.GetToken(), false)
	hocClient     = haierapi.NewHocClient()
	hworkClient   = haierapi.NewHWorkDomainClient(hworkAuthConf.GetAppId(), hworkAuthConf.GetSecretKey(), config.Global().IsTest())
	// nacos
	nacosApi, _ = newNacosClient()
)

func HcmsClient() *haierapi.HcmsApi {
	return hcmsClient
}

func HdsClient() *haierapi.HdsClient {
	return hdsClient
}

func HocClient() *haierapi.HocClient {
	return hocClient
}

func HworkClient() *haierapi.HWorkClient {
	return hworkClient
}

func NacosApi() config_client.IConfigClient {
	return nacosApi
}
