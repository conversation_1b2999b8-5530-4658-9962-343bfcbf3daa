package notice

import (
	"context"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/log"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/core/config"
)

const domainCode = "szyhjsz"

func SendInfoMessage(title, message string) {
	SendInfoMessageWithGroups(GroupOps, title, message)
}

func SendWarnMessage(title, message string) {
	SendWarnMessageWithGroups(GroupOps, title, message)
}

func SendErrorMessage(title, message string) {
	SendErrorMessageWithGroups(GroupOps, title, message)
}

func SendInfoMessageWithGroups(ug string, title, message string) {
	MustSendOpsNotify(ug, "【通知】"+title, message, ColorBlue)
}

func SendWarnMessageWithGroups(ug string, title, message string) {
	MustSendOpsNotify(ug, "【警告】"+title, message, ColorYellow)
}

func SendErrorMessageWithGroups(ug string, title, message string) {
	MustSendOpsNotify(ug, "【异常】"+title, message+"\n\n请及时处理！", ColorRed)
}

func MustSendOpsNotify(userGroup, title, message, color string) {
	//users, err := api.HdsClient().GetRoleUsers(scode, userGroup)
	users, err := api.HworkClient().GetRoleUsers(context.Background(), domainCode, userGroup)

	if err != nil {
		log.System("获取用户组失败", "userGroup", userGroup, "err", err)
		return
	}

	userIds := make([]string, 0, len(users))
	receivers := make([]string, 0, len(users))
	for _, user := range users {
		userIds = append(userIds, user.UserCode)
		receivers = append(receivers, user.UserName)
	}

	err = api.HdsClient().SendIHaierMessage(sender, scode, templateId, map[string]any{
		"color":    color,
		"title":    title,
		"time":     time.Now().Format("2006/01/02 15:04:05"),
		"env":      config.Global().Env,
		"receiver": strings.Join(receivers, ","),
		"content":  message,
		"version":  config.Version,
	}, userIds...)
	if err != nil {
		log.System("发送消息失败", "err", err)
	}
}
