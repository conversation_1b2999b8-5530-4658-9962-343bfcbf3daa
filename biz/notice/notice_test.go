package notice

import (
	"testing"

	"git.haier.net/devops/hcms-task-center/biz/api"
)

func TestSendErrorMessage(t *testing.T) {
	SendErrorMessage("test", "test")
	SendWarnMessage("test", "test")
}

func TestMustSendOpsNotify(t *testing.T) {
	users, err := api.HdsClient().GetRoleUsers(scode, "HCMS_BILL_OPS_ALIYUN")
	if err != nil {
		t.Error(err)
	}
	t.Log(users)
}

func TestSendInfoMessage(t *testing.T) {
	SendInfoMessageWithGroups("HCMS_BILL_OPS_ALIYUN", "test", "<a href='https://www.baidu.com'>test</a>")
}
