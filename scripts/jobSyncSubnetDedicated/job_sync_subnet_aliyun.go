package jobSyncSubnetDedicated

import (
	"context"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"github.com/LPX3F8/orderedmap"
	"github.com/aws/smithy-go/ptr"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func (s *SubnetSync) syncAliyunSubnetInfo(ctx context.Context, cli *aliyun.VpcClient) ([]*models.SubnetInfo, error) {
	regions, err := cli.DescribeRegions()
	if err != nil {
		return nil, bizutils.WarpClientError(cli, err)
	}
	regionsIds := orderedmap.New[string, string]()
	for _, region := range regions {
		regionsIds.Store(region.RegionId, region.RegionId)
	}
	b := tools.NewBatch[string, []*models.SubnetInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, region string) ([]*models.SubnetInfo, error) {
		regionCli := hybrid.AccountManager().AliyunDedicatedVpcClient(ctx, cli.Name(), cli.Tag(), region)
		return s.syncAliyunRegionSubnetInfo(ctx, regionCli)
	})
	return tools.MergeData(b.Outs()...), bizutils.WarpClientError(cli, b.Error())
}

func (s *SubnetSync) syncAliyunRegionSubnetInfo(ctx context.Context, cli *aliyun.VpcClient) ([]*models.SubnetInfo, error) {
	subnets, err := cli.DescribeVSwitchesOfAll(ctx)
	if err != nil {
		return nil, bizutils.WarpClientError(cli, err)
	}
	subnetInfos := make([]*models.SubnetInfo, len(subnets))
	for i, subnet := range subnets {
		creationTime, _ := time.Parse(bizutils.NormalDatetimeFmt, subnet.CreationTime)
		subnetInfos[i] = &models.SubnetInfo{
			Model:        s.Model(ctx),
			Vendor:       cli.Vendor(),
			AccountName:  cli.Name(),
			CreationTime: timeutil.ZeroTime(creationTime),
			VpcId:        subnet.VpcId,
			SubnetId:     subnet.VSwitchId,
			Region:       cli.Region(),
			Zone:         subnet.ZoneId,
			State:        subnet.Status,
			Cidr:         subnet.CidrBlock,
			Description:  subnet.Description,
			IsDeleted:    ptr.Bool(false),
		}
	}
	return subnetInfos, nil
}
