package jobSyncSubnetDedicated

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func New() *SubnetSync {
	c := config.Global()
	return &SubnetSync{
		JobBase: base.NewJobBase(
			"SUBNET",
			"同步专属云子网信息",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type SubnetSync struct {
	*base.JobBase
}

func (s *SubnetSync) Name() string {
	return base.TaskCloudSyncSubnetDedicated
}

func (s *SubnetSync) Run(ctx context.Context) (map[string]any, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx,
		[]hbc.CloudVendor{
			hbc.AliCloudDedicated,
		},
		hbc.Vpc,
		[]string{bizutils.PurposeDedicated},
	)
	if err != nil {
		return nil, err
	}

	// 获取云资源信息
	b := tools.NewBatch[client.IClient, []*models.SubnetInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.SubnetInfo, error) {
		switch defClient := input.(type) {
		case *aliyun.VpcClient:
			return s.syncAliyunSubnetInfo(ctx, defClient)
		}
		return nil, nil
	})
	if err := b.Error(); err != nil {
		s.Errorf(ctx, "sync subnet failed, cause: %s", err)
		return nil, err
	}

	return nil, bizutils.CreateOrUpdateSubnet(ctx, tools.MergeData(b.Outs()...)...)
}
