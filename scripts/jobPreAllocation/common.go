package jobPreAllocation

import (
	"fmt"
	"reflect"
	"strings"

	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/bssopenapi"

	"git.haier.net/devops/hcms-task-center/biz/notice"
)

// GetPropertyValue 使用反射根据对象属性名获取对象属性值
func GetPropertyValue(obj interface{}, propertyName string) interface{} {
	val := reflect.ValueOf(obj)

	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return nil
	}

	field := val.FieldByName(propertyName)
	if !field.IsValid() {
		return nil
	}

	return field.Interface()
}

func ExtractPriorityProperties(instance *bssopenapi.ResourceInstanceList, rule models.CloudCostUnitMoveRule) (string, string) {
	var priorityOfInstance, priorityOfRule string
	priorityOfInstanceVal := GetPropertyValue(instance, rule.Priority)
	priorityOfRuleVal := GetPropertyValue(rule, rule.Priority)
	if priorityOfInstanceVal != nil {
		priorityOfInstance = fmt.Sprintf("%v", priorityOfInstanceVal)
	}
	if priorityOfRuleVal != nil {
		priorityOfRule = fmt.Sprintf("%v", priorityOfRuleVal)
	}
	return priorityOfInstance, priorityOfRule
}

func SplitTag(inputStr string) map[string]string {
	// 第一步，将输入字符串按照分号 '; ' 进行分割
	splitBySemicolon := strings.Split(inputStr, "; ")

	// 初始化结果字典
	result := make(map[string]string)

	// 第二步，将每个分割得到的字符串按照空格 ' ' 进行分割，得到 key 和 value 的字符串
	for _, s := range splitBySemicolon {
		splitBySpace := strings.Split(s, " ")

		// 第三步，构造字典
		if len(splitBySpace) == 2 {
			key := strings.Split(splitBySpace[0], ":")[1]

			var value string
			valueParts := strings.Split(splitBySpace[1], ":")
			if len(valueParts) == 2 {
				value = strings.Split(splitBySpace[1], ":")[1]
			}
			result[key] = value
		}
	}

	return result
}

func NotifyResult(results []*AllocatingResult, notMatchedItems *[]bssopenapi.ResourceInstanceList, account string) error {
	hasFailedItems := false
	for _, r := range results {
		if !r.Response.Success {
			hasFailedItems = true
			break
		}
	}
	if hasFailedItems {
		failedTable := "**Cloud**\t**Resource_Id**\t**Commodity_Code**\t**From_Id**\t**To_Id**\t**To_Name**\n"
		for _, r := range results {
			if !r.Response.Success {
				failedTable += fmt.Sprintf("【%s】\t【%s】\t【%s】\t【%d】\t【%d】\t【%s】\n",
					"aliyun", r.ResourceId, r.CommodityCode, r.FromUnitId, r.ToUnitId, r.ToUnitName)
			}
		}
		notice.SendInfoMessageWithGroups(notice.BillDev, account+"账号预分账失败项目", failedTable)
	}

	if len(*notMatchedItems) > 0 {
		notMatchedTable := "**CommodityCode**\t**ResourceTag**\t**ResourceId**\t**ResourceGroup**\n"
		for _, item := range *notMatchedItems {
			notMatchedTable += fmt.Sprintf("【%s】\t【%s】\t【%s】\t【%s】\n",
				item.CommodityCode, item.ResourceTag, item.ResourceId, item.ResourceGroup)
		}
		notice.SendInfoMessageWithGroups(notice.BillAliyunOps, account+"账号未匹配资源", notMatchedTable)
	}

	return nil
}
