package jobPreAllocation

import (
	"context"
	"fmt"
	"strings"

	"git.haier.net/devops/ops-golang-common/common/types"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/bssopenapi"
)

func (p *PreAllocation) QueryScodeUnitIdMap(ctx context.Context, cli *aliyun.BillClient) (map[string]bssopenapi.CostUnitDtoListItem, error) {
	scodeUnitIdMap := make(map[string]bssopenapi.CostUnitDtoListItem)

	setRegionFunc := func(req *bssopenapi.QueryCostUnitRequest) {
		req.RegionId = "cn-qingdao"
	}
	f := []func(req *bssopenapi.QueryCostUnitRequest){setRegionFunc}

	costUnits, err := cli.QueryCostUnit(ctx, f...)
	if err != nil {
		p.Errorf(ctx, "QueryCostUnit error: %v", err)
		return scodeUnitIdMap, err
	}

	for _, item := range costUnits {
		parts := strings.Split(item.UnitName, "-")
		appscode := parts[len(parts)-1]
		scodeUnitIdMap[appscode] = item
	}
	return scodeUnitIdMap, nil
}

func (p *PreAllocation) QueryCostUnitResource(ctx context.Context, cli *aliyun.BillClient, unitId int) (*[]bssopenapi.ResourceInstanceList, error) {
	setRegionAndUnitIdFunc := func(req *bssopenapi.QueryCostUnitResourceRequest) {
		req.RegionId = "cn-qingdao"
	}
	f := []func(req *bssopenapi.QueryCostUnitResourceRequest){setRegionAndUnitIdFunc}

	instanceList, err := cli.QueryCostUnitResource(ctx, unitId, f...)
	if err != nil {
		p.Errorf(ctx, "QueryCostUnitResourceWithChan error: %v", err)
		return instanceList, err
	}

	return instanceList, err
}

func createCostUnit(ctx context.Context, project *types.ALMProject, appscode string, cli *aliyun.BillClient,
) (*bssopenapi.CreateCostUnitResponse, error) {
	createFuncs := []func(req *bssopenapi.CreateCostUnitRequest){
		func(req *bssopenapi.CreateCostUnitRequest) {
			req.RegionId = "cn-qingdao"
		},
	}
	unitName := fmt.Sprintf("%v-%v-%v", project.Id, project.ProductName, appscode)
	unitEntityList := &[]bssopenapi.CreateCostUnitUnitEntityList{
		{
			UnitName:     unitName,
			ParentUnitId: "-1",
			OwnerUid:     cli.Identifier(),
		},
	}

	fmt.Printf("unitEntityList: %v\n", utils.JsonString(unitEntityList))
	return cli.CreateCostUnit(ctx, unitEntityList, createFuncs...)
}

func allocateCostUnitResource(ctx context.Context, fromUnitId int, toUnit bssopenapi.CostUnitDtoListItem,
	commodityCode string, instance *bssopenapi.ResourceInstanceList, cli *aliyun.BillClient,
) (*AllocatingResult, error) {
	allocateFuncs := []func(req *bssopenapi.AllocateCostUnitResourceRequest){
		func(req *bssopenapi.AllocateCostUnitResourceRequest) {
			req.RegionId = "cn-qingdao"
		},
	}

	resourceInstanceList := &[]bssopenapi.AllocateCostUnitResourceResourceInstanceList{
		{
			CommodityCode:  commodityCode,
			ApportionCode:  instance.ApportionCode,
			ResourceId:     instance.ResourceId,
			ResourceUserId: cli.Identifier(),
		},
	}

	fmt.Printf("+++%s+++from id: %v, to: [id = %d, name = %s], CommodityCode: %v, ResourceId: %s \n",
		cli.Name(), fromUnitId, toUnit.UnitId, toUnit.UnitName, commodityCode, instance.ResourceId)

	toUnitId := int(toUnit.UnitId)
	resp, err := cli.AllocateCostUnitResource(ctx, fromUnitId, toUnitId, resourceInstanceList, allocateFuncs...)
	return &AllocatingResult{
		Account:       cli.Name(),
		UserId:        cli.Identifier(),
		FromUnitId:    fromUnitId,
		ToUnitId:      toUnitId,
		ToUnitName:    toUnit.UnitName,
		CommodityCode: commodityCode,
		ApportionCode: instance.ApportionCode,
		ResourceId:    instance.ResourceId,
		Response:      resp,
	}, err
}

func (p *PreAllocation) PreAllocation(ctx context.Context, cli *aliyun.BillClient, moveRules []models.CloudCostUnitMoveRule) (
	[]*AllocatingResult, *[]bssopenapi.ResourceInstanceList, error) {
	results := make([]*AllocatingResult, 0)
	notMatchedItems := make([]bssopenapi.ResourceInstanceList, 0)
	costUnitResources, err := p.QueryCostUnitResource(ctx, cli, 0)
	if err != nil {
		p.Errorf(ctx, "QueryCostUnitResource error: %v", err)
		return nil, nil, err
	}

	scodeUnitIdMap, err := p.QueryScodeUnitIdMap(ctx, cli)
	if err != nil {
		p.Errorf(ctx, "QueryScodeUnitIdMap error: %v", err)
		return nil, nil, err
	}

	allocators := []Allocator{
		ExclusiveCommodityAllocator,
		CustomRuleAllocator,
		TagOrResourceGroupAllocator,
	}

	for _, instance := range *costUnitResources {
		// 账号能匹配上规则的，优先走规则匹配，匹配不上的再走标签或资源组匹配
		if len(moveRules) > 0 {
			_matched := false
			var _err error

			for _, rule := range moveRules {
				matched, result, err := ApplyAllocators(ctx, p.Logger(), cli, rule, &instance, scodeUnitIdMap, allocators...)
				if result != nil {
					if err != nil {
						result.Response.Message = err.Error()
					}
					results = append(results, result)
				}

				fmt.Printf("-->instance = %s, rule = %s matched: %v\n",
					fmt.Sprintf("%v-%v", instance.CommodityCode, instance.ResourceId),
					fmt.Sprintf("%v-%v-%v", rule.CommodityCode, rule.Priority, rule.Appscode),
					matched)

				if matched {
					_matched = true
					break
				}

				if err != nil {
					p.Errorf(ctx, "ApplyAllocator error: %v", err)
					_err = err
					break
				}
			}

			if !_matched {
				notMatchedItems = append(notMatchedItems, instance)
			}

			if _err != nil {
				return results, &notMatchedItems, err
			}
		} else {
			// 账号没有配置规则的，走标签或资源组匹配
			isMatched, result, err := TagOrResourceGroupAllocator(ctx, p.Logger(), cli, models.CloudCostUnitMoveRule{}, &instance, scodeUnitIdMap)
			if result != nil {
				if err != nil {
					result.Response.Message = err.Error()
				}
				results = append(results, result)
			}

			if !isMatched {
				notMatchedItems = append(notMatchedItems, instance)
			}

			if err != nil {
				p.Errorf(ctx, "TagOrResourceGroupAllocator error: %v", err)
				return results, &notMatchedItems, err
			}
		}
	}

	return results, &notMatchedItems, nil
}
