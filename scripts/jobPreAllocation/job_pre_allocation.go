package jobPreAllocation

import (
	"context"
	"runtime/debug"
	"strings"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

type PreAllocation struct {
	*base.JobBase
}

func New() *PreAllocation {
	return &PreAllocation{
		JobBase: base.NewJobBase("PRE_ALLOCATION",
			"账单预分账",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),

			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

func (p *PreAllocation) Name() string {
	return base.TaskPreAllocation
}
func (p *PreAllocation) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(p.Model(ctx), hbc.GoogleCloud)
	if err != nil {
		return nil, err
	}

	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, vendors,
		hbc.Bill,
		[]string{bizutils.PurposeAdmin, bizutils.PurposeDedicated},
		actfilter.SetIgnoreVendor(hbc.Private, hbc.HuaweiCloud, hbc.HuaweiCloudDedicated, hbc.TencentCloud, hbc.AWS, hbc.Azure, hbc.JXJG), // 忽略不支持的云
	)
	if err != nil {
		return nil, err
	}

	// 预分账
	b := tools.NewBatch[client.IClient, []*models.CostUnitMoveLog](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.CostUnitMoveLog, error) {
		defer func() {
			if err := recover(); err != nil {
				p.Errorf(ctx, "pre-allocation failed, cause: %s", err)
				debug.PrintStack()
			}
		}()

		switch cli := input.(type) {
		case *aliyun.BillClient:
			if !strings.Contains("hr690n, hr690x, hr690k, hr690y, hr690z", cli.Name()) {
				return nil, nil
			}
			clouds := []string{hbc.AliCloud.String(), hbc.AliCloudDedicated.String()}
			moveRules, err := bizutils.GetCostUnitMoveRules(ctx, cli.Name(), clouds)
			if err != nil {
				p.Errorf(ctx, "GetCostUnitMoveRules error: %v", err)
				return nil, err
			}

			results, notMatchedItems, err := p.PreAllocation(ctx, cli, moveRules)
			if err != nil && len(results) == 0 && len(*notMatchedItems) == 0 {
				return nil, err
			}

			NotifyResult(results, notMatchedItems, cli.Name())

			moveLogs := make([]*models.CostUnitMoveLog, len(results))
			for i, r := range results {
				var isSuccess bool
				if r.Response != nil && r.Response.Success {
					isSuccess = r.Response.Data.IsSuccess
				}

				moveLog := &models.CostUnitMoveLog{
					Cloud:          cli.Vendor().String(),
					Account:        r.Account,
					ResourceId:     r.ResourceId,
					ResourceUserId: r.UserId,
					CommodityCode:  r.CommodityCode,
					FromUnitId:     0,
					ToUnitId:       r.ToUnitId,
					ToUnitName:     r.ToUnitName,
					IsSuccess:      isSuccess,
					Response:       utils.JsonString(r.Response),
				}

				moveLogs[i] = moveLog
			}
			return moveLogs, nil

		default:
			return nil, nil
		}
	})

	results := tools.MergeData(b.Outs()...)
	if len(results) > 0 {
		bizutils.CreateCostUnitMoveLog(ctx, results...)
	}

	if err := b.Error(); err != nil {
		p.Errorf(ctx, "sync bill failed, cause: %s", err)
		return nil, err
	}

	return nil, nil
}
