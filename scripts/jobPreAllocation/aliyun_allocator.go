package jobPreAllocation

import (
	"context"
	"fmt"
	"git.haier.net/devops/ops-golang-common/utils"
	"strings"

	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/bssopenapi"

	"git.haier.net/devops/hcms-task-center/biz/api"
)

// AllocatingResult 分配财务单元请求及结果
type AllocatingResult struct {
	Account,
	UserId string
	FromUnitId    int
	ToUnitId      int
	ToUnitName    string
	ResourceId    string
	CommodityCode string
	ApportionCode string
	Response      *bssopenapi.AllocateCostUnitResourceResponse
}

type Allocator func(ctx context.Context, log *log.Logger,
	cli *aliyun.BillClient,
	rule models.CloudCostUnitMoveRule,
	instance *bssopenapi.ResourceInstanceList,
	scodeUnitIdMap map[string]bssopenapi.CostUnitDtoListItem) (
	bool, *AllocatingResult, error)

// ExclusiveCommodityAllocator 独享产品
func ExclusiveCommodityAllocator(ctx context.Context, log *log.Logger,
	cli *aliyun.BillClient,
	rule models.CloudCostUnitMoveRule,
	instance *bssopenapi.ResourceInstanceList,
	scodeUnitIdMap map[string]bssopenapi.CostUnitDtoListItem) (
	bool, *AllocatingResult, error) {

	isMatched := false
	if instance.CommodityCode == rule.CommodityCode && rule.Priority == "CommodityCode" {
		isMatched = true
	}

	if !isMatched {
		return isMatched, nil, nil
	}

	fmt.Printf("<-%s->按产品指定应用划分%s：%s; %s; %s; %s\n",
		cli.Name(), instance.CommodityCode, instance.ResourceId, instance.ResourceGroup, instance.ResourceTag, rule.Appscode)

	toUnit, ok := scodeUnitIdMap[rule.Appscode]
	if ok {
		response, err := allocateCostUnitResource(
			ctx, 0, toUnit, rule.CommodityCode, instance, cli)
		return isMatched, response, err
	}

	project, pjtErr := api.HdsClient().QueryAlmProject(rule.Appscode)
	if pjtErr != nil {
		log.Errorf(ctx, "QueryAlmProject rule = %s, instance = %s error: %v", utils.JsonString(rule), utils.JsonString(instance), pjtErr)
		return isMatched, nil, pjtErr
	}

	resp, err := createCostUnit(ctx, project, rule.Appscode, cli)
	if err != nil || !resp.Success {
		log.Errorf(ctx, "CreateCostUnit error: %v", err)
		return isMatched, nil, err
	}

	newUnitId := resp.Data.CostUnitDtoList[0]
	response, err := allocateCostUnitResource(
		ctx, 0, newUnitId, rule.CommodityCode, instance, cli)

	return isMatched, response, err
}

// CustomRuleAllocator 根据优先级设置的规则walk
func CustomRuleAllocator(ctx context.Context, log *log.Logger,
	cli *aliyun.BillClient,
	rule models.CloudCostUnitMoveRule,
	instance *bssopenapi.ResourceInstanceList,
	scodeUnitIdMap map[string]bssopenapi.CostUnitDtoListItem) (
	bool, *AllocatingResult, error) {
	isMatched := false

	priorityOfInstance, priorityOfRule := ExtractPriorityProperties(instance, rule)

	if instance.CommodityCode == rule.CommodityCode &&
		priorityOfInstance != "" &&
		priorityOfRule != "" &&
		strings.Contains(priorityOfInstance, priorityOfRule) {
		isMatched = true
	}

	if !isMatched {
		return isMatched, nil, nil
	}

	if rule.Priority == "ResourceId" && priorityOfRule == "console" && (instance.CommodityCode == "sls" || instance.CommodityCode == "sls_dataingest_public_cn") {
		parts := strings.Split(strings.Split(instance.ResourceId, ";")[2], "-")
		appscode := strings.ToUpper(parts[len(parts)-1])
		log.Infof(ctx, "%s按照实例信息划分%s：%s; %s; %s; %s",
			cli.Name(), instance.CommodityCode, instance.ResourceId, instance.ResourceGroup, instance.ResourceTag, appscode)

		toUnit, ok := scodeUnitIdMap[appscode]
		if ok {
			response, err := allocateCostUnitResource(
				ctx, 0, toUnit, rule.CommodityCode, instance, cli)
			return isMatched, response, err
		}

		projectUsingResourceId, err := api.HdsClient().QueryAlmProject(appscode)
		if err != nil {
			log.Errorf(ctx, "query alm project %s error: %v", appscode, err)
			return isMatched, nil, err
		}

		resp, err := createCostUnit(ctx, projectUsingResourceId, appscode, cli)
		if err != nil || !resp.Success {
			log.Errorf(ctx, "CreateCostUnit error: %v", err)
			return isMatched, nil, err
		}

		newUnit := resp.Data.CostUnitDtoList[0]
		response, err := allocateCostUnitResource(
			ctx, 0, newUnit, rule.CommodityCode, instance, cli)
		return isMatched, response, err
	}

	log.Infof(ctx, "按照实例信息划分%s：%s; %s; %s; %s",
		cli.Name(), instance.CommodityCode, instance.ResourceId, instance.ResourceGroup, instance.ResourceTag, rule.Appscode)
	if toUnit, ok := scodeUnitIdMap[rule.Appscode]; ok {
		response, err := allocateCostUnitResource(ctx, 0, toUnit, rule.CommodityCode, instance, cli)
		return isMatched, response, err
	}

	project, pjtErr := api.HdsClient().QueryAlmProject(rule.Appscode)
	if pjtErr != nil {
		log.Errorf(ctx, "QueryAlmProject error: %v", pjtErr)
		return isMatched, nil, pjtErr
	}

	resp, err := createCostUnit(ctx, project, rule.Appscode, cli)
	if err != nil || !resp.Success {
		log.Errorf(ctx, "CreateCostUnit error: %v", err)
		return isMatched, nil, err
	}
	newUnit := resp.Data.CostUnitDtoList[0]
	response, err := allocateCostUnitResource(ctx, 0, newUnit, rule.CommodityCode, instance, cli)
	return isMatched, response, err

	return isMatched, nil, nil
}

// TagOrResourceGroupAllocator 根据标签或资源组来分配
func TagOrResourceGroupAllocator(ctx context.Context, log *log.Logger,
	cli *aliyun.BillClient,
	rule models.CloudCostUnitMoveRule,
	instance *bssopenapi.ResourceInstanceList,
	scodeUnitIdMap map[string]bssopenapi.CostUnitDtoListItem) (
	bool, *AllocatingResult, error) {

	isMatched := false
	if instance.ResourceGroup == "" && instance.ResourceTag == "" {
		return false, nil, nil
	}

	//priorityOfInstance, priorityOfRule := ExtractPriorityProperties(instance, rule)
	//if priorityOfInstance != "" && !strings.Contains(priorityOfInstance, priorityOfRule) {
	//	isMatched = true
	//}
	isMatched = true

	if instance.ResourceTag != "" {
		result := SplitTag(instance.ResourceTag)
		if appscode, ok := result["appscode"]; ok {
			log.Infof(ctx, "%s匹配到资源分类，按照标签的S码拆分%s：%s; %s; %s; %s",
				cli.Name(), instance.CommodityCode, instance.ResourceId, instance.ResourceGroup, instance.ResourceTag, appscode)

			if toUnit, ok := scodeUnitIdMap[appscode]; ok {
				response, err := allocateCostUnitResource(ctx, 0, toUnit, instance.CommodityCode, instance, cli)
				return isMatched, response, err
			}

			projectUsingTag, err := api.HdsClient().QueryAlmProject(appscode)
			if err != nil {
				log.Errorf(ctx, "query alm project %s error: %v", appscode, err)
				return isMatched, nil, err
			}

			resp, err := createCostUnit(ctx, projectUsingTag, appscode, cli)
			if err != nil || !resp.Success {
				log.Errorf(ctx, "CreateCostUnit error: %v", err)
				return isMatched, nil, err
			}

			newUnit := resp.Data.CostUnitDtoList[0]
			response, err := allocateCostUnitResource(ctx, 0, newUnit, instance.CommodityCode, instance, cli)
			return isMatched, response, err
		}
	}

	if instance.ResourceGroup != "" {
		if instance.ResourceGroup == "默认资源组" || instance.ResourceGroup == "default resource group" {
			log.Infof(ctx, "%s匹配到资源分类，默认资源组（默认资源组是不移动的，继续放在未分配里，打印出来就看看）%s：%s; %s; %s",
				cli.Name(), instance.CommodityCode, instance.ResourceId, instance.ResourceGroup, instance.ResourceTag)
			// 默认资源组 默认资源组是不移动的，继续放在未分配里，打印出来就看看
			return false, nil, nil
		}

		parts := strings.Split(instance.ResourceGroup, "-")
		appscode := parts[len(parts)-1]
		log.Infof(ctx, "%s匹配到资源分类，开始按照资源组S码拆分%s：%s; %s; %s; %s",
			cli.Name(), instance.CommodityCode, instance.ResourceId, instance.ResourceGroup, instance.ResourceTag, appscode)

		if toUnit, ok := scodeUnitIdMap[appscode]; ok {
			response, err := allocateCostUnitResource(ctx, 0, toUnit, instance.CommodityCode, instance, cli)
			return isMatched, response, err
		}

		projectUsingResourceGroup, err := api.HdsClient().QueryAlmProject(appscode)
		if err != nil {
			log.Errorf(ctx, "QueryAlmProject error: %v", err)
			return isMatched, nil, err
		}

		resp, err := createCostUnit(ctx, projectUsingResourceGroup, appscode, cli)
		if err != nil || !resp.Success {
			log.Errorf(ctx, "CreateCostUnit error: %v", err)
			return isMatched, nil, err
		}

		newUnit := resp.Data.CostUnitDtoList[0]
		response, err := allocateCostUnitResource(ctx, 0, newUnit, instance.CommodityCode, instance, cli)
		return isMatched, response, err
	}

	return isMatched, nil, nil
}

func ApplyAllocators(ctx context.Context, log *log.Logger,
	cli *aliyun.BillClient,
	rule models.CloudCostUnitMoveRule,
	instance *bssopenapi.ResourceInstanceList,
	scodeUnitIdMap map[string]bssopenapi.CostUnitDtoListItem,
	functions ...Allocator) (matched bool, result *AllocatingResult, err error) {
	matched = false
	for _, f := range functions {
		matched, result, err = f(ctx, log, cli, rule, instance, scodeUnitIdMap)
		if err != nil {
			log.Errorf(ctx, "ApplyAllocators error: %v, with function %d", err)
			return
		}

		if matched {
			if result != nil && result.Response != nil && result.Response.Success {
				log.Infof(ctx, "%s完成财务单元划分。rule.Appscode: %s, rule.UnitId: %d", cli.Name(), rule.Appscode, result.Response.Data.ToUnitId)
			}

			log.Infof(ctx, "%s默认资源组不移动，继续放在未分配里", cli.Name())
			break
		}
	}
	if !matched {
		log.Infof(ctx, "未匹配到资源分类，通用规则均匹配不上")
	}

	return
}
