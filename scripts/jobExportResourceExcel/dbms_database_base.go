package jobExportResourceExcel

import (
	"time"
)

type RdsInstanceBase struct {
	CloudVendor        string    `gorm:"type:varchar(128);not null" json:"cloud_vendor"`
	CloudAccountName   string    `gorm:"type:varchar(128);not null" json:"cloud_account_name"`
	Region             string    `gorm:"type:varchar(128);not null" json:"region"`
	Zone               string    `gorm:"type:varchar(128)" json:"zone"`
	ALMSCode           string    `gorm:"type:varchar(128);index:idx_scode" json:"alm_scode"`                                   // 实例所属项目S码
	Project            string    `gorm:"type:varchar(128);index:idx_project" json:"project"`                                   // 实例所属项目
	Env                string    `gorm:"type:varchar(128);not null" json:"env"`                                                // 实例所属环境
	InstanceID         string    `gorm:"type:varchar(128);not null;uniqueIndex:udx_instance_id" json:"instance_id"`            // 实例id
	PrimaryInstanceID  string    `gorm:"type:varchar(128)" json:"primary_instance_id"`                                         // 主实例id
	InstanceType       string    `gorm:"type:varchar(128);not null" json:"instance_type"`                                      // 主实例，只读实例等
	InstanceStatus     string    `gorm:"type:varchar(128);not null" json:"instance_status"`                                    // 实例状态
	ConnAddr           string    `gorm:"type:varchar(255);not null;index:idx_conn_addr_port" json:"conn_addr"`                 // 实例连接串
	ConnPort           int64     `gorm:"not null;index:idx_conn_addr_port" json:"conn_port"`                                   // 实例连接端口
	EngineType         string    `gorm:"type:varchar(64);not null;index:idx_engine_type_version;" json:"engine_type"`          // 数据库类型
	EngineMajorVersion string    `gorm:"type:varchar(128);not null;index:idx_engine_type_version" json:"engine_major_version"` // 数据库大版本
	EngineMinorVersion string    `gorm:"type:varchar(128);not null;index:idx_engine_type_version" json:"engine_minor_version"` // 数据库小版本
	VpcID              string    `gorm:"type:varchar(128)"`                                                                    // 所属VPC
	ClassCode          string    `gorm:"type:varchar(128);not null" json:"class_code"`                                         // 规格码
	StorageType        string    `gorm:"type:varchar(128);not null" json:"storage_type"`                                       // 存储类型
	Category           string    `gorm:"type:varchar(128)" json:"category"`                                                    // 类型：企业版/基础版/高可用版
	CreateAt           time.Time `json:"create_at"`                                                                            // 实例创建时间
	Description        string    `gorm:"type:text" json:"description"`                                                         // 实例描述
	HDMInstanceId      string    `gorm:"type:varchar(128);not null;index:hdm_instance_id" json:"hdm_instance_id"`              // 实例HDMid
	HDMAccountName     string    `gorm:"type:varchar(128);not null" json:"hdm_account_name"`                                   // 实例接入的HDM账号
	Cpu                int       `json:"cpu"`                                                                                  // 单位个
	Memory             int64     `json:"memory"`                                                                               // 单位MB
	Storage            int       `json:"storage"`                                                                              // 单位GB
	AvailabilityValue  string    `gorm:"type:varchar(128)" json:"availability_value"`
}
