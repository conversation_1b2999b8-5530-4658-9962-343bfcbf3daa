package jobExportResourceExcel

import (
	"context"
	"fmt"
	"strconv"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/LPX3F8/orderedmap"
	"github.com/xuri/excelize/v2"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func New() *JobExportExcelResource {
	return &JobExportExcelResource{
		JobBase: base.NewJobBase(
			"EXPORT_EXCEL",
			"导出资源excel",
			"",
			taskmodels.TaskCategoryTest,
			bizutils.DataSource(),
		),
	}
}

type JobExportExcelResource struct {
	*base.JobBase
}

func (j *JobExportExcelResource) Run(ctx context.Context) (map[string]any, error) {
	if err := j.SaveHostExcel(ctx); err != nil {
		return nil, err
	}
	if err := j.SaveDatabaseExcel(ctx); err != nil {
		return nil, err
	}

	return nil, nil
}

func (j *JobExportExcelResource) SaveDatabaseExcel(ctx context.Context) error {
	m := j.Model(ctx)
	data := make([]*models.DatabaseInfo, 0)
	if err := m.Orm().Table("rc_database_info").Where("is_deleted = ?", 0).Find(&data).Error; err != nil {
		return err
	}

	cmdbData := make([]*RdsInstanceBase, 0)
	dbms := config.Global().GetStore("dbms").Model(ctx)
	if err := dbms.Orm().Table("rds_instance_base").
		Where("cloud_vendor = ? and status <> 'Offline'", "private").
		Find(&cmdbData).Error; err != nil {
		return err
	}

	om := orderedmap.New[string, *models.DatabaseInfo]()
	for _, v := range cmdbData {
		om.Store(v.ConnAddr+":"+strconv.Itoa(int(v.ConnPort)), &models.DatabaseInfo{
			Scode:         v.ALMSCode,
			InstanceId:    v.InstanceID,
			Vendor:        hbc.Private,
			EngineType:    v.EngineType,
			Host:          v.ConnAddr,
			Port:          int(v.ConnPort),
			EngineVersion: v.EngineMajorVersion,
			Env:           v.Env,
			Region:        v.Region,
			Zone:          v.Zone,
		})
	}
	for _, v := range data {
		om.Store(v.Host+":"+strconv.Itoa(v.Port), v)
	}

	file := excelize.NewFile()
	sheet := "Sheet1"
	_, _ = file.NewSheet(sheet)

	columns := []string{"S码", "实例ID", "来源", "数据库类型", "CPU", "内存", "磁盘", "连接地址", "端口", "版本", "环境", "地域", "机房", "领域", "团队", "负责人"}
	for i, v := range columns {
		_ = file.SetCellValue(sheet, GetColumn(i)+"1", v)
	}

	data = om.Slice()
	for i, v := range data {
		_ = file.SetCellValue(sheet, GetColumn(0)+strconv.Itoa(i+2), v.Scode)
		_ = file.SetCellValue(sheet, GetColumn(1)+strconv.Itoa(i+2), v.InstanceId)
		_ = file.SetCellValue(sheet, GetColumn(2)+strconv.Itoa(i+2), v.Vendor)
		_ = file.SetCellValue(sheet, GetColumn(3)+strconv.Itoa(i+2), v.EngineType)
		_ = file.SetCellValue(sheet, GetColumn(4)+strconv.Itoa(i+2), v.Cpu)
		_ = file.SetCellValue(sheet, GetColumn(5)+strconv.Itoa(i+2), v.Memory)
		_ = file.SetCellValue(sheet, GetColumn(6)+strconv.Itoa(i+2), v.DiskSize)
		_ = file.SetCellValue(sheet, GetColumn(7)+strconv.Itoa(i+2), v.Host)
		_ = file.SetCellValue(sheet, GetColumn(8)+strconv.Itoa(i+2), v.Port)
		_ = file.SetCellValue(sheet, GetColumn(9)+strconv.Itoa(i+2), v.EngineVersion)
		_ = file.SetCellValue(sheet, GetColumn(10)+strconv.Itoa(i+2), v.Env)
		_ = file.SetCellValue(sheet, GetColumn(11)+strconv.Itoa(i+2), v.Region)
		_ = file.SetCellValue(sheet, GetColumn(12)+strconv.Itoa(i+2), v.Zone)
		_ = file.SetCellValue(sheet, GetColumn(13)+strconv.Itoa(i+2), v.Domain)
		_ = file.SetCellValue(sheet, GetColumn(14)+strconv.Itoa(i+2), v.Team)
		_ = file.SetCellValue(sheet, GetColumn(15)+strconv.Itoa(i+2), fmt.Sprintf("%s-%s", v.OwnerId, v.OwnerName))
	}

	return file.SaveAs("database.xlsx")
}

func (j *JobExportExcelResource) SaveHostExcel(ctx context.Context) error {
	m := j.Model(ctx)
	data := make([]*models.HostInfo, 0)
	if err := m.Orm().Table("rc_host_info").Where("is_deleted = ?", 0).Find(&data).Error; err != nil {
		return err
	}

	file := excelize.NewFile()
	sheet := "Sheet1"
	_, _ = file.NewSheet(sheet)

	columns := []string{"S码", "服务器IP", "来源", "服务器类型", "环境", "操作系统", "CPU", "内存", "磁盘", "领域", "团队", "负责人"}
	for i, v := range columns {
		_ = file.SetCellValue(sheet, GetColumn(i)+"1", v)
	}

	for i, v := range data {
		_ = file.SetCellValue(sheet, GetColumn(0)+strconv.Itoa(i+2), v.Scode)
		_ = file.SetCellValue(sheet, GetColumn(1)+strconv.Itoa(i+2), v.PrivateIp)
		_ = file.SetCellValue(sheet, GetColumn(2)+strconv.Itoa(i+2), v.Vendor)
		_ = file.SetCellValue(sheet, GetColumn(3)+strconv.Itoa(i+2), v.HostType)
		_ = file.SetCellValue(sheet, GetColumn(4)+strconv.Itoa(i+2), v.Env)
		_ = file.SetCellValue(sheet, GetColumn(5)+strconv.Itoa(i+2), v.OsName)
		_ = file.SetCellValue(sheet, GetColumn(6)+strconv.Itoa(i+2), v.Cpu)
		_ = file.SetCellValue(sheet, GetColumn(7)+strconv.Itoa(i+2), v.Memory)
		_ = file.SetCellValue(sheet, GetColumn(8)+strconv.Itoa(i+2), v.DiskSize)
		_ = file.SetCellValue(sheet, GetColumn(9)+strconv.Itoa(i+2), v.Domain)
		_ = file.SetCellValue(sheet, GetColumn(10)+strconv.Itoa(i+2), v.Team)
		_ = file.SetCellValue(sheet, GetColumn(11)+strconv.Itoa(i+2), fmt.Sprintf("%s-%s", v.OwnerId, v.OwnerName))
	}

	return file.SaveAs("host.xlsx")
}
