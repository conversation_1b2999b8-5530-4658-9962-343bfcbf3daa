package jobSyncHostEcs

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/utils/timeutil"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/aws/smithy-go/ptr"
	ecsModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/model"
	epsModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	"github.com/pkg/errors"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

const huawei_sdk_client = "ecs"

var huawei_product_code_ecs = []string{"hws.service.type.ec2", "hws.service.type.bigdata"}

func (e *EcsSync) GetHuaweiEnterpriseProject(client *huaweicloud.EpsClient, ctx context.Context, projectId string) *epsModel.EpDetail {
	return bizutils.LoadFromMap[string, *epsModel.EpDetail](e.mtx, e.ep, projectId, func() (*epsModel.EpDetail, error) {
		if client == nil {
			return nil, nil
		}
		return client.ShowEnterpriseProject(ctx, projectId)
	})
}

func getHuaweiEcsMetaData(ecs ecsModel.ServerDetail, key string) string {
	if v, ok := ecs.Metadata[key]; ok {
		switch v {
		case "Linux", "linux":
			return common.OsTypeLinux
		default:
			return v
		}
	}
	return ""
}

func getHuaweiEcsPayType(ecs ecsModel.ServerDetail) string {
	switch getHuaweiEcsMetaData(ecs, "charging_mode") {
	case "0":
		return "PrePaid"
	case "1":
		return "PostPaid"
	default:
		return "Other"
	}
}

func getHuaweiEcsAddress(ecs ecsModel.ServerDetail, typ string) string {
	for _, v := range ecs.Addresses {
		for _, addr := range v {
			if addr.OSEXTIPStype != nil && addr.OSEXTIPStype.Value() == typ {
				return addr.Addr
			}
		}
	}
	return ""
}

func (e *EcsSync) handleHuaweiProjectPanic(panicError any, ctx context.Context, region model.Region) error {
	var msg string
	switch pe := panicError.(type) {
	case error:
		msg = pe.Error()
	case string:
		msg = pe
	}

	if strings.HasPrefix(msg, "unexpected regionId") {
		e.Warnf(ctx, "%s getHuaweiRegionEcs: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, "failed to get project id, No project id found") {
		e.Warnf(ctx, "%s getHuaweiRegionEcs: %s", region.Id, msg)
	} else {
		return errors.New(msg)
	}
	return nil
}

func (e *EcsSync) getHuaweiECSInfo(defClient *huaweicloud.IamClient, ctx context.Context) ([]*models.HostInfo, error) {
	projects, err := defClient.ListProjects(ctx)
	if err != nil {
		return nil, bizutils.WarpClientError(defClient, err)
	}
	regions, err := defClient.ListRegions(ctx)
	if err != nil {
		return nil, bizutils.WarpClientError(defClient, err)
	}

	projectBatch := tools.NewBatch[model.ProjectResult, []*models.HostInfo](ctx)
	projectBatch.Run(projects, func(ctx context.Context, project model.ProjectResult) ([]*models.HostInfo, error) {
		var targetRegion *model.Region
		for _, region := range regions {
			if region.Id == project.Name {
				targetRegion = &region
				break
			}
		}
		if targetRegion == nil {
			e.Warnf(ctx, "unexpected regionId: %s, skip ...", project.Name)
			return nil, nil
		}

		infoList, err := e.getHuaweiRegionEcs(ctx, defClient, *targetRegion, project)
		e.Infof(ctx, "get %d records for region %s", len(infoList), targetRegion.Id)
		return infoList, err
	})

	return tools.MergeData(projectBatch.Outs()...), projectBatch.Error()
}

func (e *EcsSync) getHuaweiRegionEcs(ctx context.Context, defClient client.IClient, region model.Region, project model.ProjectResult) (infoList []*models.HostInfo, err error) {
	ecsClient := hybrid.AccountManager().HuaweiEcsClient(ctx, defClient.Name(), defClient.Tag(), region.Id, project.Id)
	if ecsClient == nil || ecsClient.Client() == nil {
		return nil, nil
	}

	ecsList, listServerError := ecsClient.ListServersDetailsOfAll(ctx)
	if listServerError != nil {
		e.Errorf(ctx, "getHuaweiRegionEcs: %s, project: %s", listServerError, utils.JsonString(project))
		return nil, nil
	}

	epsClient := hybrid.AccountManager().HuaweiEpsClient(ctx, defClient.Name(), defClient.Tag(), region.Id)
	infoList = make([]*models.HostInfo, len(ecsList))
	for i, ecs := range ecsList {
		infoList[i] = e.tranceHuaweiEcs2HostInfo(ctx, ecsClient, epsClient, ecs)
	}

	filteredInfoList := make([]*models.HostInfo, 0)
	// 华为云没有192.168网段的机器，如果有是做测试用，忽略掉
	for _, info := range infoList {
		if strings.HasPrefix(info.PrivateIp, "192.168.") {
			continue
		}
		filteredInfoList = append(filteredInfoList, info)
	}

	return filteredInfoList, nil
}

func (e *EcsSync) tranceHuaweiEcs2HostInfo(
	ctx context.Context,
	ecsClient *huaweicloud.EcsClient,
	epsClient *huaweicloud.EpsClient,
	ecs ecsModel.ServerDetail,
) *models.HostInfo {

	env, scode, project := bizutils.ParseHuaweiEnvProject(ctx, ecs.Name, pointer.Value(ecs.EnterpriseProjectId), epsClient, e)
	projectInfo, _ := api.HdsClient().QueryAlmProject(scode)
	if projectInfo != nil {
		project = projectInfo.Id
	}

	creationTime, _ := time.Parse(bizutils.NormalDatetimeFmt, ecs.Created)
	expiredTime, _ := time.Parse(bizutils.HuaweiEcsTimeFormat, ecs.AutoTerminateTime)

	cpu, _ := strconv.Atoi(ecs.Flavor.Vcpus)
	ram, _ := strconv.Atoi(ecs.Flavor.Ram)

	var gpuInfo *string
	var gpuModel *string
	var imageId string
	var classCode string
	var gpuAmount *int
	var gpuMemoryMb *int
	if ecs.Flavor != nil {
		if len(ecs.Flavor.Gpus) > 0 {
			if len(ecs.Flavor.Gpus) == 1 {
				gpuModel = ecs.Flavor.Gpus[0].Name
				amount := int(*ecs.Flavor.Gpus[0].Count)
				gpuAmount = &amount
				memoryMb := int(*ecs.Flavor.Gpus[0].MemoryMb)
				gpuMemoryMb = &memoryMb
			}
			info := utils.JsonString(ecs.Flavor.Gpus)
			gpuInfo = &info
		}
		classCode = ecs.Flavor.Name
	}
	if ecs.Image != nil {
		imageId = ecs.Image.Id
	}

	tags, err := ecsClient.ShowServerTags(ecs.Id)
	if err != nil {
		e.Errorf(ctx, "ECS ShowServerTags: %s", err.Error())
	}
	monitoringMode := bizutils.MonitoringModeIsNone
	if len(tags) > 0 {
		for _, tag := range tags {
			if tag.Key == "monitor" && strings.ToLower(strings.TrimSpace(pointer.String(tag.Value))) == "false" {
				monitoringMode = bizutils.MonitoringModeIsNotNecessary
			}
		}
	}
	cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(ecsClient.Vendor()), ecsClient.Identifier(), ecs.Id, huawei_product_code_ecs)
	return &models.HostInfo{
		Model:          e.Model(ctx),
		Vendor:         ecsClient.Vendor(),
		AccountName:    ecsClient.Name(),
		InstanceId:     ecs.Id,
		InstanceName:   ecs.Name,
		CreationTime:   timeutil.ZeroTime(creationTime),
		ExpiredTime:    timeutil.ZeroTime(expiredTime),
		PrivateIp:      getHuaweiEcsAddress(ecs, "fixed"),
		NetworkType:    "vpc",
		VpcId:          getHuaweiEcsMetaData(ecs, "vpc_id"),
		Project:        project,
		Scode:          bizutils.SwapSCode(scode, cmdb.Scode),
		Env:            bizutils.UnifyEnv(env),
		OsType:         bizutils.UnifyOsType(getHuaweiEcsMetaData(ecs, "os_type")),
		OsName:         getHuaweiEcsMetaData(ecs, "os_type"),
		OsArch:         common.OsArchX8664,
		Numa:           ptr.Bool(false),
		ResourceGroup:  pointer.String(ecs.EnterpriseProjectId),
		ImageId:        imageId,
		Cpu:            uint64(cpu),
		Memory:         uint64(ram),
		HostStatus:     bizutils.UnifyHostStatus(ecs.Status),
		UniHostStatus:  getHuaweiEcsUniHostStatus(ecs.Status),
		HostType:       hbc.ECS.String(),
		HostInsId:      ecs.HostId,
		Region:         ecsClient.Region(),
		Zone:           ecs.OSEXTAZavailabilityZone,
		ClassCode:      classCode,
		ChargeType:     getHuaweiEcsPayType(ecs),
		Description:    pointer.String(ecs.Description),
		GpuInfo:        gpuInfo,
		GpuAmount:      gpuAmount,
		GpuModel:       gpuModel,
		GpuMemory:      gpuMemoryMb,
		AggregatedId:   cmdb.AggregatedID,
		MonitoringMode: monitoringMode,
		SDKClientName:  huawei_sdk_client,
	}
}

func getHuaweiEcsUniHostStatus(status string) string {
	status = strings.TrimSpace(strings.ToUpper(status))
	switch status {
	case "BUILD", "REBOOT", "HARD_REBOOT", "REBUILD", "MIGRATING", "RESIZE", "REVERT_RESIZE", "VERIFY_RESIZE":
		return "pending" // 启动中
	case "ACTIVE":
		return "running" // 运行中
	//case "":
	//	return "stopping" // 停止中
	case "SHUTOFF", "ERROR", "UNKNOWN":
		return "stopped" // 已停止
	case "DELETED", "SHELVED", "SHELVED_OFFLOADED":
		return "unsubscribing" // 退订中
	default:
		return "other"
	}
}
