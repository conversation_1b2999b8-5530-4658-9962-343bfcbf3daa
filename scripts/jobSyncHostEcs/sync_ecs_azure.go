package jobSyncHostEcs

import (
	"context"
	"strings"
	"sync"

	"git.haier.net/devops/ops-golang-common/utils/timeutil"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute/v6"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v2"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var azure_product_code_ecs = []string{"microsoft.computedisks", "microsoft.computevirtualmachines", "microsoft.computesnapshots"}

func (e *EcsSync) syncAzureEcsInfo(client *azure.VmClient, ctx context.Context) ([]*models.HostInfo, error) {
	vms, err := client.ListAllPager(ctx)
	if err != nil {
		if strings.Contains(err.Error(), "not be found") {
			return nil, nil
		}
		e.Errorf(ctx, "ListAllPager error: %v", err)
		return nil, err
	}

	b := tools.NewBatch[*armcompute.VirtualMachine, *models.HostInfo](ctx)
	b.Run(vms, func(ctx context.Context, vm *armcompute.VirtualMachine) (*models.HostInfo, error) {
		var wg sync.WaitGroup
		var diskSizeInMB float64
		scode, env, project := ParseProject(vm)
		projectInfo, _ := api.HdsClient().QueryAlmProject(scode)
		if projectInfo != nil {
			project = projectInfo.Id
		}

		if shouldIgnore(vm) {
			e.Infof(ctx, "ignore databricks vm: %s", pointer.Value(vm.ID))
			return nil, nil
		}

		rgn := GetResourceGroupName(*vm.ID)
		storageProfile := vm.Properties.StorageProfile
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(client.Vendor()), client.Identifier(), pointer.Value(vm.Name), azure_product_code_ecs)
		hostInfo := &models.HostInfo{
			Model:         e.Model(ctx),
			Vendor:        client.Vendor(),
			AccountName:   client.Name(),
			InstanceId:    *vm.Name,
			InstanceName:  *vm.Name,
			ResourceGroup: rgn,
			CreationTime:  timeutil.ZeroTimePtr(vm.Properties.TimeCreated),
			Project:       project,
			Env:           bizutils.UnifyEnv(env),
			HostType:      hbc.ECS.String(),
			Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
			Region:        *vm.Location,
			Zone:          *vm.Location,
			DiskSize:      diskSizeInMB,
			OsType:        bizutils.UnifyOsType(string(*storageProfile.OSDisk.OSType)),
			AggregatedId:  cmdb.AggregatedID,
		}

		wg.Add(1)
		go func(hostInfo *models.HostInfo, client *azure.VmClient, vm *armcompute.VirtualMachine, ctx context.Context, rgn string) {
			defer wg.Done()
			vmSize, sizeErr := getVmSizeInfo(client, vm, ctx, rgn)
			if sizeErr != nil {
				e.Errorf(ctx, "failed to get vm size info: %v", sizeErr)
				return
			}
			if vmSize.NumberOfCores != nil {
				hostInfo.Cpu = uint64(*vmSize.NumberOfCores)
			}

			if vmSize.MemoryInMB != nil {
				hostInfo.Memory = uint64(*vmSize.MemoryInMB)
			}
		}(hostInfo, client, vm, ctx, rgn)

		wg.Add(1)
		go func(hostInfo *models.HostInfo, client *azure.VmClient, vm *armcompute.VirtualMachine, ctx context.Context, rgn string) {
			defer wg.Done()
			instanceView, instanceViewErr := getVmInstanceView(client, vm, ctx, rgn)
			if instanceViewErr != nil {
				e.Errorf(ctx, "failed to get vm instance view info: %v", instanceViewErr)
				return
			}
			if len(instanceView.Statuses) > 0 {
				for _, status := range instanceView.Statuses {
					code := pointer.Value(status.Code)
					if strings.Contains(code, "PowerState") {
						s := strings.Split(code, "/") // PowerState/running
						if len(s) > 1 {
							hostInfo.HostStatus = s[1]
							hostInfo.UniHostStatus = getAzureEcsUniHostStatus(s[1])
						}
					}
				}
			}
		}(hostInfo, client, vm, ctx, rgn)

		itfClient := hybrid.AccountManager().AzureNetworkInterfaceClient(
			ctx, client.Name(), client.Tag(), client.Region(), client.TenantId(), client.Identifier())

		wg.Add(1)
		go func(hostInfo *models.HostInfo, vm *armcompute.VirtualMachine, ctx context.Context,
			itfClient *azure.NetworkInterfaceClient, client *azure.VmClient) {
			defer wg.Done()

			itf, itfErr := GetNetworkInterface(vm, itfClient, ctx)
			if itfErr != nil {
				e.Logger().Errorf(ctx, "failed to parse network interface: %v", itfErr)
				return
			}
			hostInfo.PrivateIp = pointer.String(itf.Properties.IPConfigurations[0].Properties.PrivateIPAddress)
			id := pointer.String(itf.Properties.IPConfigurations[0].Properties.Subnet.ID)
			_, vpcId, subnetId := ParseVpcIdAndSubnetId(id)

			hostInfo.SubnetId = subnetId
			hostInfo.VpcId = vpcId

			if itf.Properties.IPConfigurations[0].Properties.PublicIPAddress != nil {
				publicIpClient := hybrid.AccountManager().AzurePublicIPAddressClient(
					ctx, client.Name(), client.Tag(), client.Region(), client.TenantId(), client.Identifier())
				resourceGroupName, publicIpAddressName :=
					GetResourceGroupAndPublicIpAddressName(*itf.Properties.IPConfigurations[0].Properties.PublicIPAddress.ID)
				publicIp, pipErr := GetPublicIpAddress(resourceGroupName, publicIpAddressName, publicIpClient, ctx)
				if pipErr != nil {
					e.Logger().Errorf(ctx, "failed to parse public ip address: %v", pipErr)
					return
				}

				hostInfo.PublicIp = pointer.String(publicIp)
			}
		}(hostInfo, vm, ctx, itfClient, client)

		wg.Wait()
		return hostInfo, nil
	})

	return tools.MergeData(b.Outs()), b.Error()
}

func shouldIgnore(vm *armcompute.VirtualMachine) bool {
	if vm.Tags != nil {
		if vendor, ok := vm.Tags["Vendor"]; ok {
			if pointer.Value(vendor) == "Databricks" {
				return true
			}
		}
	}
	return false
}

func getVmSizeInfo(client *azure.VmClient, vm *armcompute.VirtualMachine, ctx context.Context, rgn string) (*armcompute.VirtualMachineSize, error) {
	var vmSize *armcompute.VirtualMachineSize
	vmSizeList, vmSizeErr := client.NewListAvailableSizesPager(ctx, rgn, *vm.Name)
	if vmSizeErr != nil {
		return nil, vmSizeErr
	}
	for _, s := range vmSizeList {
		if string(*vm.Properties.HardwareProfile.VMSize) == *s.Name {
			vmSize = s
		}
	}
	return vmSize, nil
}

func GetResourceGroupName(id string) string {
	var groupName string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			groupName = s[n+1]
		}
	}
	return groupName
}

func ParseProject(v *armcompute.VirtualMachine) (s string, env string, project string) {
	if v.Tags == nil {
		return
	}
	s = pointer.String(v.Tags["SCode"])
	env = pointer.String(v.Tags["env"])
	project = pointer.String(v.Tags["Project"])

	return
}

func GetResourceGroupAndInterfaceName(id string) (string, string) {
	var groupName, interfaceName string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			groupName = s[n+1]
		}
		if k == "networkInterfaces" {
			interfaceName = s[n+1]
		}
	}
	return groupName, interfaceName
}

func GetResourceGroupAndPublicIpAddressName(id string) (string, string) {
	var groupName, publicIpAddressName string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			groupName = s[n+1]
		}
		if k == "publicIPAddresses" {
			publicIpAddressName = s[n+1]
		}
	}
	return groupName, publicIpAddressName
}

func GetNetworkInterface(vm *armcompute.VirtualMachine, itfClient *azure.NetworkInterfaceClient, ctx context.Context) (*armnetwork.Interface, error) {
	resourceGroupName, networkInterfaceName := GetResourceGroupAndInterfaceName(*vm.Properties.NetworkProfile.NetworkInterfaces[0].ID)
	return itfClient.Get(ctx, resourceGroupName, networkInterfaceName)
}

func GetPublicIpAddress(resourceGroupName, publicIPAddressName string, publicIPAddressClient *azure.PublicIPAddressClient, ctx context.Context) (*string, error) {
	publicIp, err := publicIPAddressClient.Get(ctx, resourceGroupName, publicIPAddressName)
	if err != nil {
		return nil, err
	}

	return publicIp.Properties.IPAddress, nil
}

func ParseVpcIdAndSubnetId(id string) (resourceGroupName string, vpcId string, subnetId string) {
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			resourceGroupName = s[n+1]
		}
		if k == "virtualNetworks" {
			vpcId = s[n+1]
		}
		if k == "subnets" {
			subnetId = s[n+1]
		}
	}
	return
}

func getVmInstanceView(client *azure.VmClient, vm *armcompute.VirtualMachine, ctx context.Context, rgn string) (*armcompute.VirtualMachineInstanceView, error) {
	res, vmInstanceViewErr := client.GetVirtualMachineInstanceView(ctx, rgn, *vm.Name)
	if vmInstanceViewErr != nil {
		return nil, vmInstanceViewErr
	}

	return &res.VirtualMachineInstanceView, nil
}

func getAzureEcsUniHostStatus(status string) string {
	status = strings.TrimSpace(strings.ToLower(status))
	switch status {
	case "creating", "starting":
		return "pending" // 启动中
	case "running":
		return "running" // 运行中
	case "stopping":
		return "stopping" // 停止中
	case "stopped":
		return "stopped" // 已停止
	case "deallocated":
		return "unsubscribing" // 退订中
	default:
		return "other"
	}
}
