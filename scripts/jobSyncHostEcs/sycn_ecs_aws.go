package jobSyncHostEcs

import (
	"context"
	"strings"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/smithy-go/ptr"
)

var aws_product_code_ecs = []string{"AmazonEC2", "um7rif6n97tra9v1l09q16ct4"}

func (e *EcsSync) getAwsECSInfo(defClient *aws.Ec2Client, ctx context.Context) ([]*models.HostInfo, error) {
	regions, err := defClient.DescribeAvailableRegions(ctx)
	if err != nil {
		return nil, bizutils.WarpClientError(defClient, err)
	}

	b := tools.NewBatch[types.Region, []*models.HostInfo](ctx)
	b.Run(regions, func(ctx context.Context, r types.Region) ([]*models.HostInfo, error) {
		return e.getAwsRegionEcsInfo(defClient, ctx, r)
	})

	return tools.MergeData(b.Outs()...), b.Error()
}

func (e *EcsSync) getAwsRegionEcsInfo(defClient *aws.Ec2Client, ctx context.Context, region types.Region) ([]*models.HostInfo, error) {
	ecsClient := hybrid.AccountManager().AwsEc2Client(ctx, defClient.Name(), defClient.Tag(), pointer.String(region.RegionName))
	ecsList, err := bizutils.WithRetry(3, func() ([]types.Instance, error) {
		return ecsClient.DescribeInstancesOfAll(ctx)
	})
	if err != nil {
		return nil, bizutils.WarpClientError(ecsClient, err)
	}

	hostInfoList := make([]*models.HostInfo, len(ecsList))
	for i, ecs := range ecsList {
		ecsType := e.getAwsEcsTypes(ecsClient, ctx, ecs.InstanceType)
		scode := getTagValue(ecs, "SCode")
		project := "default"
		projectInfo, _ := api.HdsClient().QueryAlmProject(scode)
		if projectInfo != nil {
			project = projectInfo.Id
		}
		monitoringMode := bizutils.MonitoringModeIsNone
		if len(ecs.Tags) > 0 {
			for _, tag := range ecs.Tags {
				if pointer.String(tag.Key) == "monitor" && strings.ToLower(strings.TrimSpace(pointer.String(tag.Value))) == "false" {
					monitoringMode = bizutils.MonitoringModeIsNotNecessary
				}
			}
		}
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(ecsClient.Vendor()), ecsClient.Identifier(), pointer.String(ecs.InstanceId), aws_product_code_ecs)
		hostInfoList[i] = &models.HostInfo{
			Model:          e.Model(ctx),
			Vendor:         ecsClient.Vendor(),
			AccountName:    ecsClient.Name(),
			InstanceId:     pointer.String(ecs.InstanceId),
			InstanceName:   getTagValue(ecs, "name"),
			CreationTime:   timeutil.ZeroTimePtr(ecs.LaunchTime),
			ExpiredTime:    nil,
			IsDeleted:      ptr.Bool(false),
			PrivateIp:      pointer.String(ecs.PrivateIpAddress),
			PublicIp:       pointer.String(ecs.PublicIpAddress),
			NetworkType:    "vpc",
			VpcId:          pointer.String(ecs.VpcId),
			SubnetId:       pointer.String(ecs.SubnetId),
			Scode:          bizutils.SwapSCode(scode, cmdb.Scode),
			Project:        project,
			Env:            bizutils.UnifyEnv(getTagValue(ecs, "env")),
			ResourceGroup:  scode,
			OsType:         bizutils.UnifyOsType(UnifyOSTypeForAWS(ecs)),
			OsName:         pointer.String(ecs.PlatformDetails),
			OsArch:         string(ecs.Architecture),
			Numa:           ptr.Bool(false),
			ImageId:        pointer.String(ecs.ImageId),
			Cpu:            uint64(pointer.Int32(ecsType.VCpuInfo.DefaultVCpus)),
			Memory:         uint64(pointer.Int64(ecsType.MemoryInfo.SizeInMiB)),
			HostStatus:     bizutils.UnifyHostStatus(string(ecs.State.Name)),
			UniHostStatus:  getAwsEcsUniHostStatus(string(ecs.State.Name)),
			HostType:       hbc.ECS.String(),
			Region:         ecsClient.Region(),
			Zone:           pointer.String(ecs.Placement.AvailabilityZone),
			ClassCode:      string(ecs.InstanceType),
			ChargeType:     getAWSEcsChargeType(ecs),
			MonitoringMode: monitoringMode,
			AggregatedId:   cmdb.AggregatedID,
		}
	}
	return hostInfoList, nil
}

func UnifyOSTypeForAWS(ecs types.Instance) string {
	osType := pointer.String(ecs.PlatformDetails)
	switch {
	case strings.HasPrefix(osType, "Red Hat Enterprise"):
		return common.OsTypeLinux
	case strings.HasPrefix(osType, "SQL Server Stand"):
		return common.OsTypeWindows
	}
	return osType
}

func getAWSEcsChargeType(instance types.Instance) string {
	switch string(instance.InstanceLifecycle) {
	case "":
		return "on-demand"
	default:
		return string(instance.InstanceLifecycle)
	}
}

func (e *EcsSync) getAwsEcsTypes(cli *aws.Ec2Client, ctx context.Context, ecsType types.InstanceType) *types.InstanceTypeInfo {
	return bizutils.LoadFromMap[types.InstanceType, *types.InstanceTypeInfo](e.mtx, e.awsEcsTypes, ecsType, func() (*types.InstanceTypeInfo, error) {
		instanceType, err := cli.DescribeInstanceType(ctx, ecsType)
		if err != nil {
			return nil, err
		}
		if len(instanceType) > 0 {
			return &instanceType[0], nil
		}
		return nil, nil
	})
}

func getTagValue(ecs types.Instance, key string) string {
	for _, tag := range ecs.Tags {
		if strings.EqualFold(pointer.String(tag.Key), key) {
			return pointer.String(tag.Value)
		}
	}
	return ""
}

func getAwsEcsUniHostStatus(status string) string {
	status = strings.TrimSpace(strings.ToLower(status))
	switch status {
	case "pending":
		return "pending" // 启动中
	case "running":
		return "running" // 运行中
	case "stopping":
		return "stopping" // 停止中
	case "stopped":
		return "stopped" // 已停止
	case "shutting-down", "terminated":
		return "unsubscribing" // 退订中
	default:
		return "other"
	}
}
