package jobSyncHostEcs

import (
	"context"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/aws/smithy-go/ptr"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
)

var tencent_product_code_ecs = []string{"p_cvm"}

func getTencentPrivateIp(ecs *cvm.Instance) string {
	for _, ip := range ecs.PrivateIpAddresses {
		return pointer.String(ip)
	}
	return ""
}

func getTencentPublicIp(ecs *cvm.Instance) string {
	for _, ip := range ecs.PublicIpAddresses {
		return pointer.String(ip)
	}
	return ""
}

func getTencentNetworkType(ecs *cvm.Instance) string {
	if ecs.VirtualPrivateCloud != nil {
		return "vpc"
	}
	return "classic"
}

func getTencentVPCId(ecs *cvm.Instance) string {
	if ecs.VirtualPrivateCloud != nil {
		return pointer.String(ecs.VirtualPrivateCloud.VpcId)
	}
	return ""
}

func getTencentVswId(ecs *cvm.Instance) string {
	if ecs.VirtualPrivateCloud != nil {
		return pointer.String(ecs.VirtualPrivateCloud.SubnetId)
	}
	return ""
}

func getTencentOSName(ecs *cvm.Instance) string {
	if strings.HasPrefix(strings.ToLower(pointer.String(ecs.OsName)), strings.ToLower(common.OsTypeWindows)) {
		return common.OsTypeWindows
	}
	return common.OsTypeLinux
}

func tryParseTencentEnvProjects(ecs *cvm.Instance) (env, code, project string) {
	for _, tag := range ecs.Tags {
		if pointer.String(tag.Key) != "腾讯云标签" {
			continue
		}
		for _, tmp := range strings.Split(pointer.String(tag.Value), "_") {
			if bizutils.IsSCode(tmp) {
				code = tmp
			}
			if match := bizutils.TryParseEnvFromResourceGroup(tmp); match != "" {
				env = match
			}
		}
		break
	}

	if code != "" {
		if projectInfo, _ := api.HdsClient().QueryAlmProject(code); projectInfo != nil {
			project = projectInfo.Id
		}
	}
	if project == "" {
		project = "default"
	}
	env = bizutils.TryParseEnv(pointer.String(ecs.InstanceName))
	return
}

func (e *EcsSync) getTencentECSInfo(defClient *tencentcloud.EcsClient, ctx context.Context) ([]*models.HostInfo, error) {
	regions, err := defClient.DescribeRegions(ctx)
	if err != nil {
		return nil, bizutils.WarpClientError(defClient, err)
	}

	b := tools.NewBatch[*cvm.RegionInfo, []*models.HostInfo](ctx)
	b.Run(regions, func(ctx context.Context, region *cvm.RegionInfo) ([]*models.HostInfo, error) {
		ecsClient := hybrid.AccountManager().TencentEcsClient(ctx, defClient.Name(), defClient.Tag(), *region.Region)
		ecsList, descInsErr := ecsClient.DescribeInstancesOfAll(ctx)
		if descInsErr != nil {
			return nil, bizutils.WarpClientError(ecsClient, err)
		}
		hostInfoList := make([]*models.HostInfo, len(ecsList))
		for i, ecs := range ecsList {
			hostInfoList[i] = e.transTencentEcs2HostInfo(ctx, defClient, ecs)
		}
		return hostInfoList, nil
	})

	return tools.MergeData(b.Outs()...), b.Error()
}

func (e *EcsSync) transTencentEcs2HostInfo(ctx context.Context, defClient *tencentcloud.EcsClient, ecs *cvm.Instance) *models.HostInfo {
	env, scode, project := tryParseTencentEnvProjects(ecs)
	projectInfo, _ := api.HdsClient().QueryAlmProject(scode)
	if projectInfo != nil {
		project = projectInfo.Id
	}
	creationTime, _ := time.Parse(bizutils.TencentEcsTimeFormat, pointer.String(ecs.CreatedTime))
	expiredTime, _ := time.Parse(bizutils.TencentEcsTimeFormat, pointer.String(ecs.ExpiredTime))

	monitoringMode := bizutils.MonitoringModeIsNone
	if len(ecs.Tags) > 0 {
		for _, tag := range ecs.Tags {
			if pointer.String(tag.Key) == "monitor" && strings.ToLower(strings.TrimSpace(pointer.String(tag.Value))) == "false" {
				monitoringMode = bizutils.MonitoringModeIsNotNecessary
			}
		}
	}
	cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(defClient.Vendor()), defClient.Identifier(), pointer.String(ecs.InstanceId), tencent_product_code_ecs)
	return &models.HostInfo{
		Model:          e.Model(ctx),
		Vendor:         defClient.Vendor(),
		AccountName:    defClient.Name(),
		InstanceId:     pointer.String(ecs.InstanceId),
		InstanceName:   pointer.String(ecs.InstanceName),
		CreationTime:   timeutil.ZeroTime(creationTime),
		ExpiredTime:    timeutil.ZeroTime(expiredTime),
		PrivateIp:      getTencentPrivateIp(ecs),
		PublicIp:       getTencentPublicIp(ecs),
		NetworkType:    getTencentNetworkType(ecs),
		VpcId:          getTencentVPCId(ecs),
		SubnetId:       getTencentVswId(ecs),
		Scode:          bizutils.SwapSCode(scode, cmdb.Scode),
		Project:        project,
		Env:            bizutils.UnifyEnv(env),
		ResourceGroup:  scode,
		OsType:         bizutils.UnifyOsType(getTencentOSName(ecs)),
		OsName:         pointer.String(ecs.OsName),
		OsArch:         common.OsArchX8664,
		Numa:           ptr.Bool(false),
		ImageId:        pointer.String(ecs.ImageId),
		Cpu:            uint64(pointer.Int64(ecs.CPU)),
		Memory:         uint64(pointer.Int64(ecs.CPU)) * 1024,
		HostStatus:     bizutils.UnifyHostStatus(pointer.String(ecs.InstanceState)),
		UniHostStatus:  getTencentEcsUniHostStatus(pointer.String(ecs.InstanceState)),
		HostType:       hbc.ECS.String(),
		Region:         defClient.Region(),
		Zone:           pointer.String(ecs.Placement.Zone),
		ClassCode:      pointer.String(ecs.InstanceType),
		ChargeType:     pointer.String(ecs.InstanceChargeType),
		MonitoringMode: monitoringMode,
		AggregatedId:   cmdb.AggregatedID,
	}
}

func getTencentEcsUniHostStatus(status string) string {
	status = strings.TrimSpace(strings.ToUpper(status))
	switch status {
	case "PENDING", "LAUNCH_FAILED", "STARTING", "REBOOTING":
		return "pending" // 启动中
	case "RUNNING":
		return "running" // 运行中
	case "STOPPING":
		return "stopping" // 停止中
	case "STOPPED":
		return "stopped" // 已停止
	case "SHUTDOWN", "TERMINATING":
		return "unsubscribing" // 退订中
	default:
		return "other"
	}
}
