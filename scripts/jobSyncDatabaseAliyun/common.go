package jobSyncDatabaseAliyun

import (
	"git.haier.net/devops/hcms-task-center/biz/notice"
)

const rds_env = "env"
const rds_env_ch = "环境"

func returnPolarDBXClassCode(code string) (cpu, memory int) {
	switch code {
	case "polarx.x4.medium.2e":
		return 2, 8
	case "polarx.x4.large.2e":
		return 4, 16
	case "polarx.x4.xlarge.2e":
		return 8, 32
	case "polarx.x4.2xlarge.2e":
		return 16, 64
	case "mysql.n2.medium.25":
		return 2, 4
	case "mysql.n4.medium.25":
		return 2, 8
	case "mysql.n2.large.25":
		return 4, 8
	case "mysql.n4.large.25":
		return 4, 16
	case "mysql.n2.xlarge.25":
		return 8, 16
	case "mysql.n4.xlarge.25":
		return 8, 32
	case "polarx.x8.large.2e":
		return 4, 32
	case "polarx.x8.xlarge.2e":
		return 8, 64
	case "polarx.x8.2xlarge.2e":
		return 16, 128
	case "polarx.x8.4xlarge.2e":
		return 32, 128
	case "mysql.x8.medium.25":
		return 2, 16
	case "mysql.x4.large.25":
		return 4, 16
	case "mysql.x8.large.25":
		return 4, 32
	case "mysql.x4.xlarge.25":
		return 8, 32
	case "mysql.x8.xlarge.25":
		return 8, 64
	case "mysql.x4.2xlarge.25":
		return 16, 64
	case "polarx.st.8xlarge.25":
		return 60, 470
	case "polarx.st.12xlarge.25":
		return 90, 720
	default:
		notice.SendWarnMessage("未知的规格代码: PolarDBX", "规格代码："+code)
		return 0, 0
	}
}

func returnPolarDBClassCode(code string) (cpu, memory int) {
	switch code {
	case "polar.mysql.g4.medium.c":
		return 2, 8
	case "polar.mysql.g8.medium.c":
		return 2, 16
	case "polar.mysql.g2.large.c":
		return 4, 8
	case "polar.mysql.g4.large.c":
		return 4, 16
	case "polar.mysql.g8.large.c":
		return 4, 32
	case "polar.mysql.g2.xlarge.c":
		return 8, 16
	case "polar.mysql.g4.xlarge.c":
		return 8, 32
	case "polar.mysql.g8.xlarge.c":
		return 8, 64
	case "polar.mysql.g2.2xlarge.c":
		return 16, 32
	case "polar.mysql.g4.2xlarge.c":
		return 16, 64
	case "polar.mysql.g8.2xlarge.c":
		return 16, 128
	case "polar.mysql.x4.medium.c":
		return 2, 8
	case "polar.mysql.x8.medium.c":
		return 2, 16
	case "polar.mysql.x2.large.c":
		return 4, 8
	case "polar.mysql.x4.large.c":
		return 4, 16
	case "polar.mysql.x8.large.c":
		return 4, 32
	case "polar.mysql.x2.xlarge.c":
		return 8, 16
	case "polar.mysql.x4.xlarge.c":
		return 8, 32
	case "polar.mysql.x8.xlarge.c":
		return 8, 64
	case "polar.mysql.x2.2xlarge.c":
		return 16, 32
	case "polar.mysql.x4.2xlarge.c":
		return 16, 64
	case "polar.mysql.x8.2xlarge.c":
		return 16, 128
	case "polar.mysql.x2.4xlarge.c":
		return 32, 64
	case "polar.mysql.x4.4xlarge.c":
		return 32, 128
	case "polar.mysql.x8.4xlarge.c":
		return 32, 256
	case "polar.mysql.x4.8xlarge.c":
		return 64, 256
	case "polar.mysql.x8.8xlarge.c":
		return 64, 512
	case "polar.mysql.mmx4.xlarge":
		return 8, 32
	case "polar.mysql.mmx8.xlarge":
		return 8, 64
	case "polar.mysql.mmx4.2xlarge":
		return 16, 64
	case "polar.mysql.mmx8.2xlarge":
		return 16, 128
	case "polar.mysql.mmx4.4xlarge":
		return 32, 128
	case "polar.mysql.mmx8.4xlarge":
		return 32, 256
	case "polar.mysql.mmx8.8xlarge":
		return 64, 512
	case "polar.mysql.mmx8.12xlarge":
		return 88, 710
	case "polar.mysql.mmg2.xlarge":
		return 8, 16
	case "polar.mysql.mmg4.xlarge":
		return 8, 32
	case "polar.mysql.mmg4.2xlarge":
		return 16, 64
	case "polar.mysql.mmg6.2xlarge":
		return 16, 96
	case "polar.mysql.mmg8.2xlarge":
		return 16, 128
	case "polar.mysql.mmg4.4xlarge":
		return 32, 128
	case "polar.mysql.x4.medium":
		return 2, 8
	case "polar.mysql.x8.medium":
		return 2, 16
	case "polar.mysql.x4.large":
		return 4, 16
	case "polar.mysql.x8.large":
		return 4, 32
	case "polar.mysql.x4.xlarge":
		return 8, 32
	case "polar.mysql.x8.xlarge":
		return 8, 64
	case "polar.mysql.x4.2xlarge":
		return 16, 64
	case "polar.mysql.x8.2xlarge":
		return 16, 128
	case "polar.mysql.x4.4xlarge":
		return 32, 128
	case "polar.mysql.x8.4xlarge":
		return 32, 256
	case "polar.mysql.x8.8xlarge":
		return 64, 512
	case "polar.mysql.x8.12xlarge":
		return 88, 710
	case "polar.mysql.g2.medium":
		return 2, 4
	case "polar.mysql.g4.medium":
		return 2, 8
	case "polar.mysql.g2.large":
		return 4, 8
	case "polar.mysql.g4.large":
		return 4, 16
	case "polar.mysql.g2.xlarge":
		return 8, 16
	case "polar.mysql.g4.xlarge":
		return 8, 32
	case "polar.mysql.g4.2xlarge":
		return 16, 64
	case "polar.mysql.g6.2xlarge":
		return 16, 96
	case "polar.mysql.g8.2xlarge":
		return 16, 128
	case "polar.mysql.g4.4xlarge":
		return 32, 128
	case "polar.mysql.s2.large":
		return 4, 8
	case "polar.mysql.ax4.large":
		return 4, 16
	case "polar.mysql.ax4.xlarge":
		return 8, 32
	case "polar.mysql.ax8.xlarge":
		return 8, 64
	case "polar.mysql.ax8.2xlarge":
		return 16, 128
	case "polar.mysql.ax8.4xlarge":
		return 32, 256
	case "polar.mysql.ax8.8xlarge":
		return 64, 512
	case "polar.mysql.ax8.12xlarge":
		return 88, 710
	case "polar.mysql.ag4.large":
		return 4, 16
	case "polar.mysql.ag2.xlarge":
		return 8, 16
	case "polar.mysql.ag4.xlarge":
		return 8, 32
	case "polar.mysql.ag4.2xlarge":
		return 16, 64
	case "polar.mysql.ag4.4xlarge":
		return 32, 128
	case "polar.o.x4.medium":
		return 2, 8
	case "polar.o.x4.large":
		return 4, 16
	case "polar.o.x4.xlarge":
		return 8, 32
	case "polar.o.x8.xlarge":
		return 8, 64
	case "polar.o.x8.2xlarge":
		return 16, 128
	case "polar.o.x8.4xlarge":
		return 32, 256
	case "polar.o.x8.8xlarge":
		return 64, 512
	case "polar.o.x8.12xlarge":
		return 88, 710
	default:
		notice.SendWarnMessage("未知的规格代码: PolarDB", "规格代码："+code)
		return 0, 0
	}
}
