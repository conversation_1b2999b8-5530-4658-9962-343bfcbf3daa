package jobSyncDatabaseAliyun

import (
	"context"
	"sync"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewSyncPolarDBX() *SyncPolarDBX {
	return &SyncPolarDBX{
		mtx: new(sync.Mutex),
		rg:  orderedmap.New[string, *resourcemanager.ResourceGroup](),
		ep:  orderedmap.New[string, *eps.EpDetail](),
		JobBase: base.NewJobBase("SYNC_POLARDBX",
			"同步PolarDB-X资源数据",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource()),
	}
}

type SyncPolarDBX struct {
	rg  *orderedmap.OrderedMap[string, *resourcemanager.ResourceGroup]
	ep  *orderedmap.OrderedMap[string, *eps.EpDetail]
	mtx *sync.Mutex
	*base.JobBase
}

func (e *SyncPolarDBX) Name() string {
	return base.TaskCloudSyncPolarDBX
}

func (e *SyncPolarDBX) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(e.Model(ctx), hbc.GoogleCloud, hbc.Private, hbc.HuaweiCloud, hbc.TencentCloud, hbc.AWS, hbc.Azure)
	if err != nil {
		return nil, err
	}

	defaultClients, err := base.GetDefaultIClients(ctx, vendors,
		hbc.PolarDBX, []string{bizutils.PurposeAdmin},
	)
	if err != nil {
		return nil, err
	}

	b := tools.NewBatch[client.IClient, []*models.DatabaseInfo](ctx, batch.WithBatchSize(2))
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.DatabaseInfo, error) {
		switch defaultClient := input.(type) {
		case *aliyun.PolarDBXClient:
			return e.syncAliyunPolarDB(ctx, defaultClient)
		}
		return nil, nil
	})

	if err := b.Error(); err != nil {
		e.Errorf(ctx, "sync polardbx failed, cause: %s", err)
		return nil, err
	}

	// 将信息存入数据库
	return nil, bizutils.CreateOrUpdateRds(ctx, tools.MergeData(b.Outs()...)...)
}
