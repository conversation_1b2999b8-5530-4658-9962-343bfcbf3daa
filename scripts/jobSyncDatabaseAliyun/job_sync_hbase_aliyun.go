package jobSyncDatabaseAliyun

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	hbase "github.com/alibabacloud-go/hbase-20190101/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"github.com/aws/smithy-go/ptr"
)

var aliyun_product_code_hbase = []string{"hbase"}

func (j *SyncHbase) sync(ctx context.Context, cli *aliyun.HbaseClient) ([]*models.DatabaseInfo, error) {
	//regions := []string{"cn-qingdao"}
	describeRegions, err := cli.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionMap := orderedmap.New[string, string]()
	for _, region := range describeRegions {
		regionMap.Store(pointer.Value(region.RegionId), pointer.Value(region.RegionId))
	}
	regions := regionMap.Slice()

	process := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		hbClient := hybrid.AccountManager().AliyunHbaseClient(ctx, cli.Name(), cli.Tag(), regionId)
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		instances, err := hbClient.DescribeDBClustersOfAll(ctx)
		if err != nil {
			return nil, err
		}

		getMongoAttrProcess := tools.NewBatch[*hbase.DescribeInstancesResponseBodyInstancesInstance, *models.DatabaseInfo](ctx)
		getMongoAttrProcess.Run(instances, func(ctx context.Context, ins *hbase.DescribeInstancesResponseBodyInstancesInstance) (*models.DatabaseInfo, error) {
			attr, err := hbClient.DescribeDBInstanceAttribute(ctx, *ins.ClusterId)
			if err != nil {
				var sdkErr *tea.SDKError
				if !errors.As(err, &sdkErr) {
					return nil, err
				}
				if *sdkErr.Code == "IllegalOperation.Resource" && *sdkErr.StatusCode == 400 {
					return nil, nil
				}

				return nil, err
			}

			conn, err := hbClient.DescribeClusterConnection(ctx, regionId, *attr.InstanceId)
			if err != nil {
				return nil, err
			}
			connAddr := ""
			if conn.ZkConnAddrs != nil && len(conn.ZkConnAddrs.ZkConnAddr) > 0 {
				addrs := make([]string, 0)
				for _, item := range conn.ZkConnAddrs.ZkConnAddr {
					if *item.ConnAddr != "" {
						s := fmt.Sprintf("%s:%s", *item.ConnAddr, *item.ConnAddrPort)
						addrs = append(addrs, s)
					}
				}
				connAddr = strings.Join(addrs, ",")
			}
			// handle endpoint
			err = handleHbaseDatabaseEndpoint(*attr.InstanceId, conn)
			if err != nil {
				return nil, err
			}

			diskSize := *attr.MasterDiskSize + *attr.CoreDiskSize**attr.CoreNodeCount
			_, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(ins.ResourceGroupId))
			createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.CreatedTime))
			expireAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.ExpireTime))

			// get env from tag
			var env string
			if len(attr.Tags.Tag) > 0 {
			outerLoop:
				for _, tag := range attr.Tags.Tag {
					switch strings.ToLower(strings.TrimSpace(pointer.Value(tag.Key))) {
					case rds_env, rds_env_ch:
						env = pointer.Value(tag.Value)
						break outerLoop
					}
				}
			}
			contextInfo := map[string]any{
				"instance":   attr,
				"connection": conn,
			}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(attr.InstanceId), aliyun_product_code_hbase)
			return &models.DatabaseInfo{
				Vendor:            cli.Vendor(),
				AccountName:       cli.Name(),
				InstanceId:        pointer.String(attr.InstanceId),
				InstanceName:      pointer.String(attr.InstanceName),
				InstanceType:      "",
				InstanceRole:      "Master",
				Category:          "Cluster",
				IsDeleted:         ptr.Bool(false),
				PrimaryInstanceId: "",
				HostInsId:         "",
				Status:            pointer.String(attr.Status),
				ClassCode:         pointer.String(attr.CoreInstanceType),
				ChargeType:        pointer.String(attr.PayType),
				CreationTime:      timeutil.ZeroTime(createAt),
				ExpiredTime:       timeutil.ZeroTime(expireAt),
				Host:              connAddr,
				Port:              0,
				EngineType:        bizutils.DBTypeHbase,
				EngineVersion:     pointer.String(attr.MajorVersion),
				Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
				Project:           project,
				Env:               env,
				ResourceGroup:     pointer.Value(ins.ResourceGroupId),
				Cpu:               0,
				Memory:            0,
				DiskSize:          float64(diskSize * 1024),
				DiskType:          pointer.String(attr.CoreDiskType),
				VpcId:             pointer.Value(ins.VpcId),
				SubnetId:          "",
				DgDomain:          "",
				DgId:              "",
				Region:            regionId,
				Zone:              pointer.String(attr.ZoneId),
				Description:       "",
				Content:           utils.JsonString(contextInfo),
				AggregatedId:      cmdb.AggregatedID,
			}, nil
		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), process.Error()
}

func (j *SyncHbase) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](j.mtx, j.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}

func (j *SyncHbase) getCpuMemory(class string) (uint64, uint64) {
	cpuString := strings.Split(class, "C")[0]
	memString := strings.Split(strings.Split(class, "C")[1], "G")[0]
	cpu, _ := strconv.Atoi(cpuString)
	mem, _ := strconv.Atoi(memString)
	return uint64(cpu), uint64(mem * 1024)
}

func handleHbaseDatabaseEndpoint(instanceId string, connect *hbase.DescribeClusterConnectionResponseBody) error {
	data := make([]*models.DatabaseEndpoint, 0)
	if connect == nil {
		return nil
	}

	if connect.SlbConnAddrs != nil && len(connect.SlbConnAddrs.SlbConnAddr) > 0 {
		for _, addr := range connect.SlbConnAddrs.SlbConnAddr {
			port, _ := strconv.Atoi(*addr.ConnAddrInfo.ConnAddrPort)
			netType := bizutils.NetTypePublic
			if pointer.String(addr.ConnAddrInfo.NetType) == "2" {
				netType = bizutils.NetTypePrivate
			}
			data = append(data, &models.DatabaseEndpoint{
				InstanceId:   instanceId,
				EndpointType: "Slb-" + pointer.String(addr.SlbType),
				Host:         pointer.Value(addr.ConnAddrInfo.ConnAddr),
				Port:         port,
				NetType:      netType,
				CreateTime:   timeutil.ZeroTime(time.Now()),
				UpdateTime:   timeutil.ZeroTime(time.Now()),
			})
		}
	}

	if connect.ThriftConn != nil {
		addr := connect.ThriftConn
		port, _ := strconv.Atoi(*addr.ConnAddrPort)
		netType := bizutils.NetTypePublic
		if pointer.String(addr.NetType) == "2" {
			netType = bizutils.NetTypePrivate
		}
		data = append(data, &models.DatabaseEndpoint{
			InstanceId:   instanceId,
			EndpointType: "Thrift",
			Host:         pointer.Value(addr.ConnAddr),
			Port:         port,
			NetType:      netType,
			CreateTime:   timeutil.ZeroTime(time.Now()),
			UpdateTime:   timeutil.ZeroTime(time.Now()),
		})
	}

	if connect.UiProxyConnAddrInfo != nil {
		addr := connect.UiProxyConnAddrInfo
		port, _ := strconv.Atoi(*addr.ConnAddrPort)
		netType := bizutils.NetTypePublic
		if pointer.String(addr.NetType) != "PUBLIC" {
			netType = bizutils.NetTypePrivate
		}
		data = append(data, &models.DatabaseEndpoint{
			InstanceId:   instanceId,
			EndpointType: "WebUI",
			Host:         pointer.Value(addr.ConnAddr),
			Port:         port,
			NetType:      netType,
			CreateTime:   timeutil.ZeroTime(time.Now()),
			UpdateTime:   timeutil.ZeroTime(time.Now()),
		})
	}

	if connect.ZkConnAddrs != nil && len(connect.ZkConnAddrs.ZkConnAddr) > 0 {
		for _, addr := range connect.ZkConnAddrs.ZkConnAddr {
			port, _ := strconv.Atoi(*addr.ConnAddrPort)
			netType := bizutils.NetTypePublic
			if pointer.String(addr.NetType) == "2" {
				netType = bizutils.NetTypePrivate
			}
			data = append(data, &models.DatabaseEndpoint{
				InstanceId:   instanceId,
				EndpointType: "Zookeeper",
				Host:         pointer.Value(addr.ConnAddr),
				Port:         port,
				NetType:      netType,
				CreateTime:   timeutil.ZeroTime(time.Now()),
				UpdateTime:   timeutil.ZeroTime(time.Now()),
			})
		}
	}

	if err := bizutils.CreateOrUpdateEndpoint(context.Background(), data...); err != nil {
		return err
	}
	return nil
}
