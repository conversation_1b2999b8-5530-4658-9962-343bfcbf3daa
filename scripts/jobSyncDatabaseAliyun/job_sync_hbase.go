package jobSyncDatabaseAliyun

import (
	"context"
	"sync"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"
)

func NewSyncHbase() *SyncHbase {
	schedule := "0 0 1 * * *"
	return &SyncHbase{
		mtx: new(sync.Mutex),
		rg:  orderedmap.New[string, *resourcemanager.ResourceGroup](),
		ep:  orderedmap.New[string, *eps.EpDetail](),
		JobBase: base.NewJobBase(
			"cmdb-sync-hbase",
			"同步Hbase资源",
			schedule,
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncHbase struct {
	rg  *orderedmap.OrderedMap[string, *resourcemanager.ResourceGroup]
	ep  *orderedmap.OrderedMap[string, *eps.EpDetail]
	mtx *sync.Mutex
	*base.JobBase
}

func (j *SyncHbase) Name() string {
	return base.TaskCloudSyncHbase
}

func (j *SyncHbase) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(j.Model(ctx), hbc.GoogleCloud, hbc.Private, hbc.HuaweiCloud, hbc.TencentCloud, hbc.AWS, hbc.Azure)
	if err != nil {
		return nil, err
	}

	defaultClients, err := base.GetDefaultIClients(ctx, vendors, hbc.Hbase, []string{bizutils.PurposeAdmin})
	if err != nil {
		return nil, err
	}

	b := tools.NewBatch[client.IClient, []*models.DatabaseInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.DatabaseInfo, error) {
		switch defaultClient := input.(type) {
		case *aliyun.HbaseClient:
			return j.sync(ctx, defaultClient)
		}
		return nil, nil
	})

	if err := b.Error(); err != nil {
		j.Errorf(ctx, "sync hbase failed, cause: %s", err)
		return nil, err
	}

	// 将信息存入数据库
	return nil, bizutils.CreateOrUpdateRds(ctx, tools.MergeData(b.Outs()...)...)
}
