package jobSyncDatabaseAliyun

import (
	"context"
	"errors"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	adb "github.com/alibabacloud-go/adb-20190315/v5/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"github.com/aws/smithy-go/ptr"
)

var aliyun_product_code_analytic = []string{"ads"}

func (j *SyncAdbMySQL) sync(ctx context.Context, cli *aliyun.AnalyticDBMySQLClient) ([]*models.DatabaseInfo, error) {
	//regions := []string{"cn-beijing"}
	describeRegions, err := cli.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionMap := orderedmap.New[string, string]()
	for _, region := range describeRegions {
		regionMap.Store(pointer.Value(region.RegionId), pointer.Value(region.RegionId))
	}
	regions := regionMap.Slice()

	process := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		adbClient := hybrid.AccountManager().AliyunADBMySQLClient(ctx, cli.Name(), cli.Tag(), regionId)
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		instances, err := adbClient.DescribeDBClustersOfAll(ctx)
		if err != nil {
			return nil, err
		}

		getMongoAttrProcess := tools.NewBatch[*adb.DescribeDBClustersResponseBodyItemsDBCluster, *models.DatabaseInfo](ctx)
		getMongoAttrProcess.Run(instances, func(ctx context.Context, ins *adb.DescribeDBClustersResponseBodyItemsDBCluster) (*models.DatabaseInfo, error) {
			attrs, err := adbClient.DescribeDBClusterAttribute(ctx, *ins.DBClusterId)
			if err != nil {
				var sdkErr *tea.SDKError
				if !errors.As(err, &sdkErr) {
					return nil, err
				}
				if *sdkErr.Code == "IllegalOperation.Resource" && *sdkErr.StatusCode == 400 {
					return nil, nil
				}

				return nil, err
			}

			if attrs.Items == nil {
				return nil, nil
			}
			if len(attrs.Items.DBCluster) == 0 {
				return nil, nil
			}
			attr := attrs.Items.DBCluster[0]

			diskSize := *attr.DBNodeStorage * *attr.DBNodeCount
			_, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(ins.ResourceGroupId))
			createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.CreationTime))
			expireAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.ExpireTime))

			// get env from tag
			var env string
			if len(attr.Tags.Tag) > 0 {
			outerLoop:
				for _, tag := range attr.Tags.Tag {
					switch strings.ToLower(strings.TrimSpace(pointer.Value(tag.Key))) {
					case rds_env, rds_env_ch:
						env = pointer.Value(tag.Value)
						break outerLoop
					}
				}
			}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(attr.DBClusterId), aliyun_product_code_analytic)
			return &models.DatabaseInfo{
				Vendor:            cli.Vendor(),
				AccountName:       cli.Name(),
				InstanceId:        pointer.String(attr.DBClusterId),
				InstanceName:      pointer.String(attr.DBClusterDescription),
				InstanceType:      "",
				InstanceRole:      "Master",
				Category:          pointer.String(attr.Category),
				IsDeleted:         ptr.Bool(false),
				PrimaryInstanceId: "",
				HostInsId:         "",
				Status:            pointer.String(attr.DBClusterStatus),
				ClassCode:         pointer.String(attr.DBNodeClass),
				ChargeType:        pointer.String(attr.PayType),
				CreationTime:      timeutil.ZeroTime(createAt),
				ExpiredTime:       timeutil.ZeroTime(expireAt),
				Host:              pointer.String(attr.ConnectionString),
				Port:              int(*attr.Port),
				EngineType:        bizutils.DBTypeAdb,
				EngineVersion:     pointer.String(attr.EngineVersion),
				Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
				Project:           project,
				Env:               env,
				ResourceGroup:     pointer.Value(ins.ResourceGroupId),
				Cpu:               0,
				Memory:            0,
				DiskSize:          float64(diskSize * 1024),
				DiskType:          pointer.String(attr.DiskType),
				VpcId:             pointer.Value(attr.VPCId),
				SubnetId:          "",
				DgDomain:          "",
				DgId:              "",
				Region:            regionId,
				Zone:              pointer.String(attr.ZoneId),
				Description:       pointer.String(attr.DBClusterDescription),
				Content:           utils.JsonString(attr),
				AggregatedId:      cmdb.AggregatedID,
			}, nil
		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), process.Error()
}

func (j *SyncAdbMySQL) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](j.mtx, j.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}
