package jobSyncDatabaseAliyun

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	polar "github.com/alibabacloud-go/polardb-20170801/v6/client"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
)

const clusterEndpointType = "Cluster"

var aliyun_product_code_polardb = []string{"polardb"}

func (e *SyncPolarDB) syncAliyunPolarDB(ctx context.Context, cli *aliyun.PolarDBClient) ([]*models.DatabaseInfo, error) {
	describeRegions, err := cli.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionMap := orderedmap.New[string, string]()
	for _, region := range describeRegions {
		regionMap.Store(pointer.Value(region.RegionId), pointer.Value(region.RegionId))
	}
	regions := regionMap.Slice()
	process := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		polarCli := hybrid.AccountManager().AliyunPolarDBClient(ctx, cli.Name(), cli.Tag(), regionId)
		polarDBList, err := polarCli.DescribeDBClustersOfAll(ctx)
		if err != nil {
			return nil, err
		}
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		getPolarAttrProcess := tools.NewBatch[*polar.DescribeDBClustersResponseBodyItemsDBCluster, *models.DatabaseInfo](ctx)
		getPolarAttrProcess.Run(polarDBList, func(ctx context.Context, polarDBCluster *polar.DescribeDBClustersResponseBodyItemsDBCluster) (*models.DatabaseInfo, error) {
			attr, err := polarCli.DescribeDBClusterAttribute(ctx, pointer.Value(polarDBCluster.DBClusterId))
			if err != nil {
				return nil, err
			}
			endpoints, err := polarCli.DescribeDBClusterEndpoints(ctx, pointer.Value(polarDBCluster.DBClusterId))
			if err != nil {
				return nil, err
			}

			var conn string
			var port int
			for _, endpoint := range endpoints {
				// 获取cluster地址，与其他服务获取的地址保持一致（如：DMS接入地址）
				// 如果不是集群类型的，看看返回的实例数组里有没有类型字段为“primary”之类的数组元素，取这个数组元素的链接地址、端口号。理论上来说生产环境都会开集群
				if pointer.Value(endpoint.EndpointType) != clusterEndpointType && pointer.Value(endpoint.EndpointType) != "Primary" {
					continue
				}
				// 优先获取cluster地址
				if pointer.Value(endpoint.EndpointType) == clusterEndpointType {
					conn = pointer.Value(endpoint.AddressItems[0].ConnectionString)
					port, _ = strconv.Atoi(pointer.Value(endpoint.AddressItems[0].Port))
					break
				}
				// 获取不到cluster时获取primary地址
				conn = pointer.Value(endpoint.AddressItems[0].ConnectionString)
				port, _ = strconv.Atoi(pointer.Value(endpoint.AddressItems[0].Port))
			}

			cpu, memory := returnPolarDBClassCode(pointer.Value(polarDBCluster.DBNodeClass))
			_, scode, project := bizutils.ParseAliyunEnvProject(resCli, e, pointer.Value(attr.ResourceGroupId))
			createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.CreationTime))
			expireAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.ExpireTime))
			// get env from tag
			var env string
			tags, err := polarCli.ListTagResources(ctx, pointer.Value(attr.DBClusterId))
			if err != nil {
				if !strings.Contains(err.Error(), "InvalidDBClusterId.NotFound") {
					return nil, err
				}
			}
			if len(tags.TagResources.TagResource) > 0 {
			outerLoop:
				for _, tag := range tags.TagResources.TagResource {
					switch strings.ToLower(strings.TrimSpace(pointer.Value(tag.TagKey))) {
					case rds_env, rds_env_ch:
						env = pointer.Value(tag.TagValue)
						break outerLoop
					}
				}
			}

			contextInfo := map[string]any{
				"cluster":   attr,
				"endpoints": endpoints,
			}

			// handle endpoint
			err = handlePolarDatabaseEndpoint(pointer.Value(attr.DBClusterId), endpoints)
			if err != nil {
				return nil, err
			}
			// handle node
			err = handlePolarDatabaseNode(pointer.Value(attr.DBClusterId), attr.DBNodes)
			if err != nil {
				return nil, err
			}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(attr.DBClusterId), aliyun_product_code_polardb)
			return &models.DatabaseInfo{
				Vendor:        cli.Vendor(),
				AccountName:   cli.Name(),
				InstanceId:    pointer.Value(attr.DBClusterId),
				InstanceType:  jobSyncDatabaseRds.RDSTypeNormal,
				InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
				Category:      pointer.Value(attr.Category),
				Status:        pointer.Value(attr.DBClusterStatus),
				ClassCode:     pointer.Value(polarDBCluster.DBNodeClass),
				ChargeType:    pointer.Value(attr.PayType),
				CreationTime:  timeutil.ZeroTime(createAt),
				ExpiredTime:   timeutil.ZeroTime(expireAt),
				Host:          conn,
				Port:          port,
				EngineType:    fmt.Sprintf("%s_%s", pointer.Value(attr.Engine), pointer.Value(attr.DBType)),
				EngineVersion: pointer.Value(attr.DBVersion),
				Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
				Project:       project,
				Env:           env,
				ResourceGroup: pointer.Value(attr.ResourceGroupId),
				Cpu:           cpu,
				Memory:        uint64(memory),
				DiskSize:      float64(pointer.Value(attr.StorageSpace)),
				DiskType:      pointer.Value(attr.StorageType),
				VpcId:         pointer.Value(attr.VPCId),
				SubnetId:      pointer.Value(attr.VSwitchId),
				Region:        pointer.Value(attr.RegionId),
				Zone:          pointer.Value(attr.ZoneIds),
				Description:   pointer.Value(attr.DBClusterDescription),
				Content:       utils.JsonString(contextInfo),
				AggregatedId:  cmdb.AggregatedID,
			}, nil
		})
		return getPolarAttrProcess.Outs(), getPolarAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), process.Error()
}

func (e *SyncPolarDB) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](e.mtx, e.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}

func handlePolarDatabaseNode(instanceId string, nodes []*polar.DescribeDBClusterAttributeResponseBodyDBNodes) error {
	if len(nodes) == 0 {
		return nil
	}
	data := make([]*models.DatabaseNode, 0, len(nodes))
	for _, node := range nodes {
		cpu, _ := strconv.Atoi(*node.CpuCores)
		mem, _ := strconv.Atoi(*node.MemorySize)
		data = append(data, &models.DatabaseNode{
			InstanceId:      instanceId,
			Cpu:             cpu,
			Memory:          mem,
			NodeId:          pointer.Value(node.DBNodeId),
			NodeClass:       pointer.Value(node.DBNodeClass),
			NodeDescription: pointer.Value(node.DBNodeDescription),
			NodeRole:        pointer.Value(node.DBNodeRole),
			NodeStatus:      pointer.Value(node.DBNodeStatus),
			Zone:            pointer.Value(node.ZoneId),
			CreateTime:      timeutil.ZeroTime(time.Now()),
			UpdateTime:      timeutil.ZeroTime(time.Now()),
		})
	}
	if err := bizutils.CreateOrUpdateNode(context.Background(), data...); err != nil {
		return err
	}
	return nil
}

func handlePolarDatabaseEndpoint(instanceId string, endpoints []*polar.DescribeDBClusterEndpointsResponseBodyItems) error {
	if len(endpoints) == 0 {
		return nil
	}
	data := make([]*models.DatabaseEndpoint, 0, len(endpoints))
	for _, endpoint := range endpoints {
		if len(endpoint.AddressItems) == 0 {
			continue
		}
		for _, address := range endpoint.AddressItems {
			if pointer.Value(address.ConnectionString) == "" {
				continue
			}
			port, _ := strconv.Atoi(pointer.Value(address.Port))
			data = append(data, &models.DatabaseEndpoint{
				InstanceId:   instanceId,
				EndpointType: pointer.Value(endpoint.EndpointType),
				Host:         pointer.Value(address.ConnectionString),
				Port:         port,
				Description:  pointer.Value(endpoint.DBEndpointDescription),
				CreateTime:   timeutil.ZeroTime(time.Now()),
				UpdateTime:   timeutil.ZeroTime(time.Now()),
				NodeId:       pointer.Value(endpoint.Nodes),
			})
		}
	}
	if err := bizutils.CreateOrUpdateEndpoint(context.Background(), data...); err != nil {
		return err
	}
	return nil
}
