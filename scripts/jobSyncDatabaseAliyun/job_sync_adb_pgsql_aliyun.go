package jobSyncDatabaseAliyun

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	adb_pgsql "github.com/alibabacloud-go/gpdb-20160503/v4/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"github.com/aws/smithy-go/ptr"
)

var aliyun_product_code_postgresql = []string{"gpdb"}

func (j *SyncAdbPgSQL) sync(ctx context.Context, cli *aliyun.AnalyticDBPostgreSQLClient) ([]*models.DatabaseInfo, error) {
	//regions := []string{"cn-beijing"}
	describeRegions, err := cli.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionMap := orderedmap.New[string, string]()
	for _, region := range describeRegions {
		regionMap.Store(pointer.Value(region.RegionId), pointer.Value(region.RegionId))
	}
	regions := regionMap.Slice()

	process := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		adbClient := hybrid.AccountManager().AliyunADBPgSQLClient(ctx, cli.Name(), cli.Tag(), regionId)
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		instances, err := adbClient.DescribeDBClustersOfAll(ctx)
		if err != nil {
			return nil, err
		}

		getMongoAttrProcess := tools.NewBatch[*adb_pgsql.DescribeDBInstancesResponseBodyItemsDBInstance, *models.DatabaseInfo](ctx)
		getMongoAttrProcess.Run(instances, func(ctx context.Context, ins *adb_pgsql.DescribeDBInstancesResponseBodyItemsDBInstance) (*models.DatabaseInfo, error) {
			attrs, err := adbClient.DescribeDBClusterAttribute(ctx, *ins.DBInstanceId)
			if err != nil {
				var sdkErr *tea.SDKError
				if !errors.As(err, &sdkErr) {
					return nil, err
				}
				if *sdkErr.Code == "IllegalOperation.Resource" && *sdkErr.StatusCode == 400 {
					return nil, nil
				}

				return nil, err
			}

			if attrs.Items == nil {
				return nil, nil
			}
			if len(attrs.Items.DBInstanceAttribute) == 0 {
				return nil, nil
			}

			attr := attrs.Items.DBInstanceAttribute[0]
			port, _ := strconv.Atoi(*attr.Port)
			diskSize := *attr.DBInstanceStorage
			_, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(ins.ResourceGroupId))
			createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.CreationTime))
			expireAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.ExpireTime))

			// get env from tag
			var env string
			if len(attr.Tags.Tag) > 0 {
			outerLoop:
				for _, tag := range attr.Tags.Tag {
					switch strings.ToLower(strings.TrimSpace(pointer.Value(tag.Key))) {
					case rds_env, rds_env_ch:
						env = pointer.Value(tag.Value)
						break outerLoop
					}
				}
			}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(attr.DBInstanceId), aliyun_product_code_postgresql)
			return &models.DatabaseInfo{
				Vendor:            cli.Vendor(),
				AccountName:       cli.Name(),
				InstanceId:        pointer.Value(attr.DBInstanceId),
				InstanceName:      pointer.Value(attr.DBInstanceDescription),
				InstanceType:      "",
				InstanceRole:      "Master",
				Category:          pointer.Value(attr.DBInstanceCategory),
				IsDeleted:         ptr.Bool(false),
				PrimaryInstanceId: "",
				HostInsId:         "",
				Status:            pointer.Value(attr.DBInstanceStatus),
				ClassCode:         pointer.Value(attr.DBInstanceClass),
				ChargeType:        pointer.Value(attr.PayType),
				CreationTime:      timeutil.ZeroTime(createAt),
				ExpiredTime:       timeutil.ZeroTime(expireAt),
				Host:              pointer.Value(attr.ConnectionString),
				Port:              port,
				EngineType:        bizutils.DBTypeAdbPGSQL,
				EngineVersion:     pointer.Value(attr.EngineVersion),
				Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
				Project:           project,
				Env:               env,
				ResourceGroup:     pointer.Value(ins.ResourceGroupId),
				Cpu:               int(*attr.DBInstanceCpuCores),
				Memory:            uint64(*attr.DBInstanceMemory),
				DiskSize:          float64(diskSize * 1024),
				DiskType:          pointer.Value(attr.StorageType),
				VpcId:             pointer.Value(attr.VpcId),
				SubnetId:          "",
				DgDomain:          "",
				DgId:              "",
				Region:            regionId,
				Zone:              pointer.Value(attr.ZoneId),
				Description:       pointer.Value(attr.DBInstanceDescription),
				Content:           utils.JsonString(attr),
				AggregatedId:      cmdb.AggregatedID,
			}, nil
		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), process.Error()
}

func (j *SyncAdbPgSQL) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](j.mtx, j.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}
