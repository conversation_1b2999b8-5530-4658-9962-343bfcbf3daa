package jobSyncDatabaseAliyun

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	polar "github.com/alibabacloud-go/polardbx-20200202/client"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
)

var aliyun_product_code_polardbx = []string{"polardbx"}

func (e *SyncPolarDBX) syncAliyunPolarDB(ctx context.Context, cli *aliyun.PolarDBXClient) ([]*models.DatabaseInfo, error) {
	describeRegions, err := cli.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionMap := orderedmap.New[string, string]()
	for _, region := range describeRegions {
		if strings.Contains(pointer.Value(region.RegionId), "finance") {
			continue
		}
		if strings.Contains(pointer.Value(region.RegionId), "gov") {
			continue
		}
		regionMap.Store(pointer.Value(region.RegionId), pointer.Value(region.RegionId))
	}
	regions := regionMap.Slice()
	process := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		polarCli := hybrid.AccountManager().AliyunPolarDBXClient(ctx, cli.Name(), cli.Tag(), regionId)
		polarDBList, err := polarCli.DescribeDBInstancesOfAll(ctx)
		if err != nil {
			if strings.Contains(err.Error(), "InvalidRegionId") {
				return nil, nil
			}
			return nil, err
		}
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		getPolarAttrProcess := tools.NewBatch[*polar.DescribeDBInstancesResponseBodyDBInstances, *models.DatabaseInfo](ctx)
		getPolarAttrProcess.Run(polarDBList, func(ctx context.Context, polarDB *polar.DescribeDBInstancesResponseBodyDBInstances) (*models.DatabaseInfo, error) {
			attr, err := polarCli.DescribeDBInstanceAttribute(ctx, pointer.Value(polarDB.Id))
			if err != nil {
				return nil, err
			}

			port, _ := strconv.Atoi(pointer.Value(attr.Port))
			cpu, memory := returnPolarDBXClassCode(pointer.Value(polarDB.NodeClass))
			env, scode, project := bizutils.ParseAliyunEnvProject(resCli, e, pointer.Value(polarDB.ResourceGroupId))
			createAt, _ := time.Parse(bizutils.AliyunPolarDBXTimeFormat, pointer.Value(polarDB.CreateTime))
			expireAt, _ := time.Parse(bizutils.AliyunPolarDBXTimeFormat, pointer.Value(polarDB.ExpireTime))
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(polarDB.Id), aliyun_product_code_polardbx)
			return &models.DatabaseInfo{
				Vendor:        cli.Vendor(),
				AccountName:   cli.Name(),
				InstanceId:    pointer.Value(polarDB.Id),
				InstanceName:  pointer.Value(polarDB.DBInstanceName),
				InstanceType:  pointer.Value(polarDB.Type),
				InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
				Category:      pointer.Value(polarDB.CommodityCode),
				Status:        pointer.Value(attr.Status),
				ClassCode:     pointer.Value(polarDB.NodeClass),
				ChargeType:    pointer.Value(attr.PayType),
				CreationTime:  timeutil.ZeroTime(createAt),
				ExpiredTime:   timeutil.ZeroTime(expireAt),
				Host:          pointer.Value(attr.ConnectionString),
				Port:          port,
				EngineType:    fmt.Sprintf("%s_%s", pointer.Value(attr.Engine), pointer.Value(attr.DBType)),
				EngineVersion: pointer.Value(attr.DBVersion),
				Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
				Project:       project,
				Env:           env,
				ResourceGroup: pointer.Value(attr.ResourceGroupId),
				Cpu:           cpu,
				Memory:        uint64(memory),
				DiskType:      "local_disk",
				VpcId:         pointer.Value(attr.VPCId),
				SubnetId:      pointer.Value(attr.VSwitchId),
				Region:        pointer.Value(attr.RegionId),
				Zone:          pointer.Value(attr.ZoneId),
				Description:   pointer.Value(attr.Description),
				//DiskSize: uint64(pointer.Value(attr.StorageSpace)),
				Content:      utils.JsonString(attr),
				AggregatedId: cmdb.AggregatedID,
			}, nil
		})
		return getPolarAttrProcess.Outs(), getPolarAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), process.Error()
}

func (e *SyncPolarDBX) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](e.mtx, e.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}
