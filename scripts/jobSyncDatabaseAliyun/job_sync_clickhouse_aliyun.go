package jobSyncDatabaseAliyun

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	clickhouse "github.com/alibabacloud-go/clickhouse-20191111/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"github.com/aws/smithy-go/ptr"
)

var aliyun_product_code_clickhouse = []string{"clickhouse"}

func (j *SyncClickhouse) sync(ctx context.Context, cli *aliyun.ClickHouseClient) ([]*models.DatabaseInfo, error) {
	//regions := []string{"cn-qingdao"}
	describeRegions, err := cli.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionMap := orderedmap.New[string, string]()
	for _, region := range describeRegions {
		regionMap.Store(pointer.Value(region.RegionId), pointer.Value(region.RegionId))
	}
	regions := regionMap.Slice()

	process := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		chClient := hybrid.AccountManager().AliyunClickHouseClient(ctx, cli.Name(), cli.Tag(), regionId)
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		instances, err := chClient.DescribeDBClustersOfAll(ctx)
		if err != nil {
			return nil, err
		}

		getMongoAttrProcess := tools.NewBatch[*clickhouse.DescribeDBClustersResponseBodyDBClustersDBCluster, *models.DatabaseInfo](ctx)
		getMongoAttrProcess.Run(instances, func(ctx context.Context, ins *clickhouse.DescribeDBClustersResponseBodyDBClustersDBCluster) (*models.DatabaseInfo, error) {
			attr, err := chClient.DescribeDBClusterAttribute(ctx, *ins.DBClusterId)
			if err != nil {
				var sdkErr *tea.SDKError
				if !errors.As(err, &sdkErr) {
					return nil, err
				}
				if *sdkErr.Code == "IllegalOperation.Resource" && *sdkErr.StatusCode == 400 {
					return nil, nil
				}

				return nil, err
			}

			diskSize := *attr.DBNodeStorage * *attr.DBNodeCount
			_, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(ins.ResourceGroupId))
			createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.CreateTime))
			expireAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.ExpireTime))

			// get env from tag
			var env string
			if len(attr.Tags.Tag) > 0 {
			outerLoop:
				for _, tag := range attr.Tags.Tag {
					switch strings.ToLower(strings.TrimSpace(pointer.Value(tag.Key))) {
					case rds_env, rds_env_ch:
						env = pointer.Value(tag.Value)
						break outerLoop
					}
				}
			}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(attr.DBClusterId), aliyun_product_code_clickhouse)
			return &models.DatabaseInfo{
				Vendor:            cli.Vendor(),
				AccountName:       cli.Name(),
				InstanceId:        pointer.String(attr.DBClusterId),
				InstanceName:      pointer.String(attr.DBClusterDescription),
				InstanceType:      "",
				InstanceRole:      "Master",
				Category:          pointer.String(attr.Category),
				IsDeleted:         ptr.Bool(false),
				PrimaryInstanceId: "",
				HostInsId:         "",
				Status:            pointer.String(attr.DBClusterStatus),
				ClassCode:         pointer.String(attr.DBNodeClass),
				ChargeType:        pointer.String(attr.PayType),
				CreationTime:      timeutil.ZeroTime(createAt),
				ExpiredTime:       timeutil.ZeroTime(expireAt),
				Host:              pointer.String(attr.ConnectionString),
				Port:              int(*attr.Port),
				EngineType:        bizutils.DBTypeClickhouse,
				EngineVersion:     pointer.String(attr.EngineVersion),
				Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
				Project:           project,
				Env:               env, //
				ResourceGroup:     pointer.Value(ins.ResourceGroupId),
				Cpu:               0,
				Memory:            0,
				DiskSize:          float64(diskSize * 1024),
				DiskType:          pointer.String(attr.StorageType),
				VpcId:             pointer.Value(ins.VpcId),
				SubnetId:          "",
				DgDomain:          "",
				DgId:              "",
				Region:            regionId,
				Zone:              pointer.String(attr.ZoneId),
				Description:       pointer.String(attr.DBClusterDescription),
				Content:           utils.JsonString(attr),
				AggregatedId:      cmdb.AggregatedID,
			}, nil
		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), process.Error()
}

func (j *SyncClickhouse) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](j.mtx, j.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}

func (j *SyncClickhouse) getCpuMemory(class string) (uint64, uint64) {
	cpuString := strings.Split(class, "C")[0]
	memString := strings.Split(strings.Split(class, "C")[1], "G")[0]
	cpu, _ := strconv.Atoi(cpuString)
	mem, _ := strconv.Atoi(memString)
	return uint64(cpu), uint64(mem * 1024)
}
