package jobSyncDatabaseAliyun

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	hitsdb "github.com/alibabacloud-go/hitsdb-20200615/v5/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"github.com/aws/smithy-go/ptr"
)

var aliyun_product_code_hitsdb = []string{"hitsdb"}

func (j *SyncHitsdb) sync(ctx context.Context, cli *aliyun.HitsdbClient) ([]*models.DatabaseInfo, error) {
	//regions := []string{"cn-qingdao"}
	describeRegions, err := cli.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionMap := orderedmap.New[string, string]()
	for _, region := range describeRegions {
		if pointer.Value(region.RegionId) == "ap-southeast-6" ||
			pointer.Value(region.RegionId) == "ap-southeast-2" ||
			pointer.Value(region.RegionId) == "ap-south-1" ||
			pointer.Value(region.RegionId) == "ap-northeast-1" { // 网络访问超时
			continue
		}
		regionMap.Store(pointer.Value(region.RegionId), pointer.Value(region.RegionId))
	}
	regions := regionMap.Slice()

	process := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		hitsClient := hybrid.AccountManager().AliyunHitsdbClient(ctx, cli.Name(), cli.Tag(), regionId)
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		instances, err := hitsClient.DescribeDBClustersOfAll(ctx)
		if err != nil {
			return nil, err
		}

		getMongoAttrProcess := tools.NewBatch[*hitsdb.GetLindormInstanceListResponseBodyInstanceList, *models.DatabaseInfo](ctx)
		getMongoAttrProcess.Run(instances, func(ctx context.Context, ins *hitsdb.GetLindormInstanceListResponseBodyInstanceList) (*models.DatabaseInfo, error) {
			attr, err := hitsClient.DescribeDBInstanceAttribute(ctx, *ins.InstanceId)
			if err != nil {
				var sdkErr *tea.SDKError
				if !errors.As(err, &sdkErr) {
					return nil, err
				}
				if *sdkErr.Code == "IllegalOperation.Resource" && *sdkErr.StatusCode == 400 {
					return nil, nil
				}

				return nil, err
			}

			conn, err := hitsClient.GetLindormInstanceEngineList(ctx, *attr.InstanceId)
			if err != nil {
				return nil, err
			}
			connAddr := ""
			if len(conn.EngineList) > 0 {
				addrs := make([]string, 0)
				for _, item := range conn.EngineList {
					if *item.EngineType == "lindorm" && len(item.NetInfoList) > 0 {
						for _, netItem := range item.NetInfoList {
							if *netItem.Port == 9190 {
								s := fmt.Sprintf("%s:%d", *netItem.ConnectionString, *netItem.Port)
								addrs = append(addrs, s)
							}
						}
					}
				}
				connAddr = strings.Join(addrs, ",")

				// handle endpoint
				err = handleHitsdbDatabaseEndpoint(*attr.InstanceId, conn.EngineList)
				if err != nil {
					return nil, err
				}
			}

			diskSize, _ := strconv.ParseFloat(pointer.Value(attr.InstanceStorage), 64)
			_, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(ins.ResourceGroupId))
			createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.CreateTime))
			expireAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.ExpireTime))

			// get env from tag
			var env string
			tags, err := hitsClient.ListTagResources(ctx, *attr.InstanceId)
			if err != nil {
				return nil, err
			}
			if len(tags.TagResources) > 0 {
			outerLoop:
				for _, tag := range tags.TagResources {
					switch strings.ToLower(strings.TrimSpace(pointer.Value(tag.TagKey))) {
					case rds_env, rds_env_ch:
						env = pointer.Value(tag.TagValue)
						break outerLoop
					}
				}
			}
			contextInfo := map[string]any{
				"instance":       attr,
				"instanceEngine": conn,
			}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(attr.InstanceId), aliyun_product_code_hitsdb)
			return &models.DatabaseInfo{
				Vendor:            cli.Vendor(),
				AccountName:       cli.Name(),
				InstanceId:        pointer.String(attr.InstanceId),
				InstanceName:      pointer.String(attr.InstanceAlias),
				InstanceType:      "",
				InstanceRole:      "Master",
				Category:          "Cluster",
				IsDeleted:         ptr.Bool(false),
				PrimaryInstanceId: "",
				HostInsId:         "",
				Status:            pointer.String(attr.InstanceStatus),
				ClassCode:         "",
				ChargeType:        pointer.String(attr.PayType),
				CreationTime:      timeutil.ZeroTime(createAt),
				ExpiredTime:       timeutil.ZeroTime(expireAt),
				Host:              connAddr,
				Port:              0,
				EngineType:        bizutils.DBTypeHitsdb,
				EngineVersion:     "",
				Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
				Project:           project,
				Env:               env,
				ResourceGroup:     pointer.Value(ins.ResourceGroupId),
				Cpu:               0,
				Memory:            0,
				DiskSize:          diskSize * 1024,
				DiskType:          pointer.String(attr.DiskCategory),
				VpcId:             pointer.Value(ins.VpcId),
				SubnetId:          "",
				DgDomain:          "",
				DgId:              "",
				Region:            regionId,
				Zone:              pointer.String(attr.ZoneId),
				Description:       "",
				Content:           utils.JsonString(contextInfo),
				AggregatedId:      cmdb.AggregatedID,
			}, nil
		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), process.Error()
}

func (j *SyncHitsdb) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](j.mtx, j.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}

func (j *SyncHitsdb) getCpuMemory(class string) (uint64, uint64) {
	cpuString := strings.Split(class, "C")[0]
	memString := strings.Split(strings.Split(class, "C")[1], "G")[0]
	cpu, _ := strconv.Atoi(cpuString)
	mem, _ := strconv.Atoi(memString)
	return uint64(cpu), uint64(mem * 1024)
}

func handleHitsdbDatabaseEndpoint(instanceId string, endpoints []*hitsdb.GetLindormInstanceEngineListResponseBodyEngineList) error {
	if len(endpoints) == 0 {
		return nil
	}
	data := make([]*models.DatabaseEndpoint, 0, len(endpoints))
	for _, endpoint := range endpoints {
		for _, address := range endpoint.NetInfoList {
			if pointer.Value(address.ConnectionString) == "" {
				continue
			}
			netType := bizutils.NetTypePublic
			if pointer.String(address.NetType) == "2" {
				netType = bizutils.NetTypePrivate
			}
			data = append(data, &models.DatabaseEndpoint{
				InstanceId:   instanceId,
				EndpointType: pointer.Value(endpoint.EngineType),
				Host:         pointer.Value(address.ConnectionString),
				Port:         int(pointer.Int32(address.Port)),
				NetType:      netType,
				//Description:  pointer.Value(address.Description),
				CreateTime: timeutil.ZeroTime(time.Now()),
				UpdateTime: timeutil.ZeroTime(time.Now()),
			})
		}
	}
	if err := bizutils.CreateOrUpdateEndpoint(context.Background(), data...); err != nil {
		return err
	}
	return nil
}
