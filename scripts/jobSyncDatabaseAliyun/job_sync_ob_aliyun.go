package jobSyncDatabaseAliyun

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	ob "github.com/alibabacloud-go/oceanbasepro-20190901/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"github.com/aws/smithy-go/ptr"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
)

var aliyun_product_code_oceanbase = []string{"oceanbase"}

func (j *SyncOB) sync(ctx context.Context, cli *aliyun.OceanBaseClient) ([]*models.DatabaseInfo, error) {
	regions := []string{"cn-qingdao", "cn-beijing"}

	process := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		obClient := hybrid.AccountManager().AliyunOceanBaseClient(ctx, cli.Name(), cli.Tag(), regionId)
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		instances, err := obClient.DescribeInstancesOfAll(ctx)
		if err != nil {
			return nil, err
		}

		getMongoAttrProcess := tools.NewBatch[*ob.DescribeInstancesResponseBodyInstances, *models.DatabaseInfo](ctx)
		getMongoAttrProcess.Run(instances, func(ctx context.Context, ins *ob.DescribeInstancesResponseBodyInstances) (*models.DatabaseInfo, error) {
			attr, err := obClient.DescribeInstance(ctx, pointer.Value(ins.InstanceId))
			if err != nil {
				var sdkErr *tea.SDKError
				if !errors.As(err, &sdkErr) {
					return nil, err
				}
				if *sdkErr.Code == "IllegalOperation.Resource" && *sdkErr.StatusCode == 400 {
					return nil, nil
				}

				return nil, err
			}

			zoneList := make([]string, 0)
			for _, zone := range ins.AvailableZones {
				zoneList = append(zoneList, pointer.Value(zone))
			}
			diskSize, _ := strconv.ParseFloat(pointer.Value(ins.DiskSize), 64)
			_, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(ins.ResourceGroupId))
			createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.CreateTime))
			// get env from tag
			var env string
			tags, err := cli.DescribeInstanceTags(ctx, *attr.InstanceId)
			if err != nil {
				return nil, err
			}
			if len(tags.TagResources) > 0 {
			outerLoop:
				for _, tag := range tags.TagResources {
					t, _ := utils.UnmarshalString[map[string]string](pointer.Value(tag.Tag))
					switch strings.ToLower(strings.TrimSpace(t[rds_env])) {
					case rds_env, rds_env_ch:
						env = t[rds_env]
						break outerLoop
					}
				}
			}

			// 查询集群下的租户列表信息
			tenants, err := obClient.DescribeTenants(ctx, pointer.Value(attr.InstanceId))
			if err != nil {
				return nil, err
			}
			contextInfo := map[string]any{
				"cluster": attr,
				"tenants": tenants,
			}
			if len(tenants.Tenants) > 0 {
				tenantList := make([]*ob.DescribeTenantResponseBody, 0)
				for _, tenant := range tenants.Tenants {
					// 查询指定集群下的指定租户信息
					tenant, err := obClient.DescribeTenant(ctx, pointer.Value(attr.InstanceId), pointer.Value(tenant.TenantId))
					if err != nil {
						return nil, err
					}
					tenantList = append(tenantList, tenant)
				}
				contextInfo["tenantDetail"] = tenantList

				// handle endpoint
				err = handleOBDatabaseEndpoint(pointer.Value(attr.InstanceId), tenantList)
				if err != nil {
					return nil, err
				}
				// handle node
				err = handleOBDatabaseNode(pointer.Value(attr.InstanceId), tenants.Tenants)
				if err != nil {
					return nil, err
				}
			}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(attr.InstanceId), aliyun_product_code_oceanbase)
			return &models.DatabaseInfo{
				Vendor:        cli.Vendor(),
				AccountName:   cli.Name(),
				InstanceId:    pointer.Value(attr.InstanceId),
				InstanceName:  pointer.Value(attr.InstanceName),
				InstanceType:  jobSyncDatabaseRds.RDSTypeNormal,
				InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
				IsDeleted:     ptr.Bool(false),
				Category:      fmt.Sprintf("%s(%s)", pointer.Value(attr.DeployType), pointer.Value(attr.DeployMode)),
				Status:        pointer.Value(attr.Status),
				ClassCode:     pointer.Value(ins.InstanceClass),
				ChargeType:    pointer.Value(attr.PayType),
				CreationTime:  timeutil.ZeroTime(createAt),
				ExpiredTime:   nil,
				EngineType:    bizutils.DBTypeOceanBase,
				EngineVersion: pointer.Value(attr.Version),
				Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
				Project:       project,
				Env:           env,
				ResourceGroup: pointer.Value(ins.ResourceGroupId),
				Cpu:           int(pointer.Value(ins.Cpu)),
				Memory:        uint64(pointer.Value(ins.Mem)) * 1024,
				DiskSize:      diskSize * 1024,
				DiskType:      pointer.Value(ins.DiskType),
				VpcId:         pointer.Value(ins.VpcId),
				Region:        regionId,
				Zone:          strings.Join(zoneList, ","),
				Content:       utils.JsonString(contextInfo),
				AggregatedId:  cmdb.AggregatedID,
			}, nil
		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), process.Error()
}

func (j *SyncOB) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](j.mtx, j.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}

func (j *SyncOB) getCpuMemory(class string) (uint64, uint64) {
	cpuString := strings.Split(class, "C")[0]
	memString := strings.Split(strings.Split(class, "C")[1], "G")[0]
	cpu, _ := strconv.Atoi(cpuString)
	mem, _ := strconv.Atoi(memString)
	return uint64(cpu), uint64(mem * 1024)
}

func handleOBDatabaseNode(instanceId string, nodes []*ob.DescribeTenantsResponseBodyTenants) error {
	if len(nodes) == 0 {
		return nil
	}
	data := make([]*models.DatabaseNode, 0, len(nodes))
	for _, node := range nodes {
		data = append(data, &models.DatabaseNode{
			InstanceId:      instanceId,
			Cpu:             int(*node.Cpu),
			Memory:          int(*node.Mem),
			NodeId:          pointer.Value(node.TenantId),
			NodeClass:       pointer.Value(node.TenantMode),
			NodeDescription: pointer.Value(node.TenantName),
			NodeRole:        pointer.Value(node.DeployType),
			NodeStatus:      pointer.Value(node.Status),
			Zone:            pointer.Value(node.PrimaryZone),
			CreateTime:      timeutil.ZeroTime(time.Now()),
			UpdateTime:      timeutil.ZeroTime(time.Now()),
		})
	}
	if err := bizutils.CreateOrUpdateNode(context.Background(), data...); err != nil {
		return err
	}
	return nil
}

func handleOBDatabaseEndpoint(instanceId string, endpoints []*ob.DescribeTenantResponseBody) error {
	if len(endpoints) == 0 {
		return nil
	}
	data := make([]*models.DatabaseEndpoint, 0, len(endpoints))
	for _, endpoint := range endpoints {
		if endpoint.Tenant == nil {
			continue
		}
		tenant := endpoint.Tenant

		for _, address := range tenant.TenantConnections {
			if pointer.Value(address.IntranetAddress) == "" {
				continue
			}
			data = append(data, &models.DatabaseEndpoint{
				InstanceId:   instanceId,
				EndpointType: pointer.Value(address.AddressType),
				Host:         pointer.Value(address.IntranetAddress),
				Port:         int(pointer.Int32(address.IntranetPort)),
				//Description:  pointer.Value(address.Description),
				CreateTime: timeutil.ZeroTime(time.Now()),
				UpdateTime: timeutil.ZeroTime(time.Now()),
				NodeId:     pointer.Value(tenant.TenantId),
			})
		}
	}
	if err := bizutils.CreateOrUpdateEndpoint(context.Background(), data...); err != nil {
		return err
	}
	return nil
}
