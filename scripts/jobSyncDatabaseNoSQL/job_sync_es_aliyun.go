package jobSyncDatabaseNoSQL

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	es "github.com/alibabacloud-go/elasticsearch-20170613/v2/client"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"github.com/aws/smithy-go/ptr"
	"golang.org/x/time/rate"
)

var aliyun_product_code_es = []string{"elasticsearch"}

func (j *JobSyncES) syncAliyunEsInfo(ctx context.Context, cli *aliyun.ElasticSearchClient) ([]*models.DatabaseInfo, error) {
	describeRegions, err := cli.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionMap := orderedmap.New[string, string]()
	for _, region := range describeRegions {
		regionMap.Store(pointer.Value(region.RegionId), pointer.Value(region.RegionId))
	}
	regions := regionMap.Slice()

	// 2 requests every 3 seconds
	limiter := rate.NewLimiter(rate.Limit(2.0/3.0), 2)

	process := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		elasticSearchClient := hybrid.AccountManager().AliyunElasticSearchEndpoint(ctx, cli.Name(), cli.Tag(), regionId)
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		esList, err := elasticSearchClient.ListInstanceOfAll(ctx)
		if err != nil {
			if strings.Contains(err.Error(), timeOutErr) {
				return nil, nil
			}
			return nil, err
		}

		getMongoAttrProcess := tools.NewBatch[*es.ListInstanceResponseBodyResult, *models.DatabaseInfo](ctx)
		getMongoAttrProcess.Run(esList, func(ctx context.Context, ins *es.ListInstanceResponseBodyResult) (*models.DatabaseInfo, error) {
			// 限流
			if err := limiter.Wait(ctx); err != nil {
				return nil, err
			}
			attr, err := elasticSearchClient.DescribeInstance(ctx, pointer.Value(ins.InstanceId))
			if err != nil {
				if strings.Contains(err.Error(), timeOutErr) {
					return nil, nil
				}
				return nil, err
			}
			zoneList := make([]string, 0)
			for _, z := range attr.ZoneInfos {
				zoneList = append(zoneList, pointer.Value(z.ZoneId))
			}
			sort.Sort(sort.StringSlice(zoneList))

			cpu, mem := j.getCpuMemory(pointer.Value(ins.NodeSpec.Spec))
			_, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(attr.ResourceGroupId))
			createAt, _ := time.Parse(bizutils.AliyunElasticsearch, pointer.Value(attr.CreatedAt))
			// get env from tag
			var env string
			tags, err := elasticSearchClient.ListTagResources(ctx, pointer.Value(attr.InstanceId))
			if err != nil {
				return nil, err
			}
			if len(tags.TagResources.TagResource) > 0 {
			outerLoop:
				for _, tag := range tags.TagResources.TagResource {
					switch strings.ToLower(strings.TrimSpace(pointer.Value(tag.TagKey))) {
					case nosql_db_env, nosql_db_env_ch:
						env = pointer.Value(tag.TagValue)
						break outerLoop
					}
				}
			}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(attr.InstanceId), aliyun_product_code_es)
			return &models.DatabaseInfo{
				Vendor:        cli.Vendor(),
				AccountName:   cli.Name(),
				InstanceId:    pointer.Value(attr.InstanceId),
				InstanceName:  pointer.Value(attr.Description),
				InstanceType:  jobSyncDatabaseRds.RDSTypeNormal,
				InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
				IsDeleted:     ptr.Bool(false),
				Category:      pointer.Value(attr.InstanceCategory),
				Status:        pointer.Value(attr.Status),
				ClassCode:     pointer.Value(ins.NodeSpec.Spec),
				ChargeType:    pointer.Value(attr.PaymentType),
				CreationTime:  timeutil.ZeroTime(createAt),
				ExpiredTime:   nil,
				Host:          pointer.Value(attr.Domain),
				Port:          int(pointer.Value(attr.Port)),
				EngineType:    bizutils.DBTypeElasticSearch,
				EngineVersion: pointer.Value(attr.EsVersion),
				Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
				Project:       project,
				Env:           env,
				ResourceGroup: pointer.Value(attr.ResourceGroupId),
				Cpu:           int(cpu),
				Memory:        mem,
				DiskSize:      float64(pointer.Value(attr.MasterConfiguration.Disk)) * 1024,
				DiskType:      pointer.Value(attr.MasterConfiguration.DiskType),
				VpcId:         pointer.Value(attr.NetworkConfig.VpcId),
				SubnetId:      pointer.Value(attr.NetworkConfig.VswitchId),
				DgDomain:      "",
				DgId:          "",
				Region:        regionId,
				Zone:          strings.Join(zoneList, ","),
				Content:       utils.JsonString(attr),
				AggregatedId:  cmdb.AggregatedID,
			}, nil
		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), process.Error()
}

func (j *JobSyncES) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](j.mtx, j.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}

func (j *JobSyncES) getCpuMemory(class string) (uint64, uint64) {
	switch class {
	case "elasticsearch.d1.2xlarge":
		return 8, 32 * 1024
	case "elasticsearch.d1.4xlarge":
		return 16, 64 * 1024
	case "elasticsearch.i2.4xlarge":
		return 16, 128 * 1024
	case "elasticsearch.i2g.2xlarge":
		return 8, 32 * 1024
	case "elasticsearch.i2g.4xlarge":
		return 16, 64 * 1024
	case "elasticsearch.i2g.8xlarge":
		return 32, 128 * 1024
	case "elasticsearch.ic5.large":
		return 2, 2 * 1024
	case "elasticsearch.ic5.xlarge":
		return 4, 4 * 1024
	case "elasticsearch.ic5.2xlarge":
		return 8, 8 * 1024
	case "elasticsearch.ic5.3xlarge":
		return 12, 12 * 1024
	case "elasticsearch.ic5.4xlarge":
		return 16, 16 * 1024
	case "elasticsearch.n4.small":
		return 1, 2 * 1024
	case "elasticsearch.r5.2xlarge":
		return 8, 64 * 1024
	case "elasticsearch.r5.large":
		return 2, 16 * 1024
	case "elasticsearch.r5.xlarge":
		return 4, 32 * 1024
	case "elasticsearch.r6.4xlarge":
		return 16, 128 * 1024
	case "elasticsearch.r6.8xlarge":
		return 32, 256 * 1024
	case "elasticsearch.sn1ne.2xlarge", "elasticsearch.sn1ne.2xlarge.new":
		return 8, 16 * 1024
	case "elasticsearch.sn1ne.4xlarge", "elasticsearch.sn1ne.4xlarge.new":
		return 16, 32 * 1024
	case "elasticsearch.sn1ne.8xlarge", "elasticsearch.sn1ne.8xlarge.new":
		return 32, 64 * 1024
	case "elasticsearch.sn1ne.large", "elasticsearch.sn1ne.large.new":
		return 2, 4 * 1024
	case "elasticsearch.sn1ne.xlarge", "elasticsearch.sn1ne.xlarge.new":
		return 4, 8 * 1024
	case "elasticsearch.sn2ne.2xlarge", "elasticsearch.sn2ne.2xlarge.new":
		return 8, 32 * 1024
	case "elasticsearch.sn2ne.4xlarge", "elasticsearch.sn2ne.4xlarge.new":
		return 16, 64 * 1024
	case "elasticsearch.sn2ne.8xlarge":
		return 32, 128 * 1024
	case "elasticsearch.sn2ne.large", "elasticsearch.sn2ne.large.new":
		return 2, 8 * 1024
	case "elasticsearch.sn2ne.xlarge", "elasticsearch.sn2ne.xlarge.new":
		return 4, 16 * 1024
	case "elasticsearch.t6.large":
		return 2, 4 * 1024
	case "openstore.hybrid.i2.2xlarge":
		return 8, 64 * 1024
	case "openstore.hybrid.i2g.4xlarge":
		return 16, 128 * 1024
	default:
		notice.SendErrorMessage("未知的ES实例规格", fmt.Sprintf("实例规格: %s", class))
		return 0, 0
	}
}
