package jobSyncDatabaseNoSQL

import (
	"context"
	"fmt"
	"strings"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	"github.com/aws/aws-sdk-go-v2/service/elasticache"
	"github.com/aws/aws-sdk-go-v2/service/elasticache/types"
)

var aws_product_code_redis = []string{"AmazonElastiCache"}

func (n *RedisSync) syncAwsNoSQLInfo(client *aws.Ec2Client, ctx context.Context) ([]*models.DatabaseInfo, error) {
	regions, err := client.DescribeRegions(ctx)
	if err != nil {
		return nil, bizutils.WarpClientError(client, err)
	}
	regionsIds := orderedmap.New[string, string]()
	for _, region := range regions {
		regionsIds.Store(pointer.String(region.RegionName), pointer.String(region.RegionName))
	}
	b := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, region string) ([]*models.DatabaseInfo, error) {
		nosqlClient := hybrid.AccountManager().AwsNoSQLClient(ctx, client.Name(), client.Tag(), region)
		return n.syncAwsRegionNoSQLInfo(nosqlClient, ctx)
	})

	return tools.MergeData(b.Outs()...), bizutils.WarpClientError(client, b.Error())
}

func (n *RedisSync) syncAwsRegionNoSQLInfo(client *aws.RedisClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	clusters, err := bizutils.WithRetry(
		3,
		func() ([]types.CacheCluster, error) {
			return client.DescribeCacheClustersOfAll(ctx)
		},
	)

	if err != nil {
		if strings.Contains(err.Error(), bizutils.TimeoutErr) {
			return nil, nil
		}
		n.Errorf(ctx, "aws nosql查询集群出错：%v", err.Error())
		return nil, bizutils.WarpClientError(client, err)
	}

	replicationGroupIds := make(map[string]*models.DatabaseInfo)
	nosqlList := make([]*models.DatabaseInfo, len(clusters))
	// 集群信息
	for i, cluster := range clusters {
		tags, tagErr := bizutils.WithRetry(
			3,
			func() ([]types.Tag, error) {
				return client.ListTagsForResource(ctx, *cluster.ARN)
			},
		)

		if tagErr != nil {
			n.Errorf(ctx, "aws nosql查询标签出错：%v", tagErr.Error())
		}

		groups, groupErr := client.DescribeCacheSubnetGroups(ctx, func(input *elasticache.DescribeCacheSubnetGroupsInput) {
			input.CacheSubnetGroupName = cluster.CacheSubnetGroupName
		})
		if groupErr != nil {
			n.Errorf(ctx, "aws nosql查询子网组出错：%v", groupErr.Error())
			return nil, bizutils.WarpClientError(client, groupErr)
		}
		var vpcId string
		if len(groups) > 0 {
			vpcId = *groups[0].VpcId
		}

		project, scode, env := ParseInfoFromTags(tags)

		cpu, mem := client.GetClassCpuMemory(*cluster.CacheNodeType)
		host, port := n.tryParseHostAndPortOfTheFirstCacheNode(&cluster)
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(client.Vendor()), client.Identifier(), pointer.String(cluster.CacheClusterId), aws_product_code_redis)
		nosqlList[i] = &models.DatabaseInfo{
			Model:         n.Model(ctx),
			Vendor:        client.Vendor(),
			AccountName:   client.Name(),
			InstanceId:    *cluster.CacheClusterId,
			InstanceName:  *cluster.ARN,
			InstanceType:  jobSyncDatabaseRds.RDSTypeNormal,
			InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
			Category:      jobSyncDatabaseRds.RdsCategoryCluster,
			Status:        *cluster.CacheClusterStatus,
			ClassCode:     *cluster.CacheNodeType,
			CreationTime:  timeutil.ZeroTimePtr(cluster.CacheClusterCreateTime),
			ExpiredTime:   nil,
			Host:          host,
			Port:          port,
			EngineType:    bizutils.DBTypeRedis,
			EngineVersion: *cluster.EngineVersion,
			Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
			Project:       project,
			Env:           env,
			Cpu:           cpu,
			Memory:        uint64(mem),
			DiskSize:      0,
			VpcId:         vpcId,
			SubnetId:      *cluster.CacheSubnetGroupName,
			Region:        client.Region(),
			Zone:          *cluster.PreferredAvailabilityZone,
			Content:       utils.JsonString(cluster),
			AggregatedId:  cmdb.AggregatedID,
		}

		if cluster.ReplicationGroupId != nil && replicationGroupIds[*cluster.ReplicationGroupId] == nil {
			replicationGroupIds[*cluster.ReplicationGroupId] = nosqlList[i]
		}
	}

	// 查询复制组信息
	for replicationGroupId, databaseInfo := range replicationGroupIds {
		group, err := client.DescribeReplicationGroup(ctx, replicationGroupId)
		if err != nil {
			n.Errorf(ctx, "aws nosql查询复制组出错：%v", err.Error())
			return nil, bizutils.WarpClientError(client, err)
		}
		if group == nil {
			continue
		}

		host, port, readerEndpoint := n.tryParseHostAndPortOfTheFirstNodeGroup(group)

		item := &models.DatabaseInfo{
			Model:          n.Model(ctx),
			Vendor:         client.Vendor(),
			AccountName:    client.Name(),
			InstanceId:     replicationGroupId,
			InstanceName:   *group.ARN,
			InstanceType:   jobSyncDatabaseRds.RDSTypeNormal,
			InstanceRole:   jobSyncDatabaseRds.RdsRoleMaster,
			Category:       jobSyncDatabaseRds.RdsCategoryCluster,
			Status:         *group.Status,
			ClassCode:      *group.CacheNodeType,
			CreationTime:   timeutil.ZeroTimePtr(group.ReplicationGroupCreateTime),
			ExpiredTime:    nil,
			Host:           host,
			Port:           port,
			ReaderEndpoint: readerEndpoint,
			EngineType:     bizutils.DBTypeRedis,
			EngineVersion:  databaseInfo.EngineVersion,
			Scode:          databaseInfo.Scode,
			Project:        databaseInfo.Project,
			Env:            databaseInfo.Env,
			Cpu:            databaseInfo.Cpu,
			Memory:         databaseInfo.Memory,
			DiskSize:       0,
			VpcId:          databaseInfo.VpcId,
			SubnetId:       databaseInfo.SubnetId,
			Region:         client.Region(),
			Zone:           databaseInfo.Zone,
			Content:        utils.JsonString(group),
		}
		nosqlList = append(nosqlList, item)
	}

	return nosqlList, nil
}

func ParseInfoFromTags(tags []types.Tag) (project string, scode string, env string) {
	for _, tag := range tags {
		if strings.EqualFold(pointer.String(tag.Key), "SCode") {
			scode = *tag.Value
			if projectInfo, err := api.HdsClient().QueryAlmProject(scode); err == nil {
				project = projectInfo.Id
			}
		}
		switch strings.ToLower(strings.TrimSpace(pointer.String(tag.Key))) {
		case nosql_db_env, nosql_db_env_ch:
			env = pointer.String(tag.Value)
		}
	}
	return
}

func (n *RedisSync) tryParseHostAndPortOfTheFirstCacheNode(cluster *types.CacheCluster) (host string, port int) {
	for _, node := range cluster.CacheNodes {
		host = *node.Endpoint.Address
		port = int(*node.Endpoint.Port)

		if len(host) > 0 && port > 0 {
			return
		}
	}
	return
}

func (n *RedisSync) tryParseHostAndPortOfTheFirstNodeGroup(group *types.ReplicationGroup) (host string, port int, readerEndpoint string) {
	for _, nodeGroup := range group.NodeGroups {
		if nodeGroup.PrimaryEndpoint != nil {
			host = *nodeGroup.PrimaryEndpoint.Address
			port = int(*nodeGroup.PrimaryEndpoint.Port)
		}

		if nodeGroup.ReaderEndpoint != nil {
			rHost := *nodeGroup.ReaderEndpoint.Address
			rPort := int(*nodeGroup.ReaderEndpoint.Port)
			readerEndpoint = fmt.Sprintf("%s:%d", rHost, rPort)
		}

		if len(host) > 0 && port > 0 {
			return
		}
	}
	return
}
