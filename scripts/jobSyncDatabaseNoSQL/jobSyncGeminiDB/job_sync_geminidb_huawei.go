package jobSyncGeminiDB

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	epsModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/gaussdbfornosql/v3/model"
	iamModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
)

var huawei_product_code_geminidb = []string{"hws.service.type.nosql"}

func (r *NosqlSync) syncHuaweiGeminiDBInfoAllRegion(defClient *huaweicloud.IamClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	projects, err := defClient.ListProjects(ctx)
	if err != nil {
		return nil, err
	}
	regions, err := defClient.ListRegions(ctx)
	if err != nil {
		return nil, err
	}
	projectBatch := tools.NewBatch[iamModel.ProjectResult, []*models.DatabaseInfo](ctx)
	projectBatch.Run(projects, func(ctx context.Context, project iamModel.ProjectResult) ([]*models.DatabaseInfo, error) {
		var targetRegion *iamModel.Region
		for _, region := range regions {
			if region.Id == "cn-east-3" || region.Id == "la-south-2" || region.Id == "na-mexico-1" { // 网络访问超时
				continue
			}
			if region.Id == project.Name {
				targetRegion = &region
				break
			}
		}
		if targetRegion == nil {
			r.Warnf(ctx, "unexpected regionId: %s, skip ...", project.Name)
			return nil, nil
		}
		infoList, err := r.syncHuaweiGeminiDBInfoRegion(defClient, ctx, *targetRegion, project)
		r.Infof(ctx, "get %d records for all regions", len(infoList))
		return infoList, err
	})

	return tools.MergeData(projectBatch.Outs()...), projectBatch.Error()
}

func (r *NosqlSync) syncHuaweiGeminiDBInfoRegion(defClient client.IClient, ctx context.Context, region iamModel.Region, project iamModel.ProjectResult) (databaseInfo []*models.DatabaseInfo, err error) {
	defer func() {
		// 获取到非预期的region时会发生panic
		if panicError := recover(); panicError != nil {
			r.Warnf(ctx, "Panic occurred: %v", panicError)
			err = r.handleHuaweiProjectPanic(panicError, ctx, region)
		}
	}()

	rdsClient := hybrid.AccountManager().HuaweiGeminiDBClient(ctx, defClient.Name(), defClient.Tag(), region.Id, project.Id)
	if rdsClient == nil {
		return nil, nil
	}
	rsp, err := rdsClient.ListGeminiDBInstanceOfAll()

	if err != nil {
		if strings.Contains(err.Error(), "\"status_code\":401") {
			return nil, nil
		}
		if strings.Contains(err.Error(), "\"status_code\":403") {
			return nil, nil
		}
		if strings.Contains(err.Error(), "Timeout") {
			return nil, nil
		}
		return nil, err
	}
	databaseInfo = make([]*models.DatabaseInfo, len(rsp))
	for _, data := range rsp {
		epsClient := hybrid.AccountManager().HuaweiEpsClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region())
		d := r.tranceHuaweiGeminiDB2RdsInfo(ctx, rdsClient, epsClient, data)
		if d != nil {
			databaseInfo = append(databaseInfo, d)
		}
	}
	return databaseInfo, nil
}

func (r *NosqlSync) tranceHuaweiGeminiDB2RdsInfo(
	ctx context.Context,
	geminiDBClient *huaweicloud.GeminiDBClient,
	epsClient *huaweicloud.EpsClient,
	geminiData model.ListInstancesResult,
) *models.DatabaseInfo {

	_, scode, project := bizutils.ParseHuaweiEnvProject(ctx, geminiData.Name, geminiData.EnterpriseProjectId, epsClient, r)
	creationTime, _ := time.Parse(bizutils.HuaweiEcsTimeFormat, geminiData.Created)

	// get env from tag
	var env string
	tags, err := geminiDBClient.ListInstanceTags(geminiData.Id)
	if err != nil {
		return nil
	}

	if len(*tags.Tags) > 0 {
	outerLoop:
		for _, tag := range *tags.Tags {
			switch strings.ToLower(strings.TrimSpace(tag.Key)) {
			case jobSyncDatabaseRds.RDS_ENV, jobSyncDatabaseRds.RDS_ENV_CH:
				env = tag.Value
				break outerLoop
			}
		}
	}

	port, _ := strconv.Atoi(geminiData.Port)
	classCode := ""
	diskSize := 0
	if len(geminiData.Groups) > 0 {
		for _, group := range geminiData.Groups {
			if group.Volume != nil {
				diskSize, _ = strconv.Atoi(group.Volume.Size)
			}
			if len(group.Nodes) > 0 {
				classCode = group.Nodes[0].SpecCode
			}
		}
	}
	cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(geminiDBClient.Vendor()), geminiDBClient.Identifier(), geminiData.Id, huawei_product_code_geminidb)
	return &models.DatabaseInfo{
		Model:         r.Model(ctx),
		Vendor:        geminiDBClient.Vendor(),
		AccountName:   geminiDBClient.Name(),
		InstanceId:    geminiData.Id,
		InstanceName:  geminiData.Name,
		InstanceType:  geminiData.Mode,
		InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
		Category:      "",
		ResourceGroup: "",
		Status:        geminiData.Status,
		ClassCode:     classCode,
		ChargeType:    geminiData.PayMode,
		CreationTime:  timeutil.ZeroTime(creationTime),
		Host:          "",
		Port:          port,
		EngineType:    bizutils.DBTypeGeminiDB,
		EngineVersion: geminiData.Datastore.WholeVersion,
		Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
		Project:       project,
		Env:           env,
		DiskSize:      float64(diskSize) * 1024,
		VpcId:         geminiData.VpcId,
		SubnetId:      geminiData.SubnetId,
		Region:        geminiData.Region,
		Content:       utils.JsonString(geminiData),
		AggregatedId:  cmdb.AggregatedID,
	}
}

func (r *NosqlSync) GetHuaweiEnterpriseProject(client *huaweicloud.EpsClient, ctx context.Context, projectId string) *epsModel.EpDetail {
	if client == nil {
		return nil
	}
	return bizutils.LoadFromMap[string, *epsModel.EpDetail](r.mtx, r.ep, projectId, func() (*epsModel.EpDetail, error) {
		return client.ShowEnterpriseProject(ctx, projectId)
	})
}

func (r *NosqlSync) handleHuaweiProjectPanic(panicError any, ctx context.Context, region iamModel.Region) error {
	var msg string
	switch pe := panicError.(type) {
	case error:
		msg = pe.Error()
	case string:
		msg = pe
	}

	if strings.Contains(msg, "not in the following supported regions") {
		r.Logger().Warnf(ctx, "%s getHuaweiRegionGaussDB: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, "unexpected regionId") {
		r.Logger().Warnf(ctx, "%s getHuaweiRegionGaussDB: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, "failed to get project id, No project id found") {
		r.Logger().Warnf(ctx, "%s getHuaweiRegionGaussDB: %s", region.Id, msg)
	} else {
		return errors.New(msg)
	}
	return nil
}
