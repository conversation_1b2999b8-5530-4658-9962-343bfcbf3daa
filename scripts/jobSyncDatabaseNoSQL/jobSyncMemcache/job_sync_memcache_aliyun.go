package jobSyncMemcache

import (
	"context"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	redis "github.com/aliyun/alibaba-cloud-sdk-go/services/r-kvstore"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
)

const timeOutErr = "timed out"
const engineType = "Memcache"

var aliyun_product_code_memcache = []string{"redisa"}

func (n *MemcacheSync) syncAliyunNoSQLInfo(client *aliyun.KvStoreClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	resp, err := client.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}

	regionIds := orderedmap.New[string, string]()
	for _, r := range resp.RegionIds.KVStoreRegion {
		regionIds.Store(r.RegionId, r.RegionId)
	}

	regionIds.Delete("cn-heyuan-acdr-1")

	b := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	b.Run(regionIds.Slice(), func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		redisClient := hybrid.AccountManager().AliyunKvStoreClient(ctx, client.Name(), client.Tag(), regionId)
		return n.syncAliyunRegionNoSQLInfo(ctx, redisClient)
	})

	return tools.MergeData(b.Outs()...), bizutils.WarpClientError(client, b.Error())
}

func (n *MemcacheSync) syncAliyunRegionNoSQLInfo(ctx context.Context, client *aliyun.KvStoreClient) ([]*models.DatabaseInfo, error) {
	instances, err := client.DescribeInstancesOfAll(ctx, func(req *redis.DescribeInstancesRequest) {
		req.InstanceType = engineType
	})
	if err != nil {
		if strings.Contains(err.Error(), "Connect timeout") || strings.Contains(err.Error(), timeOutErr) {
			return nil, nil
		}
		return nil, err
	}
	if len(instances) == 0 {
		return nil, nil
	}

	// 阿里云redis接口限流，获取实例信息时不再并发
	b := tools.NewBatch[redis.KVStoreInstanceInDescribeInstances, *models.DatabaseInfo](ctx, batch.WithBatchSize(1))
	b.Run(instances, func(ctx context.Context, instance redis.KVStoreInstanceInDescribeInstances) (*models.DatabaseInfo, error) {
		attr, describeAttrError := client.DescribeInstanceAttribute(ctx, instance.InstanceId)
		if describeAttrError != nil {
			if strings.Contains(describeAttrError.Error(), timeOutErr) {
				return nil, nil
			}
			return nil, describeAttrError
		}
		if attr == nil {
			return nil, nil
		}

		resourceManagerClient := hybrid.AccountManager().AliyunResourceManagerClient(ctx, client.Name(), client.Tag(), client.Region())
		_, scode, project := bizutils.ParseAliyunEnvProject(resourceManagerClient, n, attr.ResourceGroupId)
		port := int(attr.Port)
		createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, attr.CreateTime)
		expireAt, _ := time.Parse(bizutils.NormalDatetimeFmt, attr.EndTime)
		// get env from tag
		var env string
		tags, err := client.ListTagResources(ctx, attr.InstanceId)
		if err != nil {
			return nil, err
		}
		if len(tags.TagResource) > 0 {
		outerLoop:
			for _, tag := range tags.TagResource {
				switch strings.ToLower(strings.TrimSpace(tag.TagKey)) {
				case nosql_db_env, nosql_db_env_ch:
					env = tag.TagValue
					break outerLoop
				}
			}
		}
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(client.Vendor()), client.Identifier(), attr.InstanceId, aliyun_product_code_memcache)
		return &models.DatabaseInfo{
			Model:         n.Model(ctx),
			Vendor:        client.Vendor(),
			AccountName:   client.Name(),
			InstanceId:    attr.InstanceId,
			InstanceName:  attr.InstanceName,
			InstanceType:  attr.InstanceType,
			InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
			Category:      attr.ArchitectureType,
			Status:        attr.InstanceStatus,
			ClassCode:     attr.InstanceClass,
			ChargeType:    attr.ChargeType,
			ResourceGroup: attr.ResourceGroupId,
			CreationTime:  timeutil.ZeroTime(createAt),
			ExpiredTime:   timeutil.ZeroTime(expireAt),
			Host:          attr.ConnectionDomain,
			Port:          port,
			EngineType:    engineType,
			EngineVersion: attr.EngineVersion,
			Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
			Project:       project,
			Env:           env,
			Memory:        uint64(attr.Capacity),
			DiskSize:      0,
			VpcId:         attr.VpcId,
			SubnetId:      attr.VSwitchId,
			Region:        attr.RegionId,
			Zone:          attr.ZoneId,
			Content:       utils.JsonString(attr),
			PrivateIp:     attr.PrivateIp,
			AggregatedId:  cmdb.AggregatedID,
		}, nil
	})

	return b.Outs(), b.Error()
}

func (n *MemcacheSync) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](n.mtx, n.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}
