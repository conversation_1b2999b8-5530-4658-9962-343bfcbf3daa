package jobSyncStarRocks

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	starrocks "github.com/alibabacloud-go/starrocks-20221019/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
)

const engineType = "starrocks"

var aliyun_product_code_starrocks = []string{"bigdata_starrocks"}

func (r *SyncStarRocks) syncAliyunStarRocksInfo(defClient *aliyun.StarRocksClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	regionsIds := orderedmap.New[string, string]()
	regionsIds.Store("cn-qingdao", "cn-qingdao")
	regionsIds.Store("cn-beijing", "cn-beijing")
	regionsIds.Store("cn-shanghai", "cn-shanghai")
	regionsIds.Store("cn-hangzhou", "cn-hangzhou")
	regionsIds.Store("cn-zhangjiakou", "cn-zhangjiakou")
	// regionsIds.Store("eu-central-1", "eu-central-1")
	// regionsIds.Store("ap-southeast-1", "ap-southeast-1")
	// regionsIds.Store("cn-qingdao-acdr-ut-1", "cn-qingdao-acdr-ut-1")

	b := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		client := hybrid.AccountManager().AliyunStarRocksClient(ctx, defClient.Name(), defClient.Tag(), regionId)
		return r.syncAliyunStarRocksRegInfo(client, ctx)
	})

	return tools.MergeData(b.Outs()...), b.Error()
}
func (r *SyncStarRocks) syncAliyunStarRocksRegInfo(client *aliyun.StarRocksClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	instances, err := client.DescribeInstances(ctx)
	if err != nil {
		return nil, err
	}
	if len(instances) == 0 {
		return nil, nil
	}

	b := tools.NewBatch[*starrocks.DescribeInstancesResponseBodyData, *models.DatabaseInfo](ctx)
	b.Run(instances, func(ctx context.Context, instance *starrocks.DescribeInstancesResponseBodyData) (*models.DatabaseInfo, error) {
		createAt := time.UnixMilli(tea.Int64Value(instance.BeginTime))
		expireAt := time.UnixMilli(tea.Int64Value(instance.ExpireTime))
		var zoneId, subnetId string
		if len(instance.VSwitches) > 0 {
			subnetId = tea.StringValue(instance.VSwitches[0].VswId)
			zoneId = tea.StringValue(instance.VSwitches[0].ZoneId)
		}

		var scode, env, project string
		instanceName := tea.StringValue(instance.InstanceName)
		for _, val := range strings.Split(instanceName, "-") {
			if bizutils.IsSCode(val) {
				scode = val
				break
			}
		}
		env = bizutils.TryParseEnv(instanceName)

		if projectInfo, _ := api.HdsClient().QueryAlmProject(scode); projectInfo != nil {
			project = projectInfo.Id
		}
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(client.Vendor()), client.Identifier(), tea.StringValue(instance.InstanceId), aliyun_product_code_starrocks)
		return &models.DatabaseInfo{
			Model:         r.Model(ctx),
			Vendor:        client.Vendor(),
			AccountName:   client.Name(),
			InstanceId:    tea.StringValue(instance.InstanceId),
			InstanceName:  instanceName,
			InstanceType:  tea.StringValue(instance.RunMode),
			InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
			Category:      tea.StringValue(instance.PackageType),
			Status:        tea.StringValue(instance.InstanceStatus),
			ClassCode:     tea.StringValue(instance.Architecture),
			ChargeType:    tea.StringValue(instance.PayType),
			ResourceGroup: tea.StringValue(instance.ResourceGroupId),
			CreationTime:  timeutil.ZeroTime(createAt),
			ExpiredTime:   timeutil.ZeroTime(expireAt),
			Host:          fmt.Sprintf("fe-%s-internal.starrocks.aliyuncs.com", tea.StringValue(instance.InstanceId)),
			Port:          9030, //fe的链接信息都是固定的不需要通过接口获取都是fe-<集群id>-internal.starrocks.aliyuncs.com  端口也是固定的
			EngineType:    engineType,
			EngineVersion: tea.StringValue(instance.Version),
			Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
			Project:       project,
			Env:           env,
			Memory:        0,
			DiskSize:      0,
			VpcId:         tea.StringValue(instance.VpcId),
			SubnetId:      subnetId,
			Region:        tea.StringValue(instance.RegionId),
			Zone:          zoneId,
			Content:       utils.JsonString(instance),
			// PrivateIp:     attr.PrivateIp,
			AggregatedId: cmdb.AggregatedID,
		}, nil
	})

	return b.Outs(), b.Error()
}

func (n *SyncStarRocks) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](n.mtx, n.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}
