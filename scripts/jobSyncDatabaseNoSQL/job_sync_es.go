package jobSyncDatabaseNoSQL

import (
	"context"
	"sync"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewSyncElasticSearch() *JobSyncES {
	schedule := "0 0 1 * * *"
	return &JobSyncES{
		mtx: new(sync.Mutex),
		rg:  orderedmap.New[string, *resourcemanager.ResourceGroup](),
		ep:  orderedmap.New[string, *eps.EpDetail](),
		JobBase: base.NewJobBase(
			"cmdb-sync-elasticsearch",
			"同步Elasticsearch资源",
			schedule,
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type JobSyncES struct {
	rg  *orderedmap.OrderedMap[string, *resourcemanager.ResourceGroup]
	ep  *orderedmap.OrderedMap[string, *eps.EpDetail]
	mtx *sync.Mutex
	*base.JobBase
}

func (j *JobSyncES) Name() string {
	return base.TaskCloudSyncElasticSearch
}

func (j *JobSyncES) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(j.Model(ctx), hbc.GoogleCloud, hbc.Private, hbc.TencentCloud, hbc.AWS, hbc.Azure)
	if err != nil {
		return nil, err
	}

	defaultClients, err := base.GetDefaultIClients(ctx, vendors,
		hbc.ElasticSearch, []string{bizutils.PurposeAdmin},
		actfilter.SetCustomProduct(hbc.AWS, hbc.ECS),                      // aws默认使用ecs client，获取地区
		actfilter.SetCustomProduct(hbc.HuaweiCloud, hbc.IAM),              // 华为云默认client使用 IAM client
		actfilter.SetCustomProduct(hbc.TencentCloud, hbc.ECS),             // 腾讯云默认client使用 Ecs client
		actfilter.SetCustomPurpose(hbc.AWS, bizutils.PurposeAccountCheck), // AWS复用 ACCOUNT_CHECK 的账号
		actfilter.SetIgnoreVendor(hbc.Private),
	)
	if err != nil {
		return nil, err
	}

	b := tools.NewBatch[client.IClient, []*models.DatabaseInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.DatabaseInfo, error) {
		switch defaultClient := input.(type) {
		case *aliyun.ElasticSearchClient:
			return j.syncAliyunEsInfo(ctx, defaultClient)
		case *huaweicloud.IamClient:
			return j.syncHuaweiEsInfo(ctx, defaultClient)
		}
		return nil, nil
	})

	if err := b.Error(); err != nil {
		j.Errorf(ctx, "sync middleware failed, cause: %s", err)
		return nil, err
	}

	// 将信息存入数据库
	return nil, bizutils.CreateOrUpdateRds(ctx, tools.MergeData(b.Outs()...)...)
}

func (j *JobSyncES) GetHuaweiEnterpriseProject(client *huaweicloud.EpsClient, ctx context.Context, projectId string) *eps.EpDetail {
	return bizutils.LoadFromMap[string, *eps.EpDetail](j.mtx, j.ep, projectId, func() (*eps.EpDetail, error) {
		return client.ShowEnterpriseProject(ctx, projectId)
	})
}
