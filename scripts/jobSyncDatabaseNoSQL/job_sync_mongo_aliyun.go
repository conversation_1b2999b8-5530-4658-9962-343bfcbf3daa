package jobSyncDatabaseNoSQL

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/alibabacloud-go/tea/tea"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	dds "github.com/alibabacloud-go/dds-20151201/v4/client"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
)

const (
	SHARDING  = "sharding"
	REPLICATE = "replicate"
)

var aliyun_product_code_mongo = []string{"dds", "ddssharding"}

type DBInstanceType string

func (insType DBInstanceType) String() string {
	return string(insType)
}

func (j *MongoDBSync) syncAliyunNoSQLInfo(ctx context.Context, cli *aliyun.MongoDBClient) ([]*models.DatabaseInfo, error) {
	describeRegions, err := cli.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionMap := orderedmap.New[string, string]()
	for _, region := range describeRegions {
		if strings.Contains(pointer.String(region.RegionName), "已关停") {
			continue
		}
		regionMap.Store(pointer.Value(region.RegionId), pointer.Value(region.RegionId))
	}
	regions := regionMap.Slice()

	replicateProcess := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	replicateProcess.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		return getMongoInstances(ctx, regionId, cli, j, REPLICATE)
	})
	replicateInstances, err := tools.MergeData(replicateProcess.Outs()...), replicateProcess.Error()
	if err != nil {
		return nil, err
	}

	shardingProcess := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	shardingProcess.Run(regions, func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		return getMongoInstances(ctx, regionId, cli, j, SHARDING)
	})
	shardingInstances, err := tools.MergeData(shardingProcess.Outs()...), shardingProcess.Error()
	if err != nil {
		return nil, err
	}

	return append(replicateInstances, shardingInstances...), nil
}

func getMongoInstances(ctx context.Context, regionId string, cli *aliyun.MongoDBClient, j *MongoDBSync, insType DBInstanceType) ([]*models.DatabaseInfo, error) {
	mongoCli := hybrid.AccountManager().AliyunMongoDBClient(ctx, cli.Name(), cli.Tag(), regionId)
	resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
	f := func(r *dds.DescribeDBInstancesRequest) {
		r.DBInstanceType = tea.String(insType.String())
	}
	mongoDBList, err := mongoCli.DescribeDBInstancesOfAll(ctx, f)
	if err != nil {
		if strings.Contains(err.Error(), "Forbidden.RAM") {
			return nil, nil
		}
		return nil, err
	}
	getMongoAttrProcess := tools.NewBatch[*dds.DescribeDBInstancesResponseBodyDBInstancesDBInstance, []*models.DatabaseInfo](ctx)
	getMongoAttrProcess.Run(mongoDBList, func(ctx context.Context, mongo *dds.DescribeDBInstancesResponseBodyDBInstancesDBInstance) ([]*models.DatabaseInfo, error) {
		databases := make([]*models.DatabaseInfo, 0)
		attr, err := mongoCli.DescribeDBInstanceAttribute(ctx, pointer.Value(mongo.DBInstanceId))
		if err != nil {
			if strings.Contains(err.Error(), timeOutErr) {
				return nil, nil
			}
			return nil, err
		}

		dbInstanceId := pointer.Value(mongo.DBInstanceId)
		var hosts []*MongoHost
		if *attr.DBInstanceType == SHARDING {
			for _, mongo := range attr.MongosList.MongosAttribute {
				hosts = append(hosts, &MongoHost{
					Conn: *mongo.ConnectSting,
					Port: int(*mongo.Port),
				})
			}

			// 查询MongoDB分片集群实例的连接信息
			addrs, err := mongoCli.DescribeShardingNetworkAddress(ctx, dbInstanceId)
			if err != nil {
				return nil, err
			}
			if len(addrs.NetworkAddresses.NetworkAddress) > 0 {
				err = handleDBEndpointForMongoSharding(dbInstanceId, addrs.NetworkAddresses.NetworkAddress)
				if err != nil {
					return nil, err
				}
			}
		} else if *attr.DBInstanceType == REPLICATE {
			for _, replica := range attr.ReplicaSets.ReplicaSet {
				if pointer.Value(replica.ReplicaSetRole) != "Primary" {
					continue
				}
				conn := pointer.Value(replica.ConnectionDomain)
				port, _ := strconv.Atoi(pointer.Value(replica.ConnectionPort))
				hosts = append(hosts, &MongoHost{
					Conn: conn,
					Port: port,
				})
			}

			err = handleDBEndpointForMongoReplicate(dbInstanceId, attr.ReplicaSets.ReplicaSet)
			if err != nil {
				return nil, err
			}
		}

		_, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(attr.ResourceGroupId))
		createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.CreationTime))
		expireAt, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.Value(attr.ExpireTime))
		class := base.GetClassCode(hbc.AliCloud, hbc.MongoDB, pointer.Value(attr.DBInstanceClass))
		// get env from tag
		var env string
		tags, err := mongoCli.ListTagResources(ctx, pointer.Value(attr.DBInstanceId))
		if err != nil {
			return nil, err
		}
		if len(tags.TagResources.TagResource) > 0 {
		outerLoop:
			for _, tag := range tags.TagResources.TagResource {
				switch strings.ToLower(strings.TrimSpace(pointer.Value(tag.TagKey))) {
				case nosql_db_env, nosql_db_env_ch:
					env = pointer.Value(tag.TagValue)
					break outerLoop
				}
			}
		}
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.Value(attr.DBInstanceId), aliyun_product_code_mongo)
		for _, host := range hosts {
			db := &models.DatabaseInfo{
				Vendor:        cli.Vendor(),
				AccountName:   cli.Name(),
				InstanceId:    pointer.Value(attr.DBInstanceId),
				InstanceName:  pointer.Value(attr.DBInstanceDescription),
				InstanceType:  pointer.Value(attr.ProtocolType),
				InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
				Category:      pointer.Value(attr.DBInstanceType),
				Status:        pointer.Value(attr.DBInstanceStatus),
				ClassCode:     pointer.Value(attr.DBInstanceClass),
				ChargeType:    pointer.Value(attr.ChargeType),
				CreationTime:  timeutil.ZeroTime(createAt),
				ExpiredTime:   timeutil.ZeroTime(expireAt),
				Host:          host.Conn,
				Port:          host.Port,
				EngineType:    pointer.Value(attr.Engine),
				EngineVersion: pointer.Value(attr.EngineVersion),
				Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
				Project:       project,
				Env:           env,
				ResourceGroup: pointer.Value(attr.ResourceGroupId),
				Cpu:           class.Cpu,
				Memory:        uint64(class.MemoryGB * 1024),
				DiskSize:      0,
				DiskType:      pointer.Value(attr.StorageType),
				VpcId:         pointer.Value(attr.VPCId),
				SubnetId:      pointer.Value(attr.VSwitchId),
				Region:        pointer.Value(attr.RegionId),
				UniRegionId:   pointer.Value(attr.RegionId),
				Zone:          pointer.Value(attr.ZoneId),
				Description:   pointer.Value(attr.DBInstanceDescription),
				Content:       utils.JsonString(attr),
				AggregatedId:  cmdb.AggregatedID,
			}

			databases = append(databases, db)
		}
		return databases, nil

	})
	return flatten(getMongoAttrProcess.Outs()), getMongoAttrProcess.Error()
}

func flatten(nestedArr [][]*models.DatabaseInfo) []*models.DatabaseInfo {
	var flatArr []*models.DatabaseInfo
	for _, subArr := range nestedArr {
		flatArr = append(flatArr, subArr...)
	}
	return flatArr
}

func (j *MongoDBSync) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](j.mtx, j.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}

type MongoHost struct {
	Conn string
	Port int
}

// MongoDB 分片链接
func handleDBEndpointForMongoSharding(instanceId string, addrs []*dds.DescribeShardingNetworkAddressResponseBodyNetworkAddressesNetworkAddress) error {
	if len(addrs) == 0 {
		return nil
	}
	data := make([]*models.DatabaseEndpoint, 0, len(addrs))
	for _, endpoint := range addrs {
		port, _ := strconv.Atoi(pointer.Value(endpoint.Port))
		data = append(data, &models.DatabaseEndpoint{
			InstanceId:   instanceId,
			EndpointType: pointer.Value(endpoint.Role),
			Host:         pointer.Value(endpoint.NetworkAddress),
			Port:         port,
			Description:  "",
			CreateTime:   timeutil.ZeroTime(time.Now()),
			UpdateTime:   timeutil.ZeroTime(time.Now()),
			NodeId:       pointer.Value(endpoint.NodeId),
		})

	}
	if err := bizutils.CreateOrUpdateEndpoint(context.Background(), data...); err != nil {
		return err
	}
	return nil
}

// MongoDB 副本链接
func handleDBEndpointForMongoReplicate(instanceId string, endpoints []*dds.DescribeDBInstanceAttributeResponseBodyDBInstancesDBInstanceReplicaSetsReplicaSet) error {
	if len(endpoints) == 0 {
		return nil
	}
	data := make([]*models.DatabaseEndpoint, 0, len(endpoints))
	for _, endpoint := range endpoints {
		port, _ := strconv.Atoi(pointer.Value(endpoint.ConnectionPort))
		data = append(data, &models.DatabaseEndpoint{
			InstanceId:   instanceId,
			EndpointType: pointer.Value(endpoint.ReplicaSetRole),
			Host:         pointer.Value(endpoint.ConnectionDomain),
			Port:         port,
			Description:  "",
			CreateTime:   timeutil.ZeroTime(time.Now()),
			UpdateTime:   timeutil.ZeroTime(time.Now()),
		})

	}
	if err := bizutils.CreateOrUpdateEndpoint(context.Background(), data...); err != nil {
		return err
	}
	return nil
}
