package jobSyncDatabaseNoSQL

const nosql_db_env = "env"
const nosql_db_env_ch = "环境"

const (
	TencentRedisType2  = "Redis 2.8内存版（标准架构）"
	TencentRedisType3  = "CKV 3.2内存版（标准架构）"
	TencentRedisType4  = "CKV 3.2内存版（集群架构）"
	TencentRedisType5  = "Redis 2.8内存版（单机）"
	TencentRedisType6  = "Redis 4.0内存版（标准架构）"
	TencentRedisType7  = "Redis 4.0内存版（集群架构）"
	TencentRedisType8  = "Redis 5.0内存版（标准架构）"
	TencentRedisType9  = "Redis 5.0内存版（集群架构）"
	TencentRedisType15 = "Redis 6.2内存版（标准架构）"
	TencentRedisType16 = "Redis 6.2内存版（集群架构）"

	TencentRedisStatusInitializing   = "待初始化"
	TencentRedisStatusInProcess      = "实例在流程中"
	TencentRedisStatusRunning        = "实例运行中"
	TencentRedisStatusIsolated       = "实例已隔离"
	TencentRedisStatusPendingDeleted = "实例待删除"
)
