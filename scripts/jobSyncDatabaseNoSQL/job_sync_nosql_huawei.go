package jobSyncDatabaseNoSQL

import (
	"context"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dcs/v2/model"
	epsModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"
	iamModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	"github.com/pkg/errors"
	"golang.org/x/time/rate"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var huawei_product_code_redis = []string{"hws.service.type.dcs"}

func (n *RedisSync) syncHuaweiNoSQLInfo(client *huaweicloud.IamClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	projects, err := client.ListProjects(ctx)
	if err != nil {
		if strings.Contains(err.Error(), "502") || strings.Contains(err.Error(), "504") {
			return nil, nil
		}
		return nil, bizutils.WarpClientError(client, err)
	}
	regions, err := client.ListRegions(ctx)
	if err != nil {
		if strings.Contains(err.Error(), "502") || strings.Contains(err.Error(), "504") {
			return nil, nil
		}
		return nil, bizutils.WarpClientError(client, err)
	}

	projectBatch := tools.NewBatch[iamModel.ProjectResult, []*models.DatabaseInfo](ctx, batch.Option(batch.WithBatchSize(3)))
	projectBatch.Run(projects, func(ctx context.Context, project iamModel.ProjectResult) ([]*models.DatabaseInfo, error) {
		var targetRegion *iamModel.Region
		for _, region := range regions {
			if region.Id == project.Name {
				targetRegion = &region
				break
			}
		}
		if targetRegion == nil {
			n.Warnf(ctx, "unexpected regionId: %s, skip ...", project.Name)
			return nil, nil
		}
		instances, err := n.getHuaweiRegionNoSQLInfo(ctx, client, *targetRegion, project)
		n.Infof(ctx, "get %d records for all regions", len(instances))
		if err != nil {
			n.Infof(ctx, "huawei account %s, error: %s", client.Name(), err)
			if strings.Contains(err.Error(), "502") || strings.Contains(err.Error(), "504") {
				err = nil
			}
		}
		return instances, err
	})

	return tools.MergeData(projectBatch.Outs()...), bizutils.WarpClientError(client, projectBatch.Error())

	// var infos []*models.DatabaseInfo
	// for _, project := range projects {
	// 	var targetRegion *iamModel.Region
	// 	for _, region := range regions {
	// 		if region.Id == project.Name {
	// 			targetRegion = &region
	// 			break
	// 		}
	// 	}
	// 	if targetRegion == nil {
	// 		n.Warnf(ctx, "unexpected regionId: %s, skip ...", project.Name)
	// 		return nil, nil
	// 	}
	// 	instances, err := n.getHuaweiRegionNoSQLInfo(ctx, client, *targetRegion, project)
	// 	n.Infof(ctx, "get %d records for all regions", len(instances))
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	infos = append(infos, instances...)
	// 	// 处理华为云每分钟请求限制
	// 	// {"status_code":429,"request_id":"18be09109f951640783d308bdfd48331", "error_code":"APIGW.0308",
	// 	// "error_message":"The throttling threshold has been reached: policy user over ratelimit,limit:120,time:1 minute","encoded_authorization_message":""}
	// 	time.Sleep(2 * time.Minute)
	// }

	// return infos, err
}

func (n *RedisSync) handleHuaweiProjectPanic(panicError any, ctx context.Context, region iamModel.Region) error {
	var msg string
	switch pe := panicError.(type) {
	case error:
		msg = pe.Error()
	case string:
		msg = pe
	}

	if strings.HasPrefix(msg, "unexpected regionId") {
		n.Warnf(ctx, "%s getHuaweiRegionNoSQL: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, "failed to get project id, No project id found") {
		n.Warnf(ctx, "%s getHuaweiRegionNoSQL: %s", region.Id, msg)
	} else if strings.Contains(msg, "is not in the following supported regions of service") {
		n.Warnf(ctx, "%s getHuaweiRegionNoSQL: %s", region.Id, msg)
	} else {
		return errors.New(msg)
	}
	return nil
}

func (n *RedisSync) getHuaweiRegionNoSQLInfo(
	ctx context.Context,
	client client.IClient,
	region iamModel.Region,
	project iamModel.ProjectResult,
) (nosqlList []*models.DatabaseInfo, err error) {
	defer func() {
		// 当RDS获取到非预期的region时会发生panic
		if panicError := recover(); panicError != nil {
			unsupportedRegionErr := n.handleHuaweiProjectPanic(panicError, ctx, region)
			if unsupportedRegionErr != nil {
				n.Logger().Error(ctx, unsupportedRegionErr)
			}
		}
	}()

	nosqlCli := hybrid.AccountManager().HuaweiNoSQLClient(ctx, client.Name(), client.Tag(), region.Id, project.Id)
	epsClient := hybrid.AccountManager().HuaweiEpsClient(ctx, client.Name(), client.Tag(), region.Id)

	if nosqlCli == nil {
		return nil, nil
	}

	instances, listErr := nosqlCli.ListInstancesOfAll()
	if listErr != nil {
		n.Errorf(ctx, "getHuaweiRegionNoSQLInfo: %s, project %s", listErr, utils.JsonString(project))
		return nil, nil
	}

	nosqlList = make([]*models.DatabaseInfo, len(instances))
	// 2 requests every 3 seconds
	limiter := rate.NewLimiter(rate.Limit(2.0/3.0), 2)
	for i, ins := range instances {
		// 限流
		if err := limiter.Wait(ctx); err != nil {
			return nil, err
		}

		attr, err := nosqlCli.ShowInstance(*ins.InstanceId)
		if err != nil {
			return nil, err
		}

		_, scode, pStr := bizutils.ParseHuaweiEnvProject(ctx, pointer.Value(attr.Name), pointer.Value(attr.EnterpriseProjectId), epsClient, n)
		creationTime, _ := time.Parse(bizutils.HuaweiRdsNoSqlTimeFormat, pointer.Value(ins.CreatedAt))
		host := pointer.Value(ins.Ip)
		domainName := pointer.String(attr.DomainName)
		if len(domainName) > 0 {
			host = domainName
		}
		// get env from tag
		var env string
		if len(pointer.Value(ins.Tags)) > 0 {
		outerLoop:
			for _, tag := range *ins.Tags {
				switch strings.ToLower(strings.TrimSpace(tag.Key)) {
				case nosql_db_env, nosql_db_env_ch:
					env = pointer.Value(tag.Value)
					break outerLoop
				}
			}
		}
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(client.Vendor()), client.Identifier(), pointer.Value(ins.InstanceId), huawei_product_code_redis)
		nosqlList[i] = &models.DatabaseInfo{
			Model:             n.Model(ctx),
			Vendor:            client.Vendor(),
			AccountName:       client.Name(),
			InstanceId:        pointer.Value(ins.InstanceId),
			InstanceName:      pointer.Value(ins.Name),
			InstanceType:      pointer.Value(ins.CpuType),
			InstanceRole:      "Master",
			Category:          attr.ProductType.Value(),
			PrimaryInstanceId: "",
			HostInsId:         "",
			Status:            pointer.Value(ins.Status),
			ClassCode:         pointer.Value(ins.SpecCode),
			ChargeType:        parseHuaweiChargeType(ins),
			ResourceGroup:     pointer.Value(ins.EnterpriseProjectId),
			CreationTime:      timeutil.ZeroTime(creationTime),
			ExpiredTime:       nil,
			Host:              host,
			Port:              int(pointer.Value(ins.Port)),
			EngineType:        pointer.Value(ins.Engine),
			EngineVersion:     pointer.Value(ins.EngineVersion),
			Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
			Project:           pStr,
			Env:               env,
			Cpu:               0,
			Memory:            uint64(pointer.Value(ins.MaxMemory)),
			DiskSize:          float64(pointer.Value(ins.Capacity) * 1024),
			DiskType:          "",
			VpcId:             pointer.Value(ins.VpcId),
			SubnetId:          pointer.Value(ins.SubnetId),
			Region:            nosqlCli.Region(),
			Zone:              (pointer.Value(ins.AzCodes))[0],
			Description:       pointer.Value(ins.Description),
			Content:           utils.JsonString(ins),
			PrivateIp:         pointer.Value(ins.Ip),
			AggregatedId:      cmdb.AggregatedID,
		}
	}

	return nosqlList, nil
}

func parseHuaweiChargeType(ins model.InstanceListInfo) string {
	switch pointer.Value(ins.ChargingMode) {
	case 0:
		return "Postpaid"
	default:
		return "Prepaid"
	}
}

func (n *RedisSync) GetHuaweiEnterpriseProject(client *huaweicloud.EpsClient, ctx context.Context, projectId string) *epsModel.EpDetail {
	if client == nil {
		return nil
	}
	return bizutils.LoadFromMap[string, *epsModel.EpDetail](n.mtx, n.ep, projectId, func() (*epsModel.EpDetail, error) {
		return client.ShowEnterpriseProject(ctx, projectId)
	})
}
