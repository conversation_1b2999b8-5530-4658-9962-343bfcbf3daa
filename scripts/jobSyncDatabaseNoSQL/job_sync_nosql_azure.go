package jobSyncDatabaseNoSQL

import (
	"context"
	"strings"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v2"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/redis/armredis/v2"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
)

var azure_product_code_redis = []string{"microsoft.cacheredis", "microsoft.networkprivateendpoints"}

func (n *RedisSync) syncAzureNoSQLInfo(client *azure.RedisClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	peClient := hybrid.AccountManager().AzurePrivateEndpointsClient(ctx, client.Name(), client.Tag(), client.Region(), client.TenantId(), client.Identifier())

	resources, err := client.NewListBySubscriptionPager(ctx)
	if err != nil {
		if strings.Contains(err.Error(), "404 Not Found") {
			return nil, nil
		}
		n.Errorf(ctx, "NewListBySubscriptionPager error: %v", err)
		return nil, err
	}

	b := tools.NewBatch[*armredis.ResourceInfo, []*models.DatabaseInfo](ctx)
	b.Run(resources, func(ctx context.Context, r *armredis.ResourceInfo) ([]*models.DatabaseInfo, error) {
		scode, env, project := ParseScodeAndEnvAndProject(r)
		resourceGroupName, serverName := GetResourceGroupAndServerName(*r.ID)

		var vpcId, subnetId string
		privateEndpointConnections := r.Properties.PrivateEndpointConnections

		if len(privateEndpointConnections) > 0 {
			id := *privateEndpointConnections[0].Properties.PrivateEndpoint.ID
			groupName, privateEndpoint := ParseResourceGroupAndPrivateEndpoint(id)

			pe, peErr := peClient.Get(ctx, groupName, privateEndpoint)
			if peErr != nil {
				n.Errorf(ctx, "syncAzureNoSQLInfo  PrivateEndpoint error: %v", err)
				return nil, err
			}
			vpcId, subnetId = ParseVpcIdAndSubnetIdFromPrivateEndpoint(pe)
		}
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(client.Vendor()), client.Identifier(), serverName, azure_product_code_redis)
		nosqlList := make([]*models.DatabaseInfo, 0)
		nosqlList = append(nosqlList, &models.DatabaseInfo{
			Model:         n.Model(ctx),
			Vendor:        client.Vendor(),
			AccountName:   client.Name(),
			InstanceId:    serverName,
			InstanceName:  *r.Name,
			InstanceType:  *r.Type,
			InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
			CreationTime:  nil,
			ExpiredTime:   nil,
			Category:      jobSyncDatabaseRds.RdsCategoryCluster,
			HostInsId:     "",
			Status:        string(*r.Properties.ProvisioningState),
			ClassCode:     string(*r.Properties.SKU.Name),
			ChargeType:    "",
			Host:          *r.Properties.HostName,
			Port:          int(*r.Properties.Port),
			EngineType:    bizutils.DBTypeRedis,
			EngineVersion: *r.Properties.RedisVersion,
			Region:        *r.Location,
			Zone:          *r.Location,
			Project:       project,
			Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
			Env:           env,
			VpcId:         vpcId,
			SubnetId:      subnetId,
			ResourceGroup: resourceGroupName,
			Content:       utils.JsonString(r),
			AggregatedId:  cmdb.AggregatedID,
		})
		return nosqlList, err

	})

	return tools.MergeData(b.Outs()...), bizutils.WarpClientError(client, b.Error())
}

func ParseScodeAndEnvAndProject(r *armredis.ResourceInfo) (scode string, env string, project string) {
	if r.Tags == nil {
		return
	}
	scode = pointer.String(r.Tags["SCode"])
	project = pointer.String(r.Tags["Project"])
	env = pointer.String(r.Tags[nosql_db_env])
	if env == "" {
		env = pointer.String(r.Tags[nosql_db_env_ch])
	}

	return
}

func GetResourceGroupAndServerName(id string) (string, string) {
	var groupName, serverName string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			groupName = s[n+1]
		}
		if k == "Redis" {
			serverName = s[n+1]
		}
	}
	return groupName, serverName
}

func ParseResourceGroupAndPrivateEndpoint(id string) (string, string) {
	var groupName, privateEndpoint string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			groupName = s[n+1]
		}
		if k == "privateEndpoints" {
			privateEndpoint = s[n+1]
		}
	}
	return groupName, privateEndpoint
}

func ParseVpcIdAndSubnetIdFromPrivateEndpoint(e *armnetwork.PrivateEndpoint) (string, string) {
	var vpcId, subnetId string

	s := strings.Split(*e.Properties.Subnet.ID, "/")
	for n, k := range s {
		if k == "virtualNetworks" {
			vpcId = s[n+1]
		}
		if k == "subnets" {
			subnetId = s[n+1]
		}
	}
	return vpcId, subnetId
}
