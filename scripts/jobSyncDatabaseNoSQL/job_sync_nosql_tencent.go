package jobSyncDatabaseNoSQL

import (
	"context"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	redis "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/redis/v20180412"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var tencent_product_code_redis = []string{"p_redis"}

func (n *RedisSync) syncTencentNoSQLInfo(client *tencentcloud.EcsClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	regions, err := client.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionsIds := orderedmap.New[string, string]()
	for _, region := range regions {
		regionsIds.Store(pointer.String(region.Region), pointer.String(region.Region))
	}
	b := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, region string) ([]*models.DatabaseInfo, error) {
		nosqlClient := hybrid.AccountManager().TencentNoSQLClient(ctx, client.Name(), client.Tag(), region)
		rdsInfos, err := n.syncTencentRegionNoSQLInfo(nosqlClient, ctx)
		if err != nil {
			terr, ok := err.(*errors.TencentCloudSDKError)
			if !ok || terr.Code != "UnsupportedRegion" {
				return nil, err
			}
		}

		return rdsInfos, err
	})

	return tools.MergeData(b.Outs()...), bizutils.WarpClientError(client, b.Error())
}

func (n *RedisSync) syncTencentRegionNoSQLInfo(client *tencentcloud.RedisClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	instances, err := client.DescribeInstancesOfAll(ctx)
	if err != nil {
		n.Errorf(ctx, "DescribeInstancesOfAll error: %v", err.Error())
		te, ok := err.(*errors.TencentCloudSDKError)
		if ok && te.Code == "UnsupportedRegion" {
			return nil, nil
		}
		return nil, err
	}
	rdsInfos := make([]*models.DatabaseInfo, len(instances))
	for i, ins := range instances {
		env, scode, project := tryParseTencentNoSQLEnvProjects(ins)
		zoneId := tryParseZoneInfoOfMasterNode(ctx, client, ins)
		creationTime, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.String(ins.Createtime))
		expiredTime, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.String(ins.DeadlineTime))
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(client.Vendor()), client.Identifier(), pointer.String(ins.InstanceId), tencent_product_code_redis)
		rdsInfos[i] = &models.DatabaseInfo{
			Model:             n.Model(ctx),
			Vendor:            client.Vendor(),
			AccountName:       client.Name(),
			InstanceId:        *ins.InstanceId,
			InstanceName:      *ins.InstanceName,
			InstanceType:      getInstanceType(ins),
			InstanceRole:      "",
			Category:          *ins.ProductType,
			PrimaryInstanceId: "",
			HostInsId:         *ins.WanIp,
			Status:            getInstanceStatus(ins),
			ClassCode:         "",
			ChargeType:        getChargeType(ins),
			CreationTime:      timeutil.ZeroTime(creationTime),
			ExpiredTime:       timeutil.ZeroTime(expiredTime),
			Host:              *ins.WanIp,
			Port:              int(*ins.Port),
			EngineType:        *ins.Engine,
			EngineVersion:     "",
			ResourceGroup:     scode,
			Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
			Project:           project,
			Env:               env,
			Cpu:               0,
			Memory:            uint64(*ins.Size),
			DiskSize:          0,
			DiskType:          "",
			VpcId:             *ins.UniqVpcId,
			SubnetId:          *ins.UniqSubnetId,
			DgDomain:          "",
			DgId:              "",
			Region:            client.Region(),
			Zone:              zoneId,
			Description:       "",
			Content:           utils.JsonString(ins),
			PublicIp:          pointer.Value(ins.WanIp),
			AggregatedId:      cmdb.AggregatedID,
		}
	}
	return rdsInfos, err
}

func getChargeType(ins *redis.InstanceSet) string {
	switch *ins.BillingMode {
	case 0:
		return "按量计费"
	case 1:
		return "包年包月"
	default:
		return ""

	}
}

func getInstanceStatus(ins *redis.InstanceSet) string {
	switch *ins.Status {
	case 0:
		return TencentRedisStatusInitializing
	case 1:
		return TencentRedisStatusInProcess
	case 2:
		return TencentRedisStatusRunning
	case -2:
		return TencentRedisStatusIsolated
	case -3:
		return TencentRedisStatusPendingDeleted
	default:
		return ""
	}
}

func getInstanceType(ins *redis.InstanceSet) string {
	switch *ins.Type {
	case 2:
		return TencentRedisType2
	case 3:
		return TencentRedisType3
	case 4:
		return TencentRedisType4
	case 5:
		return TencentRedisType5
	case 6:
		return TencentRedisType6
	case 7:
		return TencentRedisType7
	case 8:
		return TencentRedisType8
	case 9:
		return TencentRedisType9
	case 15:
		return TencentRedisType15
	case 16:
		return TencentRedisType16
	default:
		return ""
	}
}

func tryParseTencentNoSQLEnvProjects(instance *redis.InstanceSet) (env, code, project string) {
	for _, tag := range instance.InstanceTags {
		if pointer.String(tag.TagKey) != "腾讯云标签" {
			continue
		}
		tmp := strings.Split(pointer.String(tag.TagValue), "_")
		if strings.HasPrefix(strings.ToLower(tmp[0]), "s") {
			code = tmp[0]
		}
		if len(tmp) > 1 {
			if strings.HasPrefix(strings.ToLower(tmp[0]), "s") {
				code = tmp[0]
				project = tmp[1]
			} else if strings.HasPrefix(strings.ToLower(tmp[1]), "s") {
				code = tmp[1]
				project = tmp[0]
			}
		}
		break
	}

outerLoop:
	for _, tag := range instance.InstanceTags {
		switch strings.ToLower(strings.TrimSpace(pointer.String(tag.TagKey))) {
		case nosql_db_env, nosql_db_env_ch:
			env = pointer.String(tag.TagValue)
			break outerLoop
		}
	}
	if env == "" {
		env = bizutils.TryParseEnv(pointer.String(instance.InstanceName))
	}
	return
}

func tryParseZoneInfoOfMasterNode(ctx context.Context, client *tencentcloud.RedisClient, instance *redis.InstanceSet) string {
	group, err := client.DescribeInstanceZoneInfo(ctx, *instance.InstanceId)
	if err != nil {
		return ""
	}
	return *group.ZoneId
}
