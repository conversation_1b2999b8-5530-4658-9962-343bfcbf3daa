package jobSyncDatabaseNoSQL

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/css/v1/model"
	iamModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
)

var huawei_product_code_es = []string{"hws.service.type.ess"}

func (j *JobSyncES) syncHuaweiEsInfo(ctx context.Context, defClient *huaweicloud.IamClient) ([]*models.DatabaseInfo, error) {
	projects, err := defClient.ListProjects(ctx)
	if err != nil {
		return nil, err
	}
	regions, err := defClient.ListRegions(ctx)
	if err != nil {
		return nil, err
	}

	projectBatch := tools.NewBatch[iamModel.ProjectResult, []*models.DatabaseInfo](ctx)
	projectBatch.Run(projects, func(ctx context.Context, project iamModel.ProjectResult) ([]*models.DatabaseInfo, error) {
		var targetRegion *iamModel.Region
		for _, region := range regions {
			if region.Id == project.Name {
				targetRegion = &region
				break
			}
		}
		if targetRegion == nil {
			j.Warnf(ctx, "unexpected regionId: %s, skip ...", project.Name)
			return nil, nil
		}
		infoList, err := j.getHuaweiRegionES(ctx, defClient, *targetRegion, project)
		j.Infof(ctx, "get %d records for all regions", len(infoList))
		return infoList, err
	})

	return tools.MergeData(projectBatch.Outs()...), projectBatch.Error()
}

func (j *JobSyncES) handleHuaweiProjectPanic(panicError any, ctx context.Context, region iamModel.Region) error {
	var msg string
	switch pe := panicError.(type) {
	case error:
		msg = pe.Error()
	case string:
		msg = pe
	}

	if strings.HasPrefix(msg, "unexpected regionId") {
		j.Logger().Warnf(ctx, "%s getHuaweiRegionES: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, "failed to get project id, No project id found") {
		j.Logger().Warnf(ctx, "%s getHuaweiRegionES: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, fmt.Sprintf("region id '%s' is not in the following supported regions of service 'CSS'", region.Id)) {
		j.Logger().Warnf(ctx, "%s getHuaweiRegionES: %s", region.Id, msg)
	} else {
		return errors.New(msg)
	}
	return nil
}

func (j *JobSyncES) getHuaweiRegionES(ctx context.Context, defClient client.IClient, region iamModel.Region, project iamModel.ProjectResult) (infoList []*models.DatabaseInfo, err error) {
	defer func() {
		// 获取到非预期的region时会发生panic
		if panicError := recover(); panicError != nil {
			err = j.handleHuaweiProjectPanic(panicError, ctx, region)
		}
	}()

	cssClient := hybrid.AccountManager().HuaweiCssClient(ctx, defClient.Name(), defClient.Tag(), region.Id, project.Id)
	if cssClient == nil {
		return nil, nil
	}
	instanceList, listServerError := cssClient.ListClustersDetailsOfAll()
	if listServerError != nil {
		j.Logger().Errorf(ctx, "getHuaweiRegionES: %s, project: %s", listServerError, utils.JsonString(project))
		return nil, nil
	}

	epsClient := hybrid.AccountManager().HuaweiEpsClient(ctx, defClient.Name(), defClient.Tag(), region.Id)
	process := tools.NewBatch[model.ClusterList, *models.DatabaseInfo](ctx)
	process.Run(instanceList, func(ctx context.Context, input model.ClusterList) (*models.DatabaseInfo, error) {
		return j.tranceHuaweiRds2RdsInfo(ctx, cssClient, epsClient, input)
	})
	return process.Outs(), process.Error()
}

func (j *JobSyncES) tranceHuaweiRds2RdsInfo(
	ctx context.Context,
	cssClient *huaweicloud.CssClient,
	epsClient *huaweicloud.EpsClient,
	es model.ClusterList,
) (*models.DatabaseInfo, error) {
	esInfo, err := cssClient.ShowClusterDetail(pointer.Value(es.Id))
	if err != nil {
		return nil, err
	}

	_, scode, project := bizutils.ParseHuaweiEnvProject(ctx, pointer.Value(es.Name), pointer.Value(es.EnterpriseProjectId), epsClient, j)
	creationTime, _ := time.Parse(bizutils.HuaweiEcsTimeFormat, pointer.Value(esInfo.Created))
	var classCode string
	var zones []string
	for _, node := range pointer.Value(esInfo.Instances) {
		classCode = pointer.Value(node.SpecCode)
		zones = append(zones, pointer.Value(node.AzCode))
	}
	cpu, mem := bizutils.ParseCPUFromHuaweiSpec(classCode)

	// get env from tag
	var env string
	if len(*es.Tags) > 0 {
	outerLoop:
		for _, tag := range *es.Tags {
			switch strings.ToLower(strings.TrimSpace(pointer.Value(tag.Key))) {
			case nosql_db_env, nosql_db_env_ch:
				env = pointer.Value(tag.Value)
				break outerLoop
			}
		}
	}
	contextInfo := map[string]any{
		"es":      es,
		"es_info": esInfo,
	}
	cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cssClient.Vendor()), cssClient.Identifier(), pointer.Value(es.Id), huawei_product_code_es)
	return &models.DatabaseInfo{
		Model:         j.Model(ctx),
		Vendor:        cssClient.Vendor(),
		AccountName:   cssClient.Name(),
		InstanceId:    pointer.Value(es.Id),
		InstanceName:  pointer.Value(es.Name),
		InstanceType:  common.RDSTypeNormal,
		InstanceRole:  common.RdsRoleMaster,
		Category:      common.RdsCategoryCluster,
		ResourceGroup: pointer.Value(es.EnterpriseProjectId),
		Status:        pointer.Value(es.Status),
		ClassCode:     classCode,
		CreationTime:  timeutil.ZeroTime(creationTime),
		ExpiredTime:   nil,
		Host:          pointer.Value(esInfo.Endpoint),
		Port:          9200,
		EngineType:    pointer.Value(esInfo.Datastore.Type),
		EngineVersion: pointer.Value(esInfo.Datastore.Version),
		Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
		Project:       project,
		Env:           env,
		Cpu:           cpu,
		Memory:        uint64(mem) * 1024,
		VpcId:         pointer.Value(es.VpcId),
		SubnetId:      pointer.Value(es.SubnetId),
		Region:        cssClient.Region(),
		Zone:          strings.Join(zones, ","),
		Content:       utils.JsonString(contextInfo),
		AggregatedId:  cmdb.AggregatedID,
	}, nil
}
