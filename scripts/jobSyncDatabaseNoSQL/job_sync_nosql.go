package jobSyncDatabaseNoSQL

import (
	"context"
	"sync"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

const region_moscow = "ru-moscow-1"

func NewSyncKvStore() *RedisSync {
	c := config.Global()
	return &RedisSync{
		mtx: new(sync.Mutex),
		rg:  orderedmap.New[string, *resourcemanager.ResourceGroup](),
		ep:  orderedmap.New[string, *eps.EpDetail](),
		JobBase: base.NewJobBase("REDIS_SYNC",
			"同步Redis资源数据",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type RedisSync struct {
	rg  *orderedmap.OrderedMap[string, *resourcemanager.ResourceGroup]
	ep  *orderedmap.OrderedMap[string, *eps.EpDetail]
	mtx *sync.Mutex
	*base.JobBase
}

func (n *RedisSync) Name() string {
	return base.TaskCloudSyncRedis
}

func (n *RedisSync) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(n.Model(ctx), hbc.GoogleCloud, hbc.Private)
	if err != nil {
		return nil, err
	}

	defaultClients, err := base.GetDefaultIClients(ctx, vendors,
		hbc.Redis, []string{bizutils.PurposeAdmin},
		actfilter.SetCustomProduct(hbc.AWS, hbc.ECS),                      // aws默认使用ecs client，获取地区
		actfilter.SetCustomProduct(hbc.HuaweiCloud, hbc.IAM),              // 华为云默认client使用 IAM client
		actfilter.SetCustomProduct(hbc.TencentCloud, hbc.ECS),             // 腾讯云默认client使用 Ecs client
		actfilter.SetCustomPurpose(hbc.AWS, bizutils.PurposeAccountCheck), // AWS复用 ACCOUNT_CHECK 的账号
		actfilter.SetIgnoreVendor(hbc.Private),
	)
	if err != nil {
		return nil, err
	}
	// 增加华为云莫斯科地区client
	for _, c := range defaultClients {
		if c.Name() == "hr690g" && c.Vendor() == hbc.HuaweiCloud {
			defClient := hybrid.AccountManager().HuaweiIamClient(ctx, c.Name(), c.Tag(), region_moscow)
			defaultClients = append(defaultClients, defClient)
			break
		}
	}
	b := tools.NewBatch[client.IClient, []*models.DatabaseInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.DatabaseInfo, error) {
		switch defaultClient := input.(type) {
		case *aliyun.KvStoreClient:
			return n.syncAliyunNoSQLInfo(defaultClient, ctx)
		case *huaweicloud.IamClient:
			return n.syncHuaweiNoSQLInfo(defaultClient, ctx)
		case *tencentcloud.EcsClient:
			return n.syncTencentNoSQLInfo(defaultClient, ctx)
		case *aws.Ec2Client:
			return n.syncAwsNoSQLInfo(defaultClient, ctx)
		case *azure.RedisClient:
			return n.syncAzureNoSQLInfo(defaultClient, ctx)
		}
		return nil, nil
	})

	if err := b.Error(); err != nil {
		n.Errorf(ctx, "sync nosql failed, cause: %s", err)
		return nil, err
	}

	// 将信息存入数据库
	return nil, bizutils.CreateOrUpdateRds(ctx, tools.MergeData(b.Outs()...)...)
}
