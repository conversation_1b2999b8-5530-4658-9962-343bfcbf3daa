package jobCleanupConfig

import (
	"context"
	"fmt"
	"reflect"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ecs"
	"gorm.io/gorm"
)

var (
	keyFormat      = "%s%s%s%s"
	defaultRegions = map[hbc.CloudVendor]string{
		hbc.AliCloud:             "cn-qingdao",
		hbc.HuaweiCloud:          "cn-east-3",
		hbc.HuaweiCloudDedicated: "unused",
		hbc.TencentCloud:         "ap-beijing",
		hbc.GoogleCloud:          "FOREIGN",
		hbc.AWS:                  "ap-southeast-1",
		hbc.OracleCloud:          "ap-singapore-1",
		hbc.Azure:                "FOREIGN",
		hbc.AliCloudDedicated:    "cn-qingdao",
	}
	cleanUpModels = []models.DBModel{
		new(models.HostInfo),
		// new(models.DatabaseInfo),
	}
	duration = 48 * time.Hour
	// key: vendor + account_name + region + client : aliyun + account_name + cn-qingdao + ecs
	clientDict = make(map[string][]client.IClient, 0)
)

var whereSql = fmt.Sprintf("vendor in ('%s','%s','%s','%s') and is_deleted = '0' and sdk_client_name != '' and update_time <= ? ",
	hbc.AliCloud, hbc.HuaweiCloud, hbc.AliCloudDedicated, hbc.HuaweiCloudDedicated)

func Handle(ctx context.Context) error {
	deleteInstances := []models.DBModel{}
	if err := clients(ctx); err != nil {
		return err
	}
	expire, err := expireList(ctx)
	if err != nil {
		return err
	}

	for _, m := range expire {
		switch list := m.(type) {
		case []*models.HostInfo:
			deleteInstances = append(deleteInstances, catchExpire(list)...)
		case []*models.DatabaseInfo:
			deleteInstances = append(deleteInstances, catchExpire(list)...)
		}
	}
	// fmt.Println(utils.JsonString(deleteInstances))
	return update(ctx, deleteInstances)
}

func clients(ctx context.Context) error {
	resources, err := groupSDK(ctx)
	if err != nil {
		return err
	}

	// init client dict
	for _, resource := range resources {
		switch list := resource.(type) {
		case []*models.HostInfo:
			if err := catchClients(ctx, list); err != nil {
				return err
			}
		case []*models.DatabaseInfo:
			if err := catchClients(ctx, list); err != nil {
				return err
			}
		}
	}
	return nil
}

// 匹配相应的sdk客户端
func catchClients[T models.DBModel](ctx context.Context, list []T) error {
	for _, r := range list {
		if err := pick(ctx, r); err != nil {
			return err
		}
	}
	return nil
}

// 执行检查资源是否存在
func do(instanceId, key string) bool {
	var exists []bool
	for _, c := range clientDict[key] {
		switch defClient := c.(type) {
		case *aliyun.EcsClient:
			res, err := defClient.DescribeInstances(func(e *ecs.DescribeInstancesRequest) { e.InstanceIds = fmt.Sprintf("['%s']", instanceId) })
			if err == nil && len(res) > 0 {
				exists = append(exists, true)
			}
		case *huaweicloud.EcsClient:
			res, err := defClient.ShowServer(instanceId)
			if err == nil && res.Id == instanceId {
				exists = append(exists, true)
			}
		case *huaweicloud.DedicatedClient:
			if token, err := defClient.GetHuaweiOauthToken(); err == nil {
				if ecs, err := defClient.GetHuaweiDedicatedEcs(token, 1, 1, instanceId); err == nil {
					if ecs.TotalNum > 0 {
						exists = append(exists, true)
					}
				}
			}
		}
	}
	// 是否删除;true删除,false不删除
	for _, b := range exists {
		if b {
			return true
		}
	}
	return false
}

// 根据sdk_name匹配相应的官方客户端
func pick(ctx context.Context, m models.DBModel) error {
	switch model := m.(type) {
	// host
	case *models.HostInfo:
		if model.Region == "" {
			model.Region = defaultRegions[model.Vendor]
		}
		key := fmt.Sprintf(keyFormat, string(model.Vendor), model.AccountName, model.SDKClientName, model.Region)

		// 云厂商
		switch model.Vendor {
		// 公有云
		case hbc.AliCloud:
			switch model.SDKClientName {
			case string(hbc.ECS):
				clientDict[key] = []client.IClient{hybrid.AccountManager().AliyunEcsClient(ctx, model.AccountName, bizutils.PurposeAdmin, model.Region)}
			}
		case hbc.HuaweiCloud:
			switch model.SDKClientName {
			case string(hbc.ECS):
				clientDict[key] = []client.IClient{hybrid.AccountManager().HuaweiEcsClient(ctx, model.AccountName, bizutils.PurposeAdmin, model.Region, "")}
			}
		// 专属云
		case hbc.AliCloudDedicated:
			switch model.SDKClientName {
			case string(hbc.ECS):
				clientDict[key] = []client.IClient{hybrid.AccountManager().AliyunDedicatedEcsClient(ctx, model.AccountName, bizutils.PurposeDedicated, model.Region)}
			}
		case hbc.HuaweiCloudDedicated:
			switch model.SDKClientName {
			case string(hbc.ECS):
				defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.HuaweiCloudDedicated}, hbc.ECS,
					[]string{bizutils.PurposeDedicated}, actfilter.SetCustomProduct(hbc.HuaweiCloudDedicated, hbc.HuaweiDedicated),
				)
				if err != nil {
					return err
				}
				for _, c := range defaultClients {
					if c.Name() == model.AccountName {
						defClient := defaultClients[0].(*huaweicloud.DedicatedClient)
						clientDict[key] = []client.IClient{defClient}
						break
					}
				}
			}
		}
	// rds
	case *models.DatabaseInfo:
	}
	return nil
}

// 过期数据使用相应的sdk客户端检查
func catchExpire[T models.DBModel](list []T) []models.DBModel {
	var deleteInstances []models.DBModel
	key := func(v, a, s, r string) string {
		return fmt.Sprintf(keyFormat, v, a, s, r)
	}
	for _, r := range list {
		dbModel := catchDBModel(r)
		if !do(dbModel.InstanceId, key(dbModel.Vendor, dbModel.AccountName, dbModel.SDKClientName, dbModel.Region)) {
			deleteInstances = append(deleteInstances, r)
		}
	}
	return deleteInstances
}

// update is_deleted & save history
func update(ctx context.Context, deleteInstances []models.DBModel) error {
	// result := map[string]any{}
	model := bizutils.DataSource().Model(ctx)
	for _, instance := range deleteInstances {
		model.Orm().Transaction(func(tx *gorm.DB) error {
			res := tx.Model(instance).Where("id= ? and is_deleted = '0'", catchDBModel(instance).Id).Updates(map[string]interface{}{"is_deleted": true, "delete_time": time.Now()})
			if res.Error != nil {
				return res.Error
			}
			log.Infof(ctx, "clean up table %s done, %d rows deleted", instance.TableName(), res.RowsAffected)

			// save history
			var err error
			switch data := instance.(type) {
			case *models.HostInfo:
				err = bizutils.SaveHostInfoHistory(ctx, tx, data, bizutils.DelOperaTypeDelete)
			case *models.DatabaseInfo:
			}
			return err
		})
	}
	return nil
}

// 聚合统计sdk和region超过48小时的sdk客户端
func groupSDK(ctx context.Context) ([]any, error) {
	data := make([]any, 0)
	model := bizutils.DataSource().Model(ctx)
	for _, m := range cleanUpModels {
		resources := reflect.MakeSlice(reflect.SliceOf(reflect.TypeOf(m)), 0, 0).Interface()
		if err := model.Orm().Model(&m).
			Where(whereSql+"group by vendor,account_name,sdk_client_name,region", time.Now().Add(-1*duration)).
			Find(&resources).Error; err != nil {
			return nil, err
		}
		data = append(data, resources)
	}
	return data, nil
}

// 超过48小时未更新的资源数据
func expireList(ctx context.Context) ([]any, error) {
	data := make([]any, 0)
	model := bizutils.DataSource().Model(ctx)
	for _, m := range cleanUpModels {
		resources := reflect.MakeSlice(reflect.SliceOf(reflect.TypeOf(m)), 0, 0).Interface()
		if err := model.Orm().Model(&m).
			Where(whereSql, time.Now().Add(-1*duration)).Find(&resources).Error; err != nil {
			return nil, err
		}
		data = append(data, resources)
	}
	return data, nil
}

type dbModel struct {
	Id            int64
	Vendor        string
	AccountName   string
	SDKClientName string
	Region        string
	InstanceId    string
}

func catchDBModel(model models.DBModel) dbModel {
	getDbModel := func(id int64, vendor, accountName, sdkClientName, region, instanceId string) dbModel {
		return dbModel{
			Id:            id,
			Vendor:        vendor,
			AccountName:   accountName,
			Region:        region,
			InstanceId:    instanceId,
			SDKClientName: sdkClientName,
		}
	}
	switch m := model.(type) {
	case *models.HostInfo:
		return getDbModel(m.ID, string(m.Vendor), m.AccountName, m.SDKClientName, m.Region, m.InstanceId)
	case *models.DatabaseInfo:
		return getDbModel(m.ID, string(m.Vendor), m.AccountName, "", m.Region, m.InstanceId)
	case *models.MiddlewareInfo:
		return getDbModel(m.ID, string(m.Vendor), m.AccountName, "", m.Region, m.InstanceId)
	}
	return dbModel{}
}

// cloudAccount := bizutils.GetCloudAccount(ctx, hbc.AWS.String(), raw.AccountID)
// cli := aws.NewEc2Client(region, cloudAccount.AccountName, cloudAccount.AccessKey, cloudAccount.AccessKeySecret, cloudAccount.PurposeType, false)
