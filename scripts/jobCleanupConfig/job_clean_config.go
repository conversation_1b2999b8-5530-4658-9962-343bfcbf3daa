package jobCleanupConfig

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/hbc/account"
	"git.haier.net/devops/ops-golang-common/hcam"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils"
)

var (
	clientCache = make(map[string]client.IClient)
	clientLock  = new(sync.Mutex)
)

type CleanupConfig struct {
	logger *log.Logger
}

func NewCleanupConfig() *CleanupConfig {
	return &CleanupConfig{}
}

func (c *CleanupConfig) config(ctx context.Context) ([]models.CleanupConfig, error) {
	m := bizutils.DataSource().Model(ctx)
	configs := make([]models.CleanupConfig, 0)
	err := m.Orm().Model(&models.CleanupConfig{}).Where("is_enabled='1'").Find(&configs).Error
	if err != nil {
		c.logger.Errorf(ctx, "get cleanup configs error: %v", err)
		return configs, err
	}
	return configs, nil
}

type CleanupData struct {
	Host       []models.HostInfo       `json:"host"`
	RDS        []models.DatabaseInfo   `json:"rds"`
	Middleware []models.MiddlewareInfo `json:"middleware"`
}

func (c *CleanupConfig) FindInstanceByCleanupKind(ctx context.Context) (d CleanupData, err error) {
	configs, err := c.config(ctx)
	if err != nil {
		return d, err
	}

	mod := bizutils.DataSource().Model(ctx)
	for _, conf := range configs {
		switch conf.Kind {
		case "ecs":
			data, err := c.getHostList(ctx, mod, conf)
			if err != nil {
				return d, err
			}
			d.Host = append(d.Host, data...)
		case "rds":
			data, err := c.getDatabaseList(ctx, mod, conf)
			if err != nil {
				return d, err
			}
			d.RDS = append(d.RDS, data...)
		case "middleware":
			data, err := c.getMiddlewareList(ctx, mod, conf)
			if err != nil {
				return d, err
			}
			d.Middleware = append(d.Middleware, data...)
		}
	}

	return d, nil
}

func (c *CleanupConfig) getHostList(ctx context.Context, model models.Model, conf models.CleanupConfig) ([]models.HostInfo, error) {
	data := make([]models.HostInfo, 0)
	duration, err := time.ParseDuration(fmt.Sprintf("%dh", conf.ExpirationHours))
	if err != nil {
		c.logger.Errorf(ctx, "===> parse duration error: %v", err)
		c.logger.Errorf(ctx, "===> use default duration: 48h")
		duration = 48 * time.Hour
	}
	where := fmt.Sprintf("vendor = ? and host_type = ? and update_time <= ? and is_deleted = '0'")
	if conf.AccountName != "" {
		where += fmt.Sprintf(" and account_name in ('%s')", strings.Join(strings.Split(strings.TrimSpace(conf.AccountName), ","), "','"))
	}
	err = model.Orm().Model(&models.HostInfo{}).
		Where(where, conf.Vendor, conf.ProductCode, time.Now().Add(-1*duration)).
		Find(&data).Error
	if err != nil {
		c.logger.Errorf(ctx, "get host list error: %v", err)
		return []models.HostInfo{}, err
	}
	return data, nil
}

func (c *CleanupConfig) getDatabaseList(ctx context.Context, model models.Model, conf models.CleanupConfig) ([]models.DatabaseInfo, error) {
	data := make([]models.DatabaseInfo, 0)
	duration, err := time.ParseDuration(fmt.Sprintf("%dh", conf.ExpirationHours))
	if err != nil {
		c.logger.Errorf(ctx, "===> parse duration error: %v", err)
		c.logger.Errorf(ctx, "===> use default duration: 48h")
		duration = 48 * time.Hour
	}

	where := ""
	if strings.ToLower(conf.ProductCode) == "polardb" {
		where = fmt.Sprintf("vendor = ? and engine_type like '%s%%' and update_time <= ? and is_self_build = 0 and is_deleted = '0'", conf.ProductCode)
	} else {
		where = fmt.Sprintf("vendor = ? and engine_type = '%s' and update_time <= ? and is_self_build = 0 and is_deleted = '0'", conf.ProductCode)
	}
	if conf.AccountName != "" {
		where += fmt.Sprintf(" and account_name in ('%s')", strings.Join(strings.Split(strings.TrimSpace(conf.AccountName), ","), "','"))
	}

	err = model.Orm().Model(&models.DatabaseInfo{}).
		Where(where, conf.Vendor, time.Now().Add(-1*duration)).
		Find(&data).Error
	if err != nil {
		c.logger.Errorf(ctx, "get rds list error: %v", err)
		return []models.DatabaseInfo{}, err
	}
	return data, nil
}

func (c *CleanupConfig) getMiddlewareList(ctx context.Context, model models.Model, conf models.CleanupConfig) ([]models.MiddlewareInfo, error) {
	data := make([]models.MiddlewareInfo, 0)
	duration, err := time.ParseDuration(fmt.Sprintf("%dh", conf.ExpirationHours))
	if err != nil {
		c.logger.Errorf(ctx, "===> parse duration error: %v", err)
		c.logger.Errorf(ctx, "===> use default duration: 48h")
		duration = 48 * time.Hour
	}
	err = model.Orm().Model(&models.MiddlewareInfo{}).
		Where("vendor = ? and instance_type = ? and update_time <= ? and is_deleted = '0'", conf.Vendor, conf.ProductCode, time.Now().Add(-1*duration)).
		Find(&data).Error
	if err != nil {
		c.logger.Errorf(ctx, "get middleware list error: %v", err)
		return []models.MiddlewareInfo{}, err
	}
	return data, nil
}

func (c *CleanupConfig) GetClient(ctx context.Context, vendor hbc.CloudVendor, prod hbc.CloudProduct, accountName, regionId string) (client.IClient, error) {
	clientLock.Lock()
	defer clientLock.Unlock()
	cliKey := fmt.Sprintf("%s_%s_%s", vendor.String(), accountName, regionId)
	if cli, ok := clientCache[cliKey]; ok {
		return cli, nil
	}

	purpose := bizutils.PurposeAdmin
	if vendor == hbc.AliCloudDedicated {
		purpose = bizutils.PurposeDedicated
	}

	conf := config.Global()
	accountManager := hcam.NewAccountManager(*conf.GetDefaultStore())
	accounts, err := accountManager.GetAccounts(ctx, account.WithPurposeTypes(purpose), account.WithVendors(vendor))
	if err != nil {
		return nil, err
	}

	skip := true
	for _, accountInfo := range accounts {
		if accountInfo.AccountName == accountName {
			skip = false
			break
		}
	}
	if skip {
		return nil, utils.NewError("account not found: %s", accountName)
	}

	if vendor == hbc.AliCloud || vendor == hbc.AliCloudDedicated {
		cli, err := hybrid.GetClient(ctx, vendor, accountName, prod, purpose, regionId)
		if err != nil {
			return nil, err
		}
		clientCache[cliKey] = cli
		return cli, nil
	} else if vendor == hbc.HuaweiCloud {
		defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.HuaweiCloud},
			prod, []string{bizutils.PurposeAdmin},
			actfilter.SetCustomProduct(hbc.HuaweiCloud, hbc.IAM),                                   // 华为云默认client使用 IAM client
			actfilter.SetIgnoreVendor(hbc.Private, hbc.TencentCloud, hbc.AWS, hbc.Azure, hbc.JXJG), // 忽略不支持的云
		)
		if err != nil {
			return nil, err
		}
		for _, cli := range defaultClients {
			if cli.Name() == accountName {
				clientCache[cliKey] = cli
				return cli, nil
			}
		}
		return nil, utils.NewError("huawei client not found: %s, %s", accountName, regionId)
	}
	return nil, utils.NewError("unsupported vendor type: %s", vendor)
}
