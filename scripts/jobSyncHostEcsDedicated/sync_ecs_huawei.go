package jobSyncHostEcsDedicated

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
)

const u = "hcsadmin"
const huawei_sdk_client = "ecs"

var huawei_product_code_ecs = []string{"hws.service.type.ec2"}

func (e *EcsSync) getHuaweiECSInfo(defClient *huaweicloud.DedicatedClient, ctx context.Context) ([]*models.HostInfo, error) {
	return e.transHuaweiEcs2HostInfo(ctx, defClient)
}

func (e *EcsSync) transHuaweiEcs2HostInfo(ctx context.Context, defClient *huaweicloud.DedicatedClient) ([]*models.HostInfo, error) {
	pageNo := 1
	pageSize := 1000
	hostInfos := []*models.HostInfo{}
	token, err := defClient.GetHuaweiOauthToken()
	if err != nil {
		return nil, err
	}

	for ecs, err := defClient.GetHuaweiDedicatedEcs(token, pageNo, pageSize); ; ecs, err = defClient.GetHuaweiDedicatedEcs(token, pageNo, pageSize) {
		if err != nil {
			return nil, err
		}

		if len(ecs.EcsList) > 0 {
			// 这个服务包含很多机器只获取用户是hcsadmin的数据
			for _, huaweiEcs := range ecs.EcsList {
				if huaweiEcs.TenantName != u && huaweiEcs.VdcName != u {
					continue
				}
				createdAt, err := time.Parse(time.RFC3339, huaweiEcs.CreatedAt)
				if err != nil {
					return nil, err
				}
				cpu, _ := strconv.ParseUint(huaweiEcs.FlavorVcpu, 10, 64)
				memory, _ := strconv.ParseUint(huaweiEcs.FlavorRAMSize, 10, 64)
				diskSize, _ := strconv.ParseFloat(huaweiEcs.FlavorDiskSize, 64)
				scode := huaweiTag(huaweiEcs.Tags)
				var project string
				if almProject, err := api.HdsClient().QueryAlmProject(scode); err == nil && almProject != nil {
					project = almProject.Id
				}

				var gpuInfo *string
				var gpuAmount *int
				if strings.Contains(huaweiEcs.FlavorPerformanceType, "gpu") {
					amount := 1
					gpuAmount = &amount
					gpu := map[string]interface{}{
						"name":  "unknown",
						"count": gpuAmount,
					}
					gpus := []map[string]interface{}{gpu}
					jsonData, err := json.Marshal(gpus)
					if err != nil {
						gpuInfo = nil
					} else {
						jsonStr := string(jsonData)
						gpuInfo = &jsonStr
					}
				}
				cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(defClient.Vendor()), defClient.Identifier(), huaweiEcs.NativeID, huawei_product_code_ecs)
				hostInfo := &models.HostInfo{
					Model:        e.Model(ctx),
					Vendor:       defClient.Vendor(),
					AccountName:  defClient.Name(),
					InstanceId:   huaweiEcs.NativeID,
					InstanceName: huaweiEcs.Name,
					CreationTime: timeutil.ZeroTime(createdAt),
					PrivateIp:    getHuaweiEcsIp(huaweiEcs.IPAddress, ";"),
					PublicIp:     getHuaweiEcsIp(huaweiEcs.PrivateIPS, "@"),
					// NetworkType:   aliyunEcs.InstanceNetworkType,
					// VpcId:         aliyunEcs.VpcAttributes.VpcId,
					// SubnetId:      aliyunEcs.VpcAttributes.VSwitchId,
					Scode:   bizutils.SwapSCode(scode, cmdb.Scode),
					Project: project,
					// Env:           bizutils.UnifyEnv(env),
					ResourceGroup: huaweiEcs.ResourcePoolID,
					OsType:        strings.ToLower(huaweiEcs.OSType),
					OsName:        huaweiEcs.OSVersion,
					// OsArch:     getAliEcsCpuArch(aliyunEcs),
					// Numa:        ptr.Bool(getAliEcsNuma(aliyunEcs)),
					ImageId:       huaweiEcs.OSVersion,
					Cpu:           cpu,
					Memory:        memory,
					DiskSize:      diskSize * 1024,
					HostStatus:    bizutils.UnifyHostStatus(huaweiEcs.Status),
					UniHostStatus: getHuaweiUniHostStatus(huaweiEcs.Status),
					HostType:      hbc.ECS.String(),
					HostInsId:     huaweiEcs.HostID,
					Region:        huaweiEcs.RegionName,
					Zone:          huaweiEcs.AzoneName,
					ClassCode:     huaweiEcs.FlavorPerformanceType,
					// ChargeType:  aliyunEcs.InstanceChargeType,
					// Sn:          aliyunEcs.SerialNumber,
					// Description: aliyunEcs.Description,
					AggregatedId:  cmdb.AggregatedID,
					GpuInfo:       gpuInfo,
					GpuAmount:     gpuAmount,
					SDKClientName: huawei_sdk_client,
				}

				hostInfos = append(hostInfos, hostInfo)
			}
		} else {
			break
		}
		pageNo = pageNo + 1
	}

	return hostInfos, nil
}

func getHuaweiEcsIp(ipAddress, splittingSymbol string) string {
	ips := strings.Split(ipAddress, splittingSymbol)
	// 移除空字符串
	for _, ip := range ips {
		if ip != "" {
			return ip
		}
	}
	return ipAddress
}

func huaweiTag(tags string) (scode string) {
	scodeLen := 6
	//[\"组件.园区组件\",\"7a93a8d6-71d6-45b1-bd27-eb7c43129ad8\",\"S码.S03616\"]
	str := "S码."
	s := strings.Index(tags, str)
	if s != -1 {
		scode = tags[s+len(str) : s+len(str)+scodeLen]
	}
	return
}

func getHuaweiUniHostStatus(status string) string {
	status = strings.TrimSpace(strings.ToLower(status))
	switch status {
	case "running", "active":
		return "running" // 运行中
	case "stopped":
		return "stopped" // 已停止
	default:
		return "other"
	}
}
