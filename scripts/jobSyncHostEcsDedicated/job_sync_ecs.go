package jobSyncHostEcsDedicated

import (
	"context"
	"sync"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func New() *EcsSync {
	return &EcsSync{
		mtx:         new(sync.Mutex),
		rg:          orderedmap.New[string, *resourcemanager.ResourceGroup](),
		ep:          orderedmap.New[string, *eps.EpDetail](),
		awsEcsTypes: orderedmap.New[types.InstanceType, *types.InstanceTypeInfo](),
		JobBase: base.NewJobBase("ECS_SYNC",
			"同步专属云ECS资源信息",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type EcsSync struct {
	rg          *orderedmap.OrderedMap[string, *resourcemanager.ResourceGroup]
	ep          *orderedmap.OrderedMap[string, *eps.EpDetail]
	awsEcsTypes *orderedmap.OrderedMap[types.InstanceType, *types.InstanceTypeInfo]
	mtx         *sync.Mutex
	*base.JobBase
}

func (e *EcsSync) Name() string {
	return base.TaskCloudSyncEcsDedicated
}

func (e *EcsSync) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(e.Model(ctx), hbc.GoogleCloud, hbc.AliCloud, hbc.HuaweiCloud, hbc.TencentCloud, hbc.JXJG, hbc.AWS, hbc.Azure, hbc.Private)
	if err != nil {
		return nil, err
	}

	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, vendors,
		hbc.ECS,
		[]string{bizutils.PurposeDedicated},
		actfilter.SetCustomProduct(hbc.HuaweiCloudDedicated, hbc.HuaweiDedicated),
	)
	if err != nil {
		return nil, err
	}

	// 获取云资源信息
	b := tools.NewBatch[client.IClient, []*models.HostInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.HostInfo, error) {
		switch defClient := input.(type) {
		case *aliyun.EcsClient:
			return e.getAliyunECSInfo(defClient, ctx)
		case *huaweicloud.DedicatedClient:
			return e.getHuaweiECSInfo(defClient, ctx)
		default:
			return nil, nil
		}
	})

	if err := b.Error(); err != nil {
		e.Errorf(ctx, "sync ecs failed, cause: %s", err)
		return nil, err
	}

	// 将信息存入数据库
	if err = bizutils.CreateOrUpdateHost(ctx, tools.MergeData(b.Outs()...)...); err != nil {
		e.Errorf(ctx, "save ecs failed, cause: %s", err)
		return nil, err
	}

	return nil, nil
}
