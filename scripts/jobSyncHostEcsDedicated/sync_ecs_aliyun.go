package jobSyncHostEcsDedicated

import (
	"context"
	"errors"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ecs"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"github.com/aws/smithy-go/ptr"
	"github.com/nacos-group/nacos-sdk-go/vo"
)

const (
	NacosGroupId = "task_center"
	NacosDataId  = "cluster_id_conf_monitoring_mode_3"
)

var aliyun_product_code_ecs = []string{"ecs"}
var nacosConfig string

func (e *EcsSync) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](e.mtx, e.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}

func getAliEcsNuma(ecs ecs.Instance) bool {
	return strings.EqualFold(ecs.CpuOptions.Numa, "ON")
}

func getAliEcsPrivateIp(ecs ecs.Instance) (privateIp string) {
	// VPC网络
	if len(ecs.VpcAttributes.PrivateIpAddress.IpAddress) > 0 {
		privateIp = ecs.VpcAttributes.PrivateIpAddress.IpAddress[0]
		return
	}
	// 经典网络
	privateIp = ecs.InnerIpAddress.IpAddress[0]
	return
}

func getAliEcsPublicIp(ecs ecs.Instance) (publicIp string) {
	if len(ecs.PublicIpAddress.IpAddress) > 0 {
		publicIp = ecs.PublicIpAddress.IpAddress[0]
	}
	return
}

func getAliEcsCpuArch(ecs ecs.Instance) (arch string) {
	//https://help.aliyun.com/document_detail/25378.html#g8y
	if strings.HasSuffix(ecs.InstanceTypeFamily, "8y") ||
		strings.HasSuffix(ecs.InstanceTypeFamily, "6r") {
		return "arm_64"
	}
	return common.OsArchX8664
}

func (e *EcsSync) getAliyunECSInfo(defClient *aliyun.EcsClient, ctx context.Context) ([]*models.HostInfo, error) {
	regions, err := defClient.DescribeRegions()
	if err != nil {
		return nil, bizutils.WarpClientError(defClient, err)
	}
	regionsIds := orderedmap.New[string, string]()
	for _, region := range regions {
		regionsIds.Store(region.RegionId, region.RegionId)
	}

	nacosApi := api.NacosApi()
	if nacosApi == nil {
		return nil, errors.New("nacos api is nil")
	}
	nacosConfig, _ = nacosApi.GetConfig(vo.ConfigParam{
		Group:  NacosGroupId,
		DataId: NacosDataId,
	})

	b := tools.NewBatch[string, []*models.HostInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, region string) ([]*models.HostInfo, error) {
		ecsClient := hybrid.AccountManager().AliyunDedicatedEcsClient(ctx, defClient.Name(), defClient.Tag(), region)
		resourceManagerClient := hybrid.AccountManager().AliyunDedicatedResourceManagerClient(ctx, defClient.Name(), defClient.Tag(), region)
		ecsList, ecsErr := ecsClient.DescribeInstancesOfAll(ctx)
		if ecsErr != nil {
			return nil, bizutils.WarpClientError(ecsClient, err)
		}

		hosts := make([]*models.HostInfo, len(ecsList))
		for i, aliyunEcs := range ecsList {
			hosts[i] = e.transAliyunEcs2HostInfo(ctx, aliyunEcs, resourceManagerClient, ecsClient)
		}
		return hosts, nil
	})

	return tools.MergeData(b.Outs()...), b.Error()
}

func (e *EcsSync) transAliyunEcs2HostInfo(ctx context.Context, aliyunEcs ecs.Instance, resourceManagerClient *aliyun.ResourceClient, defClient *aliyun.EcsClient) *models.HostInfo {
	creationTime, _ := time.Parse(bizutils.AliyunEcsTimeFormat, aliyunEcs.CreationTime)
	expiredTime, _ := time.Parse(bizutils.AliyunEcsTimeFormat, aliyunEcs.ExpiredTime)
	env, scode, project := bizutils.ParseAliyunEnvProject(resourceManagerClient, e, aliyunEcs.ResourceGroupId)
	monitoringMode := bizutils.MonitoringModeIsNone
	if len(aliyunEcs.Tags.Tag) > 0 {
		if tagEnv := bizutils.TryParseEnvFromInstanceTag(aliyunEcs.Tags.Tag...); tagEnv != "" {
			env = tagEnv
		}
		for _, tag := range aliyunEcs.Tags.Tag {
			if tag.TagKey == "monitor" && strings.ToLower(strings.TrimSpace(tag.TagValue)) == "false" {
				monitoringMode = bizutils.MonitoringModeIsNotNecessary
			}
		}
	}
	// 判断集群id cluster_id
	if monitoringMode != bizutils.MonitoringModeIsNotNecessary {
		clusterIds, _ := utils.UnmarshalString[[]string](nacosConfig)
		for _, i := range clusterIds {
			if strings.Contains(aliyunEcs.InstanceName, i) {
				monitoringMode = bizutils.MonitoringModeIsNotNecessary
				break
			}
		}
	}
	var diskSize int
	var diskType []string
	disks, err := defClient.DescribeDisksByInstanceId(ctx, aliyunEcs.InstanceId)
	if err == nil {
		for _, d := range disks {
			diskSize = d.Size + diskSize // 磁盘大小，单位 GiB。
			diskType = append(diskType, d.Type)
		}
	}
	cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(defClient.Vendor()), defClient.Identifier(), aliyunEcs.InstanceId, aliyun_product_code_ecs)
	return &models.HostInfo{
		Model:          e.Model(ctx),
		Vendor:         defClient.Vendor(),
		AccountName:    defClient.Name(),
		InstanceId:     aliyunEcs.InstanceId,
		InstanceName:   aliyunEcs.InstanceName,
		CreationTime:   timeutil.ZeroTime(creationTime),
		ExpiredTime:    timeutil.ZeroTime(expiredTime),
		PrivateIp:      getAliEcsPrivateIp(aliyunEcs),
		PublicIp:       getAliEcsPublicIp(aliyunEcs),
		NetworkType:    aliyunEcs.InstanceNetworkType,
		VpcId:          aliyunEcs.VpcAttributes.VpcId,
		SubnetId:       aliyunEcs.VpcAttributes.VSwitchId,
		Scode:          bizutils.SwapSCode(scode, cmdb.Scode),
		Project:        project,
		Env:            bizutils.UnifyEnv(env),
		ResourceGroup:  aliyunEcs.ResourceGroupId,
		OsType:         unifyOSTypeForAli(aliyunEcs),
		OsName:         aliyunEcs.OSNameEn,
		OsArch:         getAliEcsCpuArch(aliyunEcs),
		Numa:           ptr.Bool(getAliEcsNuma(aliyunEcs)),
		ImageId:        aliyunEcs.ImageId,
		Cpu:            uint64(aliyunEcs.Cpu),
		Memory:         uint64(aliyunEcs.Memory),
		DiskSize:       float64(diskSize * 1024),
		DiskType:       strings.Join(bizutils.RemoveDuplicates(diskType), ","),
		HostStatus:     bizutils.UnifyHostStatus(aliyunEcs.Status),
		UniHostStatus:  getAliUniHostStatus(aliyunEcs.Status),
		HostType:       hbc.ECS.String(),
		HostInsId:      aliyunEcs.DedicatedHostAttribute.DedicatedHostId,
		Region:         aliyunEcs.RegionId,
		Zone:           aliyunEcs.ZoneId,
		ClassCode:      aliyunEcs.InstanceType,
		ChargeType:     aliyunEcs.InstanceChargeType,
		Sn:             aliyunEcs.SerialNumber,
		Description:    aliyunEcs.Description,
		AggregatedId:   cmdb.AggregatedID,
		MonitoringMode: monitoringMode,
		SDKClientName:  aliyun_product_code_ecs[0],
	}
}

func unifyOSTypeForAli(aliyunEcs ecs.Instance) string {
	switch aliyunEcs.OSType {
	case "linux":
		return common.OsTypeLinux
	default:
		return aliyunEcs.OSType
	}
}

func getAliUniHostStatus(status string) string {
	status = strings.TrimSpace(strings.ToLower(status))
	switch status {
	case "running":
		return "running" // 运行中
	case "stopped":
		return "stopped" // 已停止
	default:
		return "other"
	}
}
