package jobForDBMS

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"github.com/patrickmn/go-cache"

	"git.haier.net/devops/hcms-task-center/core/config"
)

var (
	mtx             = new(sync.Mutex)
	dmsAccountCache = cache.New(12*time.Hour, 12*time.Hour)
	hdmAccountCache = cache.New(12*time.Hour, 12*time.Hour)
	dbmsStore       = config.Global().GetStore("dbms")
)

func GetDmsAccounts(ctx context.Context) []*aliyun.DmsClient {
	m := dbmsStore.Model(ctx)

	whereCond := fmt.Sprintf("cloud_vendor = '%s'", hbc.AliCloud)
	if account := ctx.Value(ParamAccount); account != nil {
		if account, ok := account.(string); ok && account != "" {
			whereCond += fmt.Sprintf("and cloud_account_name in ('%s')", strings.Join(strings.Split(strings.TrimSpace(account), ","), "','"))
		}
	}

	accounts := make([]*SysCloudAccount, 0)
	if err := m.Orm().Table("sys_cloud_account").
		Where(whereCond).
		Find(&accounts).Error; err != nil {
		return nil
	}

	dmsAccounts := make([]*aliyun.DmsClient, 0)
	for _, account := range accounts {
		client, err := getDmsAccountFromCache(ctx, account.CloudAccountName)
		if err != nil {
			continue
		}
		dmsAccounts = append(dmsAccounts, client)
	}
	return dmsAccounts
}

func getDmsAccountFromCache(ctx context.Context, name string) (*aliyun.DmsClient, error) {
	mtx.Lock()
	defer mtx.Unlock()
	if act, has := dmsAccountCache.Get(name); has {
		return act.(*aliyun.DmsClient), nil
	}

	account := &SysCloudAccount{}
	m := dbmsStore.Model(ctx)
	if err := m.Orm().Table("sys_cloud_account").
		Where("cloud_vendor = ? and cloud_account_name = ?", hbc.AliCloud, name).
		First(account).Error; err != nil {
		return nil, err
	}

	client := aliyun.NewDMSClient("cn-hangzhou", name, account.EncodeAK, account.EncodeSK, "", "", true)
	return client, dmsAccountCache.Add(name, client, cache.DefaultExpiration)
}

func GetHdmAccounts(ctx context.Context) []*aliyun.HdmClient {
	m := dbmsStore.Model(ctx)

	whereCond := fmt.Sprintf("cloud_vendor = '%s'", hbc.AliCloud)
	if account := ctx.Value(ParamAccount); account != nil {
		if account, ok := account.(string); ok && account != "" {
			whereCond += fmt.Sprintf("and cloud_account_name in ('%s')", strings.Join(strings.Split(strings.TrimSpace(account), ","), "','"))
		}
	}

	accounts := make([]*SysCloudAccount, 0)
	if err := m.Orm().Table("sys_cloud_account").
		Where(whereCond).
		Find(&accounts).Error; err != nil {
		return nil
	}

	dmsAccounts := make([]*aliyun.HdmClient, 0)
	for _, account := range accounts {
		client, err := getHdmAccountFromCache(ctx, account.CloudAccountName)
		if err != nil {
			continue
		}
		dmsAccounts = append(dmsAccounts, client)
	}
	return dmsAccounts
}

func getHdmAccountFromCache(ctx context.Context, name string) (*aliyun.HdmClient, error) {
	mtx.Lock()
	defer mtx.Unlock()
	if act, has := hdmAccountCache.Get(name); has {
		return act.(*aliyun.HdmClient), nil
	}

	account := &SysCloudAccount{}
	m := dbmsStore.Model(ctx)
	if err := m.Orm().Table("sys_cloud_account").
		Where("cloud_vendor = ? and cloud_account_name = ?", hbc.AliCloud, name).
		First(account).Error; err != nil {
		return nil, err
	}

	client := aliyun.NewHDMClient("cn-hangzhou", name, account.EncodeAK, account.EncodeSK, "", "", true)
	return client, hdmAccountCache.Add(name, client, cache.DefaultExpiration)
}
