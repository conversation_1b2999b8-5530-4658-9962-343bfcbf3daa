package DBMSRdsIpCheck

import (
	"context"
	"net"
	"strconv"
	"strings"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils/iputils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/rds"
	"github.com/xuri/excelize/v2"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobForDBMS"
)

var officeNetworkCIDRs = []string{
	"**********/16",
	"**********/16",
	"**********/16",
	"**********/16",
	"**********/16",
}

var rdsDtsWhiteList = []string{
	"***********/24",
	"**************",
	"************",
	"***************/26",
	"*************/26",
	"************/26",
	"***************/26",
	"*************/26",
	"**************/26",
	"************/26",
	"************/24",
	"************/24",
	"**************/26",
	"************/24",
	"**************",
	"*************",
	"************/24",
	"************/24",
	"************/24",
	"************/24",
	"************/24",
}

type DBMSRdsIpChecker struct {
	*base.JobBase
	dbms           *models.Datasource
	invalidIpCIDRs []*net.IPNet
}

type InvalidIPWhiteList struct {
	AccountName        string `json:"account_name"`
	InstanceId         string `json:"instance_id"`
	InvalidIpGroupName string `json:"invalid_ip_group_name"`
	InvalidIp          string `json:"invalid_ip"`
	Reason             string `json:"reason"`

	info *models.DatabaseInfo
}

func New() *DBMSRdsIpChecker {
	invalidIpCIDRs := make([]*net.IPNet, 0)
	for _, officeNetworkCIDR := range officeNetworkCIDRs {
		_, whiteNet, _ := net.ParseCIDR(officeNetworkCIDR)
		invalidIpCIDRs = append(invalidIpCIDRs, whiteNet)
	}

	return &DBMSRdsIpChecker{
		invalidIpCIDRs: invalidIpCIDRs,
		dbms:           config.Global().GetStore("dbms"),
		JobBase: base.NewJobBase("DBMS_RDS_IP_CHECK",
			"检查允许办公网访问的数据库",
			base.NewSchedule(
				base.WithRandMin(),
				base.WithRandSec(),
			),
			taskmodels.TaskCategoryDBMS,
			bizutils.DataSource(),
		),
	}
}

func (s *DBMSRdsIpChecker) Name() string {
	return base.TaskCheckRdsIPWhiteList
}

func (s *DBMSRdsIpChecker) Run(ctx context.Context) (map[string]any, error) {
	dmsInsIDList, err := s.getDBMSInsIDList(ctx)
	if err != nil {
		return nil, err
	}

	vendors, err := hybrid.GetVendors(s.Model(ctx), hbc.GoogleCloud, hbc.Private, hbc.HuaweiCloud, hbc.TencentCloud, hbc.AWS, hbc.Azure)
	if err != nil {
		return nil, err
	}
	defaultClients, err := base.GetDefaultIClients(ctx, vendors, hbc.RDS, []string{bizutils.PurposeAdmin})
	if err != nil {
		return nil, err
	}

	regionSet := orderedmap.New[string, string]()
	rdsClient := defaultClients[0].(*aliyun.RDSClient)
	regions, err := rdsClient.DescribeRegions()
	if err != nil {
		return nil, err
	}
	for _, region := range regions {
		regionSet.Store(region.RegionId, region.RegionId)
	}
	regionSet.Delete("cn-nantong")

	process := tools.NewBatch[client.IClient, []*InvalidIPWhiteList](ctx)
	process.Run(defaultClients, func(ctx context.Context, client client.IClient) ([]*InvalidIPWhiteList, error) {
		allRegionInvalidIpWhiteList := make([]*InvalidIPWhiteList, 0)
		for _, regionId := range regionSet.Slice() {
			invalidIpList, err := s.checkRdsIPWhiteList(ctx, client, regionId, dmsInsIDList)
			if err != nil {
				return nil, err
			}
			allRegionInvalidIpWhiteList = append(allRegionInvalidIpWhiteList, invalidIpList...)
		}
		return allRegionInvalidIpWhiteList, nil
	})

	if err := process.Error(); err != nil {
		return nil, err
	}

	result := tools.MergeData(process.Outs()...)
	return nil, s.exportToFile(result...)
}

func (s *DBMSRdsIpChecker) getDBMSInsIDList(ctx context.Context) (*orderedmap.OrderedMap[string, string], error) {
	defaultClients := jobForDBMS.GetDmsAccounts(ctx)
	collectDMSInstanceId := orderedmap.New[string, string]()
	collectDMSInstanceIdProcess := tools.NewBatch[*aliyun.DmsClient, map[string]struct{}](ctx)
	collectDMSInstanceIdProcess.Run(defaultClients, func(ctx context.Context, client *aliyun.DmsClient) (map[string]struct{}, error) {
		getDbmsRdsInstance, err := s.getDbmsRdsInstance(ctx, client)
		if err != nil {
			return nil, err
		}
		return getDbmsRdsInstance, nil
	})
	for _, v := range collectDMSInstanceIdProcess.Outs() {
		for k := range v {
			collectDMSInstanceId.Store(k, k)
		}
	}
	return collectDMSInstanceId, nil
}

func (s *DBMSRdsIpChecker) getDbmsRdsInstance(ctx context.Context, client *aliyun.DmsClient) (map[string]struct{}, error) {
	instanceIdList := make(map[string]struct{})
	res, err := client.ListInstancesAll(ctx)
	if err != nil {
		return nil, err
	}
	for _, r := range res {
		if pointer.Value(r.InstanceSource) != "RDS" {
			continue
		}
		instanceIdList[pointer.Value(r.EcsInstanceId)] = struct{}{}
	}
	return instanceIdList, nil
}

func (s *DBMSRdsIpChecker) checkRdsIPWhiteList(ctx context.Context, client client.IClient, regionId string, scope *orderedmap.OrderedMap[string, string]) ([]*InvalidIPWhiteList, error) {
	cli := hybrid.AccountManager().AliyunRdsClient(ctx, client.Name(), client.Tag(), regionId)
	instances, err := cli.DescribeDBInstancesOfAll(ctx)
	if err != nil {
		return nil, bizutils.WarpClientError(cli, err)
	}

	checkProcess := tools.NewBatch[rds.DBInstance, []*InvalidIPWhiteList](ctx)
	checkProcess.Run(instances, func(ctx context.Context, instance rds.DBInstance) ([]*InvalidIPWhiteList, error) {
		if _, ok := scope.Load(instance.DBInstanceId); !ok {
			s.Logger().Infof(ctx, "instance %s not in scope", instance.DBInstanceId)
			return nil, nil
		}

		res, err := cli.DescribeDBInstanceIPArrayList(ctx, instance.DBInstanceId)
		if err != nil {
			return nil, err
		}

		invalidIpWhiteList := make([]*InvalidIPWhiteList, 0)
		for _, ip := range res {
			if ip.DBInstanceIPArrayAttribute == "hidden" {
				continue
			}
			invalidList, err := s.checkSecureIp(ctx, client.Name(), instance.DBInstanceId, ip.DBInstanceIPArrayName, strings.Split(ip.SecurityIPList, ",")...)
			if err != nil {
				return nil, err
			}
			invalidIpWhiteList = append(invalidIpWhiteList, invalidList...)
		}

		return invalidIpWhiteList, nil
	})
	return tools.MergeData(checkProcess.Outs()...), checkProcess.Error()
}

func (s *DBMSRdsIpChecker) checkSecureIp(ctx context.Context, accountName, insId, secGroupName string, securesIp ...string) ([]*InvalidIPWhiteList, error) {
	invalidIpWhiteList := make([]*InvalidIPWhiteList, 0)
	info := &models.DatabaseInfo{}
	if err := s.Orm(ctx).Model(info).Where("instance_id = ?", insId).Find(&info).Error; err != nil {
		return nil, err
	}

	for _, invalidCIDR := range s.invalidIpCIDRs {
		for _, ip := range securesIp {
			if ip == "127.0.0.1" {
				continue
			}
			if strings.HasPrefix(ip, "100.") {
				// 忽略阿里云内网服务
				continue
			}

			isDtsIp := false
			for _, dtsIp := range rdsDtsWhiteList {
				if ip == dtsIp {
					isDtsIp = true
					break
				}
			}
			if isDtsIp {
				continue
			}

			// 检查公网访问
			if !strings.HasPrefix(ip, "10.") && !strings.HasPrefix(ip, "192.") && !strings.HasPrefix(ip, "172.") {
				invalidIpWhiteList = append(invalidIpWhiteList, &InvalidIPWhiteList{
					AccountName:        accountName,
					InstanceId:         insId,
					InvalidIpGroupName: secGroupName,
					InvalidIp:          ip,
					Reason:             "允许公网访问",
				})
				continue
			}

			// 检查办公网
			var overlap bool
			var err error

			if ipObj := net.ParseIP(ip); ipObj != nil {
				overlap = invalidCIDR.Contains(ipObj)
			} else {
				_, ipNet, _ := net.ParseCIDR(ip)
				if ipNet != nil {
					overlap, err = iputils.IsOverlap(ipNet, invalidCIDR)
					if err != nil {
						return nil, err
					}
				} else {
					s.Logger().Errorf(ctx, "invalid ip: %s", ip)
				}
			}

			if overlap {
				invalidIpWhiteList = append(invalidIpWhiteList, &InvalidIPWhiteList{
					AccountName:        accountName,
					InstanceId:         insId,
					InvalidIpGroupName: secGroupName,
					InvalidIp:          ip,
					Reason:             "允许办公网访问：" + invalidCIDR.String(),
				})
				continue
			}
		}

		for _, i := range invalidIpWhiteList {
			i.info = info
		}

	}

	return invalidIpWhiteList, nil
}

func (s *DBMSRdsIpChecker) exportToFile(ip ...*InvalidIPWhiteList) error {
	file := excelize.NewFile()
	sheet := "Sheet1"
	_, _ = file.NewSheet(sheet)

	columns := []string{"账号", "实例ID", "实例名", "实例类型", "所属环境", "安全组名称", "异常白名单", "异常原因", "S码", "负责人", "团队", "领域"}
	for i, v := range columns {
		_ = file.SetCellValue(sheet, GetColumn(i)+"1", v)
	}

	for row, v := range ip {
		var (
			insName string
			insType string
			env     string
			sCode   string
			owner   string
			team    string
			domain  string
		)

		if v.info != nil {
			insName = v.info.InstanceName
			insType = v.info.EngineType
			env = v.info.Env
			sCode = v.info.Scode
			owner = v.info.OwnerName + "(" + v.info.OwnerId + ")"
			team = v.info.Team
			domain = v.info.Domain
		}

		values := []interface{}{
			v.AccountName,
			v.InstanceId,
			insName,
			insType,
			env,
			v.InvalidIpGroupName,
			v.InvalidIp,
			v.Reason,
			sCode,
			owner,
			team,
			domain,
		}

		for col, v := range values {
			_ = file.SetCellValue(sheet, GetColumn(col)+strconv.Itoa(row+2), v)
		}
	}

	return file.SaveAs("rds_invalid_ip_white_list.xlsx")
}
