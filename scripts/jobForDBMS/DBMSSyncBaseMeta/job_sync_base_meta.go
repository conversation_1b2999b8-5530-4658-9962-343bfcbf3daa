/*
Copyright 2018 LPX-J0HKO(lipengxiang)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package DBMSSyncBaseMeta

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobForDBMS"
)

const (
	none   = "-"
	colon  = ":"
	format = "%s" + colon + "%d"
	exist  = true
)

type DBMSBaseSync struct {
	*base.JobBase
	dbms *models.Datasource
}

type AccountInfo struct {
	Host        string `gorm:"column:conn_addr"`
	Port        int    `gorm:"column:conn_port"`
	InstanceID  string `gorm:"-"`
	AccountName string `gorm:"-"`
}

func New() *DBMSBaseSync {
	return &DBMSBaseSync{
		dbms: config.Global().GetStore("dbms"),
		JobBase: base.NewJobBase("DBMS_BASE_SYNC",
			"同步DBMS_BASE表数据",
			base.NewSchedule(
				base.WithRandMin(),
				base.WithRandSec(),
			),
			taskmodels.TaskCategoryDBMS,
			bizutils.DataSource(),
		),
	}
}

func (s *DBMSBaseSync) Name() string {
	return base.TaskDbmsBaseSync
}

func (s *DBMSBaseSync) Run(ctx context.Context) (map[string]any, error) {
	return s.syncHDMInstanceInfo(ctx)
}

func (s *DBMSBaseSync) syncHDMInstanceInfo(ctx context.Context) (map[string]any, error) {
	model := s.dbms.Model(ctx)
	orm := model.Orm()

	clients := jobForDBMS.GetHdmAccounts(ctx)
	process := tools.NewBatch[*aliyun.HdmClient, []*AccountInfo](ctx)
	process.Run(clients, func(ctx context.Context, client *aliyun.HdmClient) ([]*AccountInfo, error) {
		instances, err := client.GetHDMLogicInstancesAll(ctx)
		if err != nil {
			return nil, err
		}
		res := make([]*AccountInfo, 0)
		for _, instance := range instances {
			host := instance.ConnString
			if host == "" {
				host = instance.IP
			}
			res = append(res, &AccountInfo{
				Host:        host,
				Port:        instance.Port,
				InstanceID:  instance.InstanceID,
				AccountName: client.Name(),
			})
		}
		return res, nil
	})
	if err := process.Error(); err != nil {
		return nil, err
	}

	var all sync.Map
	var results []models.RdsInstanceBase
	if err := orm.Find(&results).Error; err != nil {
		return nil, err
	}
	for _, r := range results {
		all.Store(fmt.Sprintf(format, r.ConnAddr, r.ConnPort), exist)
	}

	globalInstance := make([]*AccountInfo, 0)
	for _, result := range process.Outs() {
		globalInstance = append(globalInstance, result...)
	}
	sql := "update rds_instance_base set hdm_instance_id = '%s', hdm_account_name = '%s' where conn_addr = '%s' and conn_port = %d"
	updateProcess := tools.NewBatch[*AccountInfo, any](ctx)
	updateProcess.Run(globalInstance, func(ctx context.Context, info *AccountInfo) (any, error) {
		if info == nil {
			return nil, nil
		}
		if info.Host == "" || info.InstanceID == "" || info.AccountName == "" {
			return nil, nil
		}
		query := fmt.Sprintf(sql, info.InstanceID, info.AccountName, info.Host, info.Port)
		s.Logger().Infof(ctx, "query: %s", query)
		res := orm.Exec(query)
		if res.Error != nil {
			s.Logger().Errorf(ctx, "update rds_instance_base error: %s, host: %s:%d", res.Error, info.Host, info.Port)
			return nil, res.Error
		}
		// 移除已存在的数据
		all.Delete(fmt.Sprintf(format, info.Host, info.Port))
		return nil, nil
	})
	if err := updateProcess.Error(); err != nil {
		return nil, err
	}

	// 遍历map更新库里多余数据
	var err error
	all.Range(func(key, value any) bool {
		address := strings.Split(key.(string), colon)

		query := fmt.Sprintf(sql, none, none, address[0], address[1])
		if err = orm.Exec(query).Error; err != nil {
			return false
		}
		return true
	})
	return nil, err
}
