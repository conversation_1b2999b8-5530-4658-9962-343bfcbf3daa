package jobForDBMS

import (
	"errors"

	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"
)

type SysCloudAccount struct {
	CloudVendor      string `gorm:"column:cloud_vendor" json:"cloud_vendor"`
	CloudAccountName string `gorm:"column:cloud_account_name" json:"cloud_account_name"`
	EncodeAK         string `gorm:"column:encode_ak" json:"encode_ak"`
	EncodeSK         string `gorm:"column:encode_sk" json:"encode_sk"`
	TenantId         string `gorm:"column:tenant_id" json:"tenant_id"`
	TenantName       string `gorm:"column:tenant_name" json:"tenant_name"`
	Enable           bool   `gorm:"column:enable" json:"enable"`
}

type RdsInstanceDB struct {
	models.Model

	CloudAccountName string `gorm:"column:cloud_account_name" json:"cloud_account_name"`
	Alias            string `gorm:"column:alias" json:"alias"`
	DatabaseId       string `gorm:"column:database_id" json:"database_id"`
	DataLinkName     string `gorm:"column:data_link_name" json:"data_link_name"`
	DBType           string `gorm:"column:db_type" json:"db_type"`
	EnvType          string `gorm:"column:env_type" json:"env_type"`
	CatalogName      string `gorm:"column:catalog_name" json:"catalog_name"`
	SchemaName       string `gorm:"column:schema_name" json:"schema_name"`
	SearchName       string `gorm:"column:search_name" json:"search_name"`
	Host             string `gorm:"column:host" json:"host"`
	Port             int    `gorm:"column:port" json:"port"`
	Logic            bool   `gorm:"column:logic" json:"logic"`
}

func (insDB *RdsInstanceDB) Save() error {
	var err error
	rdsDB := &RdsInstanceDB{}
	if err = insDB.Orm().Where("database_id = ?", insDB.DatabaseId).First(rdsDB).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			insDB.ID = 0
			err = nil
		}
	}

	if err != nil {
		return err
	}
	if rdsDB.ID > 0 {
		insDB.ID = rdsDB.ID
		if insDB.Alias != rdsDB.Alias ||
			insDB.DataLinkName != rdsDB.DataLinkName ||
			insDB.DBType != rdsDB.DBType ||
			insDB.SearchName != rdsDB.SearchName ||
			insDB.Host != rdsDB.Host ||
			insDB.CatalogName != rdsDB.CatalogName {
			return insDB.Orm().Model(insDB).Updates(insDB).Error
		}
		// 更新时间
		return insDB.Orm().Model(insDB).
			Where("database_id = ?", insDB.DatabaseId).
			Updates(map[string]interface{}{}).Error
	}
	return insDB.Orm().Model(insDB).Create(insDB).Error
}
