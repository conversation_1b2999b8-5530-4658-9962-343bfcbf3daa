package DBMSSyncDBMeta

import (
	"context"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"go.uber.org/atomic"

	dmsEnterprise "github.com/alibabacloud-go/dms-enterprise-20181101/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobForDBMS"
)

const (
	paramHost  = "host"
	paramForce = "force"
)

type DBMSMetaSync struct {
	*base.JobBase
	dbms *models.Datasource
}

func New() *DBMSMetaSync {
	return &DBMSMetaSync{
		dbms: config.Global().GetStore("dbms"),
		JobBase: base.NewJobBase("DBMS_META_SYNC",
			"同步DBMS数据",
			base.NewSchedule(
				base.WithRandMin(),
				base.WithRandSec(),
			),
			taskmodels.TaskCategoryDBMS,
			bizutils.DataSource(),
		),
	}
}

func (s *DBMSMetaSync) Name() string {
	return base.TaskDBMSSync
}

func (s *DBMSMetaSync) Run(ctx context.Context) (map[string]any, error) {
	if value := ctx.Value(jobForDBMS.ParamAccount); value != nil {
		s.Logger().Infof(ctx, "got param %s = %s", jobForDBMS.ParamAccount, value)
	}
	if value := ctx.Value(paramHost); value != nil {
		s.Logger().Infof(ctx, "got param %s = %s", paramHost, value)
	}
	if value := ctx.Value(paramForce); value != nil {
		s.Logger().Infof(ctx, "got param %s = %s", paramForce, value)
	}

	clients := jobForDBMS.GetDmsAccounts(ctx)
	process := tools.NewBatch[*aliyun.DmsClient, any](ctx)
	process.Run(clients, s.syncInstanceDatabase)

	return nil, process.Error()
}

func (s *DBMSMetaSync) syncInstanceDatabase(ctx context.Context, client *aliyun.DmsClient) (any, error) {
	var hostList []string
	instanceParam := ctx.Value(paramHost)
	if instanceParam != nil {
		if customInsHost, ok := instanceParam.(string); ok {
			hostList = strings.Split(strings.Replace(strings.TrimSpace(customInsHost), "，", ",", -1), ",")
		}
	}

	var forceUpdate bool
	forceParam := ctx.Value(paramForce)
	if forceParam != nil {
		if force, ok := forceParam.(string); ok {
			forceUpdate = strings.EqualFold(force, "true")
		}
	}

	dbInstance, err := client.ListInstancesAll(ctx)
	if err != nil {
		return nil, err
	}

	count := atomic.NewInt64(0)
	initRdsInstanceDBProcess := tools.NewBatch[*dmsEnterprise.ListInstancesResponseBodyInstanceListInstance, any](ctx)
	initRdsInstanceDBProcess.Run(dbInstance, func(ctx context.Context, instance *dmsEnterprise.ListInstancesResponseBodyInstanceListInstance) (any, error) {
		if len(hostList) > 0 {
			has := false
			for _, ins := range hostList {
				if strings.EqualFold(ins, pointer.Value(instance.Host)) {
					has = true
					break
				}
			}
			if !has {
				return nil, nil
			}
		}

		if forceUpdate {
			n := time.Now()
			s.Logger().Infof(ctx, "sync instance meta: %s", pointer.Value(instance.InstanceId))
			ok, err := client.SyncInstanceMeta(ctx, pointer.Value(instance.InstanceId))
			if err != nil {
				s.Logger().Errorf(ctx, "sync instance meta error: %s", err.Error())
			}
			s.Logger().Errorf(ctx, "sync instance meta result: %v, duration: %s", ok, time.Since(n).String())
			// 阿里云DMS同步元数据接口为异步接口，需要等待一段时间后再获取数据库列表
			// 目前暂时没有办法判断同步是否完成，所以这里等待5秒
			time.Sleep(5 * time.Second)
		}

		db, err := client.ListDatabasesAll(ctx, pointer.Value(instance.InstanceId))
		if err != nil {
			s.Logger().Errorf(ctx, "list database error: %s", err.Error())
			return nil, err
		}
		rdsInstanceDB := make([]*jobForDBMS.RdsInstanceDB, 0)
		for _, database := range db {
			rdsInstanceDB = append(rdsInstanceDB, s.initRdsInstanceDB(ctx, client, instance, database))
		}
		process := tools.NewBatch[*jobForDBMS.RdsInstanceDB, any](ctx, batch.WithBatchSize(10))
		process.Run(rdsInstanceDB, func(ctx context.Context, database *jobForDBMS.RdsInstanceDB) (any, error) {
			return nil, database.Save()
		})
		count.Add(int64(len(db)))
		return nil, process.Error()
	})

	s.Logger().Infof(ctx, "%s sync database count: %s", client.Name(), count.String())
	return nil, initRdsInstanceDBProcess.Error()
}

func (s *DBMSMetaSync) initRdsInstanceDB(ctx context.Context, client client.IClient, ins *dmsEnterprise.ListInstancesResponseBodyInstanceListInstance, db *dmsEnterprise.ListDatabasesResponseBodyDatabaseListDatabase) *jobForDBMS.RdsInstanceDB {
	catalogName := ""
	if *db.DbType == "postgresql" || *db.DbType == "oracle" {
		tmp1 := strings.Split(*db.SearchName, "【")
		tmp2 := strings.Split(tmp1[0], ":")
		catalogName = tmp2[len(tmp2)-1]
	}

	return &jobForDBMS.RdsInstanceDB{
		Model:            s.dbms.Model(ctx),
		CloudAccountName: client.Name(),
		Alias:            pointer.Value(ins.InstanceAlias),
		DatabaseId:       pointer.Value(db.DatabaseId),
		DataLinkName:     pointer.Value(ins.DataLinkName),
		DBType:           pointer.Value(db.DbType),
		EnvType:          pointer.Value(db.EnvType),
		CatalogName:      catalogName,
		SchemaName:       pointer.Value(db.SchemaName),
		SearchName:       pointer.Value(db.SearchName),
		Host:             pointer.Value(db.Host),
		Port:             int(*db.Port),
		Logic:            false,
	}
}
