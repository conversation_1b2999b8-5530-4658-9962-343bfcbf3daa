package jobSyncHbr

import (
	"strconv"
	"strings"
)

func StringOrDef(str string, def string) string {
	if str == "" {
		return def
	}
	return str
}

func String(str *string) string {
	if str == nil {
		return ""
	}
	return strings.TrimSpace(*str)
}

func Int64(i *int64) int64 {
	if i == nil {
		return 0
	}
	return *i
}

func MapDef[K comparable, V any](m map[K]V, key K, val V) V {
	if v, ok := m[key]; ok {
		return v
	}
	return val
}

func Bool(bl *bool) bool {
	if bl == nil {
		return false
	}
	return *bl
}

func Atoi(str string) int {
	v, _ := strconv.Atoi(str)
	return v
}

func normalizedEngineType(engine string) string {
	switch strings.ToLower(engine) {
	case "mysql":
		return "MySQL"
	case "redis":
		return "Redis"
	case "sqlserver":
		return "SQLServer"
	case "postgresql":
		return "PostgreSQL"
	case "oracle":
		return "Oracle"
	default:
		return engine
	}
}
