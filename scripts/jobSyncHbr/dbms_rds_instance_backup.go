package jobSyncHbr

import (
	"errors"

	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"
)

type BackupType string

const (
	BackupTypeHaiFei       BackupType = "海飞备份"
	BackupTypeHBR          BackupType = "HBR备份"
	BackupTypeDXC          BackupType = "DXC备份"
	BackupTypeDiskSnapshot BackupType = "磁盘快照"
	BackupTypeLocalDisk    BackupType = "本地备份"
	BackupTypeUnknown      BackupType = "未知"
)

func (b BackupType) String() string {
	return string(b)
}

type RdsInstanceBackup struct {
	models.Model

	Host         string     `gorm:"type:varchar(64);not null;uniqueIndex:idx_host_port" json:"host"`
	Port         int        `gorm:"not null;uniqueIndex:idx_host_port" json:"port"`
	DBType       string     `gorm:"type:varchar(64)" json:"db_type"`
	BackupType   BackupType `gorm:"type:varchar(64);uniqueIndex:idx_host_port" json:"backup_type"`
	BackupStatus string     `gorm:"type:varchar(64)" json:"backup_status"`
}

func (r *RdsInstanceBackup) Save() error {
	var err error
	rds := &RdsInstanceBackup{}
	if err = r.Orm().
		Where("host = ? and port = ? and backup_type = ?", r.Host, r.Port, r.BackupType).
		First(rds).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			r.ID = 0
			err = nil
		}
	}
	if err != nil {
		return err
	}
	if rds.ID > 0 {
		r.ID = rds.ID
		if rds.BackupType != r.BackupType || rds.BackupStatus != r.BackupStatus {
			return r.Orm().Model(r).Updates(r).Error
		}
		// 更新时间
		return r.Orm().Model(r).
			Where("host = ? and port = ? and backup_type = ?", r.Host, r.Port, r.BackupType).
			Updates(map[string]interface{}{}).Error
	}
	return r.Orm().Create(r).Error
}
