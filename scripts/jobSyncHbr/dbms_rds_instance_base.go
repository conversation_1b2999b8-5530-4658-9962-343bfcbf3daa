package jobSyncHbr

import (
	"context"
	"errors"
	"time"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"

	"git.haier.net/devops/hcms-task-center/core/config"
)

const (
	FlagEmpty Flag = iota
	FlagEnableDMS
	FlagEnableDAS
)

type DBStatus string

func (s DBStatus) String() string {
	return string(s)
}

const (
	Normal     DBStatus = "Normal"
	Offline    DBStatus = "Offline"
	PreOffline DBStatus = "PreOffline"
	Invalid    DBStatus = "Invalid"
	Unknown    DBStatus = "Unknown"
)

type RdsInstanceBase struct {
	models.Model

	// 云厂商供应商信息
	CloudVendor      hbc.CloudVendor `gorm:"type:varchar(128);not null" json:"cloud_vendor"`
	CloudAccountName string          `gorm:"type:varchar(128);not null" json:"cloud_account_name"`
	Region           string          `gorm:"type:varchar(128);not null" json:"region"`
	Zone             string          `gorm:"type:varchar(128)" json:"zone"`

	// 实例信息
	ALMSCode string `gorm:"type:varchar(128);index:idx_scode" json:"alm_scode"` // 实例所属项目S码
	Project  string `gorm:"type:varchar(128);index:idx_project" json:"project"` // 实例所属项目
	Env      string `gorm:"type:varchar(128);not null" json:"env"`              // 实例所属环境

	InstanceID         string    `gorm:"type:varchar(128);not null;uniqueIndex:udx_instance_id" json:"instance_id"`            // 实例id
	PrimaryInstanceID  string    `gorm:"type:varchar(128)" json:"primary_instance_id"`                                         // 主实例id
	InstanceType       string    `gorm:"type:varchar(128);not null" json:"instance_type"`                                      // 主实例，只读实例等
	InstanceStatus     string    `gorm:"type:varchar(128);not null" json:"instance_status"`                                    // 实例状态
	ConnAddr           string    `gorm:"type:varchar(255);not null;index:idx_conn_addr_port" json:"conn_addr"`                 // 实例连接串
	ConnPort           int64     `gorm:"not null;index:idx_conn_addr_port" json:"conn_port"`                                   // 实例连接端口
	EngineType         string    `gorm:"type:varchar(64);not null;index:idx_engine_type_version;" json:"engine_type"`          // 数据库类型
	EngineMajorVersion string    `gorm:"type:varchar(128);not null;index:idx_engine_type_version" json:"engine_major_version"` // 数据库大版本
	EngineMinorVersion string    `gorm:"type:varchar(128);not null;index:idx_engine_type_version" json:"engine_minor_version"` // 数据库小版本
	VpcID              string    `gorm:"type:varchar(128)"`                                                                    // 所属VPC
	ClassCode          string    `gorm:"type:varchar(128);not null" json:"class_code"`                                         // 规格码
	StorageType        string    `gorm:"type:varchar(128);not null" json:"storage_type"`                                       // 存储类型
	Category           string    `gorm:"type:varchar(128)" json:"category"`                                                    // 类型：企业版/基础版/高可用版
	CreateAt           time.Time `json:"create_at"`                                                                            // 实例创建时间
	Description        string    `gorm:"type:text" json:"description"`                                                         // 实例描述
	HDMInstanceId      string    `gorm:"type:varchar(128);not null;index:hdm_instance_id" json:"hdm_instance_id"`              // 实例HDMid
	HDMAccountName     string    `gorm:"type:varchar(128);not null" json:"hdm_account_name"`                                   // 实例接入的HDM账号

	// 实例规格信息
	Cpu               int    `json:"cpu"`     // 单位个
	Memory            int64  `json:"memory"`  // 单位MB
	Storage           int    `json:"storage"` // 单位GB
	AvailabilityValue string `gorm:"type:varchar(128)" json:"availability_value"`

	// 实例功能开关
	Flags  Flag     `json:"instance_flags"`
	Status DBStatus `gorm:"type:varchar(128);not null;default Normal;index:idx_instance_status" json:"status"`
}

func FindInstance(ctx context.Context, ip string, port int) ([]RdsInstanceBase, error) {
	rds := make([]RdsInstanceBase, 0)
	ds := config.Global().GetStore("dbms")
	m := ds.Model(ctx)
	return rds, m.Orm().Model(&RdsInstanceBase{}).
		Where("conn_addr = ? and conn_port = ?", ip, port).
		Find(&rds).Error
}

func (r *RdsInstanceBase) SaveEmpty(ctx context.Context) error {
	rds := &RdsInstanceBase{Model: config.Global().GetStore("dbms").Model(ctx)}

	var err error
	if r.InstanceID != "" {
		err = r.Orm().Where("instance_id = ?", r.InstanceID).Find(rds).Error
	} else if r.ConnAddr != "" && r.ConnPort > 0 {
		err = r.Orm().Where("conn_addr = ? and conn_port = ?", r.ConnAddr, r.ConnPort).Find(rds).Error
	}

	if err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil
		}
		return err
	}

	if rds.InstanceID == "" {
		return nil
	}

	r.InstanceID = rds.InstanceID
	updateParams := make(map[string]interface{})
	if rds.CloudVendor == "" && r.CloudVendor != "" {
		updateParams["cloud_vendor"] = r.CloudVendor
	}
	if rds.CloudAccountName == "" && r.CloudAccountName != "" {
		updateParams["cloud_account_name"] = r.CloudAccountName
	}
	if rds.Region == "" && r.Region != "" {
		updateParams["region"] = r.Region
	}
	if rds.Zone == "" && r.Zone != "" {
		updateParams["zone"] = r.Zone
	}
	if rds.ALMSCode == "" && r.ALMSCode != "" {
		updateParams["alms_code"] = r.ALMSCode
	}
	if rds.Project == "" && r.Project != "" {
		updateParams["project"] = r.Project
	}
	if rds.Env == "" && r.Env != "" {
		updateParams["env"] = r.Env
	}
	if rds.InstanceType == "" && r.InstanceType != "" {
		updateParams["instance_type"] = r.InstanceType
	}
	if rds.InstanceStatus == "" && r.InstanceStatus != "" {
		updateParams["instance_status"] = r.InstanceStatus
	}
	if rds.ConnAddr == "" && r.ConnAddr != "" {
		updateParams["conn_addr"] = r.ConnAddr
	}
	if rds.ConnPort == 0 && r.ConnPort != 0 {
		updateParams["conn_port"] = r.ConnPort
	}
	if rds.EngineType == "" && r.EngineType != "" {
		updateParams["engine_type"] = r.EngineType
	}
	if rds.EngineMajorVersion == "" && r.EngineMajorVersion != "" {
		updateParams["engine_major_version"] = r.EngineMajorVersion
	}
	if rds.EngineMinorVersion == "" && r.EngineMinorVersion != "" {
		updateParams["engine_minor_version"] = r.EngineMinorVersion
	}
	if rds.Category == "" && r.Category != "" {
		updateParams["category"] = r.Category
	}
	if rds.Description == "" && r.Description != "" {
		updateParams["description"] = r.Description
	}
	if rds.Cpu == 0 && r.Cpu != 0 {
		updateParams["cpu"] = r.Cpu
	}
	if rds.Memory == 0 && r.Memory != 0 {
		updateParams["memory"] = r.Memory
	}
	if rds.Storage == 0 && r.Storage != 0 {
		updateParams["storage"] = r.Storage
	}
	if rds.AvailabilityValue == "" && r.AvailabilityValue != "" {
		updateParams["availability_value"] = r.AvailabilityValue
	}
	if r.HDMInstanceId != "" && r.HDMInstanceId != rds.HDMInstanceId {
		updateParams["hdm_instance_id"] = r.HDMInstanceId
	}
	if r.HDMAccountName != "" && r.HDMAccountName != rds.HDMAccountName {
		updateParams["hdm_account_name"] = r.HDMAccountName
	}
	if r.Status != "" && r.Status != rds.Status {
		updateParams["status"] = r.Status
	}
	if len(updateParams) == 0 {
		return nil
	}
	return r.Orm().Model(r).Where("instance_id = ?", rds.InstanceID).Updates(updateParams).Error
}

func (r *RdsInstanceBase) Save() error {
	var err error
	rds := &RdsInstanceBase{}
	if err = r.Orm().
		Where("instance_id = ?", r.InstanceID).
		First(rds).Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			r.ID = 0
			err = nil
		}
	}
	if err != nil {
		return err
	}
	if rds.ID > 0 {
		r.ID = rds.ID
		if rds.CloudVendor != r.CloudVendor ||
			rds.CloudAccountName != r.CloudAccountName ||
			rds.Region != r.Region ||
			rds.Zone != r.Zone ||
			rds.ALMSCode != r.ALMSCode ||
			rds.Project != r.Project ||
			rds.Env != r.Env ||
			rds.InstanceID != r.InstanceID ||
			rds.PrimaryInstanceID != r.PrimaryInstanceID ||
			rds.InstanceType != r.InstanceType ||
			rds.InstanceStatus != r.InstanceStatus ||
			rds.ConnAddr != r.ConnAddr ||
			rds.ConnPort != r.ConnPort ||
			rds.EngineType != r.EngineType ||
			rds.EngineMajorVersion != r.EngineMajorVersion ||
			rds.EngineMinorVersion != r.EngineMinorVersion ||
			rds.VpcID != r.VpcID ||
			rds.ClassCode != r.ClassCode ||
			rds.StorageType != r.StorageType ||
			rds.Category != r.Category ||
			rds.CreateAt != r.CreateAt ||
			rds.Cpu != r.Cpu ||
			rds.Memory != r.Memory ||
			rds.Storage != r.Storage ||
			rds.Flags != r.Flags {
			return r.Orm().Model(r).Updates(r).Error
		}
		// 更新时间
		return r.Orm().Model(r).
			Where("instance_id = ?", r.InstanceID).
			Updates(map[string]interface{}{}).Error
	}
	return r.Orm().Create(r).Error
}
