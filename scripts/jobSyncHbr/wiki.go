package jobSyncHbr

import (
	"context"

	"git.haier.net/devops/ops-golang-common/sdk/feishu"
	"github.com/chyroc/lark"
)

func GetPrivateBackupInfo(ctx context.Context, client *feishu.Client, token string) ([][]lark.SheetContent, error) {
	node, err := client.GetWikiObject(ctx, token)
	if err != nil {
		return nil, err
	}

	return getSheetValuesFromNode(ctx, node, client, err)
}
func GetPrivateInstanceInfo(ctx context.Context, client *feishu.Client, token string) ([][]lark.SheetContent, error) {
	node, err := client.GetWikiObject(ctx, token)
	if err != nil {
		return nil, err
	}
	return getSheetValuesFromNode(ctx, node, client, err)
}

func getSheetValuesFromNode(ctx context.Context, node *lark.GetWikiNodeRespNode, client *feishu.Client, err error) ([][]lark.SheetContent, error) {
	sheetToken := node.ObjToken
	sheetList, _, err := client.Drive.GetSheetList(ctx, &lark.GetSheetListReq{
		SpreadSheetToken: sheetToken,
	})

	if err != nil {
		return nil, err
	}
	sheet := sheetList.Sheets[0]
	return client.GetSheetValueAll(ctx, sheetToken, sheet.SheetID)
}

func CleanupPrivateDatabaseSheetDataValidationDropdown(ctx context.Context, client *feishu.Client, sheetToken string) error {
	sheet, err := client.GetSheetSheet(ctx, sheetToken, 0)
	if err != nil {
		return err
	}
	valueRange := feishu.GetSheetValueRange(sheet)
	return removeDataValidationDropdown(ctx, client, sheetToken, valueRange)
}

func removeDataValidationDropdown(ctx context.Context, client *feishu.Client, sheetToken string, valueRange string) error {
	// 获取全部的下拉菜单
	resp, _, err := client.Drive.GetSheetDataValidationDropdown(ctx, &lark.GetSheetDataValidationDropdownReq{
		SpreadSheetToken:   sheetToken,
		Range:              valueRange,
		DataValidationType: "list",
	})
	// 清除下拉菜单
	if len(resp.DataValidations) > 0 {
		delRange := &lark.DeleteSheetDataValidationDropdownReqDataValidationRange{Range: valueRange}
		// 删除全部的下拉菜单
		validationIds := make([]int64, len(resp.DataValidations))
		for i, validation := range resp.DataValidations {
			validationIds[i] = validation.DataValidationID
		}
		delRange.DataValidationIDs = validationIds
		_, _, err := client.Drive.DeleteSheetDataValidationDropdown(ctx, &lark.DeleteSheetDataValidationDropdownReq{
			SpreadSheetToken:     sheetToken,
			DataValidationRanges: []*lark.DeleteSheetDataValidationDropdownReqDataValidationRange{delRange},
		})
		return err
	}
	return err
}
