package jobSyncHbr

import (
	"context"
	"runtime"
	"strconv"
	"strings"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/hcam"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/feishu"
	"github.com/chyroc/lark"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

const purpose string = "HBR"

type JobSyncHbr struct {
	feiShuClient *feishu.Client
	*base.JobBase
}

func NewSyncHbr() *JobSyncHbr {
	c := config.Global()
	return &JobSyncHbr{
		feiShuClient: c.FeiShuConfig.NewFeiShuClient(),
		JobBase: base.NewJobBase(
			"SYNC_HBR_CLIENT",
			"同步HBR备份",
			base.NewSchedule(),
			taskmodels.TaskCategoryDBMS,
			c.GetStore("dbms"),
		),
	}
}

func Name() string {
	return base.TaskSyncHbr
}

func (j *JobSyncHbr) Run(ctx context.Context) (map[string]any, error) {
	var err error
	exec := func(f func(ctx context.Context) error) {
		if err != nil {
			return
		}

		err = f(ctx)
	}

	exec(j.syncHBRClients)
	exec(j.syncPrivateBackupInfo)
	exec(j.dumpBackupInfoToSheet)

	if err != nil {
		return nil, err
	}

	return nil, nil
}

func (j *JobSyncHbr) syncHBRClients(ctx context.Context) error {
	savers := make([]SaveAble, 0)
	aliConfig := config.Global().AliConfig
	for _, region := range aliConfig.HBR.Regions {
		for _, engine := range aliConfig.HBR.Engines {
			client := hcam.
				NewAccountManager(*config.Global().GetDefaultStore()).
				AliyunHbrClient(ctx, aliConfig.Account, purpose, region)

			bkIns, err := client.DescribeUniBackupClientsAll(ctx, engine)
			if err != nil {
				return err
			}
			b := batch.NewBatch[*aliyun.UniBackupInstance, SaveAble](batch.WithCtx(ctx), batch.WithBatchSize(runtime.NumCPU()))
			b.Run(bkIns, func(ctx context.Context, ins *aliyun.UniBackupInstance) (SaveAble, error) {
				// 忽略172的HBR备份
				if strings.HasPrefix(ins.Ipv4, "172.") {
					return nil, nil
				}
				port := 0
				tmp := strings.Split(ins.Name, "-")
				if len(tmp) > 1 {
					port = Atoi(tmp[1])
				}
				if engine == "ORACLE" && port == 0 {
					port = 1521
				}
				status := ins.AgentStatus
				if status == "INSTALLED" {
					status = "NORMAL"
				}
				return &RdsInstanceBackup{
					Model:        j.Model(ctx),
					Host:         ins.Ipv4,
					Port:         port,
					DBType:       normalizedEngineType(engine),
					BackupType:   BackupTypeHBR,
					BackupStatus: status,
				}, nil
			})
			savers = append(savers, b.Outs()...)
		}
	}

	threadCount, _ := config.Global().CommonConf.GetInt("sync-bhr-thread-count")
	return BatchSave(ctx, savers, j.Logger(), threadCount)
}

func (j *JobSyncHbr) syncPrivateBackupInfo(ctx context.Context) error {
	threadCount, _ := config.Global().CommonConf.GetInt("sync-bhr-thread-count")
	wikiNodeToken, _ := config.Global().CommonConf.GetString("sync-hbr-wiki-node")
	hfBackupsRows, err := GetPrivateBackupInfo(ctx, j.feiShuClient, wikiNodeToken)
	if err != nil {
		return err
	}

	saver := make([]SaveAble, 0)
	for _, row := range hfBackupsRows[1:] {
		bakType := BackupTypeHaiFei
		byt := strings.ToUpper(String(row[2].String))
		switch byt {
		case "", "未知", "UNKNOWN":
			continue
		case "海飞备份":
			bakType = BackupTypeHaiFei
		case "DXC备份":
			bakType = BackupTypeDXC
		case "磁盘快照":
			bakType = BackupTypeDiskSnapshot
		case "HBR备份":
			bakType = BackupTypeHBR
		default:
			bakType = BackupType(byt)
		}

		ip := String(row[0].String)
		port := int(Int64(row[1].Int))
		ins, err := FindInstance(ctx, ip, port)
		if err != nil {
			continue
		}
		if len(ins) == 0 {
			j.Errorf(ctx, "host %s:%d not found", ip, port)
			continue
		}

		saver = append(saver, &RdsInstanceBackup{
			Model:        j.Model(ctx),
			Host:         ip,
			Port:         port,
			DBType:       ins[0].EngineType,
			BackupType:   bakType,
			BackupStatus: "NORMAL",
		})
	}

	j.Infof(ctx, "syncPrivateBackupInfo sync: %d", len(saver))
	if len(saver) == 0 {
		return err
	}
	return BatchSave(ctx, saver, j.Logger(), threadCount)
}

func (j *JobSyncHbr) dumpBackupInfoToSheet(ctx context.Context) error {
	backups := make([]*InstanceBackup, 0)
	backupTypes := []string{
		BackupTypeHaiFei.String(),
		BackupTypeHBR.String(),
		BackupTypeDXC.String(),
		BackupTypeDiskSnapshot.String(),
		BackupTypeLocalDisk.String(),
		BackupTypeUnknown.String(),
	}
	backupStatus := []string{"NORMAL", "UNKNOWN"}

	dropMap := map[string][]string{
		"C": backupTypes,
		"D": backupStatus,
	}

	query := `select * from 
	rds_instance_base i
	left join rds_instance_backup b on i.conn_addr = b.host and i.conn_port = b.port
	where  i.cloud_vendor = 'private'
	and i.engine_type <> 'Redis'
	and i.status <> 'Offline'`

	m := config.Global().GetStore("dbms").Model(ctx)
	if err := m.Orm().Raw(query).Find(&backups).Error; err != nil {
		return err
	}

	// 获取Sheet信息
	wikiNodeToken, _ := config.Global().CommonConf.GetString("sync-hbr-wiki-node")
	sheetToken, err := j.feiShuClient.GetWikiObjectToken(ctx, wikiNodeToken)
	if err != nil {
		return err
	}
	sheet, err := j.feiShuClient.GetSheetSheet(ctx, sheetToken, 0)
	if err != nil {
		return err
	}

	// 清理所有的下拉列表
	if err := CleanupPrivateDatabaseSheetDataValidationDropdown(ctx, j.feiShuClient, sheetToken); err != nil {
		return err
	}

	lines := len(backups) + 1
	values := make([][]interface{}, len(backups))
	for line, backup := range backups {
		cols := make([]interface{}, 0)
		cols = append(cols, backup.ConnAddr)                                                     // A
		cols = append(cols, backup.ConnPort)                                                     // B
		cols = append(cols, StringOrDef(backup.BackupType.String(), BackupTypeUnknown.String())) // C
		cols = append(cols, StringOrDef(backup.BackupStatus, "UNKNOWN"))                         // D
		values[line] = cols
	}
	// 根据列生成对应的值范围
	valueRange := sheet.SheetID + "!A2:" + string(rune('A'+len(values[0])-1)) + strconv.Itoa(lines)
	// 将值存入在线文档
	cells, err := j.feiShuClient.SetSheetValue(ctx, sheetToken, valueRange, values...)
	j.Infof(ctx, "update sheet for %d cells: %s", cells, valueRange)
	if err != nil {
		return err
	}

	// 设置对应列的下拉列表
	for idx, values := range dropMap {
		dropRange := sheet.SheetID + "!" + idx + "2:" + idx + strconv.Itoa(lines)
		j.Infof(ctx, "CreateSheetDataValidationDropdown for %s", dropRange)
		_, _, err := j.feiShuClient.Drive.CreateSheetDataValidationDropdown(ctx, &lark.CreateSheetDataValidationDropdownReq{
			SpreadSheetToken:   sheetToken,
			Range:              dropRange,
			DataValidationType: "list",
			DataValidation: &lark.CreateSheetDataValidationDropdownReqDataValidation{
				ConditionValues: values,
			},
		})
		if err != nil {
			j.Errorf(ctx, "CreateSheetDataValidationDropdown failed: %s", err)
		}
	}

	return nil
}

type InstanceBackup struct {
	RdsInstanceBase
	RdsInstanceBackup
}
