package jobSyncHbr

import (
	"context"
	"errors"
	"reflect"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/log"
	"go.uber.org/atomic"
)

type SaveAble interface {
	Save() error
}

type UpdateEmptyAble interface {
	SaveEmpty(ctx context.Context) error
}

func BatchSave(ctx context.Context, savers []SaveAble, log *log.Logger, threadCount int) error {
	if len(savers) == 0 {
		return nil
	}
	errMsg := ""
	for _, err := range batchSave(ctx, threadCount, savers...) {
		if err == nil {
			continue
		}
		log.Errorf(ctx, err.Error())
		errMsg += err.<PERSON>rror() + ";"
	}
	if errMsg == "" {
		return nil
	}
	return errors.New(errMsg)
}

func batchSave(ctx context.Context, threadCount int, models ...SaveAble) []error {
	nModels := make([]SaveAble, 0)
	for _, m := range models {
		if m == nil {
			continue
		}
		nModels = append(nModels, m)
	}

	if len(nModels) == 0 {
		return nil
	}
	models = nModels

	b := batch.NewBatch[SaveAble, error](batch.WithBatchSize(threadCount))
	mName := reflect.TypeOf(models[0]).Elem().Name()
	log.Infof(ctx, "Start to save '%s' with thread %d", mName, threadCount)
	process := atomic.NewInt64(0)
	_ = b.Run(models, func(ctx context.Context, model SaveAble) (error, error) {
		defer func() {
			process.Add(1)
			if i := process.Load(); i%10 == 0 {
				log.Infof(ctx, "Save %s process (%d/%d) ...", mName, i, len(models))
			}
		}()
		if model == nil {
			return nil, nil
		}
		return model.Save(), nil
	})
	log.Infof(ctx, "Save %s process (%d/%d), done", mName, process.Load(), len(models))
	return b.Outs()
}

func BatchUpdateEmpty(ctx context.Context, log *log.Logger, threadCount int, savers []UpdateEmptyAble) error {
	if len(savers) == 0 {
		return nil
	}
	errMsg := ""
	for _, err := range updateEmpty(ctx, log, threadCount, savers...) {
		if err == nil {
			continue
		}
		log.Errorf(ctx, err.Error())
		errMsg += err.Error() + ";"
	}
	if errMsg == "" {
		return nil
	}
	return errors.New(errMsg)
}

func updateEmpty(ctx context.Context, log *log.Logger, threadCount int, models ...UpdateEmptyAble) []error {
	nModels := make([]UpdateEmptyAble, 0)
	for _, m := range models {
		if m == nil {
			continue
		}
		nModels = append(nModels, m)
	}

	if len(nModels) == 0 {
		return nil
	}
	models = nModels

	b := batch.NewBatch[UpdateEmptyAble, error](batch.WithBatchSize(threadCount))
	mName := reflect.TypeOf(models[0]).Elem().Name()
	log.Infof(ctx, "Start to SaveEmpty '%s' with thread %d", mName, threadCount)
	process := atomic.NewInt64(0)
	_ = b.Run(models, func(ctx context.Context, model UpdateEmptyAble) (error, error) {
		defer func() {
			process.Add(1)
			if i := process.Load(); i%10 == 0 {
				log.Infof(ctx, "SaveEmpty %s process (%d/%d) ...", mName, i, len(models))
			}
		}()
		if model == nil {
			return nil, nil
		}
		return model.SaveEmpty(ctx), nil
	})
	log.Infof(ctx, "SaveEmpty %s process (%d/%d), done", mName, process.Load(), len(models))
	return b.Outs()
}
