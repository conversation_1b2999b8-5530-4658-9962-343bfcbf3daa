package jobSyncMiddleware

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/ormtype"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	iamModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/rocketmq/v2/model"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func (j *SyncMiddleware) syncHuaweiRocketMQInfo(ctx context.Context, cli client.IClient) ([]*models.MiddlewareInfo, error) {
	defClient := cli.(*huaweicloud.IamClient)
	projects, err := defClient.ListProjects(ctx)
	if err != nil {
		return nil, err
	}
	regions, err := defClient.ListRegions(ctx)
	if err != nil {
		return nil, err
	}

	projectBatch := tools.NewBatch[iamModel.ProjectResult, []*models.MiddlewareInfo](ctx)
	projectBatch.Run(projects, func(ctx context.Context, project iamModel.ProjectResult) ([]*models.MiddlewareInfo, error) {
		var targetRegion *iamModel.Region
		for _, region := range regions {
			if region.Id == project.Name {
				targetRegion = &region
				break
			}
		}
		if targetRegion == nil {
			j.Warnf(ctx, "unexpected regionId: %s, skip ...", project.Name)
			return nil, nil
		}
		infoList, err := j.getHuaweiRocketMQMiddleware(ctx, defClient, *targetRegion, project)
		if err != nil {
			fmt.Println(err)
		}
		j.Infof(ctx, "get %d records for all regions", len(infoList))
		return infoList, err
	})

	return tools.MergeData(projectBatch.Outs()...), projectBatch.Error()
}

func (j *SyncMiddleware) getHuaweiRocketMQMiddleware(ctx context.Context, defClient client.IClient, region iamModel.Region, project iamModel.ProjectResult) (infoList []*models.MiddlewareInfo, err error) {
	defer func() {
		// 获取到非预期的region时会发生panic
		if panicError := recover(); panicError != nil {
			err = j.handleHuaweiProjectPanic(panicError, ctx, region)
		}
	}()

	roketmqClient := hybrid.AccountManager().HuaweiRocketMQClient(ctx, defClient.Name(), defClient.Tag(), region.Id, project.Id)
	if roketmqClient == nil {
		return nil, nil
	}
	instanceList, listServerError := roketmqClient.ListInstancesOfAll()
	if listServerError != nil {
		j.Logger().Errorf(ctx, "getHuaweiRocketMQMiddleware: %s, project: %s", listServerError, utils.JsonString(project))
		return nil, nil
	}

	epsClient := hybrid.AccountManager().HuaweiEpsClient(ctx, defClient.Name(), defClient.Tag(), region.Id)
	process := tools.NewBatch[model.ShowInstanceResp, *models.MiddlewareInfo](ctx)
	process.Run(instanceList, func(ctx context.Context, input model.ShowInstanceResp) (*models.MiddlewareInfo, error) {
		return j.tranceHuaweiRocketMQ2MiddlewareInfo(ctx, roketmqClient, epsClient, input)
	})
	return process.Outs(), process.Error()
}

func (j *SyncMiddleware) tranceHuaweiRocketMQ2MiddlewareInfo(
	ctx context.Context,
	roketmqClient *huaweicloud.RocketMQClient,
	epsClient *huaweicloud.EpsClient,
	ins model.ShowInstanceResp,
) (*models.MiddlewareInfo, error) {
	instance, err := roketmqClient.ShowInstance(pointer.Value(ins.InstanceId))
	if err != nil {
		return nil, err
	}

	env, scode, project := bizutils.ParseHuaweiEnvProject(ctx, pointer.Value(ins.Name), pointer.Value(ins.EnterpriseProjectId), epsClient, j)
	creationTime, _ := time.Parse(bizutils.HuaweiEcsTimeFormat, pointer.Value(instance.CreatedAt))
	publicEndpoints := make(ormtype.StringSlice, 0)
	privateEndpoints := make(ormtype.StringSlice, 0)
	for _, a := range strings.Split(pointer.Value(instance.PublicipAddress), ",") {
		if strings.TrimSpace(a) == "" {
			continue
		}
		publicEndpoints = append(publicEndpoints, a)
	}
	cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(roketmqClient.Vendor()), roketmqClient.Identifier(), pointer.Value(instance.InstanceId), huawei_product_code_dms)
	return &models.MiddlewareInfo{
		Vendor:           roketmqClient.Vendor(),
		AccountName:      roketmqClient.Name(),
		InstanceId:       pointer.Value(instance.InstanceId),
		InstanceName:     pointer.Value(instance.Name),
		CreationTime:     timeutil.ZeroTime(creationTime),
		ExpiredTime:      nil,
		IsDeleted:        pointer.Ptr(false),
		InsType:          pointer.Value(instance.Engine),
		InsVersion:       pointer.Value(instance.EngineVersion),
		InsCategory:      pointer.Value(instance.Type).Value(),
		InsStatus:        pointer.Value(instance.Status),
		ClassCode:        pointer.Value(instance.Specification),
		ResourceGroup:    pointer.Value(instance.EnterpriseProjectId),
		Region:           roketmqClient.Region(),
		Zone:             strings.Join(pointer.Value(instance.AvailableZones), ","),
		PrivateEndpoints: &privateEndpoints,
		PublicEndpoints:  &publicEndpoints,
		PayType:          getHuaweiRocketMQMiddlewarePayType(instance),
		Description:      pointer.Value(instance.Description),
		DiskSize:         uint64(pointer.Value(instance.TotalStorageSpace)),
		DiskType:         "",
		TopicNum:         0,
		TopicQuota:       0,
		GroupNum:         0,
		GroupQuota:       0,
		MaxTPS:           0,
		MaxBandwidth:     0,
		VpcId:            pointer.Value(instance.VpcId),
		SubnetId:         pointer.Value(instance.SubnetId),
		Env:              env,
		Project:          project,
		SCode:            bizutils.SwapSCode(scode, cmdb.Scode),
		AggregatedId:     cmdb.AggregatedID,
	}, nil
}

func getHuaweiRocketMQMiddlewarePayType(ins *model.ShowInstanceResponse) string {
	switch pointer.Value(ins.ChargingMode) {
	case 0:
		return "Postpaid"
	default:
		return "Prepaid"
	}
}
