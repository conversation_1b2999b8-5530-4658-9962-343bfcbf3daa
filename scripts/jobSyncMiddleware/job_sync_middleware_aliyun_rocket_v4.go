package jobSyncMiddleware

import (
	"context"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/utils/timeutil"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/ormtype"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	rocketmq "github.com/alibabacloud-go/ons-20190214/v2/client"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func (j *SyncMiddleware) syncAliyunRocketMQV4Info(ctx context.Context, cli client.IClient) ([]*models.MiddlewareInfo, error) {
	regions := []string{"cn-qingdao", "cn-beijing"}

	process := tools.NewBatch[string, []*models.MiddlewareInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.MiddlewareInfo, error) {
		rocketMQClient := hybrid.AccountManager().AliyunRocketMQv4Client(ctx, cli.Name(), cli.Tag(), regionId)
		instanceList, err := rocketMQClient.OnsInstanceInServiceList(ctx)
		if err != nil {
			return nil, err
		}

		if len(instanceList) == 0 {
			return nil, nil
		}
		getMongoAttrProcess := tools.NewBatch[*rocketmq.OnsInstanceInServiceListResponseBodyDataInstanceVO, *models.MiddlewareInfo](ctx)
		getMongoAttrProcess.Run(instanceList, func(ctx context.Context, ins *rocketmq.OnsInstanceInServiceListResponseBodyDataInstanceVO) (*models.MiddlewareInfo, error) {
			attr, err := rocketMQClient.OnsInstanceBaseInfo(pointer.Value(ins.InstanceId))
			if err != nil {
				return nil, err
			}

			// 实例创建、过期时间
			createAt, _ := time.Parse(bizutils.DefaultTimeFormatter, pointer.Value(attr.CreateTime))
			expireAt := tools.UnixToTime(pointer.Value(attr.ReleaseTime))
			// 实例类型
			var instanceType, payType string
			switch pointer.Value(attr.InstanceType) {
			case 0:
				instanceType = "standard"
				payType = "PostPay"
			case 1:
				instanceType = "enhanced"
				payType = "PrePay"
			}
			// 连接串
			publicEndpoints := ormtype.StringSlice{pointer.Value(attr.Endpoints.HttpInternetEndpoint)}
			privateEndpoints := ormtype.StringSlice{pointer.Value(attr.Endpoints.HttpInternalEndpoint)}
			// scode
			var scode, project string
			if nil != ins.Tags {
				for _, tag := range ins.Tags.Tag {
					if strings.Contains(pointer.Value(tag.Key), "scode") {
						scode = pointer.Value(tag.Value)
						break
					}
				}

			}
			if scode != "" && bizutils.IsSCode(scode) {
				p, _ := api.HdsClient().QueryAlmProject(scode)
				project = p.Id
			}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.Value(attr.InstanceId), aliyun_product_code_ons)
			return &models.MiddlewareInfo{
				Vendor:           cli.Vendor(),
				AccountName:      cli.Name(),
				InstanceId:       pointer.Value(attr.InstanceId),
				InstanceName:     pointer.Value(attr.InstanceName),
				CreationTime:     timeutil.ZeroTime(createAt),
				ExpiredTime:      timeutil.ZeroTime(expireAt),
				IsDeleted:        nil,
				InsType:          string(hbc.RocketMQ),
				InsVersion:       "4.0",
				InsCategory:      instanceType,
				InsStatus:        getV4MqInstanceStatus(attr),
				Region:           cli.Region(),
				PayType:          payType,
				PrivateEndpoints: &privateEndpoints,
				PublicEndpoints:  &publicEndpoints,
				Description:      pointer.Value(attr.Remark),
				TopicNum:         int(pointer.Value(attr.TopicCapacity)),
				TopicQuota:       int(pointer.Value(attr.TopicCapacity)),
				Env:              common.EnvProd,
				Project:          project,
				SCode:            bizutils.SwapSCode(scode, cmdb.Scode),
				AggregatedId:     cmdb.AggregatedID,
			}, nil
		})
		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), bizutils.WarpClientError(cli, process.Error())
}

func getV4MqInstanceStatus(attr *rocketmq.OnsInstanceBaseInfoResponseBodyInstanceBaseInfo) string {
	switch pointer.Value(attr.InstanceStatus) {
	case 0:
		return "ToBeDeployed"
	case 2:
		return "Arrears"
	case 5:
		return "Running"
	case 7:
		return "Updating"
	default:
		return "Unknown"
	}
}
