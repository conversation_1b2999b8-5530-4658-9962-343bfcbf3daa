package jobSyncMiddleware

import (
	"context"

	"git.haier.net/devops/ops-golang-common/utils/timeutil"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/ormtype"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	kafka "github.com/alibabacloud-go/alikafka-20190916/v3/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var aliyun_product_code_kafka = []string{"alikafka"}

func (j *SyncMiddleware) syncAliyunKafkaInfo(ctx context.Context, cli client.IClient) ([]*models.MiddlewareInfo, error) {
	regions := []string{"cn-qingdao", "cn-beijing"}

	process := tools.NewBatch[string, []*models.MiddlewareInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.MiddlewareInfo, error) {
		kafkaClient := hybrid.AccountManager().AliyunKafkaClient(ctx, cli.Name(), cli.Tag(), regionId)
		resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		instanceList, err := kafkaClient.GetInstanceList(ctx)
		if err != nil {
			return nil, err
		}
		getMongoAttrProcess := tools.NewBatch[*kafka.GetInstanceListResponseBodyInstanceListInstanceVO, *models.MiddlewareInfo](ctx)
		getMongoAttrProcess.Run(instanceList, func(ctx context.Context, attr *kafka.GetInstanceListResponseBodyInstanceListInstanceVO) (*models.MiddlewareInfo, error) {

			privateEndpoints := ormtype.StringSlice{pointer.Value(attr.DomainEndpoint)}
			publicEndpoints := ormtype.StringSlice{pointer.Value(attr.SaslDomainEndpoint)}
			env, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(attr.ResourceGroupId))
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.Value(attr.InstanceId), aliyun_product_code_kafka)
			return &models.MiddlewareInfo{
				Vendor:           cli.Vendor(),
				AccountName:      cli.Name(),
				InstanceId:       pointer.Value(attr.InstanceId),
				InstanceName:     pointer.Value(attr.Name),
				CreationTime:     timeutil.ZeroTime(tools.UnixToTime(pointer.Value(attr.CreateTime))),
				ExpiredTime:      timeutil.ZeroTime(tools.UnixToTime(pointer.Value(attr.ExpiredTime))),
				InsType:          string(hbc.Kafka),
				InsVersion:       pointer.Value(attr.UpgradeServiceDetailInfo.Current2OpenSourceVersion),
				InsCategory:      pointer.Value(attr.SpecType),
				InsStatus:        getKafkaInstanceStatus(attr),
				ResourceGroup:    pointer.Value(attr.ResourceGroupId),
				Region:           cli.Region(),
				Zone:             pointer.Value(attr.ZoneId),
				PrivateEndpoints: &privateEndpoints,
				PublicEndpoints:  &publicEndpoints,
				ClassCode:        pointer.Value(attr.IoMaxSpec),
				PayType:          getKafkaPayType(attr),
				Description:      pointer.Value(attr.KmsKeyId),
				DiskSize:         uint64(pointer.Value(attr.DiskSize)),
				DiskType:         getKafkaDiskType(attr),
				TopicNum:         int(pointer.Value(attr.UsedTopicCount)),
				TopicQuota:       int(pointer.Value(attr.TopicNumLimit)),
				GroupNum:         int(pointer.Value(attr.UsedGroupCount)),
				MaxTPS:           int(pointer.Value(attr.IoMax)),
				MaxBandwidth:     int(pointer.Value(attr.EipMax)),
				VpcId:            pointer.Value(attr.VpcId),
				SubnetId:         pointer.Value(attr.VSwitchId),
				Env:              env,
				Project:          project,
				SCode:            bizutils.SwapSCode(scode, cmdb.Scode),
				AggregatedId:     cmdb.AggregatedID,
			}, nil

		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), bizutils.WarpClientError(cli, process.Error())
}

func getKafkaDiskType(attr *kafka.GetInstanceListResponseBodyInstanceListInstanceVO) string {
	switch pointer.Value(attr.DiskType) {
	case 0:
		return "ESSD"
	default:
		return "SSD"
	}
}

func getKafkaPayType(attr *kafka.GetInstanceListResponseBodyInstanceListInstanceVO) string {
	if pointer.Value(attr.PaidType) == 0 {
		return "PrePay"
	}
	return "PostPay"
}

func getKafkaInstanceStatus(attr *kafka.GetInstanceListResponseBodyInstanceListInstanceVO) string {
	switch pointer.Value(attr.ServiceStatus) {
	case 0:
		return "ToBeDeployed"
	case 1:
		return "Preparing"
	case 2:
		return "Initializing"
	case 3:
		return "Starting"
	case 5:
		return "Running"
	case 6:
		return "Migrating"
	case 7:
		return "PreparingUpgrade"
	case 8:
		return "Upgrading"
	case 9:
		return "PreparingChange"
	case 10:
		return "Released"
	case 11:
		return "Changing"
	case 12:
		return "Expired"
	}
	return "Unknown"
}
