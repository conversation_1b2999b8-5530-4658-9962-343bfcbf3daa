package jobSyncMiddleware

import (
	"context"
	"fmt"
	"runtime/debug"
	"strings"
	"sync"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/notice"
)

type middlewareFun func(ctx context.Context, cli client.IClient) ([]*models.MiddlewareInfo, error)

func (j *SyncMiddleware) syncAliyunMiddlewares(ctx context.Context, cli client.IClient) ([]*models.MiddlewareInfo, error) {
	middlewareFunctions := []middlewareFun{
		j.syncAliyunRocketMQInfo,
		j.syncAliyunRocketMQV4Info,
		j.syncAliyunKafkaInfo,
		j.syncAliyunRabbitMQInfo,
	}

	mtx := new(sync.Mutex)
	wg := new(sync.WaitGroup)
	middlewares := make([]*models.MiddlewareInfo, 0)
	for _, f := range middlewareFunctions {
		wg.Add(1)
		go func(f middlewareFun) {
			defer wg.Done()
			data, err := f(ctx, cli)
			if err != nil {
				if strings.Contains(err.Error(), "StatusCode: 403") {
					j.Logger().Warnf(ctx, "阿里云中间件同步失败: %s", err.Error())
				} else {
					notice.SendErrorMessage("阿里云中间件同步失败", fmt.Sprintf("阿里云中间件数据获取异常: %s \n\n %s", err.Error(), debug.Stack()))
				}
				return
			}

			mtx.Lock()
			defer mtx.Unlock()
			middlewares = append(middlewares, data...)
		}(f)
	}
	wg.Wait()
	return middlewares, nil
}
