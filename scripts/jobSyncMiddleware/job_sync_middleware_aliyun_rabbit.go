package jobSyncMiddleware

import (
	"context"

	"git.haier.net/devops/ops-golang-common/utils/timeutil"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/ormtype"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	rabbitmq "github.com/alibabacloud-go/amqp-open-20191212/v2/client"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var aliyun_product_code_ons = []string{"ons"}

func (j *SyncMiddleware) syncAliyunRabbitMQInfo(ctx context.Context, cli client.IClient) ([]*models.MiddlewareInfo, error) {
	switch cli.Name() {
	case "hr690l", "hr690m", "海尔商空智能云":
		// NOTE-********: 这三个账号目前没有实例，直接查询会报无权限，忽略
		return nil, nil
	}

	// NOTE-********: 目前阿里云只有这两个地域有开通rabbitmq，其他地区查询会报无权限
	regions := []string{"cn-qingdao", "cn-beijing"}
	process := tools.NewBatch[string, []*models.MiddlewareInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.MiddlewareInfo, error) {
		rabbitMQClient := hybrid.AccountManager().AliyunRabbitMQClient(ctx, cli.Name(), cli.Tag(), regionId)
		//resCli := hybrid.AccountManager().AliyunResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		instanceList, err := rabbitMQClient.ListInstanceOfAll(ctx)
		if err != nil {
			return nil, err
		}
		getMongoAttrProcess := tools.NewBatch[*rabbitmq.ListInstancesResponseBodyDataInstances, *models.MiddlewareInfo](ctx)
		getMongoAttrProcess.Run(instanceList, func(ctx context.Context, attr *rabbitmq.ListInstancesResponseBodyDataInstances) (*models.MiddlewareInfo, error) {
			env := bizutils.TryParseEnv(pointer.Value(attr.InstanceName))
			scode := bizutils.TryParseSCode(pointer.Value(attr.InstanceName))
			var project string
			if scode != "" {
				projectInfo, _ := api.HdsClient().QueryAlmProject(scode)
				if projectInfo != nil {
					project = projectInfo.Id
				}
			}

			privateEndpoints := ormtype.StringSlice{pointer.Value(attr.PrivateEndpoint)}
			publicEndpoints := ormtype.StringSlice{pointer.Value(attr.PublicEndpoint)}
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.Value(attr.InstanceId), aliyun_product_code_ons)
			return &models.MiddlewareInfo{
				Vendor:           cli.Vendor(),
				AccountName:      cli.Name(),
				InstanceId:       pointer.Value(attr.InstanceId),
				InstanceName:     pointer.Value(attr.InstanceName),
				CreationTime:     timeutil.ZeroTime(tools.UnixToTime(pointer.Value(attr.OrderCreateTime))),
				ExpiredTime:      timeutil.ZeroTime(tools.UnixToTime(pointer.Value(attr.ExpireTime))),
				InsType:          string(hbc.RabbitMQ),
				InsCategory:      pointer.Value(attr.InstanceType),
				InsStatus:        pointer.Value(attr.Status),
				Region:           cli.Region(),
				PrivateEndpoints: &privateEndpoints,
				PublicEndpoints:  &publicEndpoints,
				PayType:          pointer.Value(attr.OrderType),
				DiskSize:         uint64(pointer.Value(attr.StorageSize)),
				MaxTPS:           int(pointer.Value(attr.MaxTps)),
				MaxBandwidth:     int(pointer.Value(attr.MaxTps)),
				//DiskType:         getKafkaDiskType(attr),
				//VpcId:            pointer.Value(attr.VpcId),
				//SubnetId:         pointer.Value(attr.VSwitchId),
				Env:          env,
				Project:      project,
				SCode:        bizutils.SwapSCode(scode, cmdb.Scode),
				AggregatedId: cmdb.AggregatedID,
			}, nil

		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), bizutils.WarpClientError(cli, process.Error())
}
