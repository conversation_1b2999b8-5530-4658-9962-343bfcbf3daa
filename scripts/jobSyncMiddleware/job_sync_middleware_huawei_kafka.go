package jobSyncMiddleware

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/ormtype"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	iamModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/kafka/v2/model"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var huawei_product_code_dms = []string{"hws.service.type.dms"}

func (j *SyncMiddleware) syncHuaweiKafkaInfo(ctx context.Context, cli client.IClient) ([]*models.MiddlewareInfo, error) {
	defClient := cli.(*huaweicloud.IamClient)
	projects, err := defClient.ListProjects(ctx)
	if err != nil {
		return nil, err
	}
	regions, err := defClient.ListRegions(ctx)
	if err != nil {
		return nil, err
	}

	projectBatch := tools.NewBatch[iamModel.ProjectResult, []*models.MiddlewareInfo](ctx)
	projectBatch.Run(projects, func(ctx context.Context, project iamModel.ProjectResult) ([]*models.MiddlewareInfo, error) {
		var targetRegion *iamModel.Region
		for _, region := range regions {
			if region.Id == project.Name {
				targetRegion = &region
				break
			}
		}
		if targetRegion == nil {
			j.Warnf(ctx, "unexpected regionId: %s, skip ...", project.Name)
			return nil, nil
		}
		infoList, err := j.getHuaweiKafkaMiddleware(ctx, defClient, *targetRegion, project)
		if err != nil {
			fmt.Println(err)
		}
		j.Infof(ctx, "get %d records for all regions", len(infoList))
		return infoList, err
	})

	return tools.MergeData(projectBatch.Outs()...), projectBatch.Error()
}

func (j *SyncMiddleware) handleHuaweiProjectPanic(panicError any, ctx context.Context, region iamModel.Region) error {
	var msg string
	switch pe := panicError.(type) {
	case error:
		msg = pe.Error()
	case string:
		msg = pe
	}

	if strings.Contains(msg, "not in the following supported regions") {
		j.Logger().Warnf(ctx, "%s getHuaweiRegionEcs: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, "unexpected regionId") {
		j.Logger().Warnf(ctx, "%s getHuaweiRegionEcs: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, "failed to get project id, No project id found") {
		j.Logger().Warnf(ctx, "%s getHuaweiRegionEcs: %s", region.Id, msg)
	} else {
		return errors.New(msg)
	}
	return nil
}

func (j *SyncMiddleware) getHuaweiKafkaMiddleware(ctx context.Context, defClient client.IClient, region iamModel.Region, project iamModel.ProjectResult) (infoList []*models.MiddlewareInfo, err error) {
	defer func() {
		// 获取到非预期的region时会发生panic
		if panicError := recover(); panicError != nil {
			err = j.handleHuaweiProjectPanic(panicError, ctx, region)
		}
	}()

	kafkaClient := hybrid.AccountManager().HuaweiKafkaClient(ctx, defClient.Name(), defClient.Tag(), region.Id, project.Id)
	if kafkaClient == nil {
		return nil, nil
	}
	instanceList, listServerError := kafkaClient.ListInstanceOfAll()
	if listServerError != nil {
		j.Logger().Errorf(ctx, "getHuaweiKafkaMiddleware: %s, project: %s", listServerError, utils.JsonString(project))
		return nil, nil
	}

	epsClient := hybrid.AccountManager().HuaweiEpsClient(ctx, defClient.Name(), defClient.Tag(), region.Id)
	process := tools.NewBatch[model.ShowInstanceResp, *models.MiddlewareInfo](ctx)
	process.Run(instanceList, func(ctx context.Context, input model.ShowInstanceResp) (*models.MiddlewareInfo, error) {
		return j.tranceHuaweiRds2RdsInfo(ctx, kafkaClient, epsClient, input)
	})
	return process.Outs(), process.Error()
}

func (j *SyncMiddleware) tranceHuaweiRds2RdsInfo(
	ctx context.Context,
	kafkaClient *huaweicloud.KafkaClient,
	epsClient *huaweicloud.EpsClient,
	ins model.ShowInstanceResp,
) (*models.MiddlewareInfo, error) {
	instance, err := kafkaClient.ShowInstance(pointer.Value(ins.InstanceId))
	if err != nil {
		return nil, err
	}

	env, scode, project := bizutils.ParseHuaweiEnvProject(ctx, pointer.Value(ins.Name), pointer.Value(ins.EnterpriseProjectId), epsClient, j)
	creationTime, _ := time.Parse(bizutils.HuaweiEcsTimeFormat, pointer.Value(instance.CreatedAt))
	publicEndpoints := make(ormtype.StringSlice, 0)
	privateEndpoints := make(ormtype.StringSlice, 0)
	for _, a := range strings.Split(pointer.Value(instance.KafkaPrivateConnectAddress), ",") {
		if strings.TrimSpace(a) == "" {
			continue
		}
		privateEndpoints = append(privateEndpoints, a)
	}
	for _, a := range strings.Split(pointer.Value(instance.PublicConnectAddress), ",") {
		if strings.TrimSpace(a) == "" {
			continue
		}
		publicEndpoints = append(publicEndpoints, a)
	}
	cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(kafkaClient.Vendor()), kafkaClient.Identifier(), pointer.Value(instance.InstanceId), huawei_product_code_dms)
	return &models.MiddlewareInfo{
		Vendor:           kafkaClient.Vendor(),
		AccountName:      kafkaClient.Name(),
		InstanceId:       pointer.Value(instance.InstanceId),
		InstanceName:     pointer.Value(instance.Name),
		CreationTime:     timeutil.ZeroTime(creationTime),
		ExpiredTime:      nil,
		IsDeleted:        pointer.Ptr(false),
		InsType:          pointer.Value(instance.Engine),
		InsVersion:       pointer.Value(instance.EngineVersion),
		InsCategory:      pointer.Value(instance.Type).Value(),
		InsStatus:        pointer.Value(instance.Status),
		ClassCode:        pointer.Value(instance.Specification),
		ResourceGroup:    pointer.Value(instance.EnterpriseProjectId),
		Region:           kafkaClient.Region(),
		Zone:             strings.Join(pointer.Value(instance.AvailableZones), ","),
		PrivateEndpoints: &privateEndpoints,
		PublicEndpoints:  &publicEndpoints,
		PayType:          getHuaweiMiddlewarePayType(instance),
		Description:      pointer.Value(instance.Description),
		DiskSize:         uint64(pointer.Value(instance.TotalStorageSpace)),
		DiskType:         "",
		TopicNum:         0,
		TopicQuota:       0,
		GroupNum:         parsePartitionNum(instance),
		GroupQuota:       0,
		MaxTPS:           0,
		MaxBandwidth:     0,
		VpcId:            pointer.Value(instance.VpcId),
		SubnetId:         pointer.Value(instance.SubnetId),
		Env:              env,
		Project:          project,
		SCode:            bizutils.SwapSCode(scode, cmdb.Scode),
		AggregatedId:     cmdb.AggregatedID,
	}, nil
}

func parsePartitionNum(ins *model.ShowInstanceResponse) int {
	i, _ := strconv.Atoi(pointer.Value(ins.PartitionNum))
	return i
}

func getHuaweiMiddlewarePayType(ins *model.ShowInstanceResponse) string {
	switch pointer.Value(ins.ChargingMode) {
	case 0:
		return "Postpaid"
	default:
		return "Prepaid"
	}
}
