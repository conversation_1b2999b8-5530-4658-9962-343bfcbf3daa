package jobSyncMiddleware

var empty = struct{}{}
var ignoreRabbitMQAccounts = map[string]struct{}{
	"海尔商空智能云":     empty,
	"haierhealth": empty,
	"haier690":    empty,
	"hr690e":      empty,
	"hr690f":      empty,
	"hr690g":      empty,
	"hr690h":      empty,
	"hr690i":      empty,
	"hr690j":      empty,
	"hr690k":      empty,
	"hr690l":      empty,
	"hr690m":      empty,
}
var ignoreRocketMQAccounts = map[string]struct{}{
	//"海尔商空智能云": empty,
	//"haierhealth":    empty,
	//"haier690":       empty,
	//"hr690e":         empty,
	//"hr690f":         empty,
	//"hr690g":         empty,
	//"hr690h":         empty,
	//"hr690i":         empty,
	//"hr690j":         empty,
	//"hr690k":         empty,
	//"hr690l":         empty,
	//"hr690m":         empty,
}
