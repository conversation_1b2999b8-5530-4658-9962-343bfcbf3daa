package jobSyncMiddleware

import (
	"context"
	"sync"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
)

func (j *SyncMiddleware) syncHuaweiMiddlewares(ctx context.Context, cli client.IClient) ([]*models.MiddlewareInfo, error) {
	middlewareFunctions := []middlewareFun{
		j.syncHuaweiKafkaInfo,
		j.syncHuaweiRocketMQInfo,
		j.syncHuaweiRabbitMQInfo,
	}

	mtx := new(sync.Mutex)
	wg := new(sync.WaitGroup)
	middlewares := make([]*models.MiddlewareInfo, 0)
	for _, f := range middlewareFunctions {
		wg.Add(1)
		go func(f middlewareFun) {
			defer wg.Done()
			data, err := f(ctx, cli)
			if err != nil {
				j.<PERSON><PERSON>().Warnf(ctx, "华为云中间件同步失败: %s", err.Error())
				return
			}

			mtx.Lock()
			defer mtx.Unlock()
			middlewares = append(middlewares, data...)
		}(f)
	}
	wg.Wait()
	return middlewares, nil
}
