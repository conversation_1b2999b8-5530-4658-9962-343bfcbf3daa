package jobSyncHostPrivate

import (
	"fmt"
	"testing"

	"git.haier.net/devops/ops-golang-common/haierapi"
	"git.haier.net/devops/ops-golang-common/sdk/private"
	"github.com/kr/pretty"
	"github.com/stretchr/testify/assert"

	"git.haier.net/devops/hcms-task-center/biz/api"
)

func TestParseRegion(t *testing.T) {
	city := []string{
		"01-青岛",
		"01-青岛市",
		"02-北京",
		"03-南昌",
		"04-重庆",
		"05-武汉",
		"05-武汉",
		"06-顺德",
		"07-佛山",
		"08-烟台",
		"09-郑州",
		"10-合肥",
		"11-大连",
		"12-贵州",
		"13-沈阳",
		"14-天津",
		"15-遵义",
		"20-成都",
		"24-哈尔滨",
		"25-海口",
		"41-西安",
		"49-喀山",
		"50-卡马河畔切尔尼",
		"N/A",
		"青岛",
	}
	for _, c := range city {
		fmt.Println(private.ParseChineseRegion(getUnifiedCityName(c)).String())
	}
}

func TestHocClient_ListCMDB(t *testing.T) {
	pageNum := 1
	pageSize := 500
	a := assert.New(t)
	c := api.HocClient()
	resp, err := c.ListCMDB(pageNum, pageSize, func(request *haierapi.HocListCMDBRequest) {
		//request.Type = "1"
		request.ResIp = "1"
		request.IpValue = "************"
	})

	if !a.NoError(err) {
		t.Fail()
	}

	pretty.Println(resp.List)
}
