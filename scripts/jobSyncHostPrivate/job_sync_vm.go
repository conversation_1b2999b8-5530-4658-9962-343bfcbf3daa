package jobSyncHostPrivate

import (
	"context"
	"strings"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/feishu"
	"git.haier.net/devops/ops-golang-common/sdk/private"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	"github.com/aws/smithy-go/ptr"
	"github.com/vmware/govmomi/vim25/mo"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewSyncPrivateCloud() *JobSyncVM {
	c := config.Global()
	return &JobSyncVM{
		feiShuClient: c.FeiShuConfig.NewFeiShuClient(),
		JobBase: base.NewJobBase(
			"SYNC_PRIVATE_VM",
			"同步私有云虚拟机及网络信息",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type JobSyncVM struct {
	machines     map[string]mo.HostSystem
	feiShuClient *feishu.Client
	*base.JobBase
}

func (j *JobSyncVM) Name() string {
	return base.TaskCloudSyncVM
}

func (j *JobSyncVM) Run(ctx context.Context) (map[string]any, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.Private}, hbc.VSPHERE, []string{bizutils.PurposeAdmin})
	if err != nil {
		return nil, err
	}

	// 同步网络信息
	//if err := j.SyncSubnetFromWiki(ctx); err != nil {
	//	return nil, err
	//}

	// 同步虚拟机信息
	vCenterApiProcess := tools.NewBatch[client.IClient, []*models.HostInfo](ctx)
	vCenterApiProcess.Run(defaultClients, func(ctx context.Context, cli client.IClient) ([]*models.HostInfo, error) {
		switch c := cli.(type) {
		case *private.VCenterClient:
			return j.syncVMInfo(ctx, c)
		}
		return nil, nil
	})

	data := orderedmap.New[string, *models.HostInfo]()
	for _, host := range tools.MergeData(vCenterApiProcess.Outs()...) {
		data.Store(host.AccountName+host.PrivateIp, host)
	}

	// 将信息存入数据库
	return nil, bizutils.CreateOrUpdateHost(ctx, data.Slice()...)
}

func (j *JobSyncVM) syncVMInfo(ctx context.Context, cli *private.VCenterClient) ([]*models.HostInfo, error) {
	var err error
	var hostSystems []*models.HostInfo
	if cli.Name() != rrs {
		hostSystems, err = j.describeHostSystems(ctx, cli)
		if err != nil {
			return nil, err
		}
	}
	virtualMachine, err := j.DescribeVirtualMachine(ctx, cli)
	if err != nil {
		return nil, err
	}

	return tools.MergeData(hostSystems, virtualMachine), err
}
func (j *JobSyncVM) DescribeVirtualMachine(ctx context.Context, cli *private.VCenterClient) ([]*models.HostInfo, error) {
	ip_10246 := "10.246"
	privateIpFunc := func(ip string) bool {
		return strings.HasPrefix(ip, "10.")
	}
	vms, err := cli.DescribeVirtualMachine(ctx)
	if err != nil {
		return nil, err
	}

	hostInfos := make([]*models.HostInfo, 0)
	for _, vm := range vms {
		privateIp := vm.Summary.Guest.IpAddress
		if !privateIpFunc(privateIp) {
			for _, net := range vm.Guest.Net {
				if len(net.IpAddress) > 0 {
					for _, ip := range net.IpAddress {
						// rrs 10.246
						if cli.Name() == rrs {
							if strings.HasPrefix(ip, ip_10246) {
								privateIp = ip
								break
							}
						} else {
							// hongdao & huangdao
							if privateIpFunc(ip) {
								privateIp = ip
								break
							}
						}
					}
				}
			}
		}
		if cli.Name() == rrs && !strings.HasPrefix(privateIp, ip_10246) {
			continue
		}
		hostInfos = append(hostInfos, &models.HostInfo{
			Model:         j.Model(ctx),
			Vendor:        cli.Vendor(),
			AccountName:   cli.Name(),
			InstanceId:    privateIp,
			InstanceName:  vm.Summary.Guest.HostName,
			CreationTime:  timeutil.ZeroTimePtr(vm.Config.CreateDate),
			ExpiredTime:   nil,
			IsDeleted:     ptr.Bool(false),
			PrivateIp:     privateIp,
			NetworkType:   "Network",
			OsType:        getVmOsType(vm),
			OsName:        vm.Summary.Guest.GuestFullName,
			OsArch:        common.OsArchX8664,
			Numa:          ptr.Bool(false),
			ImageId:       vm.Summary.Guest.GuestFullName,
			Cpu:           uint64(vm.Summary.Config.NumCpu),
			Memory:        uint64(vm.Summary.Config.MemorySizeMB),
			DiskSize:      float64(vm.Summary.Storage.Committed / 1024 / 1024),
			HostStatus:    bizutils.UnifyHostStatus(getVmStatus(vm)),
			UniHostStatus: getPrivateUniHostStatus(bizutils.UnifyHostStatus(getVmStatus(vm))),
			HostType:      vm.Self.Type,
			HostInsId:     vm.Summary.Runtime.Host.Value,
			Region:        cli.Region(),
			Zone:          private.GetVCenterLocation(cli.Name(), "").Zone.String(),
			// Description:   vm.Summary.Config.Annotation,
			Sn: vm.Summary.Config.Uuid,
		})
	}
	return hostInfos, nil
}

func getVmStatus(vm mo.VirtualMachine) string {
	if vm.Guest == nil {
		return ""
	}
	return vm.Guest.GuestState
}

func getVmOsType(vm mo.VirtualMachine) string {
	if vm.Guest == nil {
		return ""
	}

	osType := strings.TrimRight(vm.Guest.GuestFamily, "Guest")
	switch osType {
	case "linux":
		return common.OsTypeLinux
	case "window":
		return common.OsTypeWindows
	case "vmnix-x86":
		return "Vmnix"
	case "":
		if strings.HasPrefix(vm.Config.GuestFullName, "CentOS") ||
			strings.HasPrefix(vm.Config.GuestFullName, "Red Hat") {
			return common.OsTypeLinux
		}
		fallthrough
	default:
		return osType
	}
}

func (j *JobSyncVM) describeHostSystems(ctx context.Context, cli *private.VCenterClient) ([]*models.HostInfo, error) {
	hosts, err := cli.DescribeHostSystems(ctx)
	if err != nil {
		return nil, err
	}
	hostInfos := make([]*models.HostInfo, len(hosts))
	for i, hostSystem := range hosts {
		hostInfos[i] = &models.HostInfo{
			Model:         j.Model(ctx),
			Vendor:        cli.Vendor(),
			AccountName:   cli.Name(),
			InstanceId:    hostSystem.Summary.Config.Name,
			InstanceName:  hostSystem.Summary.Host.Value,
			CreationTime:  timeutil.ZeroTimePtr(hostSystem.Runtime.BootTime),
			ExpiredTime:   nil,
			IsDeleted:     ptr.Bool(false),
			PrivateIp:     hostSystem.Summary.Config.Name,
			NetworkType:   "Network",
			OsType:        hostSystem.Summary.Config.Product.OsType,
			OsName:        hostSystem.Summary.Config.Product.Name,
			OsArch:        common.OsArchX8664,
			Numa:          ptr.Bool(false),
			ImageId:       hostSystem.Summary.Config.Product.FullName,
			Cpu:           getCpuInfo(hostSystem),
			Memory:        getMemorySize(hostSystem),
			DiskSize:      0,
			HostStatus:    bizutils.UnifyHostStatus(string(hostSystem.Runtime.PowerState)),
			UniHostStatus: getPrivateUniHostStatus(bizutils.UnifyHostStatus(string(hostSystem.Runtime.PowerState))),
			HostType:      hostSystem.Summary.Host.Type,
			Region:        cli.Region(),
			Zone:          private.GetVCenterLocation(cli.Name(), "").Zone.String(),
			// Description:   "VCenter主机",
		}
	}

	return hostInfos, nil
}

func getMemorySize(hostSystem mo.HostSystem) uint64 {
	if hostSystem.Summary.Hardware == nil {
		return 0
	}
	return uint64(hostSystem.Summary.Hardware.MemorySize / 1024 / 1024)
}

func getCpuInfo(hostSystem mo.HostSystem) uint64 {
	if hostSystem.Hardware == nil {
		return 0
	}

	return uint64(hostSystem.Hardware.CpuInfo.NumCpuThreads)
}

func getPrivateUniHostStatus(status string) string {
	status = strings.TrimSpace(strings.ToLower(status))
	switch status {
	case "running", "unallocated":
		return "running" // 运行中
	case "stopped":
		return "stopped" // 已停止
	default:
		return "other"
	}
}
