package jobSyncHostPrivate

import (
	"context"
	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/feishu"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
)

func SyncFromWiki(ctx context.Context, feiShuClient *feishu.Client, model models.Model) ([]*models.HostInfo, error) {
	wikiNodeToken, _ := config.Global().CommonConf.GetString("sync-vm-wiki-node")
	wikiNode, err := feiShuClient.GetWikiObject(ctx, wikiNodeToken)
	if err != nil {
		return nil, err
	}
	sheet, err := feiShuClient.GetSheetSheet(ctx, wikiNode.ObjToken, 0)
	if err != nil {
		return nil, err
	}
	rows, err := feiShuClient.GetSheetValueAll(ctx, wikiNode.ObjToken, sheet.SheetID)
	if err != nil {
		return nil, err
	}

	hosts := make([]*models.HostInfo, 0)
	for _, row := range rows[1:] {
		privateIp := getRowString("A", row...)
		if privateIp == "" {
			continue
		}
		sCode := getRowString("B", row...)
		instanceName := getRowString("D", row...)
		osType := getRowString("E", row...)
		osName := getRowString("F", row...)
		CPU := getRowString("G", row...)
		MemoryInGi := getRowString("H", row...)
		hostType := bizutils.UnifyHostType(getRowString("J", row...))
		region := getRowString("K", row...)
		hostStatus := getRowString("L", row...)
		description := getRowString("M", row...)
		zone1 := getRowString("N", row...)
		//zone2 := getRowString("O", row...)
		location1 := getRowString("P", row...)
		location2 := getRowString("Q", row...)
		brandName := getRowString("R", row...)
		brandModel := getRowString("S", row...)
		brandSN := getRowString("T", row...)
		createTime := getRowString("U", row...)
		expireTime := getRowString("V", row...)
		provider := getRowString("AA", row...)
		specifiedEnv := getRowString("AG", row...)
		var env = "prod"
		if strings.HasPrefix(privateIp, "10.200.73") {
			env = "test"
		}
		if specifiedEnv == "测试" {
			env = "test"
		}
		isDeleted := false
		if getRowString("Y", row...) == "是" {
			isDeleted = true
		}

		createAt, _ := time.Parse("2006/01/02", createTime)
		expireAt, _ := time.Parse("2006/01/02", expireTime)
		cpu, _ := strconv.Atoi(CPU)
		memoryInGi, _ := strconv.Atoi(MemoryInGi)
		hosts = append(hosts, &models.HostInfo{
			Model:         model,
			Vendor:        hbc.Private,
			AccountName:   getAccount(zone1),
			InstanceId:    privateIp,
			InstanceName:  instanceName,
			CreationTime:  timeutil.ZeroTime(createAt),
			ExpiredTime:   timeutil.ZeroTime(expireAt),
			IsDeleted:     &isDeleted,
			PrivateIp:     privateIp,
			NetworkType:   "Network",
			Scode:         sCode,
			Env:           env,
			ResourceGroup: sCode,
			OsType:        bizutils.UnifyOsType(osType),
			OsName:        osName,
			OsArch:        common.OsArchX8664,
			Numa:          nil,
			ImageId:       osName,
			Cpu:           uint64(cpu),
			Memory:        uint64(memoryInGi * 1024),
			DiskSize:      0,
			DiskType:      "local_disk",
			HostStatus:    bizutils.UnifyHostStatus(hostStatus),
			HostType:      hostType,
			Region:        region,
			Zone:          getZone(zone1),
			Rack:          strings.Join([]string{location1, location2}, "-"),
			ProviderName:  provider,
			BrandName:     brandName,
			HostModel:     brandModel,
			Sn:            brandSN,
			Description:   description,
		})
	}
	return hosts, nil
}
