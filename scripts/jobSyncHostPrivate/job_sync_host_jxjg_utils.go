package jobSyncHostPrivate

import (
	"fmt"
	"strings"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/haierapi"
	"git.haier.net/devops/ops-golang-common/sdk/private"
)

func getSCode(v *haierapi.HocResourceInfo) string {
	switch v.ApplicationID {
	case "", "无", "N/A":
		return "default"
	default:
		if strings.HasSuffix(v.ApplicationID, "无S码") {
			return "default"
		}
	}

	return v.ApplicationID
}

func getEnv(v *haierapi.HocResourceInfo) string {
	switch {
	case strings.Contains(v.FunctionDescription, "生产"),
		strings.Contains(v.FunctionDescription, "prod"):
		return common.EnvProd
	case strings.Contains(v.FunctionDescription, "测试"),
		strings.Contains(v.FunctionDescription, "test"):
		return common.EnvTest
	case strings.Contains(v.FunctionDescription, "开发"),
		strings.Contains(v.FunctionDescription, "dev"):
		return common.EnvDev
	}

	return v.FunctionDescription
}

func parseRegion(v *haierapi.HocResourceInfo) string {
	region := private.ParseChineseRegion(getUnifiedCityName(v.City)).String()
	switch region {
	case "N/A":
		return ""
	default:
		return region
	}
}

func parseZone(v *haierapi.HocResourceInfo) string {
	tmp := strings.Split(v.Room, "-")
	if len(tmp) > 1 {
		return tmp[1]
	}
	tmp = strings.Split(fmt.Sprintf("%s_%s", v.Park, v.Room), "-")
	if len(tmp) > 1 {
		return tmp[1]
	}
	return tmp[0]
}

func getUnifiedCityName(cityName string) string {
	city := strings.ReplaceAll(cityName, "市", "")
	tmp := strings.Split(city, "-")
	if len(tmp) > 1 {
		return tmp[1]
	}
	return tmp[0]
}

func getSn(sn string) string {
	if sn == "" || sn == "N/A" {
		return ""
	}
	return sn
}

func getHostType(hostType string) string {
	switch hostType {
	case "1":
		return common.HostTypePhysical
	case "2":
		return common.HostTypeVirtual
	}
	return ""
}

func getOsType(name string) string {
	name = strings.ToLower(name)
	switch {
	case strings.Contains(name, "windows"):
		return common.OsTypeWindows
	case strings.Contains(name, "linux"):
		return common.OsTypeLinux
	case strings.Contains(name, "centos"):
		return common.OsTypeLinux
	case strings.Contains(name, "ubuntu"):
		return common.OsTypeLinux
	}
	return name
}

func getStatus(v *haierapi.HocResourceInfo) (string, bool) {
	switch v.Status {
	case "运行":
		return common.StatusRunning, false
	}

	return common.StatusStopped, true
}

func getHostIP(v *haierapi.HocResourceInfo) string {
	ip := v.OsIP
	// 只取OSIp，IpList中的信息不准确，影响波动大
	//if ip == "" && len(v.IPLists) > 0 {
	//	ip = v.IPLists[0]
	//}
	ip = strings.TrimSpace(ip)
	return ip
}
