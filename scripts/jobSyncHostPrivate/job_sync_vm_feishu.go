package jobSyncHostPrivate

import (
	"git.haier.net/devops/ops-golang-common/sdk/private"
	"github.com/chyroc/lark"

	"git.haier.net/devops/hcms-task-center/core/tools"
)

func getAccount(zone string) string {
	switch zone {
	case "黄岛":
		return "huangdao"
	case "红岛":
		return "hongdao"
	default:
		return "none"
	}
}

func getZone(zone string) string {
	switch zone {
	case "黄岛":
		return private.ZoneHuangdaoDX403.String()
	case "红岛":
		return private.ZoneHongdaoYD.String()
	case "大兴":
		return private.ZoneDaxingSJHL.String()
	default:
		return ""
	}
}

func getRowString(col string, row ...lark.SheetContent) string {
	return tools.GetFeiShuRowString(col, row...)
}
