package jobSyncHostPrivate

import (
	"context"
	"strings"

	"git.haier.net/devops/ops-golang-common/haierapi"

	"git.haier.net/devops/hcms-task-center/biz/api"
)

var bu = "智家定制生态圈"

type checkFunc func(*haierapi.HocResourceInfo, context.Context) bool
type rrsCheckFunc func(*haierapi.HocResourceInfo, context.Context) bool

func (j *SyncHostJXJGV2) checkOwner(v *haierapi.HocResourceInfo, ctx context.Context) bool {
	if v.Bu == bu {
		return true
	}
	if v.ApplicationUserCode != "" {
		users, _ := api.HcmsClient().QueryHaierUserInfo(ctx, v.ApplicationUserCode)
		for _, u := range users {
			if u.UserId == v.ApplicationUserCode {
				return strings.Contains(u.Dept, bu)
			}
		}
	}
	if v.Userid != "" {
		users, _ := api.HcmsClient().QueryHaierUserInfo(ctx, v.Userid)
		for _, u := range users {
			if u.UserId == v.Userid {
				return strings.Contains(u.Dept, bu)
			}
		}
	}
	if v.OsAdministerUserCode != "" {
		users, _ := api.HcmsClient().QueryHaierUserInfo(ctx, v.OsAdministerUserCode)
		for _, u := range users {
			if u.UserId == v.OsAdministerUserCode {
				return strings.Contains(u.Dept, bu)
			}
		}
	}
	return false
}

func (j *SyncHostJXJGV2) checkIP(v *haierapi.HocResourceInfo, ctx context.Context) bool {
	ip := getHostIP(v)
	if ip == "" {
		return false
	}

	// 忽略172开头的ip，与腾讯云主机冲突，重复注册了
	if strings.HasPrefix(ip, "172") {
		return false
	}

	if strings.HasPrefix(ip, "10.") {
		result, err := api.HcmsClient().QueryHaierSubnetInfoByIp(ip)
		if err != nil {
			j.Logger().Errorf(ctx, "query subnet info by ip %s error: %s", ip, err.Error())
			return false
		}
		if len(result) > 0 {
			j.Logger().Debugf(ctx, "ip %s is in subnet %s[%s] %s, %s",
				ip, result[0].Vendor, result[0].Region, result[0].Cidr, result[0].Description)
			return false
		}
	}

	return true
}

func (j *SyncHostJXJGV2) checkSource(v *haierapi.HocResourceInfo, ctx context.Context) bool {
	return !(v.SystemSource == "690cmdb")
}

// 目前库里的状态保持原状态，只同步在线并且运行状态正常的主机信息
func (j *SyncHostJXJGV2) checkStatus(v *haierapi.HocResourceInfo, ctx context.Context) bool {
	_, b := getStatus(v)
	return !b
}

// 日日顺
// 专有云中10.246.*.* 这个段是vm中的虚拟机拉取jxjg时需要过滤掉,已在vmware平台拉取.
// 黄岛机房物理机10.246.*.*也是这个网段的拉取jxjg时需要过滤掉,李鹏已经手工录入.
func (j *SyncHostJXJGV2) rrsCheckIP(v *haierapi.HocResourceInfo, ctx context.Context) bool {
	ip := getHostIP(v)
	if ip == "" {
		return false
	}
	if strings.HasPrefix(ip, "10.246") {
		return false
	}
	return true
}

// 日日顺过滤掉除物理机和虚拟机之外的数据 1:物理机 2:虚拟机
func (j *SyncHostJXJGV2) rrsCheckPhysicalVM(v *haierapi.HocResourceInfo, ctx context.Context) bool {
	if v.ResultType != "1" && v.ResultType != "2" {
		return false
	}
	return true
}

func (j *SyncHostJXJGV2) doFilter(v *haierapi.HocResourceInfo, ctx context.Context) bool {

	switch v.Bu {
	case rrsBU:
		for _, f := range []rrsCheckFunc{
			j.checkSource,
			j.rrsCheckPhysicalVM,
			j.rrsCheckIP,
		} {
			if !f(v, ctx) {
				return false
			}
		}
		return true
	default:
		for _, f := range []checkFunc{
			j.checkSource,
			j.checkIP,
			j.checkOwner,
			// j.checkStatus,
		} {
			if !f(v, ctx) {
				return false
			}
		}
		return true
	}
}
