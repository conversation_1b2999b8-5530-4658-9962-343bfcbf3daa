package jobSyncHostPrivate

import (
	"context"
	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/feishu"
	"github.com/LPX3F8/orderedmap"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewSyncWikiCloud() *JobSyncWiki {
	c := config.Global()
	return &JobSyncWiki{
		feiShuClient: c.FeiShuConfig.NewFeiShuClient(),
		JobBase: base.NewJobBase(
			"SYNC_WIKI_PHYSICAL_HOST",
			"同步wiki中的物理机信息",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type JobSyncWiki struct {
	feiShuClient *feishu.Client
	*base.JobBase
}

func (j *JobSyncWiki) Name() string {
	return base.TaskCloudSyncWiki
}

func (j *JobSyncWiki) Run(ctx context.Context) (map[string]any, error) {
	data := orderedmap.New[string, *models.HostInfo]()

	wikiData, _ := SyncFromWiki(ctx, j.feiShuClient, j.Model(ctx))
	for _, host := range wikiData {
		if host.HostType != common.HostTypeVirtual {
			data.Store(host.PrivateIp, host)
		}
	}

	// 将信息存入数据库
	return nil, bizutils.CreateOrUpdateHost(ctx, data.Slice()...)
}
