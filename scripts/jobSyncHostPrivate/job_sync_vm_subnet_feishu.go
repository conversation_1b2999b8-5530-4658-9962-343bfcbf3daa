package jobSyncHostPrivate

import (
	"context"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/private"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
	"github.com/aws/smithy-go/ptr"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
)

func (j *JobSyncVM) SyncSubnetFromWiki(ctx context.Context) error {
	wikiNodeToken, _ := config.Global().CommonConf.GetString("sync-vm-subnet-node")
	wikiNode, err := j.feiShuClient.GetWikiObject(ctx, wikiNodeToken)
	if err != nil {
		return err
	}
	sheet, err := j.feiShuClient.GetSheetSheet(ctx, wikiNode.ObjToken, 0)
	if err != nil {
		return err
	}
	rows, err := j.feiShuClient.GetSheetValueAll(ctx, wikiNode.ObjToken, sheet.SheetID)
	if err != nil {
		return err
	}

	subnets := make([]*models.SubnetInfo, 0)
	for _, row := range rows[1:] {
		region := getRowString("A", row...)
		cidr := getRowString("B", row...)
		desc := getRowString("C", row...)
		status := getRowString("D", row...)
		if cidr == "" {
			continue
		}

		var (
			accountName, zone string
			isDeleted         bool
		)

		if region == "黄岛" {
			accountName = "huangdao"
			zone = private.ZoneHuangdaoDX403.String()
		} else {
			accountName = "hongdao"
			zone = private.ZoneHongdaoYD.String()
		}
		if status == "正常" {
			status = common.StatusAvailable
		} else {
			isDeleted = true
			status = common.StatusUnusable
		}
		region = "cn-qingdao"

		subnets = append(subnets, &models.SubnetInfo{
			Model:        j.Model(ctx),
			Vendor:       hbc.Private,
			AccountName:  accountName,
			CreationTime: nil,
			SubnetId:     stringutil.Md5(cidr),
			Region:       region,
			Zone:         zone,
			State:        status,
			Cidr:         cidr,
			Description:  desc,
			IsDeleted:    ptr.Bool(isDeleted),
		})
	}

	return bizutils.CreateOrUpdateSubnet(ctx, subnets...)
}
