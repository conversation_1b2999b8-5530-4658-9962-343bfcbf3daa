/*
Copyright 2023 LPX-E5BD8(lipengxiang)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package jobSyncHostPrivate

import (
	"context"
	"strings"
	"sync"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/haierapi"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/aws/smithy-go/ptr"
	"github.com/chyroc/lark"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/ormtype"
	"git.haier.net/devops/ops-golang-common/models/uuid"
	"git.haier.net/devops/ops-golang-common/sdk/feishu"
	"github.com/vmware/govmomi/vim25/mo"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewSyncFactory() *JobSyncFactory {
	c := config.Global()
	return &JobSyncFactory{
		feiShuClient: c.FeiShuConfig.NewFeiShuClient(),
		JobBase: base.NewJobBase(
			"SYNC_FACTORY",
			"同步工厂主机信息",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type JobSyncFactory struct {
	machines     map[string]mo.HostSystem
	feiShuClient *feishu.Client
	*base.JobBase
}

const (
	PhysicalHost = "1"
	VirtualHost  = "2"
)

func (j *JobSyncFactory) Name() string {
	return base.TaskSyncFactory
}

func (j *JobSyncFactory) Run(ctx context.Context) (map[string]any, error) {
	hosts, err := j.SyncFromWiki(ctx)
	if err != nil {
		return nil, err
	}

	return nil, bizutils.CreateOrUpdateHost(ctx, hosts...)
}

func (j *JobSyncFactory) SyncFromWiki(ctx context.Context) ([]*models.HostInfo, error) {
	wikiNodeToken, _ := config.Global().CommonConf.GetString("sync-factory-wiki-node")
	wikiNode, err := j.feiShuClient.GetWikiObject(ctx, wikiNodeToken)
	if err != nil {
		return nil, err
	}
	sheet, err := j.feiShuClient.GetSheetSheet(ctx, wikiNode.ObjToken, 0)
	if err != nil {
		return nil, err
	}
	rows, err := j.feiShuClient.GetSheetValueAll(ctx, wikiNode.ObjToken, sheet.SheetID)
	if err != nil {
		return nil, err
	}

	hosts := make([]*models.HostInfo, 0)

	b := tools.NewBatch[[]lark.SheetContent, []*models.HostInfo](ctx, batch.WithBatchSize(5))
	b.Run(rows[1:], func(ctx context.Context, row []lark.SheetContent) ([]*models.HostInfo, error) {
		_hosts := make([]*models.HostInfo, 0)

		privateIp := getRowString("B", row...)
		if privateIp == "" {
			return nil, nil
		}

		hocChan := make(chan *HocCmdbResult, 2)
		var _wg sync.WaitGroup

		_wg.Add(1)
		go func() {
			defer _wg.Done()

			// find from hoc, if found, prefer that
			resp, err := findFromHoc(privateIp, PhysicalHost)
			hocChan <- &HocCmdbResult{
				resp,
				err,
			}
		}()

		_wg.Add(1)
		go func() {
			defer _wg.Done()

			// find from hoc, if found, prefer that
			resp, err := findFromHoc(privateIp, VirtualHost)
			hocChan <- &HocCmdbResult{
				resp,
				err,
			}
		}()
		_wg.Wait()
		close(hocChan)

		for hocCmdbResult := range hocChan {
			list := hocCmdbResult.HocListCMDBResult.List
			if len(list) > 0 {
				for _, host := range list {
					h := transToHostInfo(host, ctx, j.Logger(), j.Model(ctx))
					if h != nil {
						_hosts = append(_hosts, h)
					}
				}
			}
		}

		if len(_hosts) > 1 {
			notice.SendWarnMessageWithGroups(notice.BillDev, "Hoc存在ip冲突："+privateIp, utils.JsonString(_hosts))
			return _hosts, nil
		}

		hostStatus := getRowString("M", row...)
		if len(_hosts) > 0 {
			// 工厂文档还在运行的机器不按照hoc进行下线处理
			if strings.TrimSpace(strings.ToLower(hostStatus)) == "running" {
				_hosts[0].HostStatus, _hosts[0].IsDeleted = common.StatusRunning, ptr.Bool(false)
			}
			return _hosts, nil
		}

		// HOC找不到的话继续处理
		sCode := getRowString("E", row...)
		instanceName := getRowString("A", row...)
		osType := getRowString("F", row...)
		osName := getRowString("G", row...)
		hostType := bizutils.UnifyHostType(getRowString("K", row...))
		region := getRowString("L", row...)
		description := getRowString("N", row...)
		var env = "prod"
		isDeleted := false
		if getRowString("Z", row...) == "是" {
			isDeleted = true
		}

		name := getRowString("C", row...)
		id := getRowString("D", row...)

		h := &models.HostInfo{
			Model:         j.Model(ctx),
			Vendor:        hbc.Factory,
			AccountName:   "-",
			InstanceId:    privateIp,
			InstanceName:  instanceName,
			CreationTime:  nil,
			ExpiredTime:   nil,
			IsDeleted:     &isDeleted,
			PrivateIp:     privateIp,
			NetworkType:   "Network",
			Scode:         sCode,
			Env:           env,
			ResourceGroup: sCode,
			OsType:        bizutils.UnifyOsType(osType),
			OsName:        osName,
			OsArch:        common.OsArchX8664,
			ImageId:       osName,
			DiskSize:      0,
			DiskType:      "local_disk",
			HostStatus:    bizutils.UnifyHostStatus(hostStatus),
			HostType:      hostType,
			Region:        region,
			Description:   description,
			Maintainer:    &ormtype.StringSlice{name + "|" + id},
		}

		h.Sn = strings.TrimSuffix(strings.Join([]string{h.Scode, h.PrivateIp, h.Sn}, "_"), "_")
		h.InstanceId = uuid.HostResourceId(h)

		_hosts = append(_hosts, h)
		return _hosts, nil
	})

	hosts = append(hosts, tools.MergeData(b.Outs()...)...)

	return hosts, nil
}

type HocCmdbResult struct {
	*haierapi.HocListCMDBResult
	error
}

func findFromHoc(privateIp, hostType string) (*haierapi.HocListCMDBResult, error) {
	requestFunc := func(request *haierapi.HocListCMDBRequest) {
		request.ResultType = hostType
		request.IpValue = strings.TrimSpace(privateIp)
		request.ResIp = "1"
	}
	resp, err := api.HocClient().ListCMDB(1, 10, requestFunc)
	return resp, err
}
