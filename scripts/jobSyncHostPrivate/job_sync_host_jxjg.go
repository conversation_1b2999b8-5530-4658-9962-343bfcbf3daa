package jobSyncHostPrivate

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"golang.org/x/sync/errgroup"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/haierapi"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/uuid"
	"github.com/aws/smithy-go/ptr"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

const rrs = "rrs"
const rrsBU = "日日顺物流"
const (
	saveCmdbThreads = 2   // 保存主机信息的并发数
	listCmdbThreads = 2   // 调用CMDB接口的并发数，总线程数需要 = listCmdbThreads x len(syncHostTypes)
	listCmdbPerPage = 200 // 每页主机请求数量
)

var types = []string{"1", "2"}

//
//
//                                                                  ┌─────────────┐
//                                                               ┌─▶│  ListCMDB   │───┐
//                                                               │  └─────────────┘   │
//                              ┌─────────────────────────────┐  │  ┌─────────────┐   │
//                   ┌───go────▶│    syncJXJGHost(Type=1)     │──┴─▶│  ListCMDB   │───┤
//                   │          └─────────────────────────────┘     └─────────────┘   │
//                   │                                                                │   ┌──────────────────┐
//                   │                                                                ├──▶│    originChan    │──┐
//                   │                                                                │   └──────────────────┘  │
//  ┌──────┐         │          ┌─────────────────────────────┐      ┌─────────────┐  │                         │
//  │ Run  │─────────┼───go────▶│    syncJXJGHost(Type=2)     │──┬──▶│  ListCMDB   │──┤                         │
//  └──────┘         │          └─────────────────────────────┘  │   └─────────────┘  │            haierapi.HocResourceInfo
//                   │                                           │   ┌─────────────┐  │                         │
//                   │                                           └──▶│  ListCMDB   │──┘                         │
//                   │                                               └─────────────┘                            │
//                   │                                                                                          │
//                   │          ┌─────────────────────────────┐                                                 │
//                   └───go────▶│         receiveData         │◀────────────────────────────────────────────────┘
//                              └─────────────────────────────┘
//                                             │     ┌────────────────┐    ┌─────────────┐    ┌─────────────┐   ┌────────────────┐
//                                             ├────▶│    doFilter    │───▶│   checkIP   │ ──▶│ checkOwner  │──▶│ saveSaveHosts  │
//                                             │     └────────────────┘    └─────────────┘    └─────────────┘   └────────────────┘
//                                             │     ┌────────────────┐    ┌─────────────┐    ┌─────────────┐   ┌────────────────┐
//                                             ├────▶│    doFilter    │───▶│   checkIP   │ ──▶│ checkOwner  │──▶│ saveSaveHosts  │
//                                             │     └────────────────┘    └─────────────┘    └─────────────┘   └────────────────┘
//                                             │     ┌────────────────┐    ┌─────────────┐    ┌─────────────┐   ┌────────────────┐
//                                             └────▶│    doFilter    │───▶│   checkIP   │ ──▶│ checkOwner  │──▶│ saveSaveHosts  │
//                                                   └────────────────┘    └─────────────┘    └─────────────┘   └────────────────┘

func NewSyncJXJG() *SyncHostJXJGV2 {

	var (
		c = config.Global()
		// originChanLen   = listCmdbThreads * len(types) * listCmdbPerPage // 保证能够存放所有的数据
	)

	return &SyncHostJXJGV2{
		perPage:         listCmdbPerPage,
		listCmdbThreads: listCmdbThreads,
		saveCmdbThreads: saveCmdbThreads,
		syncHostTypes:   types,
		// originChan:      make(chan []*haierapi.HocResourceInfo, originChanLen),
		// wg:              &sync.WaitGroup{},
		saved: new(atomic.Int64),
		JobBase: base.NewJobBase("JXJG_SYNC",
			"同步集团资源到本地数据库",
			base.NewSchedule(
				base.WithHour(0),   // 0点
				base.WithRandMin(), // 任意分钟
				base.WithSec(0),    // 0秒
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type SyncHostJXJGV2 struct {
	syncHostTypes   []string // 需要同步的主机类型，不同类型之间同步时为并发逻辑
	perPage         int      // 每页数量
	listCmdbThreads int      // 调用CMDB接口的并发数，总线程数需要 = listCmdbThreads x len(syncHostTypes)
	saveCmdbThreads int      // 保存主机信息的并发数
	total           int
	saved           *atomic.Int64
	originChan      chan []*haierapi.HocResourceInfo
	// wg              *sync.WaitGroup

	*base.JobBase
}

func (j *SyncHostJXJGV2) Name() string {
	return base.TaskSyncJXJG
}

func (j *SyncHostJXJGV2) Run(ctx context.Context) (map[string]any, error) {
	j.originChan = make(chan []*haierapi.HocResourceInfo, listCmdbThreads*len(types)*listCmdbPerPage)
	eg := errgroup.Group{}
	eg.Go(func() error {
		return j.receiveOriginData(ctx)
	})
	eg.Go(func() error {
		defer func() {
			close(j.originChan)
		}()
		for _, hostType := range j.syncHostTypes {
			if err := j.syncJXJGHost(ctx, hostType); err != nil {
				return err
			}
		}
		return nil
	})

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	j.Logger().Infof(ctx, "sync jxjg host done, total: %d， saved: %d", j.total, j.saved.Load())
	// clear total saved
	j.total = 0
	j.saved.Store(0)
	return nil, nil
}

func (j *SyncHostJXJGV2) syncJXJGHost(ctx context.Context, hostType string) error {
	deadLine, hasDeadline := ctx.Deadline()
	requestFunc := func(request *haierapi.HocListCMDBRequest) {
		request.Type = hostType
	}
	resp, err := api.HocClient().ListCMDB(1, j.perPage, requestFunc)
	if err != nil {
		return err
	}
	pages := make([]int, 0)
	for i := 1; i <= resp.Pages; i++ {
		pages = append(pages, i)
	}

	process := tools.NewBatch[int, any](ctx, batch.WithBatchSize(j.listCmdbThreads))
	process.Run(pages, func(ctx context.Context, page int) (any, error) {
		if hasDeadline && time.Now().After(deadLine) {
			return nil, errors.New("deadline exceeded")
		}
		resp, err := api.HocClient().ListCMDB(page, j.perPage, requestFunc)
		if err != nil {
			return nil, err
		}
		j.originChan <- resp.List
		return nil, nil
	})

	return process.Error()
}

func (j *SyncHostJXJGV2) receiveOriginData(ctx context.Context) error {
	deadline, hasDeadline := ctx.Deadline()
	for hosts := range j.originChan {
		if hasDeadline && time.Now().After(deadline) {
			j.Logger().Errorf(ctx, "receiveOriginData deadline exceeded")
			return errors.New("receiveOriginData deadline exceeded")
		}

		j.total += len(hosts)
		process := tools.NewBatch[*haierapi.HocResourceInfo, any](ctx, batch.WithBatchSize(j.saveCmdbThreads))
		process.Run(hosts, func(ctx context.Context, host *haierapi.HocResourceInfo) (any, error) {
			if j.doFilter(host, ctx) {
				return nil, j.saveSaveHosts(ctx, host)
			}
			return nil, nil
		})

		_ = process.Outs()
		e := process.Errors()
		j.Logger().Infof(ctx, "sync jxjg hosts, total: %d， saved: %d, failed: %d", j.total, j.saved.Load(), len(e))
		if len(e) > 0 {
			j.Logger().Errorf(ctx, "sync jxjg hosts error: %s", tools.MergeErrors(e).Error())
			return fmt.Errorf("sync jxjg hosts error: %s", tools.MergeErrors(e).Error())
		}
	}

	j.Logger().Infof(ctx, "save hosts info done")
	return nil
}

func (j *SyncHostJXJGV2) saveSaveHosts(ctx context.Context, host *haierapi.HocResourceInfo) error {
	h := transToHostInfo(host, ctx, j.Logger(), j.Model(ctx))
	if h == nil {
		return nil
	}
	if err := bizutils.CreateOrUpdateHost(ctx, h); err != nil {
		j.Errorf(ctx, "save host info error %s", err.Error())
		return err
	}
	j.saved.Add(1)
	return nil
}

func transToHostInfo(v *haierapi.HocResourceInfo, ctx context.Context, logger *log.Logger, model models.Model) *models.HostInfo {
	createTime, _ := time.Parse("2006-01-02", v.StartDate)
	termDate, _ := time.Parse("2006-01-02", v.TermDate)
	status, isDeleted := getStatus(v)
	sCode := getSCode(v)
	projectId := v.ApplicationName
	if bizutils.IsSCode(sCode) {
		p, err := api.HdsClient().QueryAlmProject(sCode)
		if err != nil {
			logger.Errorf(ctx, "query alms code %s error: %s", sCode, err.Error())
			return nil
		}
		if p == nil {
			return nil
		}
		u, err := api.HcmsClient().QueryHaierUserInfo(ctx, p.Owner)
		if err != nil {
			logger.Errorf(ctx, "query user %s error: %s", p.Owner, err.Error())
			return nil
		}
		if len(u) > 0 && !strings.Contains(u[0].Dept, bu) && !strings.Contains(u[0].Dept, rrsBU) {
			logger.Debugf(ctx, "user %s dept %s is not belong to [%s] [%s]", u[0].UserName, u[0].Dept, bu, rrsBU)
			return nil
		}
		projectId = p.Id
	}
	accountName := "-"
	var maintainingTeam string
	var monitoringMode int
	if v.Bu == rrsBU {
		accountName = rrs
		monitoringMode = bizutils.MonitoringModeIsHOC // hoc监控
		maintainingTeam = v.OsMaintenanceTeam
	}
	osType := getOsType(v.OsName)
	cpu, _ := strconv.Atoi(v.CPUNumber)
	memory, _ := strconv.Atoi(v.MemoryTotalG)
	disk, _ := strconv.Atoi(v.DiskTotalG)
	h := &models.HostInfo{
		Model:         model,
		Vendor:        hbc.JXJG,
		AccountName:   accountName,
		InstanceId:    v.CmdbID,
		InstanceName:  v.Hostname,
		CreationTime:  timeutil.ZeroTime(createTime),
		ExpiredTime:   timeutil.ZeroTime(termDate),
		IsDeleted:     ptr.Bool(isDeleted),
		PrivateIp:     getHostIP(v),
		VpcId:         v.Intranet,
		SubnetId:      v.Intranet,
		Scode:         sCode,
		Project:       projectId,
		Env:           bizutils.UnifyEnv(getEnv(v)),
		ResourceGroup: sCode,
		OsType:        osType,
		OsName:        v.OsName,
		OsArch:        common.OsArchX8664,
		Numa:          ptr.Bool(false),
		ImageId:       "",
		Cpu:           uint64(cpu),
		Memory:        uint64(memory * 1024),
		DiskSize:      float64(disk * 1024),
		DiskType:      "local",
		HostStatus:    status,
		UniHostStatus: strings.ToLower(status),
		HostType:      getHostType(v.Type),
		Region:        parseRegion(v),
		Zone:          parseZone(v),
		ChargeType:    v.OsMaintenanceTeam,
		Rack:          v.Rack,
		ProviderName:  v.HardwaremaintenanceTeam,
		BrandName:     v.Brand1,
		//Sn:            getSn(v.SerialNumber),  // 大坑：JXJG返回的服务器信息中SN码可能会重复
		Description:     v.Cmporderid,
		MaintainingTeam: maintainingTeam,
		MonitoringMode:  monitoringMode,
	}

	// 根据描述信息，重新设置一下环境
	refreshInstanceEnvByDesc(v, h)
	h.Sn = strings.TrimSuffix(strings.Join([]string{v.CmdbID, h.PrivateIp, h.Sn}, "_"), "_")
	h.InstanceId = uuid.HostResourceId(h)
	return h
}

func refreshInstanceEnvByDesc(v *haierapi.HocResourceInfo, h *models.HostInfo) {
	switch {
	case strings.Contains(v.FunctionDescription, "已退订"):
		h.Env = common.EnvTest // 退订的主机都算测试机，不再运维
	case strings.Contains(v.FunctionDescription, "未退订"):
		h.Env = common.EnvProd // 未退订的主机无法确认环境都算生产，需要提醒业务尽早迁移或下线
	case strings.Contains(v.FunctionDescription, "无订单"):
		h.Env = common.EnvTest // 没有订单的主机都是业务自己搬的，
	}
}
