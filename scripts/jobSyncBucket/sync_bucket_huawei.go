package jobSyncBucket

import (
	"context"
	"fmt"
	"strings"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"github.com/aws/smithy-go/ptr"
	"github.com/huaweicloud/huaweicloud-sdk-go-obs/obs"
)

func (r *BucketSync) syncHuaweiBucketInfo(defClient *huaweicloud.ObsClient, ctx context.Context) ([]*models.BucketInfo, error) {
	bucketList, err := defClient.ListBuckets()
	if err != nil {
		// notfound
		if strings.Contains(err.Error(), "Status=404 Not Found") {
			return nil, nil
		}
		return nil, err
	}
	b := tools.NewBatch[obs.Bucket, *models.BucketInfo](ctx)
	b.Run(bucketList.Buckets, func(ctx context.Context, bucket obs.Bucket) (*models.BucketInfo, error) {
		return r.syncHuaweiRegionRdsInfo(ctx, bucket, defClient)
	})

	return tools.MergeData(b.Outs()), b.Error()
}

func (r *BucketSync) syncHuaweiRegionRdsInfo(ctx context.Context, bucket obs.Bucket, cli *huaweicloud.ObsClient) (*models.BucketInfo, error) {
	metadata, err := cli.GetBucketMetadata(bucket.Name)
	if err != nil {
		// notfound
		if strings.Contains(err.Error(), "Status=404 Not Found") {
			return nil, nil
		}
		return nil, err
	}
	info, err := cli.GetBucketStorageInfo(bucket.Name)
	if err != nil {
		return nil, err
	}
	location, err := cli.GetBucketLocation(bucket.Name)
	if err != nil {
		return nil, err
	}

	// instanceId是包含地区的字符串
	cmdb := getHuaweiCMDBBySupplementId(ctx, cli.Identifier(), bucket.Name)

	// 适用于长期不访问（平均几年访问一次）数据的业务场景。
	var storage, objectCount int64
	var infrequentStorage, infrequentObjectCount int64
	var archiveStorage, archiveObjectCount int64
	var coldStorage, coldObjectCount int64
	switch metadata.StorageClass {
	case obs.StorageClassStandard: //标准存储。
		storage = info.Size
		objectCount = int64(info.ObjectNumber)
	case obs.StorageClassWarm: //低频访问存储。
		infrequentStorage = info.Size
		infrequentObjectCount = int64(info.ObjectNumber)
	case obs.StorageClassCold: //归档存储。
		archiveStorage = info.Size
		archiveObjectCount = int64(info.ObjectNumber)
	case obs.StorageClassDeepArchive: //深度归档存储（受限公测）
		coldStorage = info.Size
		coldObjectCount = int64(info.ObjectNumber)
	}

	return &models.BucketInfo{
		Model:        r.Model(ctx),
		Scode:        cmdb.Scode,
		AggregatedId: cmdb.AggregatedID,
		Vendor:       string(cli.Vendor()),
		AccountName:  cli.Name(),
		BucketName:   bucket.Name,
		Location:     location.Location,
		CreationDate: bucket.CreationDate,
		StorageClass: string(metadata.StorageClass),
		// AccessMonitor:          pointer.Value(info.BucketInfo.AccessMonitor),
		ExtranetEndpoint: fmt.Sprintf("obs.%s.myhuaweicloud.com", location.Location),
		// IntranetEndpoint:       endpoint,
		// DataRedundancyType:     pointer.Value(info.BucketInfo.DataRedundancyType),
		Versioning: metadata.Version,
		// TransferAcceleration:   pointer.Value(info.BucketInfo.TransferAcceleration),
		// CrossRegionReplication: pointer.Value(info.BucketInfo.CrossRegionReplication),
		Storage:     bytesToMB(storage),
		ObjectCount: objectCount,
		// MultipartUploadCount: state.MultipartUploadCount,
		// LiveChannelCount:   state.LiveChannelCount,
		// MultipartPartCount: state.MultipartUploadCount,
		// LastModifiedTime:            time.Unix(state.LastModifiedTime, 0),
		InfrequentAccessStorage: bytesToMB(infrequentStorage),
		// InfrequentAccessRealStorage: state.InfrequentAccessRealStorage,
		InfrequentAccessObjectCount: infrequentObjectCount,
		ArchiveStorage:              bytesToMB(archiveStorage),
		// ArchiveRealStorage:          bytesToMB(state.ArchiveRealStorage),
		ArchiveObjectCount: archiveObjectCount,
		ColdArchiveStorage: bytesToMB(coldStorage),
		// ColdArchiveRealStorage: bytesToMB(state.ColdArchiveRealStorage),
		ColdArchiveObjectCount: coldObjectCount,
		IsDeleted:              ptr.Bool(false),
	}, nil
}

func getHuaweiCMDBBySupplementId(ctx context.Context, accountId, instanceId string) models.CmdbProductOverview {
	m := config.Global().GetDefaultStore().Model(ctx)
	var cmdb models.CmdbProductOverview
	m.Orm().Model(&models.CmdbProductOverview{}).
		Where("vendor='huawei' and product_code ='hws.service.type.obs' and account_id =? and instance_id = ?", accountId, instanceId).
		Take(&cmdb)
	return cmdb
}
