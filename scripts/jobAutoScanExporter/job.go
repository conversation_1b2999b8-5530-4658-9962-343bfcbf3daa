package jobAutoScanExporter

import (
	"context"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"net"
	"os"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/common/types"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/sirupsen/logrus"
	"go.uber.org/atomic"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

type AutoScanExporter struct {
	*base.JobBase
}

func New() *AutoScanExporter {
	return &AutoScanExporter{
		JobBase: base.NewJobBase("AUTO_SCAN_EXPORTER",
			"自动扫描注册NodeExporter",
			base.NewSchedule(base.WithHour(0), base.WithRandMin(), base.WithSec(0)),
			taskmodels.TaskCategoryOps,
			bizutils.DataSource(),
		),
	}
}

func (a *AutoScanExporter) Name() string {
	return base.TaskAutoScanExporter
}

func (a *AutoScanExporter) Run(ctx context.Context) (map[string]any, error) {
	host := make([]*models.HostInfo, 0)

	m := a.Model(ctx)
	err := m.Orm().Model(new(models.HostInfo)).
		Where("vendor in ? and is_deleted = '0' and account_name not in ?", []hbc.CloudVendor{
			hbc.AliCloud,
			hbc.Private,
			hbc.JXJG,
		}, []string{
			"<EMAIL>",
		}).
		Find(&host).
		Error
	if err != nil {
		return nil, err
	}

	mtx := new(sync.Mutex)
	f, _ := os.OpenFile("ip.txt", os.O_CREATE|os.O_WRONLY, 0644)
	a.Logger().Infof(ctx, "host len: %d", len(host))
	ports := []int{9100, 9101, 19100, 19101, 19182}
	batch.SetLogLevel(logrus.InfoLevel)
	b := tools.NewBatch[*models.HostInfo, *types.RegisterPrometheusDetail](ctx,
		batch.WithBatchSize(runtime.NumCPU()),
		batch.WithShowLog(true),
	)
	b.Run(host, func(ctx context.Context, hostInfo *models.HostInfo) (*types.RegisterPrometheusDetail, error) {
		var ok bool
		var p int
		for _, port := range ports {
			if dial(hostInfo.PrivateIp, port) {
				p = port
				ok = true
			}
		}

		if !ok {
			mtx.Lock()
			a.Logger().Infof(ctx, "need %s", hostInfo.PrivateIp)
			_, _ = f.WriteString(strings.Join([]string{hostInfo.PrivateIp, hostInfo.Scode, hostInfo.Project}, ",") + "\n")
			mtx.Unlock()
			return nil, nil
		}

		endpoint := "QD"
		if strings.Contains(hostInfo.Region, "beijing") {
			endpoint = "BJ"
		}
		ws, _ := api.HdsClient().QueryProjectWorkspace(hostInfo.Scode)
		if len(ws) > 0 {
			for _, w := range ws {
				if w.AlmCode == hostInfo.Project {
					hostInfo.Project = w.ID
				}
			}
		}

		return &types.RegisterPrometheusDetail{
			AgentVersion:  "1.5.0",
			Endpoint:      endpoint,
			Env:           hostInfo.Env,
			NodeType:      hostInfo.HostType,
			OsType:        strings.Split(hostInfo.OsType, "/")[0],
			Port:          p,
			Region:        hostInfo.Region,
			ScrapeAddress: hostInfo.PrivateIp,
			Project:       hostInfo.Project,
			Source:        hostInfo.Vendor.String(),
			SvcType:       "node",
			TargetAddress: hostInfo.PrivateIp,
			Name:          hostInfo.InstanceName,
			ResourceId:    hostInfo.ResourceId,
		}, nil
	})

	result := b.Outs()
	total := atomic.NewInt64(0)
	bb := tools.NewBatch[*types.RegisterPrometheusDetail, struct{}](ctx)
	bb.Run(result, func(ctx context.Context, info *types.RegisterPrometheusDetail) (struct{}, error) {
		defer a.Logger().Infof(ctx, "register prometheus target: [%s] %s:%d", info.OsType, info.ScrapeAddress, info.Port)

		//
		info.Env = strings.ToLower(info.Env)
		switch info.Env {
		case "dev", "test", "pre", "prod":
		case "uat", "stage":
			info.Env = "pre"
		default:
			info.Env = "prod"
		}

		//
		switch info.OsType {
		case common.OsTypeLinux:
			info.OsType = "LINUX"
		case "Linux":
			info.OsType = strings.ToUpper(info.OsType)
		case "Windows":
			info.OsType = strings.ToUpper(info.OsType)
		default:
			info.OsType = "LINUX"
		}

		//
		switch info.NodeType {
		case "ecs":
			info.NodeType = "virtual"
		case "Physical":
			info.NodeType = "physical"
		case "Virtual", "VirtualMachine":
			info.NodeType = "virtual"
		}

		switch info.Source {
		case "private":
			info.Source = "privatecloud"
		case "tencent":
			info.Source = "tencentcloud"
		case "huawei":
			info.Source = "huaweicloud"
		}

		if info.Project == "" {
			info.Project = "unknown"
		}

		_, err := bizutils.WithRetry(3, func() (any, error) {
			err := api.HdsClient().PrometheusRegister(ctx, info)
			return nil, err
		})
		total.Add(1)
		return struct{}{}, err
	})

	_ = bb.Outs()
	a.Logger().Infof(ctx, "register prometheus target total: %d", total.Load())
	return nil, bb.Error()
}

func dial(ip string, port int) bool {
	conn, err := net.DialTimeout("tcp", ip+":"+strconv.Itoa(port), time.Second)
	if err != nil {
		return false
	}
	_ = conn.Close()
	return true
}
