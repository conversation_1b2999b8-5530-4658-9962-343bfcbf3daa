package jobSendDataToCMDB

type ConsoleHostFunction struct {
	HostIP        int64  `gorm:"column:host_ip;not null;default:0;comment:'主机ip 原角色状态有四种：NotSupport NotInit Failed Successful, NotSupport设为default!'"`
	Node          string `gorm:"column:node;not null;default:'NotSupport';comment:'机器监控，原LINUX/WINDOWS'"`
	Docker        string `gorm:"column:docker;not null;default:'NotSupport'"`
	Elasticsearch string `gorm:"column:elasticsearch;not null;default:'NotSupport'"`
	MySQL         string `gorm:"column:mysql;not null;default:'NotSupport'"`
	Nginx         string `gorm:"column:nginx;not null;default:'NotSupport'"`
	Oracle        string `gorm:"column:oracle;not null;default:'NotSupport'"`
	Redis         string `gorm:"column:redis;not null;default:'NotSupport'"`
	Zookeeper     string `gorm:"column:zookeeper;not null;default:'NotSupport'"`
	Kafka         string `gorm:"column:kafka;not null;default:'NotSupport'"`
	MongoDB       string `gorm:"column:mongodb;not null;default:'NotSupport'"`
	ActiveMQ      string `gorm:"column:activemq;not null;default:'NotSupport'"`
	Apache        string `gorm:"column:apache;not null;default:'NotSupport'"`
	Cobar         string `gorm:"column:cobar;not null;default:'NotSupport'"`
	FastDFS       string `gorm:"column:fastdfs;not null;default:'NotSupport'"`
	IIS           string `gorm:"column:iis;not null;default:'NotSupport'"`
	JStorm        string `gorm:"column:jstorm;not null;default:'NotSupport'"`
	LTS           string `gorm:"column:lts;not null;default:'NotSupport'"`
	LVS           string `gorm:"column:lvs;not null;default:'NotSupport'"`
	Memcache      string `gorm:"column:memcache;not null;default:'NotSupport'"`
	MyCat         string `gorm:"column:mycat;not null;default:'NotSupport'"`
	Others        string `gorm:"column:others;not null;default:'NotSupport'"`
	RabbitMQ      string `gorm:"column:rabbitmq;not null;default:'NotSupport'"`
	RocketMQ      string `gorm:"column:rocketmq;not null;default:'NotSupport'"`
	Solr          string `gorm:"column:solr;not null;default:'NotSupport'"`
	SQLServer     string `gorm:"column:sqlserver;not null;default:'NotSupport'"`
	Storm         string `gorm:"column:storm;not null;default:'NotSupport'"`
	Tomcat        string `gorm:"column:tomcat;not null;default:'NotSupport'"`
}

func (ConsoleHostFunction) TableName() string {
	return "console_host_function"
}
