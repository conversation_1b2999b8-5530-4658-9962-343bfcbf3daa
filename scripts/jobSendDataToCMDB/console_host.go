package jobSendDataToCMDB

import (
	"time"
)

type ConsoleHost struct {
	IP                int64     `gorm:"column:ip;primary_key"`
	Hostname          string    `gorm:"column:hostname"`
	OS                string    `gorm:"column:os"`
	Distribution      string    `gorm:"column:distribution"`
	CPUCores          string    `gorm:"column:cpu_cores"`
	Memory            string    `gorm:"column:memory"`
	HardDisk          string    `gorm:"column:hard_disk"`
	User              string    `gorm:"column:user"`
	Password          string    `gorm:"column:password"`
	Port              string    `gorm:"column:port"`
	AccessMethod      string    `gorm:"column:access_method"`
	AccessStatus      int8      `gorm:"column:access_status"`
	Cluster           string    `gorm:"column:cluster"`
	Source            string    `gorm:"column:source"`
	OldPaasMachineNme string    `gorm:"column:old_paas_machine_name"`
	Creator           string    `gorm:"column:creator"`
	CreatedAt         time.Time `gorm:"column:created_at"`
	UpdatedAt         time.Time `gorm:"column:updated_at"`
	Type              string    `gorm:"column:type"`
	ISP               string    `gorm:"column:isp"`
	Position          string    `gorm:"column:position"`
	BasisInfo         string    `gorm:"column:basis_info"`
	Configuration     string    `gorm:"column:configuration"`
	Region            string    `gorm:"column:region"`
	Status            string    `gorm:"column:status"`
	SpecialUse        string    `gorm:"column:special_use"`
	ProjectID         string    `gorm:"column:project_id"`
	UniRegionId       string    `gorm:"column:uni_region_id"`
	ResourceId        string    `gorm:"column:resource_id"`
}

func (ConsoleHost) TableName() string {
	return "console_host"
}
