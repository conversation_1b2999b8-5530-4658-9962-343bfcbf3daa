package jobSendDataToCMDB

import (
	"context"
	"errors"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/updater"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/LPX3F8/orderedmap"
	"gorm.io/gorm"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

type SyncHost2CMDB struct {
	*base.JobBase
}

func New(ds models.Datasource) *SyncHost2CMDB {
	// 每天凌晨1点执行一次
	schedule := "0 0 1 * * *"
	return &SyncHost2CMDB{
		JobBase: base.NewJobBase(
			"cmdb-sync-host",
			"同步资源数据到技术架构CMDB",
			schedule,
			taskmodels.TaskCategoryResource,
			&ds,
		),
	}
}

func (b *SyncHost2CMDB) Run(ctx context.Context) (map[string]any, error) {
	var (
		// hcms数据源
		hcmsModel = bizutils.DataSource().Model(ctx)
		hcmsOrm   = hcmsModel.Orm()
		// cmdb数据源
		console      = config.Global().GetStore("console")
		consoleModel = console.Model(ctx)
		consoleOrm   = consoleModel.Orm()
	)

	// 查询所有主机，将主机信息转换为console_host表的数据结构，同时过滤掉非IPv4的主机
	hosts, consoleHosts, err := b.generateConsoleHostInfo(hcmsOrm)
	if err != nil {
		return nil, err
	}

	// 同步主机信息，包括：
	// console_host 表
	// console_host_function 表
	// console_host_project 表
	now := time.Now()
	syncConsoleHostProcess := tools.NewBatch[*ConsoleHost, any](ctx, batch.WithBatchSize(10))
	syncConsoleHostProcess.Run(consoleHosts, func(ctx context.Context, consoleHost *ConsoleHost) (any, error) {
		host, _ := hosts.Load(consoleHost.ResourceId)
		return b.syncConsoleHostData(ctx, now, consoleOrm, hcmsOrm, consoleHost, host)
	})
	_ = syncConsoleHostProcess.Outs()
	if err = syncConsoleHostProcess.Error(); err != nil {
		return nil, err
	}

	// 清理过期数据
	cleanData := make(map[string]any)
	res := consoleOrm.Table("console_host").
		Where("updated_at < ?", now.Add(-time.Hour*24*7)).
		Delete(nil)
	if res.Error != nil {
		return nil, res.Error
	}
	cleanData["console_host"] = res.RowsAffected

	res = consoleOrm.Table("console_host_project").
		Where("host_ip not in (select ip from console_host)").
		Update("is_delete", 1)
	if res.Error != nil {
		return nil, res.Error
	}
	cleanData["console_host_project"] = res.RowsAffected
	return cleanData, err
}

func (b *SyncHost2CMDB) syncConsoleHostData(ctx context.Context, nowTime time.Time, consoleOrm, hcmsOrm *gorm.DB, consoleHost *ConsoleHost, host *models.HostInfo) (any, error) {
	cmdbData := &ConsoleHost{}
	// 相同的厂商相同的IP不同的账号
	err := consoleOrm.Table(consoleHost.TableName()).Where("ip = ? and resource_id = ?", consoleHost.IP, consoleHost.ResourceId).Find(cmdbData).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 同步console_host表
	if cmdbData.IP > 0 {
		consoleHost.User = cmdbData.User
		consoleHost.Password = cmdbData.Password
		consoleHost.Port = cmdbData.Port
		consoleHost.AccessMethod = cmdbData.AccessMethod
		consoleHost.AccessStatus = cmdbData.AccessStatus
		consoleHost.OldPaasMachineNme = cmdbData.OldPaasMachineNme
		consoleHost.Creator = cmdbData.Creator
		consoleHost.Configuration = cmdbData.Configuration
		consoleHost.SpecialUse = cmdbData.SpecialUse
		if consoleHost.HardDisk == "" {
			consoleHost.HardDisk = cmdbData.HardDisk
		}
		updateDataFields := updater.GetChangedFields(cmdbData, consoleHost, false)
		updateDataFields["updated_at"] = nowTime
		if len(updateDataFields) > 1 {
			b.Logger().Debugf(ctx, "update host to cmdb: %s | %s", utils.JsonString(updateDataFields), utils.JsonString(consoleHost))
		}
		if err := consoleOrm.Table(consoleHost.TableName()).Where("ip = ? and source = ?", consoleHost.IP, consoleHost.ResourceId).Updates(updateDataFields).Error; err != nil {
			b.Errorf(ctx, "update host to cmdb error: %s | %s", err.Error(), utils.JsonString(consoleHost))
			return nil, err
		}
	} else {
		if err := consoleOrm.Create(consoleHost).Error; err != nil {
			b.Errorf(ctx, "create host to cmdb error: %s | %s", err.Error(), utils.JsonString(consoleHost))
			return nil, err
		}
	}

	// 同步console_host_function表
	consoleHostFunction := new(ConsoleHostFunction)
	consoleHostFunction.HostIP = consoleHost.IP
	if err := consoleOrm.Table(consoleHostFunction.TableName()).Where("host_ip = ?", consoleHost.IP).
		FirstOrCreate(consoleHostFunction).Error; err != nil {
		return nil, err
	}

	// 同步console_host_project表
	workspaces := make([]*models.ResourceWorkspace, 0)
	hcmsOrm.Model(new(models.ResourceWorkspace)).Where("resource_id = ?", host.ResourceId).Find(&workspaces)
	for _, workspace := range workspaces {
		newHp := b.transToHostProject(nowTime, consoleHost.IP, host, workspace)
		hp := &ConsoleHostProject{}
		findHpErr := consoleOrm.Table("console_host_project").Where("host_ip = ? and project = ?", consoleHost.IP, workspace.Project).Find(hp).Error
		if findHpErr != nil && !errors.Is(findHpErr, gorm.ErrRecordNotFound) {
			return nil, findHpErr
		}

		if hp.HostIP > 0 {
			updateProjectFields := updater.GetChangedFields(hp, newHp, false)
			updateProjectFields["updated_at"] = nowTime
			err := consoleOrm.Table("console_host_project").Where("host_ip = ? and project = ?", consoleHost.IP, workspace.Project).Updates(updateProjectFields).Error
			if err != nil {
				b.Errorf(ctx, "update host project to cmdb error: %s | %s", err.Error(), utils.JsonString(newHp))
				return nil, err
			}
			continue
		}

		if err := consoleOrm.Create(newHp).Error; err != nil {
			b.Errorf(ctx, "create host project to cmdb error: %s | %s", err.Error(), utils.JsonString(newHp))
			return nil, err
		}
	}

	return nil, nil
}

func (b *SyncHost2CMDB) generateConsoleHostInfo(hcmsOrm *gorm.DB) (*orderedmap.OrderedMap[string, *models.HostInfo], []*ConsoleHost, error) {
	now := time.Now()
	result := make([]*ConsoleHost, 0)
	hosts := make([]*models.HostInfo, 0)
	filteredHosts := orderedmap.New[string, *models.HostInfo]()

	// 查询所有主机
	err := hcmsOrm.Table("rc_host_info").Where("is_deleted = '0' and vendor <> 'factory'").Find(&hosts).Error
	if err != nil {
		return nil, nil, err
	}

	// 过滤掉非IPv4的主机
	var ip net.IP
	for _, host := range hosts {
		ip = net.ParseIP(host.PrivateIp)
		if ip == nil || ip.To4() == nil {
			continue
		}

		h := b.transToCMDB(now, host)
		result = append(result, h)
		filteredHosts.Store(host.ResourceId, host)
	}

	return filteredHosts, result, nil
}

func (b *SyncHost2CMDB) transToHostProject(now time.Time, intIp int64, host *models.HostInfo, workspace *models.ResourceWorkspace) *ConsoleHostProject {
	createdAt := time.Time{}
	if host.CreationTime != nil {
		createdAt = *host.CreationTime
	}
	return &ConsoleHostProject{
		HostIP:         intIp,
		Project:        workspace.Project,
		Env:            host.Env,
		Description:    host.Description,
		CreatedAt:      createdAt,
		UpdatedAt:      now,
		CloudAccountID: host.AccountName,
		CloudHostID:    host.InstanceId,
		SyncStatus:     1,
	}
}

func (b *SyncHost2CMDB) transToCMDB(now time.Time, host *models.HostInfo) *ConsoleHost {
	var cluster = "QD"
	if host.UniRegionId == "cn-beijing" {
		cluster = "BJ"
	}

	var disk string
	if host.DiskSize > 0 {
		switch host.Vendor {
		case hbc.Private, hbc.Factory:
			disk = fmt.Sprint(int(host.DiskSize), "G")
		default:
			disk = fmt.Sprint(int(host.DiskSize/1024), "G")
		}
	}

	var isp = host.Vendor.String()
	if host.Vendor == hbc.Private {
		isp = "智家大运维"
	}
	if host.Vendor == hbc.JXJG {
		isp = host.ChargeType
	}

	scode := host.Scode
	if bizutils.IsSCode(scode) && host.Project != "" {
		b := false
		projects, _ := api.HdsClient().QueryProjectWorkspace(host.Project)
		for _, s := range projects {
			if scode == s.AlmSCode {
				scode = s.AlmSCode
				b = true
				break
			}
		}
		if !b {
			if len(projects) > 0 {
				scode = projects[0].AlmSCode
			}
		}
	}

	var source = host.Vendor.String()
	switch source {
	case hbc.TencentCloud.String():
		source = "tencentcloud"
	case hbc.HuaweiCloud.String():
		source = "huaweicloud"
	case hbc.Private.String():
		source = "private_cloud"
	case hbc.JXJG.String():
		source = "jxjg"
	}

	switch host.HostStatus {
	case "":
	case "Running", "Stopping", "Stopped", "Deploying", "Maintain":
	case "RUNNING", "running", "ACTIVE", "poweredOn", "active":
		host.HostStatus = common.StatusRunning
	case "stopped", "Shutoff", "Shutdown", "poweredOff", "Notrunning", "Terminated", "unallocated":
		host.HostStatus = common.StatusStopped
	case "deallocated":
		host.HostStatus = common.StatusDeleted
	default:
		notice.SendErrorMessage("未支持的主机映射状态", fmt.Sprintf("主机状态: %s, 主机Ip: %s", host.HostStatus, host.PrivateIp))
	}

	switch host.HostType {
	case "ecs", "Virtual", "VirtualMachine":
		host.HostType = "virtual"
	case "Physical", "HostSystem":
		host.HostType = "physical"
	}

	host.OsType = formatOsType(host.OsType)
	createdAt := time.Time{}
	if host.CreationTime != nil {
		createdAt = *host.CreationTime
	}

	return &ConsoleHost{
		IP:           tools.InetAtoN(host.PrivateIp),
		Hostname:     host.InstanceName,
		OS:           host.OsType,
		Distribution: host.OsName,
		CPUCores:     strconv.Itoa(int(host.Cpu)),
		Memory:       strconv.Itoa(int(host.Memory/1024)) + "G",
		HardDisk:     disk,
		Cluster:      cluster,
		Source:       source,
		CreatedAt:    createdAt,
		UpdatedAt:    now,
		Type:         host.HostType,
		ISP:          isp,
		Position:     host.Zone,
		BasisInfo:    host.Sn,
		Region:       host.Region,
		Status:       host.HostStatus,
		ProjectID:    scode,
		UniRegionId:  host.UniRegionId,
		ResourceId:   host.ResourceId,
	}
}

func formatOsType(osType string) string {
	osType = strings.TrimSpace(strings.ToLower(osType))
	if strings.Contains(osType, "windows") {
		return "WINDOWS"
	}
	if strings.Contains(osType, "linux") {
		return "LINUX"
	}
	if strings.Contains(osType, "N/A") || strings.Contains(osType, "") {
		return "UNKNOWN"
	}
	if strings.Contains(osType, "aix") {
		return "AIX"
	}
	if strings.Contains(osType, "esxi") {
		return "ESXI"
	}
	if strings.Contains(osType, "vmnix") {
		return "VMNIX"
	}
	if strings.Contains(osType, "vsca") {
		return "VSCA"
	}

	return strings.ToUpper(osType)
}
