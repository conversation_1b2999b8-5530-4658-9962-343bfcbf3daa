package jobSendDataToCMDB

import (
	"time"
)

type ConsoleHostProject struct {
	HostIP         int64     `gorm:"column:host_ip"`
	Project        string    `gorm:"column:project"`
	Env            string    `gorm:"column:env"`
	Description    string    `gorm:"column:description"`
	Creator        string    `gorm:"column:creator"`
	CreatedAt      time.Time `gorm:"column:created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at"`
	CloudAccountID string    `gorm:"column:cloud_account_id"`
	CloudHostID    string    `gorm:"column:cloud_host_id"`
	SyncStatus     int       `gorm:"column:sync_status"`
	IsDelete       int       `gorm:"column:is_delete"`
}

func (ConsoleHostProject) TableName() string {
	return "console_host_project"
}
