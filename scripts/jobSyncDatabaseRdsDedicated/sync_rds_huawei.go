package jobSyncDatabaseRdsDedicated

import (
	"context"
	"strconv"
	"strings"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
)

func (r *RdsSync) syncHuaweiRdsInfo(defClient *huaweicloud.DedicatedClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	pageNo := 1
	pageSize := 1000
	databaseInfos := []*models.DatabaseInfo{}
	token, err := defClient.GetHuaweiOauthToken()
	if err != nil {
		return nil, err
	}

	for rds, err := defClient.GetHuaweiDedicatedRds(token, pageNo, pageSize); ; rds, err = defClient.GetHuaweiDedicatedRds(token, pageNo, pageSize) {
		if err != nil {
			return nil, err
		}

		if len(rds.RdsList) > 0 {
			for _, huaweiRds := range rds.RdsList {
				port, _ := strconv.Atoi(huaweiRds.Port)
				diskSize, _ := strconv.ParseFloat(huaweiRds.DataVolumeSizeInBytes, 64)
				cpu, memory := getCpuMemoryByFlavor(huaweiRds.FlavorCode)

				var scode, project, env string
				tags, err := defClient.GetHuaweiDedicatedRdsTags(token, huaweiRds.ResID)
				if err == nil {
					if len(tags.Datas) > 0 {
						for _, tag := range tags.Datas {
							if len(tag.Tags) == 0 {
								continue
							}
							for _, t := range tag.Tags {
								if env == "" {
									switch strings.ToLower(strings.TrimSpace(t.Key)) {
									case jobSyncDatabaseRds.RDS_ENV, jobSyncDatabaseRds.RDS_ENV_CH:
										env = t.Value
									}
								}
								if scode == "" {
									if t.Key == "S码" {
										scode = t.Value
										if almProject, err := api.HdsClient().QueryAlmProject(scode); err == nil && almProject != nil {
											project = almProject.Id
										}
									}
								}
							}
						}
					}
				}

				databaseInfo := &models.DatabaseInfo{
					Model:         r.Model(ctx),
					Vendor:        defClient.Vendor(),
					AccountName:   defClient.Name(),
					InstanceId:    huaweiRds.NativeID,
					InstanceName:  huaweiRds.Name,
					InstanceType:  getHuaweiRdsType(huaweiRds.HaMode), // 主实例 只读实例等
					InstanceRole:  getHuaweiRdsRole(huaweiRds.HaMode), // 主备
					Category:      huaweiRds.HaMode,
					ResourceGroup: huaweiRds.ResourcePoolName,
					Status:        huaweiRds.Status,
					ClassCode:     huaweiRds.FlavorCode,
					CreationTime:  timeutil.ZeroTime(huaweiRds.CreatedTime),
					Host:          huaweiRds.DataVip,
					Port:          port,
					EngineType:    huaweiRds.EngineName,
					EngineVersion: huaweiRds.EngineVersion,
					Scode:         scode,
					Project:       project,
					Cpu:           cpu,
					Memory:        memory,
					DiskSize:      diskSize,
					DiskType:      huaweiRds.VolumeType,
					VpcId:         huaweiRds.VpcID,
					SubnetId:      getHuaweiSubnetId(huaweiRds.NetworkID),
					Region:        huaweiRds.LogicalRegionName,
					Zone:          huaweiRds.BizRegionName,
					Env:           env,
					Content:       utils.JsonString(huaweiRds),
					// ChargeType:  huaweiRds.PayType,
					// ExpiredTime: timeutil.ZeroTime(expireAt),
					// Description: huaweiRds.DBInstanceDescription,
				}

				databaseInfos = append(databaseInfos, databaseInfo)
			}
		} else {
			break
		}
		pageNo = pageNo + 1
	}

	return databaseInfos, nil
}

func getHuaweiSubnetId(networkId string) string {
	ids, err := utils.UnmarshalString[[]string](networkId)
	if err != nil || len(ids) == 0 {
		return ""
	}
	return ids[0]
}

func getHuaweiRdsType(rds string) string {
	switch strings.ToLower(rds) {
	case "replica":
		return jobSyncDatabaseRds.RDSTypeReadonly
	default:
		return jobSyncDatabaseRds.RDSTypeNormal
	}
}

func getHuaweiRdsRole(rds string) string {
	switch getHuaweiRdsType(rds) {
	case jobSyncDatabaseRds.RDSTypeNormal:
		return jobSyncDatabaseRds.RdsRoleMaster
	default:
		return jobSyncDatabaseRds.RdsRoleSlave
	}
}

func getCpuMemoryByFlavor(flavorCode string) (cpu int, memory uint64) {
	switch flavorCode {
	case "rds.mysql.c2.large.ha":
		cpu, memory = 2, 4*1024
	case "rds.mysql.c2.xlarge.ha":
		cpu, memory = 4, 8*1024
	case "rds.mysql.c2.2xlarge.ha":
		cpu, memory = 8, 16*1024
	case "rds.mysql.c2.4xlarge.ha":
		cpu, memory = 16, 32*1024
	case "rds.mysql.c2.8xlarge.ha":
		cpu, memory = 32, 64*1024
	case "rds.mysql.s1.xlarge.ha": //这台是上rds测试的时候建的
	}
	return
}
