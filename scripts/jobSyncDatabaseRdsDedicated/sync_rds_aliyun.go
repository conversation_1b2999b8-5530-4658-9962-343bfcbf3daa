package jobSyncDatabaseRdsDedicated

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/rds"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
)

var aliyun_product_code_rds = []string{"rds"}

func (r *RdsSync) syncAliyunRdsInfo(defClient *aliyun.RDSClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	regions, err := defClient.DescribeRegions()
	if err != nil {
		return nil, err
	}
	regionsIds := orderedmap.New[string, string]()
	for _, region := range regions {
		regionsIds.Store(region.RegionId, region.RegionId)
	}
	// 移除nantong机房，网络不通
	regionsIds.Delete("cn-nantong")
	regionsIds.Delete("cn-heyuan-acdr-1")
	regionsIds.Delete("cn-shanghai-cloudspe")

	b := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, regionId string) ([]*models.DatabaseInfo, error) {
		rdsClient := hybrid.AccountManager().AliyunDedicatedRdsClient(ctx, defClient.Name(), bizutils.PurposeDedicated, regionId)
		if nil == rdsClient {
			return nil, nil
		}
		return r.syncAliyunRegionRdsInfo(ctx, rdsClient)
	})

	return tools.MergeData(b.Outs()...), b.Error()
}

func (r *RdsSync) syncAliyunRegionRdsInfo(ctx context.Context, cli *aliyun.RDSClient) ([]*models.DatabaseInfo, error) {
	rdsInstance, err := cli.DescribeDBInstancesOfAll(ctx)
	if err != nil {
		if strings.Contains(err.Error(), "ConnectTimeout") {
			return nil, nil
		}
		return nil, err
	}
	if len(rdsInstance) == 0 {
		return nil, nil
	}
	attrs, err := r.batchGetAttrs(cli, ctx, rdsInstance...)
	if err != nil {
		return nil, err
	}
	resourceManagerClient := hybrid.AccountManager().AliyunDedicatedResourceManagerClient(ctx, cli.Name(), cli.Tag(), cli.Region())
	b := tools.NewBatch[rds.DBInstanceAttribute, *models.DatabaseInfo](ctx)
	b.Run(attrs, func(ctx context.Context, attr rds.DBInstanceAttribute) (*models.DatabaseInfo, error) {
		_, scode, project := bizutils.ParseAliyunEnvProject(resourceManagerClient, r, attr.ResourceGroupId)
		port, _ := strconv.Atoi(attr.Port)
		cpu, _ := strconv.Atoi(attr.DBInstanceCPU)
		createAt, _ := time.Parse(bizutils.NormalDatetimeFmt, attr.CreationTime)
		expireAt, _ := time.Parse(bizutils.NormalDatetimeFmt, attr.ExpireTime)

		// get env from tag
		var flag bool
		var env string
		tags, err := cli.DescribeDBInstanceByTags(ctx, attr.DBInstanceId)
		if err != nil {
			if !strings.Contains(err.Error(), "InvalidDBInstanceId.NotFound") {
				return nil, err
			}
		}
		if len(tags) > 0 {
			for _, tag := range tags {
			outerLoop:
				for _, t := range tag.Tags.Tag {
					switch strings.ToLower(strings.TrimSpace(t.TagKey)) {
					case jobSyncDatabaseRds.RDS_ENV, jobSyncDatabaseRds.RDS_ENV_CH:
						env = t.TagValue
						flag = true
						break outerLoop
					}
				}
				if flag {
					break
				}
			}
		}
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), attr.DBInstanceId, aliyun_product_code_rds)
		return &models.DatabaseInfo{
			Model:             r.Model(ctx),
			Vendor:            cli.Vendor(),
			AccountName:       cli.Name(),
			InstanceId:        attr.DBInstanceId,
			InstanceName:      attr.DBInstanceDescription,
			InstanceType:      attr.DBInstanceType,
			InstanceRole:      attr.DBInstanceType,
			Category:          attr.Category,
			ResourceGroup:     attr.ResourceGroupId,
			PrimaryInstanceId: attr.MasterInstanceId,
			Status:            attr.DBInstanceStatus,
			ClassCode:         attr.DBInstanceClass,
			ChargeType:        attr.PayType,
			CreationTime:      timeutil.ZeroTime(createAt),
			ExpiredTime:       timeutil.ZeroTime(expireAt),
			Host:              attr.ConnectionString,
			Port:              port,
			EngineType:        attr.Engine,
			EngineVersion:     attr.EngineVersion,
			Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
			Project:           project,
			Env:               env,
			Cpu:               cpu,
			Memory:            uint64(attr.DBInstanceMemory),
			DiskSize:          float64(attr.DBInstanceStorage * 1024),
			DiskType:          attr.DBInstanceStorageType,
			VpcId:             attr.VpcId,
			SubnetId:          attr.VSwitchId,
			Region:            attr.RegionId,
			Zone:              attr.ZoneId,
			Description:       attr.DBInstanceDescription,
			Content:           utils.JsonString(attr),
			AggregatedId:      cmdb.AggregatedID,
		}, nil
	})

	return b.Outs(), b.Error()
}

func (r *RdsSync) batchGetAttrs(rdsClient *aliyun.RDSClient, ctx context.Context, rdsInstances ...rds.DBInstance) ([]rds.DBInstanceAttribute, error) {
	var (
		err        error
		resp       *rds.DescribeDBInstanceAttributeResponse
		rdsIdGroup = make([][]string, 0)
	)

	idx := -1
	for i, instance := range rdsInstances {
		if i%30 == 0 {
			rdsIdGroup = append(rdsIdGroup, make([]string, 0))
			idx++
		}
		rdsIdGroup[idx] = append(rdsIdGroup[idx], instance.DBInstanceId)
	}

	total := 0
	for _, g := range rdsIdGroup {
		total += len(g)
	}
	if total != len(rdsInstances) {
		panic(total)
	}
	b := tools.NewBatch[[]string, []rds.DBInstanceAttribute](ctx)
	b.Run(rdsIdGroup, func(ctx context.Context, rdsIds []string) ([]rds.DBInstanceAttribute, error) {
		if len(rdsIds) == 0 {
			r.Warnf(ctx, "receive none rds id list")
			return nil, nil
		}
		resp, err = rdsClient.DescribeDBInstanceAttribute(ctx, rdsIds...)
		if err != nil {
			return nil, err
		}
		return resp.Items.DBInstanceAttribute, nil
	})
	return tools.MergeData(b.Outs()...), b.Error()
}

func (r *RdsSync) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](r.mtx, r.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}
