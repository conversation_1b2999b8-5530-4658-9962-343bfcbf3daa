package jobSyncResource

import (
	"context"
	"fmt"
	"time"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

const (
	specialLayout = "2006-01-02-07:00"
)

type SyncResource struct {
	Ds     *models.Datasource
	Logger *log.Logger
}

func (s *SyncResource) Sync(ctx context.Context) error {
	vendors, err := hybrid.GetVendors(bizutils.DataSource().Model(ctx), hbc.GoogleCloud, hbc.Private, hbc.HuaweiCloud, hbc.TencentCloud, hbc.AWS, hbc.Azure)
	if err != nil {
		return err
	}

	defaultClients, err := base.GetDefaultIClients(ctx, vendors, hbc.RM, []string{bizutils.PurposeAdmin})
	if err != nil {
		return err
	}

	b := tools.NewBatch[client.IClient, []*models.ResourceInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.ResourceInfo, error) {
		switch defaultClient := input.(type) {
		case *aliyun.ResourceClient:
			return s.sync(ctx, defaultClient)
		}
		return nil, nil
	})

	if err := b.Error(); err != nil {
		s.Logger.Errorf(ctx, "sync resource failed, cause: %s", err)
		return err
	}

	return bizutils.CreateOrUpdateResource(ctx, tools.MergeData(b.Outs()...)...)
}
func (s *SyncResource) sync(ctx context.Context, cli *aliyun.ResourceClient) ([]*models.ResourceInfo, error) {
	resources, err := cli.ListResources(ctx)
	if err != nil {
		return nil, err
	}

	resourceInfoSlice := make([]*models.ResourceInfo, len(resources))
	for i, resource := range resources {
		// 将字符串解析为 time.Time 对象
		createDate, err := time.Parse(time.RFC3339, resource.CreateDate)
		if err != nil {
			if createDate, err = time.Parse(specialLayout, resource.CreateDate); err != nil {
				fmt.Println("Error parsing date:", err)
				return nil, err
			}
		}

		resourceInfoSlice[i] = &models.ResourceInfo{
			Service:         resource.Service,
			ResourceType:    resource.ResourceType,
			ResourceGroupId: resource.ResourceGroupId,
			ResourceId:      resource.ResourceId,
			CreateDate:      createDate,
			RegionId:        resource.RegionId,
		}
	}

	return resourceInfoSlice, nil
}
