package jobSyncResource

import (
	"testing"

	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/stretchr/testify/assert"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
)

func TestSyncResource_Sync(t *testing.T) {
	a := assert.New(t)
	sr := &SyncResource{
		Ds:     bizutils.DataSource(),
		Logger: log.NewWithOption("aliyunResourceSync", log.WithShowCaller(true)),
	}
	err := sr.Sync(context.NewContext())
	a.NoError(err)
}
