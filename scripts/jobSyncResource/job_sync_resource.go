package jobSyncResource

import (
	"context"

	"git.haier.net/devops/ops-golang-common/log"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

type JobSyncResource struct {
	*base.JobBase
}

func (j *JobSyncResource) Name() string {
	return base.TaskSyncResource
}

func New() *JobSyncResource {
	return &JobSyncResource{
		JobBase: base.NewJobBase("SYNC_ALIYUN_RESOURCE",
			"同步阿里云实例信息",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(30),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

func (j *JobSyncResource) Run(ctx context.Context) (map[string]any, error) {
	sr := &SyncResource{
		Ds:     bizutils.DataSource(),
		Logger: log.NewWithOption("aliyunResourceSync", log.WithShowCaller(true)),
	}

	return nil, sr.Sync(ctx)
}
