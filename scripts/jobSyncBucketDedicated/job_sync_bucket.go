package jobSyncBucketDedicated

import (
	"context"
	"sync"

	"git.haier.net/devops/ops-golang-common/sdk/aliyun"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func New() *BucketSync {
	c := config.Global()
	return &BucketSync{
		mtx: new(sync.Mutex),
		rg:  orderedmap.New[string, *resourcemanager.ResourceGroup](),
		ep:  orderedmap.New[string, *eps.EpDetail](),
		JobBase: base.NewJobBase(
			"BUCKET_SYNC",
			"同步专属云BUCKET资源信息",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type BucketSync struct {
	rg  *orderedmap.OrderedMap[string, *resourcemanager.ResourceGroup]
	ep  *orderedmap.OrderedMap[string, *eps.EpDetail]
	mtx *sync.Mutex
	*base.JobBase
}

func (r *BucketSync) Name() string {
	return base.TaskCloudSyncBucketDedicated
}

func (r *BucketSync) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(r.Model(ctx), hbc.GoogleCloud)
	if err != nil {
		return nil, err
	}
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, vendors, hbc.OSS, []string{bizutils.PurposeDedicated},
		actfilter.SetIgnoreVendor(hbc.AliCloud, hbc.Factory, hbc.Private, hbc.HuaweiCloud, hbc.HuaweiCloudDedicated, hbc.TencentCloud, hbc.OracleCloud, hbc.GoogleCloud, hbc.AWS, hbc.Azure, hbc.JXJG))
	if err != nil {
		return nil, err
	}

	// 获取云资源信息
	b := tools.NewBatch[client.IClient, []*models.BucketInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.BucketInfo, error) {
		var result []*models.BucketInfo
		var syncError error
		switch defClient := input.(type) {
		case *aliyun.OssClient:
			result, syncError = r.syncAliyunBucketInfo(defClient, ctx)
		}
		return result, bizutils.WarpClientError(input, syncError)
	})
	if err := b.Error(); err != nil {
		r.Errorf(ctx, "sync dedicated bucket failed, cause: %s", err)
		return nil, err
	}

	// 将信息存入数据库
	return nil, bizutils.CreateOrUpdateBucket(ctx, tools.MergeData(b.Outs()...)...)
}
