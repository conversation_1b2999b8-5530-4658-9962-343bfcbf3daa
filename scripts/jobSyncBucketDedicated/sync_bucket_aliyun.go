package jobSyncBucketDedicated

import (
	"context"
	"fmt"
	"strings"

	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
	"github.com/aws/smithy-go/ptr"
)

func (r *BucketSync) syncAliyunBucketInfo(defClient *aliyun.OssClient, ctx context.Context) ([]*models.BucketInfo, error) {
	bucketList, err := defClient.ListBuckets(ctx)
	if err != nil {
		return nil, err
	}
	b := tools.NewBatch[oss.BucketProperties, *models.BucketInfo](ctx)
	b.Run(bucketList.Buckets, func(ctx context.Context, bucket oss.BucketProperties) (*models.BucketInfo, error) {
		ossClient := hybrid.AccountManager().AliyunDedicatedOssClient(ctx, defClient.Name(), defClient.Tag(), pointer.Value(bucket.Region), pointer.Value(bucket.IntranetEndpoint))
		return r.syncAliyunRegionRdsInfo(ctx, bucket, ossClient)
	})

	return tools.MergeData(b.Outs()), b.Error()
}

func (r *BucketSync) syncAliyunRegionRdsInfo(ctx context.Context, bucket oss.BucketProperties, cli *aliyun.OssClient) (*models.BucketInfo, error) {
	bucketName := pointer.Value(bucket.Name)
	info, err := cli.GetBucketInfo(ctx, bucketName)
	if err != nil {
		if strings.Contains(err.Error(), "read: connection reset by peer") {
			return nil, nil
		}
		// if strings.Contains(err.Error(), "timeout") {
		// 	return nil, nil
		// }
		if !strings.Contains(err.Error(), "bucket does not exist") {
			return nil, err
		}
	}
	state, err := cli.GetBucketStat(ctx, bucketName)
	if err != nil {
		return nil, err
	}
	// instanceId是包含地区的字符串
	instanceId := fmt.Sprintf("%s;%s", pointer.Value(bucket.Region), pointer.Value(bucket.StorageClass))
	cmdb := getCMDBBySupplementId(ctx, cli.Identifier(), instanceId, bucketName)

	return &models.BucketInfo{
		Model:                  r.Model(ctx),
		AggregatedId:           cmdb.AggregatedID,
		Scode:                  cmdb.Scode,
		Vendor:                 string(cli.Vendor()),
		AccountName:            cli.Name(),
		BucketName:             bucketName,
		Location:               pointer.Value(info.BucketInfo.Location),
		CreationDate:           pointer.Value(info.BucketInfo.CreationDate),
		StorageClass:           pointer.Value(info.BucketInfo.StorageClass),
		AccessMonitor:          pointer.Value(info.BucketInfo.AccessMonitor),
		ExtranetEndpoint:       pointer.Value(info.BucketInfo.ExtranetEndpoint),
		IntranetEndpoint:       pointer.Value(info.BucketInfo.IntranetEndpoint),
		DataRedundancyType:     pointer.Value(info.BucketInfo.DataRedundancyType),
		Versioning:             pointer.Value(info.BucketInfo.Versioning),
		TransferAcceleration:   pointer.Value(info.BucketInfo.TransferAcceleration),
		CrossRegionReplication: pointer.Value(info.BucketInfo.CrossRegionReplication),
		Storage:                bytesToMB(state.Storage),
		ObjectCount:            state.ObjectCount,
		MultipartUploadCount:   state.MultipartUploadCount,
		LiveChannelCount:       state.LiveChannelCount,
		MultipartPartCount:     state.MultipartUploadCount,
		// LastModifiedTime:            time.Unix(state.LastModifiedTime, 0),
		InfrequentAccessStorage:     bytesToMB(state.InfrequentAccessStorage),
		InfrequentAccessRealStorage: bytesToMB(state.InfrequentAccessRealStorage),
		InfrequentAccessObjectCount: state.InfrequentAccessObjectCount,
		ArchiveStorage:              bytesToMB(state.ArchiveStorage),
		ArchiveRealStorage:          bytesToMB(state.ArchiveRealStorage),
		ArchiveObjectCount:          state.ArchiveObjectCount,
		ColdArchiveStorage:          bytesToMB(state.ColdArchiveStorage),
		ColdArchiveRealStorage:      bytesToMB(state.ColdArchiveRealStorage),
		ColdArchiveObjectCount:      state.ColdArchiveObjectCount,
		IsDeleted:                   ptr.Bool(false),
	}, nil
}

func getCMDBBySupplementId(ctx context.Context, accountId, instanceId, supplement string) models.CmdbProductOverview {
	m := config.Global().GetDefaultStore().Model(ctx)
	var cmdb models.CmdbProductOverview
	like := instanceId + "%"
	m.Orm().Model(&models.CmdbProductOverview{}).
		Where("vendor='aliyun_dedicated' and product_code ='oss' and account_id =? and instance_id like ? and supplement_id = ?", accountId, like, supplement).
		Take(&cmdb)
	return cmdb
}

func bytesToMB(bytes int64) float64 {
	const bytesInMB = 1024 * 1024
	return float64(bytes) / bytesInMB
}
