package jobTest

import (
	"context"
	"math/rand"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewTest(name string) taskcenter.ITask {
	return &TestJob{
		JobBase: base.NewJobBase(
			name,
			"测试任务",
			"0 0 1 * * *",
			taskmodels.TaskCategoryTest,
			bizutils.DataSource()),
	}
}

type TestJob struct {
	*base.JobBase
}

func (j *TestJob) Run(ctx context.Context) (map[string]any, error) {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	ii := r.Intn(30)
	for i := 0; i < ii; i++ {
		j.<PERSON>().Infof(ctx, "%s task running ...", j.Name())
		time.Sleep(time.Second)
	}
	return nil, nil
}
