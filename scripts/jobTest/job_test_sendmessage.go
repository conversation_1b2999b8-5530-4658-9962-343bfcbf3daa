package jobTest

import (
	"context"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/taskcenter"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewTestSendMessage() taskcenter.ITask {
	return &SendMessage{
		JobBase: base.NewJobBase(
			"jobTestSendMessage",
			"测试任务：发送消息",
			"0 0 1 * * *",
			taskmodels.TaskCategoryTest,
			bizutils.DataSource()),
	}
}

type SendMessage struct {
	*base.JobBase
}

func (c *SendMessage) Run(ctx context.Context) (map[string]any, error) {
	notice.SendInfoMessage("测试", "消息发送测试")
	return nil, nil
}
