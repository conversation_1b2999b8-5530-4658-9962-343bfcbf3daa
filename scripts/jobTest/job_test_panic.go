package jobTest

import (
	"context"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewTestPanic() taskcenter.ITask {
	return &Panic{
		JobBase: base.NewJobBase(
			"jobTestPanic",
			"测试任务：panic",
			"0 0 1 * * *",
			taskmodels.TaskCategoryTest,
			bizutils.DataSource()),
	}
}

type Panic struct {
	*base.JobBase
}

func (j *Panic) Run(ctx context.Context) (map[string]any, error) {
	panic("test panic")
}
