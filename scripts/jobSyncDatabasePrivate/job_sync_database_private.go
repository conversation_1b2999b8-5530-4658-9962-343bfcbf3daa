package jobSyncDatabasePrivate

import (
	"context"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"strings"
	"sync"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/feishu"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/chyroc/lark"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func New() *SyncOB {
	c := config.Global()
	schedule := "0 0 1 * * *"
	return &SyncOB{
		mtx:          new(sync.Mutex),
		feiShuClient: c.FeiShuConfig.NewFeiShuClient(),
		JobBase: base.NewJobBase(
			"cmdb-sync-db-private",
			"同步私有云数据库信息",
			schedule,
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncOB struct {
	mtx          *sync.Mutex
	feiShuClient *feishu.Client
	*base.JobBase
}

func (s *SyncOB) Name() string {
	return base.TaskCloudSyncPrivateDB
}

func (s *SyncOB) Run(ctx context.Context) (map[string]any, error) {
	wikiNodeToken, _ := config.Global().CommonConf.GetString("sync-private-db")
	wikiNode, err := s.feiShuClient.GetWikiObject(ctx, wikiNodeToken)
	if err != nil {
		return nil, err
	}
	sheet, err := s.feiShuClient.GetSheetSheet(ctx, wikiNode.ObjToken, 0)
	if err != nil {
		return nil, err
	}
	rows, err := s.feiShuClient.GetSheetValueAll(ctx, wikiNode.ObjToken, sheet.SheetID)
	if err != nil {
		return nil, err
	}

	process := tools.NewBatch[[]lark.SheetContent, *models.DatabaseInfo](ctx)
	process.Run(rows[1:], func(ctx context.Context, row []lark.SheetContent) (*models.DatabaseInfo, error) {
		ip := tools.GetFeiShuRowString("E", row...)
		if ip == "" {
			return nil, nil
		}

		host := &models.HostInfo{}
		if err := s.Orm(ctx).Model(host).Where("private_ip = ? order by is_deleted asc limit 1", ip).Find(&host).Error; err != nil {
			return nil, err
		}
		if host.ID == 0 {
			s.Logger().Errorf(ctx, "database %s not found", tools.GetFeiShuRowString("A", row...))
		}
		scode := tools.GetFeiShuRowString("H", row...)
		if host.Scode != scode {
			scode = host.Scode
		}

		status := tools.GetFeiShuRowString("R", row...)
		deleted := status != "Normal"
		var project, domain, team, userId, userName string
		projectInfo, _ := api.HdsClient().QueryAlmProject(scode)
		if projectInfo != nil {
			project = projectInfo.Id
			domain = projectInfo.Domain
			team = projectInfo.OrgName
			userId = projectInfo.Owner
			userName = projectInfo.OwnerName
		}

		db := &models.DatabaseInfo{
			Vendor:        hbc.Private,
			AccountName:   "-",
			InstanceId:    "ZJ_" + strings.Replace(strings.Replace(tools.GetFeiShuRowString("A", row...), ":", "_", -1), ".", "_", -1),
			InstanceName:  tools.GetFeiShuRowString("A", row...),
			InstanceType:  tools.GetFeiShuRowString("D", row...),
			InstanceRole:  tools.GetFeiShuRowString("M", row...),
			IsDeleted:     &deleted,
			Category:      tools.GetFeiShuRowString("N", row...),
			Status:        status,
			Host:          tools.GetFeiShuRowString("E", row...),
			Port:          int(tools.GetRowInt("F", row...)),
			EngineType:    bizutils.UnifyDBType(tools.GetFeiShuRowString("D", row...)),
			Scode:         tools.GetFeiShuRowString("H", row...),
			Project:       project,
			Env:           bizutils.UnifyEnv(tools.GetFeiShuRowString("G", row...)),
			ResourceGroup: scode,
			Region:        tools.GetFeiShuRowString("K", row...),
			Zone:          tools.GetFeiShuRowString("L", row...),
			Description:   tools.GetFeiShuRowString("J", row...),
			Domain:        domain,
			Team:          team,
			OwnerId:       userId,
			OwnerName:     userName,
		}

		// 如果能找到宿主机
		if host.ID > 0 {
			db.Vendor = host.Vendor
			db.AccountName = host.AccountName
			db.HostInsId = host.InstanceId
			db.CreationTime = timeutil.ZeroTime(host.CreateTime)
			db.ExpiredTime = timeutil.ZeroTimePtr(host.ExpiredTime)
			db.Cpu = int(host.Cpu)
			db.Memory = host.Memory
			db.DiskSize = host.DiskSize
			db.DiskType = host.DiskType
			db.VpcId = host.VpcId
			db.SubnetId = host.SubnetId
			db.Region = host.Region
			db.UniRegionId = host.UniRegionId
			db.Zone = host.Zone
			if pointer.Value(host.IsDeleted) == true {
				db.IsDeleted = host.IsDeleted
			}
		}

		return db, nil
	})

	if err := process.Error(); err != nil {
		return nil, err
	}

	return nil, bizutils.CreateOrUpdateRds(ctx, process.Outs()...)
}
