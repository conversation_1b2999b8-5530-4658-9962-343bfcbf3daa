package jobCleanupData

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/scripts/jobCleanupConfig"
	"git.haier.net/devops/ops-golang-common/models/models"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

var txOpt = &sql.TxOptions{Isolation: sql.LevelRepeatableRead, ReadOnly: false}

// 软删除模型列表
var cleanUpModels = []models.DBModel{
	new(models.DatabaseInfo),
	new(models.HostInfo),
	new(models.SubnetInfo),
	new(models.MiddlewareInfo),
}

// 硬删除模型列表
var hardDeleteModels = []models.DBModel{
	new(models.ResourceWorkspace),
}

func New() *Cleanup {
	c := config.Global()
	return &Cleanup{
		JobBase: base.NewJobBase("DATA_CLEAN",
			"清理资源表中过期数据",
			base.NewSchedule(
				base.WithMin(0),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type Cleanup struct {
	*base.JobBase
}

func (c *Cleanup) Name() string {
	return base.TaskDataClean
}

//func (c *Cleanup) Run(ctx context.Context) (map[string]any, error) {
//	model := c.Model(ctx)
//	result := map[string]any{}
//	durationStr, _ := config.Global().CommonConf.GetString("cleanup-duration")
//	duration, err := time.ParseDuration(durationStr)
//	if err != nil {
//		c.Errorf(ctx, "===> parse duration error: %v", err)
//		c.Errorf(ctx, "===> use default duration: 48h")
//		duration = 48 * time.Hour
//	}
//
//	for _, m := range cleanUpModels {
//		c.Infof(ctx, "start clean up table %s ...", m.TableName())
//		res := model.Orm().Model(m).
//			Where("update_time <= ? and is_deleted = '0'", time.Now().Add(-1*duration)).
//			Updates(map[string]interface{}{
//				"is_deleted": true,
//			})
//
//		if res.Error != nil {
//			return result, res.Error
//		}
//		count := res.RowsAffected
//		result[m.TableName()] = count
//		c.Infof(ctx, "clean up table %s done, %d rows deleted", m.TableName(), result[m.TableName()])
//	}
//
//	for _, m := range hardDeleteModels {
//		c.Infof(ctx, "start hard delete table %s ...", m.TableName())
//		res := model.Orm().Where("update_time <= ? ", time.Now().Add(-1*duration)).Delete(m)
//		if res.Error != nil {
//			return result, res.Error
//		}
//		count := res.RowsAffected
//		result[m.TableName()] = count
//		c.Infof(ctx, "hard delete table %s done, %d rows deleted", m.TableName(), result[m.TableName()])
//	}
//
//	return result, nil
//}

func (c *Cleanup) Run(ctx context.Context) (map[string]any, error) {

	// conf := jobCleanupConfig.NewCleanupConfig()
	// cleanupData, err := conf.FindInstanceByCleanupKind(ctx)
	// if err != nil {
	// 	return nil, err
	// }

	// _, err = c.Check(ctx, cleanupData)
	// if err != nil {
	// 	return nil, err
	// }

	return nil, jobCleanupConfig.Handle(ctx)
}

func (c *Cleanup) cleanupDatabase(ctx context.Context, dataList []models.DatabaseInfo) (err error) {
	uniqueMap := make(map[string]struct{})
	m := config.Global().GetDefaultStore().Model(ctx)

	for _, data := range dataList {
		uniKey := fmt.Sprintf("%s-%s-%s", data.Vendor, data.AccountName, data.InstanceId)
		if _, ok := uniqueMap[uniKey]; !ok {
			uniqueMap[uniKey] = struct{}{}
		} else {
			continue
		}

		// 开启事务
		now := time.Now()
		tx := m.Orm().Begin(txOpt)
		err = tx.Table("rc_database_info").Select("is_deleted", "delete_time", "update_time").Where("id = ?", data.ID).
			Updates(map[string]interface{}{
				"is_deleted":  true,
				"delete_time": now,
				"update_time": now,
			}).Error
		if err != nil {
			tx.Rollback()
			return err
		}
		err = bizutils.SaveDatabaseInfoHistory(ctx, tx, &data, bizutils.DelOperaTypeDelete)
		if err != nil {
			tx.Rollback()
			return err
		}

		// 提交事务
		if err = tx.Commit().Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return nil
}

func (c *Cleanup) cleanupHost(ctx context.Context, dataList []models.HostInfo) (err error) {
	uniqueMap := make(map[string]struct{})
	m := config.Global().GetDefaultStore().Model(ctx)

	for _, data := range dataList {
		uniKey := fmt.Sprintf("%s-%s-%s", data.Vendor, data.AccountName, data.InstanceId)
		if _, ok := uniqueMap[uniKey]; !ok {
			uniqueMap[uniKey] = struct{}{}
		} else {
			continue
		}

		// 开启事务
		now := time.Now()
		tx := m.Orm().Begin(txOpt)
		err = tx.Table("rc_host_info").Select("is_deleted", "delete_time", "update_time").Where("id = ?", data.ID).
			Updates(map[string]interface{}{
				"is_deleted":  true,
				"delete_time": now,
				"update_time": now,
			}).Error
		if err != nil {
			tx.Rollback()
			return err
		}
		err = bizutils.SaveHostInfoHistory(ctx, tx, &data, bizutils.DelOperaTypeDelete)
		if err != nil {
			tx.Rollback()
			return err
		}

		// 提交事务
		if err = tx.Commit().Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return nil
}
