package jobCleanupData

import (
	"context"
	"fmt"
	"strings"

	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/scripts/jobCleanupConfig"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ecs"
)

func (c *Cleanup) Check(ctx context.Context, data jobCleanupConfig.CleanupData) (b bool, err error) {

	if len(data.RDS) > 0 {
		cleanResults, err := databaseInfo(ctx, data.RDS)
		if err != nil {
			return false, err
		}
		if len(cleanResults) > 0 {
			err = c.cleanupDatabase(ctx, cleanResults)
			if err != nil {
				return false, err
			}
		}
	}

	if len(data.Host) > 0 {
		cleanResults, err := hostInfo(ctx, data.Host)
		if err != nil {
			return false, err
		}
		if len(cleanResults) > 0 {
			err = c.cleanupHost(ctx, cleanResults)
			if err != nil {
				return false, err
			}
		}
	}

	return true, nil
}

func databaseInfo(ctx context.Context, dataList []models.DatabaseInfo) ([]models.DatabaseInfo, error) {
	cleanResults := make([]models.DatabaseInfo, 0)

	config := jobCleanupConfig.NewCleanupConfig()
	for _, data := range dataList {

		prod := hbc.RDS
		switch strings.ToLower(data.EngineType) {
		case "mysql", "oracle", "postgres", "sqlserver":
			prod = hbc.RDS
		case "elasticsearch":
			prod = hbc.ElasticSearch
		case "mongodb":
			prod = hbc.MongoDB
		case "redis", "tairrdb":
			prod = hbc.Redis
		}
		if strings.Contains(strings.ToLower(data.EngineType), "polardb") {
			prod = hbc.PolarDB
		}

		defClient, err := config.GetClient(ctx, data.Vendor, prod, data.AccountName, data.Region)
		if err != nil {
			if strings.Contains(err.Error(), "account not found") {
				fmt.Println("[account not found] accountName: ", data.AccountName, ", vendor: ", data.Vendor)
				continue
			}
			if strings.Contains(err.Error(), "not in the following supported regions") {
				fmt.Println("[not in the following supported regions] regions: ", data.Region)
				continue
			}
			return nil, err
		}

		// 根据客户端类型调用sdk详情api
		switch defClient.(type) {
		case *aliyun.RDSClient:
			cli := hybrid.AccountManager().AliyunRdsClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region())
			res, err := cli.DescribeDBInstanceAttribute(ctx, data.InstanceId)
			if err != nil {
				if !strings.Contains(err.Error(), "NotFound") {
					return nil, err
				}
				//if !strings.Contains(err.Error(), "InvalidDBInstanceId.NotFound") {
				//	return nil, err
				//}
				//if !strings.Contains(err.Error(), "InvalidDBInstanceName.NotFound") {
				//	return nil, err
				//}
			}
			if len(res.Items.DBInstanceAttribute) == 0 {
				cleanResults = append(cleanResults, data)
			}
		case *aliyun.ElasticSearchClient:
			cli := hybrid.AccountManager().AliyunElasticSearchEndpoint(ctx, defClient.Name(), defClient.Tag(), defClient.Region())
			res, err := cli.DescribeInstance(ctx, data.InstanceId)
			if err != nil {
				if !strings.Contains(err.Error(), "InstanceNotFound") {
					return nil, err
				}
			}
			if res == nil {
				cleanResults = append(cleanResults, data)
			}
		case *aliyun.PolarDBClient:
			cli := hybrid.AccountManager().AliyunPolarDBClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region())
			res, err := cli.DescribeDBClusterAttribute(ctx, data.InstanceId)
			if err != nil {
				if !strings.Contains(err.Error(), "InvalidDBClusterId.NotFound") {
					return nil, err
				}
			}
			if res == nil {
				cleanResults = append(cleanResults, data)
			}
		case *aliyun.KvStoreClient:
			cli := hybrid.AccountManager().AliyunKvStoreClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region())
			res, err := cli.DescribeInstanceAttribute(ctx, data.InstanceId)
			if err != nil {
				if !strings.Contains(err.Error(), "InvalidInstanceId.NotFound") {
					return nil, err
				}
			}
			if res == nil {
				cleanResults = append(cleanResults, data)
			}
		case *huaweicloud.IamClient:
			switch strings.ToLower(data.EngineType) {
			case "mysql", "oracle", "postgres", "sqlserver":
				cli := hybrid.AccountManager().HuaweiRdsClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region(), defClient.ProjectId())
				res, err := cli.ShowInstance(ctx, data.InstanceId)
				if err != nil {
					return nil, err
				}
				if res == nil {
					cleanResults = append(cleanResults, data)
				}
			case "elasticsearch": //es
				cli := hybrid.AccountManager().HuaweiCssClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region(), defClient.ProjectId())
				res, err := cli.ShowClusterDetail(data.InstanceId)
				if err != nil {
					if !strings.Contains(err.Error(), "No resources are found or the access is denied") {
						return nil, err
					}
				}
				if res == nil {
					cleanResults = append(cleanResults, data)
				}
			case "redis", "tairrdb": //redis
				cli := hybrid.AccountManager().HuaweiNoSQLClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region(), defClient.ProjectId())
				res, err := cli.ShowInstance(data.InstanceId)
				if err != nil {
					if !strings.Contains(err.Error(), "This DCS instance does not exist") {
						return nil, err
					}
				}
				if res == nil {
					cleanResults = append(cleanResults, data)
				}
			}
		}
	}

	return cleanResults, nil
}

func hostInfo(ctx context.Context, dataList []models.HostInfo) ([]models.HostInfo, error) {
	cleanResults := make([]models.HostInfo, 0)

	config := jobCleanupConfig.NewCleanupConfig()
	for _, data := range dataList {
		prod := hbc.ECS
		if strings.ToLower(data.HostType) != hbc.ECS.String() {
			continue
		}

		defClient, err := config.GetClient(ctx, data.Vendor, prod, data.AccountName, data.Region)
		if err != nil {
			if strings.Contains(err.Error(), "account not found") {
				fmt.Println("[account not found] accountName: ", data.AccountName, ", vendor: ", data.Vendor)
				continue
			}
			if strings.Contains(err.Error(), "not in the following supported regions") {
				fmt.Println("[not in the following supported regions] regions: ", data.Region)
				continue
			}
			return nil, err
		}

		// 根据客户端类型调用sdk详情api
		switch defClient.(type) {
		case *aliyun.EcsClient:
			if data.Vendor == "aliyun" {
				cli := hybrid.AccountManager().AliyunEcsClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region())
				request := func(e *ecs.DescribeInstancesRequest) {
					e.InstanceIds = fmt.Sprintf("['%s']", data.InstanceId)
				}
				res, err := cli.DescribeInstances(request)
				if err != nil {
					return nil, err
				}
				if len(res) == 0 {
					cleanResults = append(cleanResults, data)
				}
			}

			if data.Vendor == "aliyun_dedicated" {
				cli := hybrid.AccountManager().AliyunDedicatedEcsClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region())
				request := func(e *ecs.DescribeInstancesRequest) {
					e.InstanceIds = fmt.Sprintf("['%s']", data.InstanceId)
				}
				res, err := cli.DescribeInstances(request)
				if err != nil {
					return nil, err
				}
				if len(res) == 0 {
					cleanResults = append(cleanResults, data)
				}
			}
		}
	}

	return cleanResults, nil
}
