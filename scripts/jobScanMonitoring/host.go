package jobScanMonitoring

import (
	"context"
	"errors"
	"git.haier.net/devops/ops-golang-common/sdk/zabbix"
)

// GetHosts 批量按照instance_id查询zabbix中的监控信息
func GetHosts(ctx context.Context, z *zabbix.Context, hosts []string) ([]zabbix.HostObject, error) {
	if z == nil {
		return nil, errors.New("zabbix context hasn't been initialized properly")
	}
	if len(hosts) < 1 {
		return nil, errors.New("hosts is empty")
	}

	param := zabbix.HostGetParams{
		GetParameters: zabbix.GetParameters{
			Filter: map[string]interface{}{
				"host": hosts,
			},
		},
	}

	hostObjs, _, err := z.HostGet(param)
	if err != nil {
		return nil, err
	}

	return hostObjs, nil
}
