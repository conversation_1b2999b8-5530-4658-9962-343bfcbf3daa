package jobScanMonitoring

const (
	MonitoringModeIsNone         = 0
	MonitoringModeIsZabbix       = 1
	MonitoringModeIsPrometheus   = 2
	MonitoringModeIsNotNecessary = 3
)

const (
	DbmsModeIsNone      = 0
	DbmsModeIsSteady    = 1
	DbmsModeIsSafe      = 2
	DbmsModeIsSensitive = 3
)

// ChunkSlice splits a slice into chunks of the specified size.
func ChunkSlice[T any](slice []T, chunkSize int) [][]T {
	if chunkSize <= 0 {
		return nil
	}

	var chunks [][]T
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize

		if end > len(slice) {
			end = len(slice)
		}

		chunks = append(chunks, slice[i:end])
	}

	return chunks
}
