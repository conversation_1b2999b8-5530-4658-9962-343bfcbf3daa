package jobScanMonitoring

import (
	"context"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/zabbix"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewScanZabbixMonitoring() *ScanZabbixMonitoring {
	return &ScanZabbixMonitoring{
		JobBase: base.NewJobBase(base.TaskScanZabbixMonitoring,
			"扫描Zabbix监控",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithDay(1),
			),
			taskmodels.TaskCategoryOps,
			bizutils.DataSource(),
		),
	}
}

type ScanZabbixMonitoring struct {
	*base.JobBase
}

func (*ScanZabbixMonitoring) Name() string {
	return base.TaskScanZabbixMonitoring
}

func (j *ScanZabbixMonitoring) Run(ctx context.Context) (map[string]any, error) {
	credentials := config.Global().ZabbixConfig.GetCredentials()
	if len(credentials) < 1 {
		return nil, nil
	}

	b := tools.NewBatch[*zabbix.Credential, *zabbix.Context](ctx)
	b.Run(credentials, func(ctx context.Context, credential *zabbix.Credential) (*zabbix.Context, error) {
		z := &zabbix.Context{}
		err := z.Login(credential.Host, credential.User, credential.Password)
		if err != nil {
			return nil, err
		}
		return z, nil
	})

	zabbixCtxs := tools.MergeData(b.Outs())
	if len(zabbixCtxs) < 1 {
		return nil, nil
	}

	zb := tools.NewBatch[*zabbix.Context, error](ctx)
	zb.Run(zabbixCtxs, func(ctx context.Context, zabbixCtx *zabbix.Context) (error, error) {
		defer func(zabbixCtx *zabbix.Context) {
			err := zabbixCtx.Logout()
			if err != nil {
				log.Error(ctx, "Logout error: ", err)
			}
		}(zabbixCtx)

		// 1、获取cmdb所有主机
		model := j.Model(ctx)
		var hostInfos []models.HostInfo
		model.Orm().Model(&models.HostInfo{}).
			Where("is_deleted != 1 and monitoring_mode != 3").
			Find(&hostInfos)

		// 2、获取cmdb所有数据库
		var databaseInfos []models.DatabaseInfo
		model.Orm().Model(&models.DatabaseInfo{}).
			Where("is_deleted != 1 and monitoring_mode != 3").
			Find(&databaseInfos)

		chunkSize := 50
		hostChunkArray := ChunkSlice[models.HostInfo](hostInfos, chunkSize)
		dbChunkArray := ChunkSlice[models.DatabaseInfo](databaseInfos, chunkSize)

		// 3、批量查询服务器zabbix中的监控状态并进行更新
		hb := tools.NewBatch[[]models.HostInfo, []zabbix.HostObject](ctx)
		hb.Run(hostChunkArray, func(ctx context.Context, hostChunk []models.HostInfo) ([]zabbix.HostObject, error) {
			hosts := make([]string, 0)
			for _, host := range hostChunk {
				hosts = append(hosts, host.InstanceId)
			}
			hostObjs, err := GetHosts(ctx, zabbixCtx, hosts)
			if err != nil {
				return nil, err
			}
			return hostObjs, nil
		})

		hostsInZb := tools.MergeData(hb.Outs()...)
		hostAvailableMap := make(map[string]int, 0)
		for _, _host := range hostsInZb {
			hostAvailableMap[_host.Host] = _host.Available
		}

		for _, _host := range hostInfos {
			mode := MonitoringModeIsZabbix
			if ifAvailable, ok := hostAvailableMap[_host.InstanceId]; ok {
				if ifAvailable != zabbix.HostAvailableAvailable {
					mode = MonitoringModeIsNone
				}
			} else {
				mode = MonitoringModeIsNone
			}
			model.Orm().Model(&models.HostInfo{}).
				Where("instance_id = ?", _host.InstanceId).Updates(models.HostInfo{
				MonitoringMode: mode,
			})
		}

		db := tools.NewBatch[[]models.DatabaseInfo, []zabbix.HostObject](ctx)
		db.Run(dbChunkArray, func(ctx context.Context, dbChunk []models.DatabaseInfo) ([]zabbix.HostObject, error) {
			dbs := make([]string, 0)
			for _, _db := range dbChunk {
				dbs = append(dbs, _db.InstanceId)
			}
			hostObjs, err := GetHosts(ctx, zabbixCtx, dbs)
			if err != nil {
				return nil, err
			}
			return hostObjs, nil
		})
		dbsInZb := tools.MergeData(db.Outs()...)
		dbAvailableMap := make(map[string]int, 0)
		for _, _db := range dbsInZb {
			dbAvailableMap[_db.Host] = _db.Available
		}

		for _, _db := range databaseInfos {
			mode := MonitoringModeIsZabbix
			if ifAvailable, ok := dbAvailableMap[_db.InstanceId]; ok {
				if ifAvailable != zabbix.HostAvailableAvailable {
					mode = MonitoringModeIsNone
				}
			} else {
				mode = MonitoringModeIsNone
			}
			model.Orm().Model(&models.DatabaseInfo{}).Where("instance_id = ?", _db.InstanceId).Updates(models.DatabaseInfo{MonitoringMode: mode})
		}

		return nil, nil
	})

	return nil, nil
}
