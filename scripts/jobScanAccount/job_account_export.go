package jobScanAccount

import (
	"context"
	"strings"
	"sync"

	"git.haier.net/devops/ops-golang-common/haierapi"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	myCtx "git.haier.net/devops/ops-golang-common/utils/context"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ram"
	"github.com/aws/aws-sdk-go-v2/service/iam/types"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	cam "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cam/v20190116"
	"go.uber.org/atomic"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewExporter() *Export {
	c := config.Global()
	return &Export{
		logger:  log.New("ACC_SCAN"),
		ds:      c.GetStore(c.DefaultStoreName),
		mtx:     new(sync.Mutex),
		counter: map[hbc.CloudVendor]map[string]*atomic.Int64{},
		cli:     api.HcmsClient(),
	}
}

type UserAccount struct {
	Vendor          hbc.CloudVendor
	Account         string
	UserAccountName string
	UserId          string
	UserName        string
	Dept            string
}

type Export struct {
	schedule string
	enable   bool
	logger   *log.Logger
	ds       *models.Datasource
	counter  map[hbc.CloudVendor]map[string]*atomic.Int64
	cli      *haierapi.HcmsApi
	mtx      *sync.Mutex
}

func (s *Export) Run(ctx *myCtx.Context) ([]*UserAccount, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(s.ds.Model(ctx), hbc.GoogleCloud)
	if err != nil {
		return nil, err
	}
	// 获取云账号
	clients, err := base.GetDefaultIClients(
		ctx, vendors,
		hbc.IAM,
		[]string{bizutils.PurposeAccountCheck},
		actfilter.SetIgnoreVendor(hbc.Private))
	if err != nil {
		return nil, err
	}

	b := tools.NewBatch[client.IClient, []*UserAccount](ctx)
	b.Run(clients, func(ctx context.Context, input client.IClient) ([]*UserAccount, error) {
		switch c := input.(type) {
		case *aliyun.RamClient:
			return s.aliyunUserFilter(ctx, c)
		case *aws.IamClient:
			return s.awsUserFilter(ctx, c)
		case *tencentcloud.CamClient:
			return s.tencentUserFilter(ctx, c)
		case *huaweicloud.IamClient:
			return s.huaweiUserFilter(ctx, c)
		}
		return nil, nil
	})
	if err = b.Error(); err != nil {
		return nil, err
	}

	exprUsers := make([]*UserAccount, 0)
	for _, users := range b.Outs() {
		exprUsers = append(exprUsers, users...)
	}

	return exprUsers, nil
}

// ====================================================================

func (s *Export) aliyunUserFilter(ctx context.Context, c *aliyun.RamClient) ([]*UserAccount, error) {
	users, err := c.ListUsersAll()
	if err != nil {
		s.logger.Errorf(ctx, "aliyunUserFilter failed: %s", err)
		return nil, err
	}
	return handlerBatchUser[ram.User](ctx, s, c, users,
		func(ctx context.Context, user ram.User) (*UserAccount, error) {
			if IsUserId(user.UserName) {
				return s.getUsers(ctx, c, user.UserName, user.UserId)
			}
			return nil, nil
		})
}

func (s *Export) awsUserFilter(ctx context.Context, c *aws.IamClient) ([]*UserAccount, error) {
	users, err := c.ListUsers(ctx)
	if err != nil {
		s.logger.Errorf(ctx, "awsUserFilter failed: %s", err)
		return nil, err
	}

	return handlerBatchUser[types.User](ctx, s, c, users,
		func(ctx context.Context, user types.User) (*UserAccount, error) {
			if IsUserId(pointer.String(user.UserName)) {
				return s.getUsers(ctx, c, pointer.String(user.UserName), pointer.String(user.UserName))
			}
			return nil, nil
		})
}

func (s *Export) tencentUserFilter(ctx context.Context, c *tencentcloud.CamClient) ([]*UserAccount, error) {
	users, err := c.ListUsers(ctx)
	if err != nil {
		s.logger.Errorf(ctx, "tencentUserFilter failed: %s", err)
		return nil, err
	}
	return handlerBatchUser[*cam.SubAccountInfo](ctx, s, c, users,
		func(ctx context.Context, user *cam.SubAccountInfo) (*UserAccount, error) {
			if IsUserId(pointer.String(user.Remark)) {
				return s.getUsers(ctx, c, pointer.String(user.Remark), pointer.String(user.Name))
			}
			return nil, nil
		})
}

func (s *Export) huaweiUserFilter(ctx context.Context, c *huaweicloud.IamClient) ([]*UserAccount, error) {
	users, err := c.ListUsers(ctx)
	if err != nil {
		s.logger.Errorf(ctx, "huaweiUserFilter failed: %s", err)
		return nil, err
	}

	return handlerBatchUser[model.KeystoneListUsersResult](ctx, s, c, users,
		func(ctx context.Context, user model.KeystoneListUsersResult) (*UserAccount, error) {
			// 与管理员协商，在用户描述处增加 [用户|工号|..] 方式增加账号描述
			tmp := strings.Split(pointer.String(user.Description), "|")
			if len(tmp) > 1 {
				return s.getUsers(ctx, c, tmp[1], user.Name)
			}
			// 防止遗漏
			if IsUserId(user.Name) {
				return s.getUsers(ctx, c, user.Name, user.Name)
			}
			// 防止遗漏
			if ok, perId := IsHwUserId(user.Name); ok {
				return s.getUsers(ctx, c, perId, user.Name)
			}
			return nil, nil
		})
}

// 通过Resource-Center接口查询用户是否在职
// 接口单IP限流每秒1000次
func (s *Export) getUsers(ctx context.Context, c client.IClient, userId, userAccountName string) (*UserAccount, error) {
	account, _ := s.cli.QueryHaierUserInfo(ctx, userId)
	u := &UserAccount{
		Vendor:          c.Vendor(),
		Account:         c.Name(),
		UserAccountName: userAccountName,
		UserId:          userId,
	}
	if account != nil && len(account) > 0 {
		u.UserName = account[0].UserName
		u.Dept = account[0].Dept
	}

	return u, nil
}

func (s *Export) count(c client.IClient, count int) {
	s.mtx.Lock()
	defer s.mtx.Unlock()
	if _, ok := s.counter[c.Vendor()]; !ok {
		s.counter[c.Vendor()] = map[string]*atomic.Int64{}
	}
	if _, ok := s.counter[c.Vendor()][c.Name()]; !ok {
		s.counter[c.Vendor()][c.Name()] = new(atomic.Int64)
	}
	s.counter[c.Vendor()][c.Name()].Add(int64(count))
}

func handlerBatchUser[IN any](
	ctx context.Context, s *Export, c client.IClient, inputs []IN, handleFunc func(ctx context.Context, input IN) (*UserAccount, error)) ([]*UserAccount, error) {
	s.count(c, len(inputs))
	b := tools.NewBatch[IN, *UserAccount](
		ctx,
	).Run(inputs, func(ctx context.Context, input IN) (*UserAccount, error) {
		return handleFunc(ctx, input)
	})
	return b.Outs(), b.Error()
}
