package jobScanAccount

import (
	"regexp"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
)

var regUserId, _ = regexp.Compile(`^[0-9a-zA-Z]\d{7}$`)
var regHwUserId, _ = regexp.Compile(`^hw[0-9a-zA-Z]\d{7}$`)

func IsUserId(str string) bool {
	return regUserId.MatchString(str)
}

func IsHwUserId(str string) (bool, string) {
	return regHwUserId.MatchString(str), strings.TrimPrefix(strings.ToLower(str), "hw")
}

func DatetimeString(t time.Time) string {
	return t.Format(bizutils.DefaultTimeFormatter)
}
