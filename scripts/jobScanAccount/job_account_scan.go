package jobScanAccount

import (
	"context"
	"strings"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/haierapi"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ram"
	"github.com/aws/aws-sdk-go-v2/service/iam/types"
	"github.com/gin-gonic/gin"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	cam "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cam/v20190116"
	"go.uber.org/atomic"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func New() *AccountScan {
	return &AccountScan{
		mtx:     new(sync.Mutex),
		counter: map[hbc.CloudVendor]map[string]*atomic.Int64{},
		cli:     api.HcmsClient(),
		JobBase: base.NewJobBase(
			base.TaskCloudAccountScan,
			"扫描过期账号",
			base.NewSchedule(
				base.WithMin(0),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryOps,
			bizutils.DataSource(),
		)}
}

type ExpiredUser struct {
	Vendor          hbc.CloudVendor
	Account         string
	UserAccountName string
	UserId          string
	ScanTime        string
}

type AccountScan struct {
	counter map[hbc.CloudVendor]map[string]*atomic.Int64
	mtx     *sync.Mutex
	cli     *haierapi.HcmsApi

	*base.JobBase
}

type AccountCount struct {
	Vendor      hbc.CloudVendor `json:"vendor"`
	AccountName string          `json:"accountName"`
	Count       int64           `json:"count"`
}

func (s *AccountScan) Name() string {
	return base.TaskCloudAccountScan
}

func (s *AccountScan) Run(ctx context.Context) (map[string]any, error) {
	startTime := time.Now()
	// 获取云列表
	vendors, err := hybrid.GetVendors(s.Model(ctx), hbc.GoogleCloud)
	if err != nil {
		return nil, err
	}
	// 获取云账号
	clients, err := base.GetDefaultIClients(ctx, vendors, hbc.IAM, []string{bizutils.PurposeAccountCheck})
	if err != nil {
		return nil, err
	}

	b := tools.NewBatch[client.IClient, []*ExpiredUser](ctx)
	b.Run(clients, func(ctx context.Context, input client.IClient) ([]*ExpiredUser, error) {
		switch c := input.(type) {
		case *aliyun.RamClient:
			users, err := s.aliyunUserFilter(ctx, c)
			return users, bizutils.WarpClientError(input, err)
		case *aws.IamClient:
			users, err := s.awsUserFilter(ctx, c)
			return users, bizutils.WarpClientError(input, err)
		case *tencentcloud.CamClient:
			users, err := s.tencentUserFilter(ctx, c)
			return users, bizutils.WarpClientError(input, err)
		case *huaweicloud.IamClient:
			users, err := s.huaweiUserFilter(ctx, c)
			return users, bizutils.WarpClientError(input, err)
		}
		return nil, nil
	})
	if err = b.Error(); err != nil {
		return nil, err
	}

	exprUsers := make([]*ExpiredUser, 0)
	for _, users := range b.Outs() {
		exprUsers = append(exprUsers, users...)
	}

	return s.countResult(startTime, exprUsers), nil
}

func (s *AccountScan) countResult(startTime time.Time, exprUsers []*ExpiredUser) gin.H {
	accounts := make(map[hbc.CloudVendor][]*AccountCount)
	for vendor, accountCount := range s.counter {
		if accounts[vendor] == nil {
			accounts[vendor] = make([]*AccountCount, 0)
		}
		for account, count := range accountCount {
			accounts[vendor] = append(accounts[vendor], &AccountCount{
				Vendor:      vendor,
				AccountName: account,
				Count:       count.Load(),
			})
		}
	}

	result := gin.H{
		"start_time":  DatetimeString(startTime),
		"end_time":    DatetimeString(time.Now()),
		"counter":     accounts,
		"expire_user": exprUsers,
	}
	return result
}

// ====================================================================

func (s *AccountScan) aliyunUserFilter(ctx context.Context, c *aliyun.RamClient) ([]*ExpiredUser, error) {
	users, err := bizutils.WithRetry(
		3,
		func() ([]ram.User, error) {
			return c.ListUsersAll()
		},
	)
	if err != nil {
		s.Errorf(ctx, "aliyunUserFilter failed: %s", err)
		return nil, err
	}
	return handlerBatchCheckUserExpire[ram.User](ctx, s, c, users,
		func(ctx context.Context, user ram.User) (*ExpiredUser, error) {
			if IsUserId(user.UserName) {
				return s.getExpiredUser(ctx, c, user.UserName, user.UserId)
			}
			return nil, nil
		})
}

func (s *AccountScan) awsUserFilter(ctx context.Context, c *aws.IamClient) ([]*ExpiredUser, error) {
	users, err := bizutils.WithRetry(
		3,
		func() ([]types.User, error) {
			return c.ListUsers(ctx)
		},
	)
	if err != nil {
		s.Errorf(ctx, "awsUserFilter failed: %s", err)
		return nil, err
	}

	return handlerBatchCheckUserExpire[types.User](ctx, s, c, users,
		func(ctx context.Context, user types.User) (*ExpiredUser, error) {
			if IsUserId(pointer.String(user.UserName)) {
				return s.getExpiredUser(ctx, c, pointer.String(user.UserName), pointer.String(user.UserName))
			}
			return nil, nil
		})
}

func (s *AccountScan) tencentUserFilter(ctx context.Context, c *tencentcloud.CamClient) ([]*ExpiredUser, error) {
	users, err := bizutils.WithRetry(
		3,
		func() ([]*cam.SubAccountInfo, error) {
			return c.ListUsers(ctx)
		},
	)

	if err != nil {
		s.Errorf(ctx, "tencentUserFilter failed: %s", err)
		return nil, err
	}
	return handlerBatchCheckUserExpire[*cam.SubAccountInfo](ctx, s, c, users,
		func(ctx context.Context, user *cam.SubAccountInfo) (*ExpiredUser, error) {
			if IsUserId(pointer.String(user.Remark)) {
				return s.getExpiredUser(ctx, c, pointer.String(user.Remark), pointer.String(user.Name))
			}
			return nil, nil
		})
}

func (s *AccountScan) huaweiUserFilter(ctx context.Context, c *huaweicloud.IamClient) ([]*ExpiredUser, error) {
	users, err := bizutils.WithRetry(
		3,
		func() ([]model.KeystoneListUsersResult, error) {
			return c.ListUsers(ctx)
		},
	)

	if err != nil {
		s.Errorf(ctx, "huaweiUserFilter failed: %s", err)
		return nil, err
	}

	return handlerBatchCheckUserExpire[model.KeystoneListUsersResult](ctx, s, c, users,
		func(ctx context.Context, user model.KeystoneListUsersResult) (*ExpiredUser, error) {
			// 与管理员协商，在用户描述处增加 [用户|工号|..] 方式增加账号描述
			tmp := strings.Split(pointer.String(user.Description), "|")
			if len(tmp) > 1 {
				return s.getExpiredUser(ctx, c, tmp[1], user.Name)
			}
			// 防止遗漏
			if IsUserId(user.Name) {
				return s.getExpiredUser(ctx, c, user.Name, user.Name)
			}
			// 防止遗漏
			if ok, perId := IsHwUserId(user.Name); ok {
				return s.getExpiredUser(ctx, c, perId, user.Name)
			}
			return nil, nil
		})
}

// 通过Resource-Center接口查询用户是否在职
// 接口单IP限流每秒1000次
func (s *AccountScan) getExpiredUser(ctx context.Context, c client.IClient, userId, userAccountName string) (*ExpiredUser, error) {
	userInfo, err := s.cli.QueryHaierUserInfo(ctx, userId)
	if err != nil {
		return nil, err
	}
	if len(userInfo) != 0 {
		return nil, nil
	}
	return &ExpiredUser{
		Vendor:          c.Vendor(),
		Account:         c.Name(),
		UserAccountName: userAccountName,
		UserId:          userId,
		ScanTime:        DatetimeString(time.Now()),
	}, nil
}

func (s *AccountScan) count(c client.IClient, count int) {
	s.mtx.Lock()
	defer s.mtx.Unlock()
	if _, ok := s.counter[c.Vendor()]; !ok {
		s.counter[c.Vendor()] = map[string]*atomic.Int64{}
	}
	if _, ok := s.counter[c.Vendor()][c.Name()]; !ok {
		s.counter[c.Vendor()][c.Name()] = new(atomic.Int64)
	}
	s.counter[c.Vendor()][c.Name()].Add(int64(count))
}

func handlerBatchCheckUserExpire[IN any](
	ctx context.Context, s *AccountScan, c client.IClient, inputs []IN, handleFunc func(ctx context.Context, input IN) (*ExpiredUser, error)) ([]*ExpiredUser, error) {
	s.count(c, len(inputs))
	b := tools.NewBatch[IN, *ExpiredUser](
		ctx,
	).Run(inputs, func(ctx context.Context, input IN) (*ExpiredUser, error) {
		return handleFunc(ctx, input)
	})
	return b.Outs(), b.Error()
}
