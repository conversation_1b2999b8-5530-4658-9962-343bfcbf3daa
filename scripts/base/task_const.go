package base

const (
	TaskCloudAccountScan             = "cloud-scan-account"
	TaskCloudSyncEcs                 = "cloud-sync-ecs"
	TaskCloudSyncEcsDedicated        = "cloud-sync-ecs-dedicated"
	TaskCloudSyncRds                 = "cloud-sync-rds"
	TaskCloudSyncBucket              = "cloud-sync-bucket"
	TaskCloudSyncBucketDedicated     = "cloud-sync-bucket-dedicated"
	TaskCloudSyncGaussDB             = "cloud-sync-gaussdb"
	TaskCloudSyncGeminiDB            = "cloud-sync-geminidb"
	TaskCloudSyncStarRocks           = "cloud-sync-starrocks"
	TaskCloudSyncRdsDedicated        = "cloud-sync-rds-dedicated"
	TaskCloudSyncMemcache            = "cloud-sync-memcache"
	TaskCloudSyncRedis               = "cloud-sync-redis"
	TaskCloudSyncRedisDedicated      = "cloud-sync-redis-dedicated"
	TaskCloudSyncMongoDB             = "cloud-sync-mongodb"
	TaskCloudSyncPolarDB             = "cloud-sync-polardb"
	TaskCloudSyncPolarDBX            = "cloud-sync-polardbx"
	TaskCloudSyncElasticSearch       = "cloud-sync-elasticsearch"
	TaskCloudSyncHbase               = "cloud-sync-hbase"
	TaskCloudSyncHitsdb              = "cloud-sync-hitsdb"
	TaskCloudSyncAdb                 = "cloud-sync-adb"
	TaskCloudSyncAdbPgSQL            = "cloud-sync-adb-pgsql"
	TaskCloudSyncClickhouse          = "cloud-sync-clickhouse"
	TaskCloudSyncOceanBase           = "cloud-sync-oceanbase"
	TaskCloudSyncPrivateDB           = "cloud-sync-privatedb"
	TaskCloudSyncMiddleware          = "cloud-sync-middleware"
	TaskCloudSyncMiddlewareDedicated = "cloud-sync-middleware-dedicated"
	TaskCloudSyncSubnet              = "cloud-sync-subnet"
	TaskCloudSyncSubnetDedicated     = "cloud-sync-subnet-dedicated"
	TaskCloudSyncVM                  = "cloud-sync-vm"
	TaskCloudSyncWiki                = "cloud-sync-wiki"
	TaskDataClean                    = "cloud-data-clean"
	TaskJobClean                     = "cloud-job-clean"
	TaskHostClean                    = "cloud-host-clean"
	TaskSyncJXJG                     = "cloud-sync-jxjg"
	TaskSyncFactory                  = "cloud-sync-factory"
	TaskAutoScanExporter             = "cloud-auto-scan-exporter"
	TaskSyncBill                     = "cloud-sync-bill"
	TaskRefineBillAliyun             = "cloud-refine-bill-aliyun"
	TaskRefineBillHuawei             = "cloud-refine-bill-huawei"
	TaskRefineBillTencent            = "cloud-refine-bill-tencent"
	TaskRefineBillAzure              = "cloud-refine-bill-azure"
	TaskRefineBillAws                = "cloud-refine-bill-aws"
	TaskRefineBillGcloud             = "cloud-refine-bill-gcloud"
	TaskRefineBillOracle             = "cloud-refine-bill-oracle"
	TaskRetrieveAliyunBill           = "cloud-retrieve-aliyun-bill"
	TaskRetrieveHuaweiBill           = "cloud-retrieve-huawei-bill"
	TaskRetrieveTencentBill          = "cloud-retrieve-tencent-bill"
	TaskRetrieveGoogleCloudBill      = "cloud-retrieve-gcloud-bill"
	TaskRetrieveAwsBill              = "cloud-retrieve-aws-bill"
	TaskSyncDownloadBillReport       = "cloud-sync-download-bill-report"
	TaskSyncAliyunBill               = "cloud-sync-aliyun-bill"
	TaskSyncHuaweiyunBill            = "cloud-sync-huaweicloud-bill"
	TaskSyncHuaweiyunDedicatedBill   = "cloud-sync-huaweicloud-dedicated-bill"
	TaskSyncTencentBill              = "cloud-sync-tencentcloud-bill"
	TaskSyncGoogleBill               = "cloud-sync-googlecloud-bill"
	TaskSyncOracleBill               = "cloud-sync-oracle-bill"
	TaskSyncAzureBill                = "cloud-sync-azure-bill"
	TaskSyncAwsBill                  = "cloud-sync-aws-bill"
	TaskOracleDownloadBill           = "cloud-oracle-download-bill"
	TaskAwsDownloadBill              = "cloud-aws-download-bill"
	TaskAwsCompareBill               = "cloud-aws-compare-bill"
	TaskAwsPricingBill               = "cloud-aws-prince-bill"
	TaskSyncResource                 = "cloud-sync-resource"
	TaskPreAllocation                = "cloud-aliyun-pre-allocation"
	TaskRegisterPrometheus           = "cloud-register-prometheus"
	TaskDBMSSync                     = "dbms-meta-sync"
	TaskDbmsBaseSync                 = "dbms-base-sync"
	TaskCheckRdsIPWhiteList          = "check-rds-ip-white-list"
	TaskSyncHbr                      = "dbms-sync-hbr"
	TaskScanZabbixMonitoring         = "scan-zabbix-monitoring"
	TaskSyncNetworkDevice            = "network-device-sync"
)
