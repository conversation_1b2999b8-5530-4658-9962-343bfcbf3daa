package base

import (
	"fmt"
	"math/rand"
	"strconv"
)

type ScheduleOption func(schedule *Schedule)

func WithSec(s int) ScheduleOption {
	return func(schedule *Schedule) {
		schedule.sec = &s
	}
}

func WithMin(s int) ScheduleOption {
	return func(schedule *Schedule) {
		schedule.min = &s
	}
}

func WithHour(s int) ScheduleOption {
	return func(schedule *Schedule) {
		schedule.hour = &s
	}
}

func WithDay(s int) ScheduleOption {
	return func(schedule *Schedule) {
		schedule.day = &s
	}
}

func WithMonth(s int) ScheduleOption {
	return func(schedule *Schedule) {
		schedule.mon = &s
	}
}

func WithWeek(s int) ScheduleOption {
	return func(schedule *Schedule) {
		schedule.week = &s
	}
}

func WithYear(s int) ScheduleOption {
	return func(schedule *Schedule) {
		schedule.year = &s
	}
}

func WithRandSec() ScheduleOption {
	return func(schedule *Schedule) {
		h := rand.Intn(60)
		schedule.min = &h
	}
}

func WithRandMin() ScheduleOption {
	return func(schedule *Schedule) {
		m := rand.Intn(60)
		schedule.min = &m
	}
}

func WithRandHour() ScheduleOption {
	return func(schedule *Schedule) {
		h := rand.Intn(24)
		schedule.hour = &h
	}
}

func NewSchedule(opts ...ScheduleOption) string {
	s := &Schedule{}
	for _, o := range opts {
		o(s)
	}
	return s.String()
}

// Schedule [秒] 分 时 日 月 周 [年]
type Schedule struct {
	sec  *int // 0-59
	min  *int // 0-59
	hour *int // 0-23
	day  *int // 1-31
	mon  *int // 1-12
	week *int // 1-7
	year *int
}

func (s *Schedule) p(i *int) string {
	if i == nil {
		return "*"
	}
	return strconv.Itoa(*i)
}

func (s *Schedule) String() string {
	return fmt.Sprintf("%s %s %s %s %s %s %s",
		s.p(s.sec), s.p(s.min), s.p(s.hour), s.p(s.day), s.p(s.mon), s.p(s.week), s.p(s.year))
}
