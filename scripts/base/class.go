package base

import (
	"git.haier.net/devops/ops-golang-common/hbc"
)

type ClassCode struct {
	Vendor         hbc.CloudVendor
	Product        hbc.CloudProduct
	ClassCode      string
	Cpu            int
	MemoryGB       int
	MaxIOPs        int
	MaxConnections int
	MaxStoreGB     int
	Enable         bool
}

func GetClassCode(vendor hbc.CloudVendor, product hbc.CloudProduct, code string) ClassCode {
	if productClassCode[vendor] == nil {
		return ClassCode{}
	}
	if productClassCode[vendor][product] == nil {
		return ClassCode{}
	}
	return productClassCode[vendor][product][code]
}

var productClassCode = map[hbc.CloudVendor]map[hbc.CloudProduct]map[string]ClassCode{
	hbc.AliCloud: {
		hbc.MongoDB: {
			// https://help.aliyun.com/document_detail/311403.html?spm=a2c4g.343375.0.0.74254de216bfys
			"dds.sn2.large.1":          {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.sn2.large.1", Cpu: 4, MemoryGB: 8, MaxIOPs: 20000, MaxConnections: 6000},
			"dds.sn4.xlarge.1":         {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.sn4.xlarge.1", Cpu: 4, MemoryGB: 16, MaxIOPs: 20000, MaxConnections: 8000, MaxStoreGB: 2000},
			"dds.sn2.xlarge.1":         {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.sn2.xlarge.1", Cpu: 8, MemoryGB: 16, MaxIOPs: 20000, MaxConnections: 8000, MaxStoreGB: 2000},
			"dds.sn4.2xlarge.1":        {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.sn4.2xlarge.1", Cpu: 8, MemoryGB: 32, MaxIOPs: 20000, MaxConnections: 10000, MaxStoreGB: 2000},
			"mdb.shard.2x.large.s":     {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.2x.large.s", Cpu: 2, MemoryGB: 4, MaxIOPs: 10500, MaxConnections: 1000, MaxStoreGB: 16000},
			"mdb.shard.4x.large.s":     {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.4x.large.s", Cpu: 2, MemoryGB: 8, MaxIOPs: 10500, MaxConnections: 3000, MaxStoreGB: 16000},
			"mdb.shard.2x.xlarge.s":    {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.2x.xlarge.s", Cpu: 4, MemoryGB: 8, MaxIOPs: 21000, MaxConnections: 3000, MaxStoreGB: 16000},
			"mdb.shard.4x.xlarge.s":    {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.4x.xlarge.s", Cpu: 4, MemoryGB: 16, MaxIOPs: 21000, MaxConnections: 5000, MaxStoreGB: 16000},
			"mdb.shard.2x.2xlarge.s":   {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.2x.2xlarge.s", Cpu: 8, MemoryGB: 16, MaxIOPs: 26250, MaxConnections: 5000, MaxStoreGB: 16000},
			"mdb.shard.4x.2xlarge.s":   {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.4x.2xlarge.s", Cpu: 8, MemoryGB: 32, MaxIOPs: 26250, MaxConnections: 8000, MaxStoreGB: 16000},
			"mdb.shard.2x.4xlarge.s":   {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.2x.4xlarge.s", Cpu: 16, MemoryGB: 32, MaxIOPs: 42000, MaxConnections: 8000, MaxStoreGB: 16000},
			"mdb.shard.4x.4xlarge.s":   {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.4x.4xlarge.s", Cpu: 16, MemoryGB: 64, MaxIOPs: 42000, MaxConnections: 16000, MaxStoreGB: 16000},
			"mdb.shard.2x.8xlarge.s":   {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.2x.8xlarge.s", Cpu: 32, MemoryGB: 64, MaxIOPs: 50000, MaxConnections: 16000, MaxStoreGB: 16000},
			"mdb.shard.4x.8xlarge.s":   {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mdb.shard.4x.8xlarge.s", Cpu: 32, MemoryGB: 128, MaxIOPs: 50000, MaxConnections: 16000, MaxStoreGB: 16000},
			"dds.mongo.mid":            {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.mongo.mid", Cpu: 1, MemoryGB: 2, MaxIOPs: 0, MaxConnections: 500, MaxStoreGB: 0},
			"dds.mongo.standard":       {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.mongo.standard", Cpu: 2, MemoryGB: 4, MaxIOPs: 0, MaxConnections: 1000, MaxStoreGB: 0},
			"dds.mongo.large":          {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.mongo.large", Cpu: 4, MemoryGB: 8, MaxIOPs: 0, MaxConnections: 3000, MaxStoreGB: 0},
			"dds.mongo.xlarge":         {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.mongo.xlarge", Cpu: 8, MemoryGB: 16, MaxIOPs: 0, MaxConnections: 5000, MaxStoreGB: 0},
			"dds.mongo.2xlarge":        {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.mongo.2xlarge", Cpu: 8, MemoryGB: 32, MaxIOPs: 0, MaxConnections: 8000, MaxStoreGB: 0},
			"dds.mongo.4xlarge":        {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.mongo.4xlarge", Cpu: 16, MemoryGB: 64, MaxIOPs: 0, MaxConnections: 16000, MaxStoreGB: 0},
			"mongo.x8.medium":          {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mongo.x8.medium", Cpu: 2, MemoryGB: 16, MaxIOPs: 0, MaxConnections: 2500, MaxStoreGB: 0},
			"mongo.x8.large":           {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mongo.x8.large", Cpu: 4, MemoryGB: 32, MaxIOPs: 0, MaxConnections: 5000, MaxStoreGB: 0},
			"mongo.x8.xlarge":          {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mongo.x8.xlarge", Cpu: 8, MemoryGB: 64, MaxIOPs: 0, MaxConnections: 10000, MaxStoreGB: 0},
			"mongo.x8.2xlarge":         {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mongo.x8.2xlarge", Cpu: 16, MemoryGB: 128, MaxIOPs: 0, MaxConnections: 20000, MaxStoreGB: 0},
			"mongo.x8.4xlarge":         {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "mongo.x8.4xlarge", Cpu: 32, MemoryGB: 256, MaxIOPs: 0, MaxConnections: 40000, MaxStoreGB: 0},
			"dds.mongo.2xmonopolize":   {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.mongo.2xmonopolize", Cpu: 60, MemoryGB: 440, MaxIOPs: 0, MaxConnections: 100000, MaxStoreGB: 0},
			"dds.mongo.3xmonopolize":   {Vendor: hbc.AliCloud, Product: hbc.MongoDB, ClassCode: "dds.mongo.3xmonopolize", Cpu: 90, MemoryGB: 660, MaxIOPs: 0, MaxConnections: 100000, MaxStoreGB: 0},
			"mdb.shard.4x.large.d":     {Vendor: "", Product: "", ClassCode: "mdb.shard.4x.large.d", Cpu: 2, MemoryGB: 8, MaxIOPs: 0, MaxConnections: 3000, MaxStoreGB: 0},
			"mdb.shard.8x.large.d":     {Vendor: "", Product: "", ClassCode: "mdb.shard.8x.large.d", Cpu: 2, MemoryGB: 16, MaxIOPs: 0, MaxConnections: 5000, MaxStoreGB: 0},
			"mdb.shard.4x.xlarge.d":    {Vendor: "", Product: "", ClassCode: "mdb.shard.4x.xlarge.d", Cpu: 4, MemoryGB: 16, MaxIOPs: 0, MaxConnections: 5000, MaxStoreGB: 0},
			"mdb.shard.8x.xlarge.d":    {Vendor: "", Product: "", ClassCode: "mdb.shard.8x.xlarge.d", Cpu: 4, MemoryGB: 32, MaxIOPs: 0, MaxConnections: 8000, MaxStoreGB: 0},
			"mdb.shard.2x.2xlarge.d":   {Vendor: "", Product: "", ClassCode: "mdb.shard.2x.2xlarge.d", Cpu: 8, MemoryGB: 16, MaxIOPs: 0, MaxConnections: 5000, MaxStoreGB: 0},
			"mdb.shard.4x.2xlarge.d":   {Vendor: "", Product: "", ClassCode: "mdb.shard.4x.2xlarge.d", Cpu: 8, MemoryGB: 32, MaxIOPs: 0, MaxConnections: 8000, MaxStoreGB: 0},
			"mdb.shard.8x.2xlarge.d":   {Vendor: "", Product: "", ClassCode: "mdb.shard.8x.2xlarge.d", Cpu: 8, MemoryGB: 64, MaxIOPs: 0, MaxConnections: 16000, MaxStoreGB: 0},
			"mdb.shard.2x.4xlarge.d":   {Vendor: "", Product: "", ClassCode: "mdb.shard.2x.4xlarge.d", Cpu: 16, MemoryGB: 32, MaxIOPs: 0, MaxConnections: 8000, MaxStoreGB: 0},
			"mdb.shard.4x.4xlarge.d":   {Vendor: "", Product: "", ClassCode: "mdb.shard.4x.4xlarge.d", Cpu: 16, MemoryGB: 64, MaxIOPs: 0, MaxConnections: 16000, MaxStoreGB: 0},
			"mdb.shard.8x.4xlarge.d":   {Vendor: "", Product: "", ClassCode: "mdb.shard.8x.4xlarge.d", Cpu: 16, MemoryGB: 128, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"mdb.shard.2x.8xlarge.d":   {Vendor: "", Product: "", ClassCode: "mdb.shard.2x.8xlarge.d", Cpu: 32, MemoryGB: 64, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"mdb.shard.4x.8xlarge.d":   {Vendor: "", Product: "", ClassCode: "mdb.shard.4x.8xlarge.d", Cpu: 32, MemoryGB: 128, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"mdb.shard.8x.8xlarge.d":   {Vendor: "", Product: "", ClassCode: "mdb.shard.8x.8xlarge.d", Cpu: 32, MemoryGB: 256, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"mdb.shard.2x.16xlarge.d":  {Vendor: "", Product: "", ClassCode: "mdb.shard.2x.16xlarge.d", Cpu: 64, MemoryGB: 128, MaxIOPs: 0, MaxConnections: 64000, MaxStoreGB: 0},
			"mdb.shard.4x.16xlarge.d":  {Vendor: "", Product: "", ClassCode: "mdb.shard.4x.16xlarge.d", Cpu: 64, MemoryGB: 256, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"mdb.shard.8x.16xlarge.d":  {Vendor: "", Product: "", ClassCode: "mdb.shard.8x.16xlarge.d", Cpu: 64, MemoryGB: 512, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"mdb.shard.2x.xlarge.d":    {Vendor: "", Product: "", ClassCode: "mdb.shard.2x.xlarge.d", Cpu: 4, MemoryGB: 8, MaxIOPs: 0, MaxConnections: 3000, MaxStoreGB: 0},
			"dds.mongos.mid":           {Vendor: "", Product: "", ClassCode: "dds.mongos.mid", Cpu: 1, MemoryGB: 2, MaxIOPs: 0, MaxConnections: 1000, MaxStoreGB: 0},
			"dds.mongos.standard":      {Vendor: "", Product: "", ClassCode: "dds.mongos.standard", Cpu: 2, MemoryGB: 4, MaxIOPs: 0, MaxConnections: 2000, MaxStoreGB: 0},
			"dds.mongos.large":         {Vendor: "", Product: "", ClassCode: "dds.mongos.large", Cpu: 4, MemoryGB: 8, MaxIOPs: 0, MaxConnections: 4000, MaxStoreGB: 0},
			"dds.mongos.xlarge":        {Vendor: "", Product: "", ClassCode: "dds.mongos.xlarge", Cpu: 8, MemoryGB: 16, MaxIOPs: 0, MaxConnections: 8000, MaxStoreGB: 0},
			"dds.mongos.2xlarge":       {Vendor: "", Product: "", ClassCode: "dds.mongos.2xlarge", Cpu: 8, MemoryGB: 32, MaxIOPs: 0, MaxConnections: 16000, MaxStoreGB: 0},
			"dds.mongos.4xlarge":       {Vendor: "", Product: "", ClassCode: "dds.mongos.4xlarge", Cpu: 16, MemoryGB: 64, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"dds.shard.mid":            {Vendor: "", Product: "", ClassCode: "dds.shard.mid", Cpu: 1, MemoryGB: 2, MaxIOPs: 0, MaxConnections: 8000, MaxStoreGB: 0},
			"dds.shard.standard":       {Vendor: "", Product: "", ClassCode: "dds.shard.standard", Cpu: 2, MemoryGB: 4, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"dds.shard.large":          {Vendor: "", Product: "", ClassCode: "dds.shard.large", Cpu: 4, MemoryGB: 8, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"dds.shard.xlarge":         {Vendor: "", Product: "", ClassCode: "dds.shard.xlarge", Cpu: 8, MemoryGB: 16, MaxIOPs: 0, MaxConnections: 0, MaxStoreGB: 0},
			"dds.shard.2xlarge":        {Vendor: "", Product: "", ClassCode: "dds.shard.2xlarge", Cpu: 8, MemoryGB: 32, MaxIOPs: 0, MaxConnections: 16000, MaxStoreGB: 0},
			"dds.shard.4xlarge":        {Vendor: "", Product: "", ClassCode: "dds.shard.4xlarge", Cpu: 16, MemoryGB: 64, MaxIOPs: 0, MaxConnections: 32000, MaxStoreGB: 0},
			"dds.shard.sn8.xlarge.3":   {Vendor: "", Product: "", ClassCode: "dds.shard.sn8.xlarge.3", Cpu: 2, MemoryGB: 16, MaxIOPs: 0, MaxConnections: 8000, MaxStoreGB: 0},
			"dds.shard.sn8.2xlarge.3":  {Vendor: "", Product: "", ClassCode: "dds.shard.sn8.2xlarge.3", Cpu: 4, MemoryGB: 32, MaxIOPs: 0, MaxConnections: 9000, MaxStoreGB: 0},
			"dds.shard.sn8.4xlarge.3":  {Vendor: "", Product: "", ClassCode: "dds.shard.sn8.4xlarge.3", Cpu: 8, MemoryGB: 64, MaxIOPs: 0, MaxConnections: 18000, MaxStoreGB: 0},
			"dds.shard.sn8.8xlarge.3":  {Vendor: "", Product: "", ClassCode: "dds.shard.sn8.8xlarge.3", Cpu: 16, MemoryGB: 128, MaxIOPs: 0, MaxConnections: 16000, MaxStoreGB: 0},
			"dds.shard.sn8.16xlarge.3": {Vendor: "", Product: "", ClassCode: "dds.shard.sn8.16xlarge.3", Cpu: 32, MemoryGB: 256, MaxIOPs: 0, MaxConnections: 32000, MaxStoreGB: 0},
			"dds.cs.mid":               {Vendor: "", Product: "", ClassCode: "dds.cs.mid", Cpu: 1, MemoryGB: 2, MaxIOPs: 0, MaxConnections: 8000, MaxStoreGB: 0},
		},
		hbc.PolarDB: {},
	},
}
