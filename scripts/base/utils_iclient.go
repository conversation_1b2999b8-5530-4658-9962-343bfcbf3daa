package base

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func GetDefaultIClients(ctx context.Context, vendors []hbc.CloudVendor, product hbc.CloudProduct, purposes []string, customs ...actfilter.CustomConfigOption) ([]client.IClient, error) {
	b := tools.NewBatch[hbc.CloudVendor, []client.IClient](ctx)
	customConfig := actfilter.NewCustomConfig(customs...)
	filteredVendors := make([]hbc.CloudVendor, 0)
	for _, v := range vendors {
		if _, ok := customConfig.IgnoreVendor[v]; !ok {
			filteredVendors = append(filteredVendors, v)
		}
	}

	b.Run(filteredVendors, func(ctx context.Context, vendor hbc.CloudVendor) ([]client.IClient, error) {
		return getClients(ctx, vendor, customConfig, product, purposes)
	})
	if err := b.Error(); err != nil {
		return nil, utils.NewError("get sdk client failed: %s", err)
	}
	clients := make([]client.IClient, 0)
	for _, cs := range b.Outs() {
		if len(cs) > 0 {
			clients = append(clients, cs...)
		}
	}
	return clients, nil
}

func getClients(ctx context.Context, vendor hbc.CloudVendor, customConfig *actfilter.CustomConfig, product hbc.CloudProduct, purposes []string) (client []client.IClient, err error) {
	defer func() {
		if r := recover(); r != nil {
			if pic, ok := r.(error); ok {
				err = pic
			} else {
				err = utils.NewError("get sdk client failed: %v", r)
			}
		}
	}()

	if newProduct, ok := customConfig.CustomProduct[vendor]; ok {
		product = newProduct
	}
	if newPurposes, ok := customConfig.CustomPurposes[vendor]; ok {
		purposes = newPurposes
	}
	if product == "" {
		panic("product is empty")
	}
	if len(purposes) == 0 {
		panic("purpose is empty")
	}
	return hybrid.GetClients(ctx, vendor, product, purposes, customConfig.CustomRegion[vendor])
}
