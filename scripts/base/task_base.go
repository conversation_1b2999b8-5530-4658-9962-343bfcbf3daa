package base

import (
	"context"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
)

func NewJobBase(name, comment, schedule string, category taskmodels.TaskCategory, ds *models.Datasource) *JobBase {
	return &JobBase{
		ds:       ds,
		schedule: schedule,
		name:     name,
		comment:  comment,
		logger:   log.NewWithOption(name, log.WithShowCaller(true)),
		timeout:  time.Hour,
		category: category,
	}
}

type JobBase struct {
	name     string
	comment  string
	schedule string
	ds       *models.Datasource
	logger   *log.Logger
	timeout  time.Duration
	category taskmodels.TaskCategory
}

func (b *JobBase) Run(ctx context.Context) (map[string]any, error) {
	//TODO implement me
	panic("implement me")
}

func (b *JobBase) Name() string {
	return b.name
}

func (b *JobBase) Comment() string {
	return b.comment
}

func (b *JobBase) SetComment(str string) {
	b.comment = str
}

func (b *JobBase) Category() taskmodels.TaskCategory {
	return b.category
}

func (b *JobBase) SetCategory(category taskmodels.TaskCategory) {
	b.category = category
}

func (b *JobBase) Timeout() time.Duration {
	if b.timeout == 0 {
		return time.Hour
	}
	return b.timeout
}

func (b *JobBase) SetTimeout(timeout time.Duration) {
	b.timeout = timeout
}

func (b *JobBase) Schedule() string {
	return b.schedule
}

func (b *JobBase) SetSchedule(schedule string) {
	b.schedule = schedule
}

func (b *JobBase) Enable() bool {
	return false
}

func (b *JobBase) Model(ctx context.Context) models.Model {
	return b.ds.Model(ctx)
}

func (b *JobBase) Orm(ctx context.Context) *gorm.DB {
	m := b.ds.Model(ctx)
	return m.Orm()
}

func (b *JobBase) Logger() *log.Logger {
	return b.logger
}

func (b *JobBase) Infof(ctx context.Context, fmt string, args ...interface{}) {
	b.logger.Infof(ctx, fmt, args...)
}

func (b *JobBase) Errorf(ctx context.Context, fmt string, args ...interface{}) {
	b.logger.Errorf(ctx, fmt, args...)
}

func (b *JobBase) Warnf(ctx context.Context, fmt string, args ...interface{}) {
	b.logger.Warnf(ctx, fmt, args...)
}

func (b *JobBase) Debugf(ctx context.Context, fmt string, args ...interface{}) {
	b.logger.Debugf(ctx, fmt, args...)
}
