package jobAutoRegisterExporter

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/ops-golang-common/common/types"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/feishu"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"go.uber.org/atomic"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func New() *JobRegisterPrometheus {
	c := config.Global()
	return &JobRegisterPrometheus{
		feiShuClient: c.FeiShuConfig.NewFeiShuClient(),
		JobBase: base.NewJobBase(
			"PrometheusRegister",
			"注册NodeExporter到Prometheus服务",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryOps,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type JobRegisterPrometheus struct {
	feiShuClient *feishu.Client
	*base.JobBase
}

func (j *JobRegisterPrometheus) Name() string {
	return base.TaskRegisterPrometheus
}

func (j *JobRegisterPrometheus) Run(ctx context.Context) (map[string]any, error) {
	wikiNode, err := j.feiShuClient.GetWikiObject(ctx, "ANEowfwj3ib9kAk2UIec7e78nTg")
	if err != nil {
		j.Errorf(ctx, "get wiki node error: %s", err.Error())
		return nil, err
	}
	sheet, err := j.feiShuClient.GetSheetSheet(ctx, wikiNode.ObjToken, 0)
	if err != nil {
		j.Errorf(ctx, "get sheet sheet error: %s", err.Error())
		return nil, err
	}
	rows, err := j.feiShuClient.GetSheetValueAll(ctx, wikiNode.ObjToken, sheet.SheetID)
	if err != nil {
		j.Errorf(ctx, "get sheet value error: %s", err.Error())
		return nil, err
	}

	typeInfos := make([]*RegisterInfo, 0)
	for _, cols := range rows {
		if pointer.Value(cols[0].String) == "" {
			continue
		}

		ip := strings.TrimSpace(pointer.Value(cols[0].String))
		port, _ := strconv.Atoi(strings.Split(ip, ":")[1])
		ip = strings.Split(ip, ":")[0]
		typeInfos = append(typeInfos, &RegisterInfo{
			IP:      ip,
			Port:    port,
			Version: strings.TrimSpace(pointer.Value(cols[1].String)),
		})
	}

	b := tools.NewBatch[*RegisterInfo, *types.RegisterPrometheusDetail](ctx)
	b.Run(typeInfos, func(ctx context.Context, input *RegisterInfo) (*types.RegisterPrometheusDetail, error) {
		// 未注册
		hostInfos, err := api.HcmsClient().QueryHaierHostInfoByIp(input.IP)
		if err != nil {
			j.Errorf(ctx, "query host info by ip: %s error: %v", input.IP, err)
			return nil, err
		}
		if len(hostInfos) == 0 {
			j.Warnf(ctx, "query host info by ip: %s not found", input.IP)
			return nil, nil
		}
		hostInfo := hostInfos[0]
		endpoint := "QD"
		if strings.Contains(hostInfo.Region, "beijing") {
			endpoint = "BJ"
		}
		ws, _ := api.HdsClient().QueryProjectWorkspace(hostInfo.Scode)
		if len(ws) > 0 {
			for _, w := range ws {
				if w.AlmCode == hostInfo.Project {
					hostInfo.Project = w.ID
				}
			}
		}

		return &types.RegisterPrometheusDetail{
			AgentVersion:  input.Version,
			Endpoint:      endpoint,
			Env:           hostInfo.Env,
			NodeType:      hostInfo.HostType,
			OsType:        strings.Split(hostInfo.OsType, "/")[0],
			Port:          input.Port,
			Region:        hostInfo.Region,
			ScrapeAddress: input.IP,
			Project:       hostInfo.Project,
			Source:        hostInfo.Vendor.String(),
			SvcType:       "node",
			TargetAddress: input.IP,
			Name:          hostInfo.InstanceName,
		}, nil
	})

	jxjgHosts, err := api.HcmsClient().QueryHaierHostInfoByVendorAll(hbc.JXJG)
	if err != nil {
		j.Errorf(ctx, "query jxjg host info error: %v", err)
		return nil, err
	}

	bb := tools.NewBatch[*models.HostInfo, *types.RegisterPrometheusDetail](ctx)
	bb.Run(jxjgHosts, func(ctx context.Context, hostInfo *models.HostInfo) (*types.RegisterPrometheusDetail, error) {
		port := 19100
		if strings.Contains(strings.ToLower(hostInfo.OsType), "windows") {
			port = 19182
		}
		if hostInfo.Scode == "" {
			return nil, nil
		}
		ws, _ := api.HdsClient().QueryProjectWorkspace(hostInfo.Scode)
		if len(ws) > 0 {
			for _, w := range ws {
				if w.AlmCode == hostInfo.Project {
					hostInfo.Project = w.ID
				}
			}
		}

		return &types.RegisterPrometheusDetail{
			AgentVersion:  "unknown",
			Endpoint:      "QD",
			Env:           hostInfo.Env,
			NodeType:      hostInfo.HostType,
			OsType:        strings.Split(hostInfo.OsType, "/")[0],
			Port:          port,
			Project:       hostInfo.Project,
			Region:        hostInfo.Region,
			ScrapeAddress: hostInfo.PrivateIp,
			Source:        hostInfo.Vendor.String(),
			SvcType:       "node",
			TargetAddress: hostInfo.PrivateIp,
			ResourceId:    hostInfo.ResourceId,
			Name:          hostInfo.InstanceName,
		}, nil
	})
	if b.Error() != nil {
		j.Errorf(ctx, "batch run error: %v", b.Error())
		return nil, err
	}

	results, invalid := validateResults(tools.MergeData(b.Outs(), bb.Outs())...)
	j.Infof(ctx, "%d need to register,invalid results: %d", len(results), len(invalid))

	total := atomic.NewInt64(0)
	bbb := tools.NewBatch[*types.RegisterPrometheusDetail, struct{}](ctx)
	bbb.Run(results, func(ctx context.Context, input *types.RegisterPrometheusDetail) (struct{}, error) {
		defer j.Infof(ctx, "register prometheus target: [%s]%s:%d", input.OsType, input.ScrapeAddress, input.Port)
		_, err := bizutils.WithRetry(3, func() (any, error) {
			err := api.HdsClient().PrometheusRegister(ctx, input)
			if err != nil {
				time.Sleep(time.Second)
			}
			return nil, err
		})
		total.Add(1)
		return struct{}{}, err
	})

	_ = bbb.Outs()
	j.Infof(ctx, "register prometheus target total: %d", total.Load())
	return nil, bbb.Error()
}

func validateResults(infos ...*types.RegisterPrometheusDetail) ([]*types.RegisterPrometheusDetail, []*types.RegisterPrometheusDetail) {
	result := make([]*types.RegisterPrometheusDetail, 0)
	invalid := make([]*types.RegisterPrometheusDetail, 0)
	for _, info := range infos {
		if info == nil {
			continue
		}
		info.Env = strings.ToLower(info.Env)
		switch info.Env {
		case "dev", "test", "pre", "prod":
		case "uat", "stage":
			info.Env = "pre"
		default:
			info.Env = "prod"
		}
		switch info.OsType {
		case "Linux":
			info.OsType = strings.ToUpper(info.OsType)
		case "Windows":
			info.OsType = strings.ToUpper(info.OsType)
		default:
			info.OsType = "LINUX"
		}
		switch info.NodeType {
		case "ecs":
			info.NodeType = "virtual"
		case "Physical", "physical":
			info.NodeType = "physical"
		case "Virtual", "VirtualMachine", "virtual":
			info.NodeType = "virtual"
		default:
			log.Warnf(nil, "unknown node type: %s", info.NodeType)
			invalid = append(invalid, info)
			continue
		}
		switch info.Region {
		case "":
			log.Warnf(nil, "%s region is empty", info.ScrapeAddress)
			invalid = append(invalid, info)
			continue
		}

		switch info.Project {
		case "":
			log.Warnf(nil, "%s project is empty", info.ScrapeAddress)
			invalid = append(invalid, info)
			continue
		}

		switch info.Region {
		case "N/A":
			info.Region = "cn-qingdao"
		}
		info.SvcType = "node"

		switch info.Source {
		case "private":
			info.Source = "privatecloud"
		case "tencent":
			info.Source = "tencentcloud"
		case "huawei":
			info.Source = "huaweicloud"
		case "jxjg":
			info.Source = "jxjg"
		}

		result = append(result, info)
	}

	return result, invalid
}
