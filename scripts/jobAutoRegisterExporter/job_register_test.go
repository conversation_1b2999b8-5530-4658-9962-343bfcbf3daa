package jobAutoRegisterExporter

import (
	context2 "context"
	"fmt"
	"strconv"
	"strings"
	"testing"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/common/types"
	"git.haier.net/devops/ops-golang-common/haierapi"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/context"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var hdsConf = config.Global().GetApp("hds")
var testHdsClient = haierapi.NewHdsClient("TASK_CENTER", hdsConf.GetToken(), true)
var feishuClient = config.Global().FeiShuConfig.NewFeiShuClient()

// 单测用于删除节点
func TestJobRegisterPrometheus_Delete(t *testing.T) {
	deleteHostList := []string{
		"*************",
		"*************",
	}

	for _, ip := range deleteHostList {
		hosts, err := api.HcmsClient().QueryHaierHostInfoByIp(ip)
		if err != nil {
			t.Error(err)
			continue
		}

		for _, h := range hosts {
			if h.PrivateIp != ip {
				continue
			}

			if err := testHdsClient.PrometheusDeregister(context.NewContext(), "node", h.ResourceId); err != nil {
				t.Log(err)
			} else {
				t.Log("delete success: ", ip)
			}
			break
		}
	}
}

func TestJRegisterPrometheus(t *testing.T) {
	ctx := context.NewContext()
	//wikiNode, err := feishuClient.GetWikiObject(ctx, "ANEowfwj3ib9kAk2UIec7e78nTg")
	//if err != nil {
	//	t.Errorf("get wiki node error: %s", err.Error())
	//	t.Fail()
	//}
	//sheet, err := feishuClient.GetSheetSheet(ctx, wikiNode.ObjToken, 0)
	//if err != nil {
	//	t.Errorf("get sheet sheet error: %s", err.Error())
	//	t.Fail()
	//}
	//rows, err := feishuClient.GetSheetValueAll(ctx, wikiNode.ObjToken, sheet.SheetID)
	//if err != nil {
	//	t.Errorf("get sheet value error: %s", err.Error())
	//	t.Fail()
	//}
	//
	//typeInfos := make([]*RegisterInfo, 0)
	//for _, cols := range rows {
	//	if pointer.Value(cols[0].String) == "" {
	//		continue
	//	}
	//
	//	ip := strings.TrimSpace(pointer.Value(cols[0].String))
	//	port, _ := strconv.Atoi(strings.Split(ip, ":")[1])
	//	ip = strings.Split(ip, ":")[0]
	//	typeInfos = append(typeInfos, &RegisterInfo{
	//		IP:      ip,
	//		Port:    port,
	//		Version: strings.TrimSpace(pointer.Value(cols[1].String)),
	//	})
	//}

	jxjgHost, err := api.HcmsClient().QueryHaierHostInfoByVendorAll(hbc.JXJG)
	if err != nil {
		t.Error(err)
		t.Fail()
	}

	hostMap := make(map[string]*models.HostInfo)
	hosts := make([]string, 0)
	for _, h := range jxjgHost {
		port := "19100"
		if strings.Contains(strings.ToLower(h.OsType), "windows") {
			port = "19182"
		}
		hosts = append(hosts, fmt.Sprintf("%s:%s", h.PrivateIp, port))
		hostMap[h.PrivateIp] = h
	}

	typeInfos := make([]*RegisterInfo, 0)
	for _, host := range hosts {
		tmp := strings.Split(host, ":")
		ip := tmp[0]
		port, _ := strconv.Atoi(tmp[1])
		typeInfos = append(typeInfos, &RegisterInfo{
			IP:      ip,
			Port:    port,
			Version: "1.5.0",
		})
	}

	b := tools.NewBatch[*RegisterInfo, *types.RegisterPrometheusDetail](ctx)
	b.Run(typeInfos, func(ctx context2.Context, input *RegisterInfo) (*types.RegisterPrometheusDetail, error) {
		// 未注册
		//hostInfos, err := api.HcmsClient().QueryHaierHostInfoByIp(input.IP)
		//if err != nil {
		//	t.Errorf("query host info by ip: %s error: %v", input.IP, err)
		//	return nil, err
		//}
		//if len(hostInfos) == 0 {
		//	t.Errorf("query host info by ip: %s not found", input.IP)
		//	return nil, nil
		//}
		hostInfo := hostMap[input.IP]
		endpoint := "QD"
		if strings.Contains(hostInfo.Region, "beijing") {
			endpoint = "BJ"
		}
		ws, _ := api.HdsClient().QueryProjectWorkspace(hostInfo.Scode)
		if len(ws) > 0 {
			for _, w := range ws {
				if w.AlmCode == hostInfo.Project {
					hostInfo.Project = w.ID
				}
			}
		}

		osType := strings.Split(hostInfo.OsType, "/")[0]
		request := &types.RegisterPrometheusDetail{
			AgentVersion:  input.Version,
			Endpoint:      endpoint,
			Env:           hostInfo.Env,
			NodeType:      hostInfo.HostType,
			OsType:        osType,
			Port:          input.Port,
			Region:        hostInfo.UniRegionId,
			ScrapeAddress: input.IP,
			Project:       hostInfo.Project,
			Source:        hostInfo.Vendor.String(),
			SvcType:       "node",
			TargetAddress: input.IP,
			Name:          hostInfo.InstanceName,
			ResourceId:    hostInfo.ResourceId,
		}

		formatRegister(request)
		fmt.Println("==> register", request.ScrapeAddress, request.Port)
		if err := testHdsClient.PrometheusRegister(ctx, request); err != nil {
			fmt.Println("==> register error: ", utils.JsonString(request))
			fmt.Println(err)
		}

		return nil, nil
	})
	_ = b.Outs()

	//ds := config.Global().GetDefaultStore()
	//m := ds.Model(context.NewContext())
	//var hostList []string
	//m.Orm().Table("rc_host_info").Where("is_deleted = ? and vendor = ?", 0, hbc.AliCloud).Select("private_ip").Find(&hostList)
	//fmt.Println("==> register", len(hostList), "hosts")
	//ctx := context.NewContext()
	//for _, ip := range hostList {
	//	info, err := api.HcmsClient().QueryHaierHostInfoByIp(ip)
	//	if err != nil {
	//		fmt.Printf("%s QueryHaierHostInfoByIp error: %s \n", ip, err)
	//		continue
	//	}
	//	if info == nil || len(info) == 0 {
	//		fmt.Println("not found:", ip)
	//		continue
	//	}
	//	hostInfo := info[0]
	//	endpoint := "QD"
	//	if strings.Contains(hostInfo.Region, "beijing") {
	//		endpoint = "BJ"
	//	}
	//	ws, _ := testHdsClient.QueryProjectWorkspace(hostInfo.Scode)
	//	if len(ws) > 0 {
	//		for _, w := range ws {
	//			if w.AlmCode == hostInfo.Project {
	//				hostInfo.Project = w.ID
	//			}
	//		}
	//	}
	//
	//	port := 9100
	//	if hostInfo.Vendor == hbc.JXJG {
	//		port = 19100
	//	}
	//	osType := strings.Split(hostInfo.OsType, "/")[0]
	//	request := &types.RegisterPrometheusDetail{
	//		AgentVersion:  "1.5.0",
	//		Endpoint:      endpoint,
	//		Env:           hostInfo.Env,
	//		NodeType:      hostInfo.HostType,
	//		OsType:        osType,
	//		Port:          port,
	//		Region:        hostInfo.UniRegionId,
	//		ScrapeAddress: ip,
	//		Project:       hostInfo.Project,
	//		Source:        hostInfo.Vendor.String(),
	//		SvcType:       "node",
	//		TargetAddress: ip,
	//		Name:          hostInfo.InstanceName,
	//		ResourceId:    hostInfo.ResourceId,
	//	}
	//
	//	formatRegister(request)
	//	if err := testHdsClient.PrometheusRegister(ctx, request); err != nil {
	//		fmt.Println(err)
	//	}
	//}
}

func formatRegister(request *types.RegisterPrometheusDetail) {
	request.Env = strings.ToLower(request.Env)
	switch request.Env {
	case "dev", "test", "pre", "prod":
	case "uat", "stage":
		request.Env = "pre"
	default:
		request.Env = "prod"
	}

	//
	switch request.OsType {
	case common.OsTypeLinux:
		request.OsType = "LINUX"
	case "Linux":
		request.OsType = strings.ToUpper(request.OsType)
	case "Windows":
		request.OsType = strings.ToUpper(request.OsType)
	default:
		request.OsType = "LINUX"
	}

	switch request.NodeType {
	case "ecs", "ECS":
		request.NodeType = "virtual"
	case "Physical":
		request.NodeType = "physical"
	case "Virtual", "VirtualMachine":
		request.NodeType = "virtual"
	}

	switch request.Source {
	case "private":
		request.Source = "privatecloud"
	case "tencent":
		request.Source = "tencentcloud"
	case "huawei":
		request.Source = "huaweicloud"
	}

	if request.Project == "" {
		request.Project = "unknown"
	}
}
