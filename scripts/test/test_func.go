package test

import (
	"context"
	"time"

	myCtx "git.haier.net/devops/ops-golang-common/utils/context"

	"git.haier.net/devops/hcms-task-center/core/taskcenter"
)

func runTask(t taskcenter.ITask, timeout ...time.Duration) (map[string]any, error) {
	var ctx context.Context
	var cancel context.CancelFunc
	ctx = myCtx.NewContext()
	if len(timeout) > 0 {
		ctx, cancel = context.WithTimeout(ctx, timeout[0])
	}

	ctx = context.WithValue(ctx, "host", "rm-m5ewo68vc45y4c5we.mysql.rds.aliyuncs.com")
	//ctx = context.WithValue(ctx, "account", "hr690n")
	ctx = context.WithValue(ctx, "force", "true")
	if cancel != nil {
		defer cancel()
	}
	return t.Run(ctx)
}
