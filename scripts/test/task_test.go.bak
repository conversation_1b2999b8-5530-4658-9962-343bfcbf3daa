package test

import (
	context2 "context"
	"fmt"
	"os"
	"strings"
	"testing"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/stretchr/testify/assert"
	"golang.org/x/time/rate"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobAutoRegisterExporter"
	"git.haier.net/devops/hcms-task-center/scripts/jobAutoScanExporter"
	"git.haier.net/devops/hcms-task-center/scripts/jobCleanupData"
	"git.haier.net/devops/hcms-task-center/scripts/jobCleanupTask"
	"git.haier.net/devops/hcms-task-center/scripts/jobForDBMS/DBMSRdsIpCheck"
	"git.haier.net/devops/hcms-task-center/scripts/jobForDBMS/DBMSSyncBaseMeta"
	"git.haier.net/devops/hcms-task-center/scripts/jobForDBMS/DBMSSyncDBMeta"
	"git.haier.net/devops/hcms-task-center/scripts/jobPreAllocation"
	"git.haier.net/devops/hcms-task-center/scripts/jobScanAccount"
	"git.haier.net/devops/hcms-task-center/scripts/jobScanMonitoring"
	"git.haier.net/devops/hcms-task-center/scripts/jobScanRdsIpWhiteList"
	"git.haier.net/devops/hcms-task-center/scripts/jobSendDataToCMDB"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/pullBill"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/refineBill"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/retrieveBill"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBucket"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBucketDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseAliyun"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQL"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQL/jobSyncGeminiDB"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQL/jobSyncMemcache"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQL/jobSyncStarRocks"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseNoSQLDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabasePrivate"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds/jobSyncGaussDB"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRdsDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncHbr"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncHostEcs"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncHostEcsDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncHostPrivate"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncMiddleware"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncMiddlewareDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncNetworkDevice"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncResource"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncSubnet"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncSubnetDedicated"
	"git.haier.net/devops/hcms-task-center/scripts/jobValidate"
)

var c = config.Global()
var ds = c.GetDefaultStore()

func TestSync2CMDB(t *testing.T) {
	a := assert.New(t)
	data, err := runTask(jobSendDataToCMDB.New(*ds))
	a.NoError(err)
	fmt.Println(utils.JsonString(data))
}

func TestCleanup(t *testing.T) {
	a := assert.New(t)
	data, err := runTask(jobCleanupData.New())
	a.NoError(err)
	fmt.Println(utils.JsonString(data))
}

func TestAutoRegister(t *testing.T) {
	a := assert.New(t)
	data, err := runTask(jobAutoScanExporter.New())
	a.NoError(err)
	fmt.Println(utils.JsonString(data))
}

func TestAutoExporter(t *testing.T) {
	a := assert.New(t)
	data, err := runTask(jobAutoRegisterExporter.New())
	a.NoError(err)
	fmt.Println(utils.JsonString(data))
}

func TestSyncRDS(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseRds.New())
	a.NoError(err)
}

func TestSyncDedicatedRDS(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseRdsDedicated.New())
	a.NoError(err)
	_, err = runTask(jobSyncDatabaseNoSQLDedicated.NewSyncKvStore())
	a.NoError(err)
}

func TestSyncNoSqlMemcache(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncMemcache.NewSyncMemcache())
	a.NoError(err)
}

func TestSyncNoSqlRDS(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseNoSQL.NewSyncKvStore())
	a.NoError(err)
}

func TestSyncMongoDB(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseNoSQL.NewSyncMongoDB())
	a.NoError(err)
}

func TestSyncPolarDB(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseAliyun.NewSyncPolarDB())
	a.NoError(err)
}

func TestSyncPolarDBX(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseAliyun.NewSyncPolarDBX())
	a.NoError(err)
}

func TestSyncES(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseNoSQL.NewSyncElasticSearch())
	a.NoError(err)
}

func TestSyncAdb(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseAliyun.NewSyncAdb())
	a.NoError(err)
}

func TestSyncAdbPgSQL(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseAliyun.NewSyncAdbPgSQL())
	a.NoError(err)
}

func TestSyncClickHouse(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseAliyun.NewSyncClickhouse())
	a.NoError(err)
}

func TestSyncHbase(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseAliyun.NewSyncHbase())
	a.NoError(err)
}

func TestSyncHitsDB(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseAliyun.NewSyncHitsdb())
	a.NoError(err)
}

func TestSyncOceanBase(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabaseAliyun.NewSyncOceanBase())
	a.NoError(err)
}

func TestSyncGaussDB(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncGaussDB.New())
	a.NoError(err)
}

func TestSyncGeminiDB(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncGeminiDB.New())
	a.NoError(err)
}

func TestSyncPrivateDB(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncDatabasePrivate.New())
	a.NoError(err)
}

func TestSyncBucket(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncBucket.New())
	a.NoError(err)
}

func TestSyncBucketDedicate(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncBucketDedicated.New())
	a.NoError(err)
}

func TestSyncJXJG_V2(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncHostPrivate.NewSyncJXJG())
	a.NoError(err)
}

func TestSyncMiddleware(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncMiddleware.NewSyncMiddleware())
	a.NoError(err)
}

func TestSyncMiddlewareDedicated(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncMiddlewareDedicated.NewSyncMiddlewareDedicated())
	a.NoError(err)
}

func TestCleanTask(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobCleanupTask.New())
	a.NoError(err)
}

func TestJobValidate(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobValidate.NewJobValidate())
	a.NoError(err)
}

func TestHostDiffData(t *testing.T) {
	ctx := context.NewContext()
	console := config.Global().GetStore("console")
	consoleModel := console.Model(ctx)
	consoleOrm := consoleModel.Orm()
	hcmsModel := bizutils.DataSource().Model(ctx)
	hcmsOrm := hcmsModel.Orm()

	hosts := make([]jobSendDataToCMDB.ConsoleHost, 0)
	if err := consoleOrm.Table("console_host_230615").Find(&hosts).Error; err != nil {
		panic(err)
	}
	b := tools.NewBatch[jobSendDataToCMDB.ConsoleHost, struct{}](ctx, batch.WithBatchSize(50))
	f, _ := os.OpenFile("test.txt", os.O_CREATE|os.O_WRONLY, 0666)
	b.Run(hosts, func(ctx context2.Context, host jobSendDataToCMDB.ConsoleHost) (struct{}, error) {
		if host.Source != "jxjg" && host.Source != "private_cloud" {
			return struct{}{}, nil
		}

		c := int64(0)
		err := hcmsOrm.Table("rc_host_info").Where("private_ip = ?", tools.InetNtoA(host.IP)).Count(&c).Error
		if err != nil {
			panic(err)
		}
		if c == 0 {
			if host.ProjectID != "" {
				project, _ := api.HdsClient().QueryAlmProject(host.ProjectID)
				if project == nil {
					return struct{}{}, nil
				}
				user, _ := api.HcmsClient().QueryHaierUserInfo(ctx, project.Owner)
				if len(user) == 0 {
					return struct{}{}, nil
				}
				if !strings.Contains(user[0].Dept, "智家定制生态圈") {
					return struct{}{}, nil
				}
			}
			fmt.Println(tools.InetNtoA(host.IP), host.Source, host.ProjectID)
			_, _ = f.Write([]byte(fmt.Sprintf("%s %s %s\n", tools.InetNtoA(host.IP), host.Source, host.ProjectID)))
		}
		return struct{}{}, nil
	})
	b.Outs()
}

func TestQueryProject(t *testing.T) {
	p, e := api.HdsClient().QueryAlmProject("S03178")
	fmt.Println(p, e)
	ws, e := api.HdsClient().QueryProjectWorkspace("S03178")
	fmt.Println(utils.JsonString(ws), e)

}

func TestSyncAliyunBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewAliyunSyncBill())
	a.NoError(err)
}

func TestSyncHuaweicloudBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewHuaweicloudSyncBill())
	a.NoError(err)
}

func TestSyncHuaweicloudDedicateBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewHuaweicloudDedicatedSyncBill())
	a.NoError(err)
}

func TestSyncTencentCloudBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewTencentCloudSyncBill())
	a.NoError(err)
}

func TestAliyunRefineBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(refineBill.NewRefineBillAliyun())
	a.NoError(err)
}

func TestHuaweiRefineBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(refineBill.NewRefineBillHuawei())
	a.NoError(err)
}

func TestTencentRefineBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(refineBill.NewRefineBillTencent())
	a.NoError(err)
}

func TestSyncGoogleCloudBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewGoogleCloudSyncBill())
	a.NoError(err)
}

func TestOracleDownloadBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewOracleDownloadBill())
	a.NoError(err)
}

func TestSyncOracleCloudBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewOracleCloudSyncBill())
	a.NoError(err)
}

func TestSyncAzureBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewAzureSyncBill())
	a.NoError(err)
}

func TestAwsDownloadBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewAwsDownloadBill())
	a.NoError(err)
}

func TestAwsRefineBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(refineBill.NewRefineBillAws())
	a.NoError(err)
}

func TestSyncAwsBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewAwsSyncBill())
	a.NoError(err)
}

// func TestCompareAwsCloudBill(t *testing.T) {
// 	a := assert.New(t)
// 	_, err := runTask(pullBill.NewAwsCompareBill())
// 	a.NoError(err)
// }

func TestPricingAwsCloudBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(pullBill.NewAwsPricingBill())
	a.NoError(err)
}

func TestAwsCloudRetrieveBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(retrieveBill.NewAwsRetrieveBill())
	a.NoError(err)
}

func TestTencentRetrieveBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(retrieveBill.NewTencentRetrieveBill())
	a.NoError(err)
}

func TestGoogleCloudRetrieveBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(retrieveBill.NewGoogleCloudRetrieveBill())
	a.NoError(err)
}

func TestScanAccount(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobScanAccount.New())
	a.NoError(err)
}

func TestDBMSMetaSync(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(DBMSSyncDBMeta.New())
	a.NoError(err)
}

func TestDBMSMetaSync2(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(DBMSSyncBaseMeta.New())
	a.NoError(err)
}

func TestJobScanRdsIpWhitList(tt *testing.T) {
	a := assert.New(tt)
	_, err := runTask(jobScanRdsIpWhiteList.New())
	a.NoError(err)
}

func TestDbmsScanRdsIpWhitList(tt *testing.T) {
	a := assert.New(tt)
	_, err := runTask(DBMSRdsIpCheck.New())
	a.NoError(err)
}

func TestPreAllocation(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobPreAllocation.New())
	a.NoError(err)
}

func TestSyncECS(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncHostEcs.New())
	a.NoError(err)
}

func TestSyncEcsDedicated(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncHostEcsDedicated.New())
	a.NoError(err)
}

func TestSyncSubnetDedicated(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncSubnetDedicated.New())
	a.NoError(err)
}

func TestSyncHostFactory(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncHostPrivate.NewSyncFactory())
	a.NoError(err)
}

func TestGetSyncMonthDate(t *testing.T) {
	dates := billHandler.GetSyncMonthDate(context.NewContext(), 15)
	for _, date := range dates {
		fmt.Println(date)
	}
}

func TestSyncSubnet(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncSubnet.New())
	a.NoError(err)
}

func TestSyncHbr(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncHbr.NewSyncHbr())
	a.NoError(err)

}

func TestSyncResource(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncResource.New())
	a.NoError(err)
}

func TestNewSyncWikiCloud(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncHostPrivate.NewSyncWikiCloud())
	a.NoError(err)
}

func TestZabbixMonitoring(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobScanMonitoring.NewScanZabbixMonitoring())
	a.NoError(err)
}

func TestAzureRefineBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(refineBill.NewRefineBillAzure())
	a.NoError(err)
}

func TestOracleRefineBill(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(refineBill.NewRefineBillOracle())
	a.NoError(err)
}
func TestRateLimiter(t *testing.T) {
	a := assert.New(t)
	ctx := context.NewContext()
	limiter := rate.NewLimiter(rate.Limit(2.0/3.0), 2) // 120 requests per minute = 2 requests per second
	for i := 0; i < 1200; i++ {
		if err := limiter.Wait(ctx); err != nil {
			log.Errorf(ctx, "error waiting for rate limit: %v\n", err)
		}
		fmt.Printf("--> %d\n", i)
	}
	a.NoError(nil)
}

func TestSyncNetworkDevice(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncNetworkDevice.NewSyncNetworkDevice())
	a.NoError(err)
}

func TestSyncStarRocks(t *testing.T) {
	a := assert.New(t)
	_, err := runTask(jobSyncStarRocks.New())
	a.NoError(err)
}
