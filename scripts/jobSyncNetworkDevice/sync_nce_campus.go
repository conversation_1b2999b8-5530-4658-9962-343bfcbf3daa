package jobSyncNetworkDevice

import (
	"context"
	"crypto/tls"
	"fmt"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/aws/smithy-go/ptr"
	"github.com/imroc/req/v3"
)

// SyncFromNCECampus 从NCE-Campus同步网络设备信息
func SyncFromNCECampus(ctx context.Context, job *SyncNetworkDevice) ([]*models.NetworkDevice, error) {
	logger := job.Logger()

	// 1. 获取NCE-Campus配置信息
	cfg := config.Global().NCECampusConfig
	if cfg == nil {
		return nil, fmt.Errorf("未找到NCE-Campus配置")
	}

	// 2. 初始化NCE-Campus客户端
	nceClient := &NCECampus{
		userName:   cfg.Username,
		password:   cfg.Password,
		ctx:        ctx,
		logger:     logger,
		httpClient: req.C(),
	}

	// 配置HTTP客户端
	nceClient.httpClient.SetTimeout(time.Duration(cfg.Timeout) * time.Second)
	nceClient.httpClient.SetCommonRetryCount(0)
	nceClient.httpClient.EnableKeepAlives()
	nceClient.httpClient.SetIdleConnTimeout(60 * time.Second)
	nceClient.httpClient.EnableForceHTTP1()

	if cfg.Insecure {
		nceClient.httpClient.SetTLSClientConfig(&tls.Config{
			InsecureSkipVerify: true,
		})
	}

	// 3. 获取网络设备列表
	devices, err := nceClient.getNetworkDeviceList()
	if err != nil {
		return nil, fmt.Errorf("获取网络设备列表失败: %w", err)
	}

	if len(devices) == 0 {
		return devices, nil
	}

	// 4. 保存设备信息到数据库
	model := job.Model(ctx)
	if err := CreateOrUpdateNetworkDevices(ctx, model, devices, logger); err != nil {
		return nil, fmt.Errorf("保存网络设备信息失败: %w", err)
	}

	// 5. 获取所有设备列表
	db := model.Orm()
	var deviceList []models.NetworkDevice
	if err := db.Table("rc_network_device").Where("source = ?", "NCE-Campus").Find(&deviceList).Error; err != nil {
		return nil, fmt.Errorf("获取网络设备列表失败: %w", err)
	}

	// 6. 根据设备获取端口

	// 并发处理配置
	concurrency := 10
	if cfg.Concurrency > 0 {
		concurrency = cfg.Concurrency
	}

	// 创建工作线程池
	deviceChan := make(chan models.NetworkDevice, len(deviceList))
	resultChan := make(chan struct {
		deviceName string
		success    bool
		count      int
	}, len(deviceList))

	// 启动工作线程
	for i := 0; i < concurrency; i++ {
		go func() {
			for device := range deviceChan {
				ports, err := nceClient.getDeviceInterface(device.DeviceCode, device.ID)
				success := false
				count := 0

				if err == nil {
					if err := CreateOrUpdateNetworkDevicePorts(ctx, model.Orm(), ports, logger); err == nil {
						success = true
						count = len(ports)
					}
				}

				resultChan <- struct {
					deviceName string
					success    bool
					count      int
				}{
					deviceName: device.Hostname,
					success:    success,
					count:      count,
				}
			}
		}()
	}

	// 提交设备到处理队列
	validDeviceCount := 0
	for _, device := range deviceList {
		deviceChan <- device
		validDeviceCount++
	}
	close(deviceChan)

	// 收集处理结果
	successCount := 0
	failedDevices := make([]string, 0)
	for i := 0; i < validDeviceCount; i++ {
		result := <-resultChan
		if result.success {
			successCount++
		} else {
			failedDevices = append(failedDevices, result.deviceName)
		}
	}

	if len(failedDevices) > 0 {
		logger.Warnf(ctx, "有%d个设备处理失败: %v", len(failedDevices), failedDevices)
	}

	logger.Infof(ctx, "完成设备接口信息同步，成功处理%d/%d个设备", successCount, len(deviceList))
	return devices, nil
}

func (n *NCECampus) getToken() (string, error) {
	now := time.Now()

	// 使用读锁检查缓存的token是否有效
	n.tokenMutex.RLock()
	if n.token != "" && now.Before(n.tokenExpiry) {
		token := n.token
		n.tokenMutex.RUnlock()
		//fmt.Println("使用缓存的token")
		return token, nil
	}
	n.tokenMutex.RUnlock()

	// 使用写锁获取新token
	n.tokenMutex.Lock()
	defer n.tokenMutex.Unlock()

	// 双重检查，避免其他goroutine已经更新了token
	if n.token != "" && now.Before(n.tokenExpiry) {
		//fmt.Println("使用缓存的token")
		return n.token, nil
	}

	fmt.Println("NCE 获取新token")

	url := fmt.Sprintf("%s/controller/v2/tokens", config.Global().NCECampusConfig.GetBaseURL())
	body := map[string]string{
		"userName": n.userName,
		"password": n.password,
	}

	ctx, cancel := context.WithTimeout(n.ctx, 30*time.Second)
	defer cancel()

	resp, err := n.httpClient.R().
		SetContext(ctx).
		SetBody(body).
		Post(url)

	if err != nil {
		time.Sleep(300 * time.Millisecond)
		resp, err = n.httpClient.R().
			SetContext(ctx).
			SetBody(body).
			Post(url)

		if err != nil {
			return "", fmt.Errorf("获取token失败: %w", err)
		}
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("API返回非200状态码: %d", resp.StatusCode)
	}

	var tokenResp TokenResponse
	if err := resp.Unmarshal(&tokenResp); err != nil {
		return "", fmt.Errorf("解析token响应失败: %w", err)
	}

	if tokenResp.ErrCode != "0" {
		return "", fmt.Errorf("获取token失败: %s (错误码: %s)", tokenResp.ErrMsg, tokenResp.ErrCode)
	}

	token := tokenResp.Data.TokenID
	if token == "" {
		return "", fmt.Errorf("响应中没有找到有效的token")
	}

	expiryTime := now.Add(20 * time.Minute)

	n.token = token
	n.tokenTime = now
	n.tokenExpiry = expiryTime

	return token, nil
}

func (n *NCECampus) getNetworkDeviceList() ([]*models.NetworkDevice, error) {
	baseURL := fmt.Sprintf("%s/controller/campus/v3/devices", config.Global().NCECampusConfig.GetBaseURL())
	token, err := n.getToken()
	if err != nil {
		return nil, fmt.Errorf("获取授权token失败: %w", err)
	}

	headers := map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", token),
		"Content-Type":  "application/json",
	}

	ctx, cancel := context.WithTimeout(n.ctx, 30*time.Second)
	defer cancel()

	var allDevices []NCEDevice
	pageSize := 100
	currentPage := 1
	totalPages := 1

	for currentPage <= totalPages {
		queryParams := map[string]string{
			"pageIndex": fmt.Sprintf("%d", currentPage),
			"pageSize":  fmt.Sprintf("%d", pageSize),
		}

		try := 0
		maxRetries := 3
		var resp *req.Response
		var requestErr error

		for try < maxRetries {
			try++
			resp, requestErr = n.httpClient.R().
				SetContext(ctx).
				SetHeaders(headers).
				SetQueryParams(queryParams).
				Get(baseURL)

			if requestErr == nil {
				break
			}

			if try < maxRetries {
				time.Sleep(time.Duration(try) * time.Second)
			}
		}

		if requestErr != nil {
			return nil, fmt.Errorf("请求设备列表失败: %w", requestErr)
		}

		if resp.StatusCode != 200 {
			return nil, fmt.Errorf("API返回非200状态码: %d", resp.StatusCode)
		}

		var deviceResp DeviceResponse
		if err := resp.Unmarshal(&deviceResp); err != nil {
			return nil, fmt.Errorf("解析设备列表响应失败: %w", err)
		}

		if deviceResp.ErrCode != "0" && deviceResp.ErrCode != "" {
			return nil, fmt.Errorf("API错误: %s (错误码: %s)", deviceResp.ErrMsg, deviceResp.ErrCode)
		}

		allDevices = append(allDevices, deviceResp.Data...)

		if currentPage == 1 {
			totalPages = (deviceResp.TotalRecords + pageSize - 1) / pageSize
		}

		currentPage++
	}

	return n.convertDevices(allDevices)
}

func (n *NCECampus) convertDevices(nceDevices []NCEDevice) ([]*models.NetworkDevice, error) {
	var result []*models.NetworkDevice
	for _, nceDevice := range nceDevices {
		device, err := n.convertToNetworkDevice(nceDevice)
		if err != nil {
			continue
		}
		result = append(result, device)
	}
	return result, nil
}

func (n *NCECampus) convertToNetworkDevice(device NCEDevice) (*models.NetworkDevice, error) {
	statusMap := map[string]string{
		"0": "ACTIVE",
		"1": "ACTIVE",
		"2": "INACTIVE",
		"3": "INACTIVE",
	}

	status := statusMap[device.Status]
	if status == "" {
		status = "unknown"
	}

	return &models.NetworkDevice{
		DeviceCode:   device.ID,
		SerialNumber: device.ESN,
		Source:       "NCE-Campus",
		ResourceId:   GetResourceId(device.ESN),
		Hostname:     device.Name,
		DeviceType:   GetDeviceType(device.DeviceType),
		IsPhysical:   ptr.Bool(true),
		ManagementIP: device.ManageIP,
		Brand:        device.Vendor,
		ModelField:   device.NEType,
		Status:       status,
		RawApiData:   utils.JsonString(device),
		IsDeleted:    ptr.Bool(false),
		IsAPIPull:    ptr.Bool(true),
	}, nil
}

// getDeviceInterface 根据设备获取端口
func (n *NCECampus) getDeviceInterface(deviceId string, tableId int64) ([]models.NetworkDevicePort, error) {
	url := fmt.Sprintf("%s/controller/campus/v1/oamservice/device/queryDeviceInterface/%s",
		config.Global().NCECampusConfig.GetBaseURL(), deviceId)

	token, err := n.getToken()
	if err != nil {
		return nil, fmt.Errorf("获取授权token失败: %w", err)
	}

	headers := map[string]string{
		"x-access-token": token,
		"Content-Type":   "application/json",
	}

	ctx, cancel := context.WithTimeout(n.ctx, 15*time.Second)
	defer cancel()

	resp, err := n.httpClient.R().
		SetContext(ctx).
		SetHeaders(headers).
		Get(url)

	if err != nil {
		return nil, fmt.Errorf("请求设备接口失败: %w", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API返回非200状态码: %d", resp.StatusCode)
	}

	var deviceInterface DeviceInterface
	if err := resp.Unmarshal(&deviceInterface); err != nil {
		return nil, fmt.Errorf("解析设备接口响应失败: %w", err)
	}

	if deviceInterface.ErrCode != "0" && deviceInterface.ErrCode != "" {
		return nil, fmt.Errorf("获取设备接口API错误: %s (错误码: %s)", deviceInterface.ErrMsg, deviceInterface.ErrCode)
	}

	currentTime := time.Now()
	networkPorts := make([]models.NetworkDevicePort, 0, len(deviceInterface.Data))
	for _, iface := range deviceInterface.Data {
		if iface.InterfaceName == "" {
			continue
		}

		IPAddress := ""
		if iface.IPv4 != "" && iface.IPv4 != "0.0.0.0" {
			IPAddress = iface.IPv4
		}

		port := models.NetworkDevicePort{
			Model: models.Model{
				CreateTime: currentTime,
				UpdateTime: currentTime,
			},
			DeviceID:        tableId,
			PortName:        iface.InterfaceName,
			IPAddress:       IPAddress,
			NegotiatedSpeed: getPortSpeed(iface.InterfaceName),
			Description:     iface.Description,
			IsDeleted:       ptr.Bool(false),
		}

		networkPorts = append(networkPorts, port)
	}

	return networkPorts, nil
}

// getPortSpeed 根据端口名称匹配协商速率 单位M
// GigabitEthernet    =  1G
// MEth               =  1G
// GE                 =  1G
// XGigabitEthernet   =  10G
// 10GE               =  10G
// eth                =  10G
// 100GE              =  100G
func getPortSpeed(portName string) int {
	portName = strings.ToLower(portName)
	if strings.Contains(portName, "gigabitEthernet") || strings.Contains(portName, "meth") || strings.Contains(portName, "ge") {
		return 1000
	} else if strings.Contains(portName, "xgigabitethernet") || strings.Contains(portName, "10ge") || strings.Contains(portName, "eth") {
		return 10000
	} else if strings.Contains(portName, "100ge") {
		return 100000
	}
	return 0
}
