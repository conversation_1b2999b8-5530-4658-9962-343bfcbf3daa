package jobSyncNetworkDevice

import (
	"context"
	"sync"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/ops-golang-common/models/models"
)

// 数据源类型常量
const (
	DataSourceZabbix    = "zabbix"
	DataSourceNEOSight  = "neo-sight"
	DataSourceNCECampus = "nce-campus"
)

// NewSyncNetworkDevice 创建网络设备同步任务实例
func NewSyncNetworkDevice() *SyncNetworkDevice {
	c := config.Global()
	return &SyncNetworkDevice{
		mtx: new(sync.Mutex),
		JobBase: base.NewJobBase(
			"NETWORK_DEVICE_SYNC",
			"同步网络设备信息",
			base.NewSchedule(
				base.WithHour(3),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

// SyncNetworkDevice 网络设备同步任务
type SyncNetworkDevice struct {
	mtx *sync.Mutex
	*base.JobBase
}

// Name 返回任务名称
func (s *SyncNetworkDevice) Name() string {
	return base.TaskSyncNetworkDevice
}

// Run 执行同步任务
func (s *SyncNetworkDevice) Run(ctx context.Context) (map[string]any, error) {
	logger := s.Logger()
	logger.Infof(ctx, "开始同步所有网络设备信息")

	// 并发同步所有数据源
	var wg sync.WaitGroup
	var mu sync.Mutex
	results := make(map[string]interface{})
	errors := make(map[string]error)

	// 定义要同步的所有数据源
	dataSources := []string{
		DataSourceZabbix,
		DataSourceNEOSight,
		DataSourceNCECampus,
	}

	// 为每个数据源启动一个goroutine
	for _, source := range dataSources {
		wg.Add(1)
		go func(dataSource string) {
			defer wg.Done()

			logger.Infof(ctx, "开始同步 %s 数据源的网络设备信息", dataSource)

			var devices []*models.NetworkDevice
			var err error

			switch dataSource {
			case DataSourceNCECampus:
				devices, err = SyncFromNCECampus(ctx, s)
			case DataSourceNEOSight:
				devices, err = SyncFromNEOSight(ctx, s)
			case DataSourceZabbix:
				devices, err = SyncFromZabbix(ctx, s)
			}

			// 记录同步结果
			mu.Lock()
			defer mu.Unlock()

			if err != nil {
				logger.Errorf(ctx, "从 %s 同步网络设备信息失败: %v", dataSource, err)
				errors[dataSource] = err
			} else {
				count := len(devices)
				results[dataSource] = count
				logger.Infof(ctx, "成功从 %s 同步 %d 个网络设备", dataSource, count)
			}
		}(source)
	}

	// 等待所有同步任务完成
	wg.Wait()
	logger.Infof(ctx, "所有数据源同步任务已完成")

	// 计算总设备数
	totalCount := 0
	for _, count := range results {
		if countVal, ok := count.(int); ok {
			totalCount += countVal
		}
	}

	// 构造结果
	result := map[string]any{
		"total": totalCount,
		"detail": map[string]any{
			"results": results,
			"errors":  errors,
		},
	}

	// 检查是否存在错误
	if len(errors) > 0 {
		logger.Warnf(ctx, "部分数据源同步出现错误，成功同步了%d个设备", totalCount)
	} else {
		logger.Infof(ctx, "所有数据源同步成功，共同步%d个设备", totalCount)
	}

	return result, nil
}
