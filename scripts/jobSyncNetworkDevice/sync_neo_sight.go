package jobSyncNetworkDevice

import (
	"context"
	"crypto/tls"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/aws/smithy-go/ptr"
	"github.com/imroc/req/v3"
	"gorm.io/gorm"
)

// SyncFromNEOSight 从Neo-Sight同步网络设备信息
func SyncFromNEOSight(ctx context.Context, job *SyncNetworkDevice) ([]*models.NetworkDevice, error) {
	logger := job.Logger()

	// 1. 获取Neo-Sight配置信息
	cfg := config.Global().NEOSightConfig
	if cfg == nil {
		return nil, fmt.Errorf("未找到Neo-Sight配置")
	}

	// 2. 初始化Neo-Sight客户端
	neoClient := &NEOSight{
		userName:   cfg.Username,
		password:   cfg.Password,
		ctx:        ctx,
		logger:     logger,
		httpClient: req.C(),
	}

	// 配置HTTP客户端
	neoClient.httpClient.SetTimeout(time.Duration(cfg.Timeout) * time.Second)
	neoClient.httpClient.SetCommonRetryCount(0)
	neoClient.httpClient.EnableKeepAlives()
	neoClient.httpClient.SetIdleConnTimeout(60 * time.Second)
	neoClient.httpClient.EnableForceHTTP1()

	if cfg.Insecure {
		neoClient.httpClient.SetTLSClientConfig(&tls.Config{
			InsecureSkipVerify: true,
		})
	}

	// 3. 获取网络设备列表
	devices, err := neoClient.getNetworkDeviceList()
	if err != nil {
		return nil, fmt.Errorf("获取网络设备列表失败: %w", err)
	}

	if len(devices) == 0 {
		return devices, nil
	}

	// 4. 保存设备信息到数据库
	model := job.Model(ctx)
	if err := CreateOrUpdateNetworkDevices(ctx, model, devices, logger); err != nil {
		return nil, fmt.Errorf("保存网络设备信息失败: %w", err)
	}

	// 5. 获取所有设备列表
	db := model.Orm()
	var deviceCount int64
	if err := db.Table("rc_network_device").Where("source = ?", "NeoSight").Count(&deviceCount).Error; err != nil {
		return nil, fmt.Errorf("获取网络设备数量失败: %w", err)
	}

	// 6. 并发处理配置
	concurrency := 100
	if cfg.Concurrency > 0 {
		concurrency = cfg.Concurrency
	}

	// 创建上下文和同步原语
	processingCtx, cancelProcessing := context.WithCancel(ctx)
	defer cancelProcessing()

	var wg sync.WaitGroup
	var resultWg sync.WaitGroup

	// 创建通道
	deviceChan := make(chan models.NetworkDevice, 100)
	resultChan := make(chan struct {
		deviceName string
		success    bool
		count      int
		err        error
	}, 200)

	// 启动goroutines
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for {
				select {
				case device, ok := <-deviceChan:
					if !ok {
						return
					}

					ports, err := neoClient.getDeviceInterface(ctx, db, device.DeviceCode, device.ID, device.ManagementIP)
					success := false
					count := 0
					var portErr error

					if err == nil {
						if portErr = CreateOrUpdateNetworkDevicePorts(ctx, db, ports, logger); portErr == nil {
							success = true
							count = len(ports)
						}
					} else {
						portErr = err
					}

					select {
					case resultChan <- struct {
						deviceName string
						success    bool
						count      int
						err        error
					}{
						deviceName: device.Hostname,
						success:    success,
						count:      count,
						err:        portErr,
					}:
					case <-processingCtx.Done():
						return
					}
				case <-processingCtx.Done():
					return
				}
			}
		}()
	}

	// 启动结果处理线程
	resultWg.Add(1)
	go func() {
		defer resultWg.Done()
		totalProcessed := 0
		for result := range resultChan {
			if result.success {
				totalProcessed++
			}
		}
	}()

	// 处理所有批次
	batchSize := 50
	totalProcessedCount := 0

	for offset := 0; offset < int(deviceCount) && processingCtx.Err() == nil; offset += batchSize {
		var deviceBatch []models.NetworkDevice
		if err := db.Table("rc_network_device").
			Where("source = ?", "NeoSight").
			Offset(offset).
			Limit(batchSize).
			Find(&deviceBatch).Error; err != nil {
			continue
		}

		// 当前批次成功处理的设备数
		currentBatchProcessedCount := 0
		for _, device := range deviceBatch {
			if processingCtx.Err() != nil {
				break
			}

			select {
			case deviceChan <- device:
				currentBatchProcessedCount++
				totalProcessedCount++
			case <-processingCtx.Done():
				goto Done
			}
		}

		if processingCtx.Err() == nil {
			time.Sleep(500 * time.Millisecond)
		}
	}

Done:
	close(deviceChan)
	wg.Wait()
	close(resultChan)

	resultTimeoutChan := time.After(1 * time.Minute)
	resultDone := make(chan struct{})

	go func() {
		resultWg.Wait()
		close(resultDone)
	}()

	select {
	case <-resultDone:
	case <-resultTimeoutChan:
		cancelProcessing()
	}

	return devices, nil
}

func (n *NEOSight) getNetworkDeviceList() ([]*models.NetworkDevice, error) {
	baseURL := fmt.Sprintf("%s/rest/openapi/network/nedevice", config.Global().NEOSightConfig.GetBaseURL())
	token, err := n.getToken()
	if err != nil {
		return nil, fmt.Errorf("获取授权token失败: %w", err)
	}

	headers := map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", token),
		"Content-Type":  "application/json",
	}

	ctx, cancel := context.WithTimeout(n.ctx, 30*time.Second)
	defer cancel()

	var allDevices []NEODevice
	size := 100
	start := 0
	totalSize := 0

	queryParams := map[string]string{
		"start": fmt.Sprintf("%d", start),
		"size":  fmt.Sprintf("%d", size),
	}

	resp, requestErr := n.httpClient.R().
		SetContext(ctx).
		SetHeaders(headers).
		SetQueryParams(queryParams).
		Get(baseURL)

	if requestErr != nil {
		return nil, fmt.Errorf("请求设备列表失败: %w", requestErr)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API返回非200状态码: %d", resp.StatusCode)
	}

	var deviceResp DeviceNEOResponse
	if err := resp.Unmarshal(&deviceResp); err != nil {
		return nil, fmt.Errorf("解析设备列表响应失败: %w", err)
	}

	if deviceResp.Code != 0 {
		return nil, fmt.Errorf("API错误: %s (错误码: %d)", deviceResp.Description, deviceResp.Code)
	}

	allDevices = append(allDevices, deviceResp.Data...)
	totalSize = deviceResp.TotalSize

	for start+size < totalSize {
		start += size
		queryParams["start"] = fmt.Sprintf("%d", start)

		resp, requestErr := n.httpClient.R().
			SetContext(ctx).
			SetHeaders(headers).
			SetQueryParams(queryParams).
			Get(baseURL)

		if requestErr != nil || resp.StatusCode != 200 {
			break
		}

		var pageResp DeviceNEOResponse
		if err := resp.Unmarshal(&pageResp); err != nil || pageResp.Code != 0 {
			break
		}

		allDevices = append(allDevices, pageResp.Data...)
	}

	return n.convertDevices(allDevices)
}

func (n *NEOSight) convertDevices(neoDevices []NEODevice) ([]*models.NetworkDevice, error) {
	var result []*models.NetworkDevice
	for _, neoDevice := range neoDevices {
		device, err := n.convertToNetworkDevice(neoDevice)
		if err != nil {
			continue
		}
		result = append(result, device)
	}
	return result, nil
}

func (n *NEOSight) convertToNetworkDevice(device NEODevice) (*models.NetworkDevice, error) {
	statusMap := map[string]string{
		"1": "ACTIVE",
		"2": "INACTIVE",
	}

	status := statusMap[device.Nestate]
	if status == "" {
		status = "unknown"
	}

	sn := device.NeeSN
	if sn == "" {
		serialNumber, err := n.GetDeviceSerialNumber(device.NeDN)
		if err != nil {
			return nil, err
		}
		sn = serialNumber
	}

	return &models.NetworkDevice{
		DeviceCode:   device.NeDN,
		SerialNumber: sn,
		Source:       "NeoSight",
		ResourceId:   GetResourceId(sn),
		Hostname:     device.NeName,
		AliasName:    device.AliasName,
		DeviceType:   GetDeviceType(device.NeCategory),
		IsPhysical:   ptr.Bool(true),
		ManagementIP: device.NeIP,
		Brand:        device.NeVendorName,
		ModelField:   device.NeType,
		Status:       status,
		RawApiData:   utils.JsonString(device),
		IsDeleted:    ptr.Bool(false),
		IsAPIPull:    ptr.Bool(true),
	}, nil
}

func (n *NEOSight) getDeviceInterface(ctx context.Context, db *gorm.DB, deviceId string, tableId int64, ipAddress string) ([]models.NetworkDevicePort, error) {
	url := fmt.Sprintf("%s/rest/openapi/networkinventoryservice/v2/interfaces",
		config.Global().NEOSightConfig.GetBaseURL())

	token, err := n.getToken()
	if err != nil {
		return nil, fmt.Errorf("获取授权token失败: %w", err)
	}

	headers := map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", token),
		"Content-Type":  "application/json",
	}

	ctx, cancel := context.WithTimeout(n.ctx, 15*time.Second)
	defer cancel()

	condition := fmt.Sprintf(`{"logOp":"and","complex":[{"simple":[{"name":"nedn","operator":"equal","value":"%s"}]}]}`, deviceId)
	orderBy := `{"field":"INTERFACEID","asc":true}`

	queryParams := map[string]string{
		"condition": condition,
		"orderBy":   orderBy,
	}

	resp, err := n.httpClient.R().
		SetContext(ctx).
		SetHeaders(headers).
		SetQueryParams(queryParams).
		Get(url)

	if err != nil {
		time.Sleep(500 * time.Millisecond)
		resp, err = n.httpClient.R().
			SetContext(ctx).
			SetHeaders(headers).
			SetQueryParams(queryParams).
			Get(url)

		if err != nil {
			return nil, fmt.Errorf("请求设备接口失败: %w", err)
		}
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API返回非200状态码: %d", resp.StatusCode)
	}

	var interfaceResp NEOInterfaceResponse
	if err := resp.Unmarshal(&interfaceResp); err != nil {
		return nil, fmt.Errorf("解析设备接口响应失败: %w", err)
	}

	if interfaceResp.ErrorCode != 0 {
		return nil, fmt.Errorf("获取设备接口API错误: %s (错误码: %d)", interfaceResp.ErrorMsg, interfaceResp.ErrorCode)
	}

	networkPorts := make([]models.NetworkDevicePort, 0, len(interfaceResp.IfDataList))
	currentTime := time.Now()
	for _, iface := range interfaceResp.IfDataList {
		if iface.IfDesc == "" {
			continue
		}

		ifType := strings.ToLower(iface.IfType)
		if ifType != "ethernet" && ifType != "meth" {
			continue
		}

		IPAddress := ""
		if iface.IPAddress != "" && iface.IPAddress != "0.0.0.0" {
			IPAddress = iface.IPAddress
		}

		port := models.NetworkDevicePort{
			Model: models.Model{
				CreateTime: currentTime,
				UpdateTime: currentTime,
			},
			DeviceID:        tableId,
			PortName:        iface.IfDesc,
			Description:     iface.IfAlias,
			IPAddress:       IPAddress,
			NegotiatedSpeed: convertToMB(iface.IfSpeed),
			Duplex:          iface.IfDuplexMode,
			IsDeleted:       ptr.Bool(false),
		}

		networkPorts = append(networkPorts, port)
	}

	//  取不到端口的交换机设备类型直接修改为"工业交换机"，然后给添加24个虚拟端口
	if len(networkPorts) == 0 {
		fmt.Printf("设备%s没有端口，修改设备类型为工业交换机\n", deviceId)
		// 修改设备类型
		if err := db.Table("rc_network_device").Where("id = ?", tableId).Update("device_type", "工业交换机").Error; err != nil {
			return nil, fmt.Errorf("修改设备类型失败: %w", err)
		}

		for i := 1; i <= 24; i++ {
			port := models.NetworkDevicePort{
				Model: models.Model{
					CreateTime: currentTime,
					UpdateTime: currentTime,
				},
				DeviceID:        tableId,
				PortName:        fmt.Sprintf("Ethernet Port %d", i),
				Description:     "",
				IPAddress:       ipAddress,
				NegotiatedSpeed: 0,
				Duplex:          "",
				IsDeleted:       ptr.Bool(false),
			}

			networkPorts = append(networkPorts, port)
		}
	}

	return networkPorts, nil
}

func convertToMB(input string) int {
	input = strings.TrimSpace(input)
	var numberPart, unitPart string

	for i, char := range input {
		if char >= '0' && char <= '9' || char == '.' {
			numberPart += string(char)
		} else {
			unitPart = input[i:]
			break
		}
	}

	if unitPart == "" {
		unitPart = "M"
	}

	number, err := strconv.ParseFloat(numberPart, 64)
	if err != nil {
		return 0
	}

	switch strings.ToUpper(unitPart) {
	case "M":
		return int(number)
	case "G":
		return int(number * 1000)
	default:
		return 0
	}
}

func (n *NEOSight) getToken() (string, error) {
	now := time.Now()

	// 使用读锁检查缓存的token是否有效
	n.tokenMutex.RLock()
	if n.token != "" && now.Before(n.tokenExpiry) {
		token := n.token
		n.tokenMutex.RUnlock()
		//fmt.Printf("使用缓存的token，当前时间：%v，过期时间：%v\n", now, n.tokenExpiry)
		return token, nil
	}
	n.tokenMutex.RUnlock()

	// 使用写锁获取新token
	n.tokenMutex.Lock()
	defer n.tokenMutex.Unlock()

	// 双重检查，避免其他goroutine已经更新了token
	if n.token != "" && now.Before(n.tokenExpiry) {
		//fmt.Printf("使用缓存的token，当前时间：%v，过期时间：%v\n", now, n.tokenExpiry)
		return n.token, nil
	}

	fmt.Printf("eSight 获取新token，当前时间：%v，原token过期时间：%v\n", now, n.tokenExpiry)

	url := fmt.Sprintf("%s/rest/plat/smapp/v1/sessions", config.Global().NEOSightConfig.GetBaseURL())
	body := map[string]string{
		"grantType": "password",
		"userName":  n.userName,
		"value":     n.password,
	}

	ctx, cancel := context.WithTimeout(n.ctx, 30*time.Second)
	defer cancel()

	resp, err := n.httpClient.R().
		SetContext(ctx).
		SetBody(body).
		Put(url)

	if err != nil {
		time.Sleep(300 * time.Millisecond)
		resp, err = n.httpClient.R().
			SetContext(ctx).
			SetBody(body).
			Put(url)

		if err != nil {
			return "", fmt.Errorf("获取token失败: %w", err)
		}
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("API返回非200状态码: %d", resp.StatusCode)
	}

	var tokenResp TokenNEOResponse
	if err := resp.Unmarshal(&tokenResp); err != nil {
		return "", fmt.Errorf("解析token响应失败: %w", err)
	}

	token := tokenResp.AccessSession
	if token == "" {
		return "", fmt.Errorf("响应中没有找到有效的token")
	}

	// 设置过期时间为当前时间加上19分钟（比20分钟少一点，留出安全余量）
	expiryTime := now.Add(19 * time.Minute)
	n.token = token
	n.tokenTime = now
	n.tokenExpiry = expiryTime

	return token, nil
}

func (n *NEOSight) GetDeviceSerialNumber(neDN string) (string, error) {
	url := fmt.Sprintf("%s/rest/openapi/network/frame", config.Global().NEOSightConfig.GetBaseURL())
	token, err := n.getToken()
	if err != nil {
		return "", fmt.Errorf("获取授权token失败: %w", err)
	}

	headers := map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", token),
		"Content-Type":  "application/json",
	}

	queryParams := map[string]string{
		"nedn": neDN,
	}

	ctx, cancel := context.WithTimeout(n.ctx, 10*time.Second)
	defer cancel()

	resp, err := n.httpClient.R().
		SetContext(ctx).
		SetHeaders(headers).
		SetQueryParams(queryParams).
		Get(url)

	if err != nil {
		return "", fmt.Errorf("请求设备序列号失败: %w", err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("API返回非200状态码: %d", resp.StatusCode)
	}

	var frameResp FrameResponse
	if err := resp.Unmarshal(&frameResp); err != nil {
		return "", fmt.Errorf("解析设备序列号响应失败: %w", err)
	}

	if frameResp.Code != 0 {
		return "", fmt.Errorf("获取设备序列号API错误: %s (错误码: %d)", frameResp.Description, frameResp.Code)
	}

	if len(frameResp.Data) == 0 {
		return "", nil
	}

	return frameResp.Data[0].SerialNum, nil
}
