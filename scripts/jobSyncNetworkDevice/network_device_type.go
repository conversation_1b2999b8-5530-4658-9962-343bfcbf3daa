package jobSyncNetworkDevice

import (
	"context"
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
	"github.com/imroc/req/v3"
)

// ===================== NCE Campus相关结构体 =====================

// NCECampus NCE-Campus客户端
type NCECampus struct {
	userName    string
	password    string
	ctx         context.Context
	logger      *log.Logger
	httpClient  *req.Client
	token       string
	tokenTime   time.Time
	tokenExpiry time.Time
	tokenMutex  sync.RWMutex
}

// TokenResponse NCE-Campus令牌响应结构
type TokenResponse struct {
	Data struct {
		TokenID     string `json:"token_id"`
		ExpiredDate string `json:"expiredDate"`
	} `json:"data"`
	ErrCode string `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

// DeviceResponse NCE-Campus设备响应结构体
type DeviceResponse struct {
	ErrCode      string      `json:"errcode"`
	ErrMsg       string      `json:"errmsg"`
	PageIndex    int         `json:"pageIndex"`
	PageSize     int         `json:"pageSize"`
	TotalRecords int         `json:"totalRecords"`
	Data         []NCEDevice `json:"data"`
}

// NCEDevice NCE-Campus单个设备信息结构体
type NCEDevice struct {
	ID                     string   `json:"id"`
	Name                   string   `json:"name"`
	ESN                    string   `json:"esn"`
	DeviceModel            string   `json:"deviceModel"`
	DeviceType             string   `json:"deviceType"`
	Status                 string   `json:"status"`
	SiteID                 string   `json:"siteId"`
	MAC                    string   `json:"mac"`
	IP                     string   `json:"ip"`
	ManageIP               string   `json:"manageIp"`
	NEType                 string   `json:"neType"`
	Version                string   `json:"version"`
	Vendor                 string   `json:"vendor"`
	Description            string   `json:"description"`
	ResourceID             string   `json:"resourceId"`
	TenantID               string   `json:"tenantId"`
	TenantName             string   `json:"tenantName"`
	SiteName               string   `json:"siteName"`
	CreateTime             string   `json:"createTime"`
	RegisterTime           string   `json:"registerTime"`
	ModifyTime             string   `json:"modifyTime"`
	StartupTime            string   `json:"startupTime"`
	Tags                   []string `json:"tags"`
	SystemIP               string   `json:"systemIp"`
	PatchVersion           string   `json:"patchVersion"`
	ZTPConfirm             bool     `json:"ztpConfirm"`
	ManageStatus           string   `json:"manageStatus"`
	ManageStatusDownReason []string `json:"manageStatusDownReason"`
	Role                   []string `json:"role"`
	Performance            string   `json:"performance"`
	Uptime                 string   `json:"uptime"`
}

// DeviceInterface NCE-Campus设备接口响应结构体
type DeviceInterface struct {
	ErrCode      string                  `json:"errcode"`
	ErrMsg       string                  `json:"errmsg"`
	PageIndex    int                     `json:"pageIndex"`
	PageSize     int                     `json:"pageSize"`
	TotalRecords int                     `json:"totalRecords"`
	Data         []DeviceInterfaceDetail `json:"data"`
}

// DeviceInterfaceDetail NCE-Campus单个设备接口详情结构体
type DeviceInterfaceDetail struct {
	InterfaceName      string            `json:"interfaceName"`
	IPv4               string            `json:"ipv4"`
	IPv6               string            `json:"ipv6"`
	Alias              string            `json:"alias"`
	MgrStatus          int               `json:"mgrStatus"`
	MTU                int               `json:"mtu"`
	VpnInstanceName    string            `json:"vpnInstanceName"`
	Description        string            `json:"description"`
	TcpAdjustMss       string            `json:"tcpAdjustMss"`
	PhysAddress        string            `json:"physAddress"`
	IPv4ProtocalStatus int               `json:"ipv4ProtocalStatus"`
	IPv6ProtocalStatus int               `json:"ipv6ProtocalStatus"`
	BandWidth          string            `json:"bandWidth"`
	Mode               int               `json:"mode"`
	OpticalModuleInfo  OpticalModuleInfo `json:"opticalModuleInfo"`
}

// OpticalModuleInfo NCE-Campus设备光模块信息结构体
type OpticalModuleInfo struct {
	PartNumber   string `json:"partNumber"`
	SerialNumber string `json:"serialNumber"`
	Manufacturer string `json:"manufacturer"`
	Status       int    `json:"status"`
}

// ===================== NEOSight相关结构体 =====================

// NEOSight NEOSight客户端
type NEOSight struct {
	userName    string
	password    string
	ctx         context.Context
	logger      *log.Logger
	httpClient  *req.Client
	token       string
	tokenTime   time.Time
	tokenExpiry time.Time
	tokenMutex  sync.RWMutex
	tokenOnce   sync.Once
}

// TokenNEOResponse Neo-Sight令牌响应结构
type TokenNEOResponse struct {
	AccessSession string `json:"accessSession"`
	RoaRand       string `json:"roaRand"`
	Expires       int    `json:"expires"`
}

// DeviceNEOResponse Neo-Sight设备响应结构体
type DeviceNEOResponse struct {
	Code        int         `json:"code"`
	Data        []NEODevice `json:"data"`
	Description string      `json:"description"`
	TotalSize   int         `json:"totalSize"`
	PageSize    int         `json:"pageSize"`
}

// NEODevice Neo-Sight单个设备信息结构体
type NEODevice struct {
	NeDN              string        `json:"nedn"`              // 设备DN
	NeID              string        `json:"neid"`              // 设备ID 没有值
	AliasName         string        `json:"aliasname"`         // 网管名称
	NeName            string        `json:"nename"`            // 设备名称
	NeCategory        string        `json:"necategory"`        // 设备分类
	NeType            string        `json:"netype"`            // 设备类型
	NeVendorName      string        `json:"nevendorname"`      // 设备厂商信息(设备厂商ID)
	NeeSN             string        `json:"neesn"`             // 设备序列号
	NeIP              string        `json:"neip"`              // 设备IP地址
	NeMAC             string        `json:"nemac"`             // 设备MAC地址
	Version           string        `json:"version"`           // 设备版本
	NePatchVersion    string        `json:"nepatchversion"`    // 补丁版本
	NeSysoID          string        `json:"nesysoid"`          // 设备sysoid
	Nestate           string        `json:"nestate"`           // 网络设备状态，1：在线 2：离线
	LastpolTime       string        `json:"lastpolltime"`      // 上次同步时间
	Createtime        string        `json:"createtime"`        // 设备创建时间
	TimeZoneID        string        `json:"timezoneid"`        // 设备时区
	NeIPType          string        `json:"neiptype"`          // ip地址类型，0：IPv4 1：IPv6
	NEIP2NUMBER       string        `json:"NEIP2NUMBER"`       // ip地址转换成后的数值
	Subnet            string        `json:"subnet"`            // 所属子网
	NeGroupName       string        `json:"negroupname"`       // 所属分组名称
	NeGroupList       []NeGroupList `json:"negrouplist"`       // 所属分组模型集合
	NeOSVersion       string        `json:"neosversion"`       // 软件版本
	NeContact         string        `json:"necontact"`         // 设备联系人
	NePosition        string        `json:"neposition"`        // 设备位置
	Memo              string        `json:"memo"`              // 设备备注
	MaintainUnit      string        `json:"maintainunit"`      // 维保单位
	PutIntoActionTime string        `json:"putintoactiontime"` // 投入使用时间
	UseFulLife        string        `json:"usefullife"`        // 维保到期时间
	NeRuntime         string        `json:"neruntime"`         // 设备启动时间
	NeDescribe        string        `json:"nedescribe"`        // 设备描述
	AssetManager      string        `json:"assetManager"`      // 资产管理人
	AssetNumber       string        `json:"assetNumber"`       // 资产编号
	AssetDate         string        `json:"assetDate"`         // 购买日期
}

// NeGroupList Neo-Sight设备分组信息
type NeGroupList struct {
	ID   string `json:"id"`   // 分组ID
	Name string `json:"name"` // 分组名称
}

// DeviceNEOInterface Neo-Sight设备接口响应结构体
type DeviceNEOInterface struct {
	Code        int                        `json:"code"`
	Data        []DeviceNEOInterfaceDetail `json:"data"`
	Description string                     `json:"description"`
	TotalSize   int                        `json:"totalSize"`
	PageSize    int                        `json:"pageSize"`
}

// DeviceNEOInterfaceDetail Neo-Sight单个设备接口详情结构体
type DeviceNEOInterfaceDetail struct {
	InterfaceName string `json:"interfaceName"`
	IPv4          string `json:"ipv4"`
	IPv6          string `json:"ipv6"`
	Alias         string `json:"alias"`
	Status        int    `json:"status"`
	MTU           int    `json:"mtu"`
	Description   string `json:"description"`
	PhysAddress   string `json:"mac"`
	Speed         string `json:"speed"`
	Duplex        string `json:"duplex"`
}

// NEOInterfaceResponse Neo-Sight设备接口响应结构体
type NEOInterfaceResponse struct {
	IfDataList  []NEOInterfaceData `json:"ifDataList"`
	ErrorCode   int                `json:"error_code"`
	ErrorMsg    string             `json:"error_msg"`
	TotalCount  int                `json:"totalCount"`
	CurrentPage int                `json:"currentPage"`
	TotalPage   int                `json:"totalPage"`
}

// NEOInterfaceData Neo-Sight设备接口数据
type NEOInterfaceData struct {
	NeDN          string `json:"nedn"`
	InterfaceId   string `json:"interfaceId"`
	IfIndex       int    `json:"ifIndex"`
	IfDesc        string `json:"ifDesc"`
	IfAlias       string `json:"ifAlias"`
	IPAddress     string `json:"ipAddress"`
	IfOperStatus  string `json:"ifOperStatus"`
	IfAdminStatus string `json:"ifAdminStatus"`
	IfSpeed       string `json:"ifSpeed"`
	IfRemark      string `json:"ifRemark"`
	IfMtu         string `json:"ifMtu"`
	IfDuplexMode  string `json:"ifDuplexMode"`
	IfType        string `json:"ifType"`
	ServiceType   int    `json:"serviceType"`
	IfMAC         string `json:"ifMAC"`
	TrunkName     string `json:"trunkname"`
}

// 定义响应结构体
type FrameData struct {
	SerialNum string `json:"serialNum"`
	NeDN      string `json:"neDN"`
}

type FrameResponse struct {
	Code        int         `json:"code"`
	Data        []FrameData `json:"data"`
	PageSize    int         `json:"pageSize"`
	TotalSize   int         `json:"totalSize"`
	Description string      `json:"description"`
}

// ===================== Zabbix相关结构体 =====================

// ZabbixClient 封装Zabbix API客户端
type ZabbixClient struct {
	Host string
	ID   int
	Auth string
	Http *http.Client
}

// ZabbixCredential Zabbix登录凭证
type ZabbixCredential struct {
	Host     string
	User     string
	Password string
}

// ZabbixAPIRequest 表示Zabbix API请求结构
type ZabbixAPIRequest struct {
	JsonRpc string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params"`
	ID      int         `json:"id"`
	Auth    string      `json:"auth,omitempty"`
}

// ZabbixAPIResponse 表示Zabbix API响应结构
type ZabbixAPIResponse struct {
	JsonRpc string          `json:"jsonrpc"`
	Result  json.RawMessage `json:"result"`
	Error   *ZabbixError    `json:"error,omitempty"`
	ID      int             `json:"id"`
}

// ZabbixError 表示Zabbix API错误结构
type ZabbixError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data"`
}

// ZabbixHostInterface 表示Zabbix主机接口
type ZabbixHostInterface struct {
	InterfaceID int    `json:"interfaceid,string"`
	HostID      int    `json:"hostid,string"`
	Main        int    `json:"main,string"`
	Type        int    `json:"type,string"`
	UseIP       int    `json:"useip,string"`
	IP          string `json:"ip"`
	DNS         string `json:"dns"`
	Port        string `json:"port"`
}

// ZabbixHostObject 表示Zabbix主机对象
type ZabbixHostObject struct {
	HostID      int                   `json:"hostid,string"`
	Host        string                `json:"host"`
	Name        string                `json:"name"`
	Status      int                   `json:"status,string"`
	Available   int                   `json:"available,string"`
	Description string                `json:"description"`
	Interfaces  []ZabbixHostInterface `json:"interfaces"`
}

// ZabbixItem 表示Zabbix监控项
type ZabbixItem struct {
	ItemID      int    `json:"itemid,string"`
	Name        string `json:"name"`
	Key_        string `json:"key_"`
	HostID      int    `json:"hostid,string"`
	LastValue   string `json:"lastvalue"`
	LastClock   int64  `json:"lastclock,string"`
	ValueType   int    `json:"value_type,string"`
	Description string `json:"description"`
	Status      int    `json:"status,string"`
	Type        int    `json:"type,string"`
}
