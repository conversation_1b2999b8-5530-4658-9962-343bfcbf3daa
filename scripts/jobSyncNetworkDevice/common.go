package jobSyncNetworkDevice

import (
	"context"
	"fmt"
	"sync"

	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
	"gorm.io/gorm"
)

// CreateOrUpdateNetworkDevices 创建或更新网络设备信息
func CreateOrUpdateNetworkDevices(ctx context.Context, model models.Model, devices []*models.NetworkDevice, logger *log.Logger) error {
	if len(devices) == 0 {
		return nil
	}

	logger.Infof(ctx, "设备同步开始: 共%d个[%s]设备", len(devices), devices[0].Source)

	db := model.Orm()

	// 收集设备IP并建立映射
	ipToDevice := make(map[string]*models.NetworkDevice, len(devices))
	ipList := make([]string, 0, len(devices))
	for _, device := range devices {
		if device.ManagementIP != "" {
			ipList = append(ipList, device.ManagementIP)
			ipToDevice[device.ManagementIP] = device
		}
	}

	// 查询现有设备
	var existingDevices []models.NetworkDevice
	if err := db.Model(&models.NetworkDevice{}).
		Where("management_ip IN ?", ipList).
		Select("id, management_ip, source, is_physical").
		Find(&existingDevices).Error; err != nil {
		return fmt.Errorf("查询现有设备失败: %w", err)
	}

	// 建立IP到设备信息的映射
	existingIPMap := make(map[string]models.NetworkDevice)
	for _, device := range existingDevices {
		existingIPMap[device.ManagementIP] = device
	}

	var hostnameMap = make(map[string]struct{})
	// 如果是NeoSight类型的，需要根据hostname来判断
	if devices[0].Source == "NeoSight" {
		// 查询hostname
		var existHostnameDevices []models.NetworkDevice
		if err := db.Model(&models.NetworkDevice{}).
			Where("source in ?", []string{"zabbix", "NCE-Campus"}).
			Select("id, hostname").
			Find(&existHostnameDevices).Error; err != nil {
			return fmt.Errorf("查询所有hostname失败: %w", err)
		}
		for _, device := range existHostnameDevices {
			if device.Hostname != "" {
				hostnameMap[device.Hostname] = struct{}{}
			}
		}
	}

	// 分组设备
	var devicesToUpdate, devicesToCreate []*models.NetworkDevice
	for _, device := range devices {

		if device.Source == "NeoSight" && device.Hostname != "" {
			if _, exists := hostnameMap[device.Hostname]; exists {
				continue
			}
		}
		if existingDevice, exists := existingIPMap[device.ManagementIP]; exists {
			// 检查是否需要跳过更新：如果设备类型是NeoSight或zabbix，并且是虚拟设备，则跳过更新
			if (existingDevice.Source == "NeoSight" || existingDevice.Source == "zabbix") &&
				existingDevice.IsPhysical != nil && !*existingDevice.IsPhysical {
				logger.Infof(ctx, "跳过虚拟设备更新: IP=%s, Source=%s, IsPhysical=%v",
					device.ManagementIP, existingDevice.Source, *existingDevice.IsPhysical)
				continue
			}
			device.ID = existingDevice.ID
			devicesToUpdate = append(devicesToUpdate, device)
		} else {
			devicesToCreate = append(devicesToCreate, device)
		}
	}

	// 并发处理配置
	const (
		concurrency = 10
		batchSize   = 50
	)

	// 错误收集
	var (
		wg      sync.WaitGroup
		errChan = make(chan error, concurrency*2)
		sem     = make(chan struct{}, concurrency)
	)

	// 批量创建设备
	if len(devicesToCreate) > 0 {
		for i := 0; i < len(devicesToCreate); i += batchSize {
			wg.Add(1)
			end := getMinInteger(i+batchSize, len(devicesToCreate)) // 计算批次边界
			batch := devicesToCreate[i:end]

			go func(devices []*models.NetworkDevice) {
				defer wg.Done()
				sem <- struct{}{}
				defer func() { <-sem }()

				if err := db.CreateInBatches(devices, len(devices)).Error; err != nil {
					errChan <- fmt.Errorf("批量创建设备失败: %w", err)
				}
			}(batch)
		}
	}

	// 批量更新设备
	if len(devicesToUpdate) > 0 {
		for i := 0; i < len(devicesToUpdate); i += batchSize {
			wg.Add(1)
			end := getMinInteger(i+batchSize, len(devicesToUpdate)) // 计算批次边界
			batch := devicesToUpdate[i:end]

			go func(devices []*models.NetworkDevice) {
				defer wg.Done()
				sem <- struct{}{}
				defer func() { <-sem }()

				if err := db.Transaction(func(tx *gorm.DB) error {
					for _, dev := range devices {
						deviceCopy := *dev
						if err := tx.Model(&deviceCopy).Updates(map[string]interface{}{
							"device_code": deviceCopy.DeviceCode,
							//"serial_number": deviceCopy.SerialNumber,
							//"source":        deviceCopy.Source,
							//"resource_id":   deviceCopy.ResourceId,
							"hostname": deviceCopy.Hostname,
							//"alias_name":    deviceCopy.AliasName,
							//"device_type":   deviceCopy.DeviceType,
							//"is_physical":   deviceCopy.IsPhysical,
							"management_ip": deviceCopy.ManagementIP,
							//"brand":          deviceCopy.Brand,
							//"model":          deviceCopy.ModelField,
							//"administrator":  deviceCopy.Administrator,
							"status": deviceCopy.Status,
							//"dc_id":          deviceCopy.DcID,
							//"property_type":  deviceCopy.PropertyType,
							//"supplier":       deviceCopy.Supplier,
							//"maint_supplier": deviceCopy.MaintSupplier,
							"raw_api_data": deviceCopy.RawApiData,
							"is_deleted":   deviceCopy.IsDeleted,
						}).Error; err != nil {
							return err
						}
					}
					return nil
				}); err != nil {
					errChan <- fmt.Errorf("批量更新设备失败: %w", err)
				}
			}(batch)
		}
	}

	// 等待所有任务完成
	wg.Wait()
	close(errChan)

	// 检查错误
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	logger.Infof(ctx, "[%s]设备同步完成: 创建%d个，更新%d个", devices[0].Source, len(devicesToCreate), len(devicesToUpdate))
	return nil
}

// getMinInteger 返回两个整数中的较小值
func getMinInteger(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// CreateOrUpdateNetworkDevicePorts 创建或更新网络设备端口信息
func CreateOrUpdateNetworkDevicePorts(ctx context.Context, db *gorm.DB, ports []models.NetworkDevicePort, logger *log.Logger) error {
	if len(ports) == 0 {
		return nil
	}

	deviceID := ports[0].DeviceID

	// 查询设备信息
	var device models.NetworkDevice
	if err := db.Model(&models.NetworkDevice{}).
		Where("id = ?", deviceID).
		Select("id, source, is_physical, management_ip").
		First(&device).Error; err != nil {
		return fmt.Errorf("查询设备信息失败: %w", err)
	}

	// 查询现有端口
	var existingPorts []models.NetworkDevicePort
	if err := db.Where("device_id = ?", deviceID).
		Select("id, device_id, port_name").
		Find(&existingPorts).Error; err != nil {
		return fmt.Errorf("查询现有端口失败: %w", err)
	}

	// 构建端口名到ID的映射
	portNameToID := make(map[string]int64)
	currentPortNames := make(map[string]struct{}) // 用于记录当前同步的端口
	for _, port := range existingPorts {
		portNameToID[port.PortName] = port.ID
	}

	// 分离需要创建和更新的端口
	var portsToCreate, portsToUpdate []models.NetworkDevicePort

	for _, port := range ports {
		portCopy := port
		currentPortNames[port.PortName] = struct{}{} // 记录当前处理的端口

		if id, exists := portNameToID[port.PortName]; exists {
			portCopy.ID = id
			portsToUpdate = append(portsToUpdate, portCopy)
		} else {
			portsToCreate = append(portsToCreate, portCopy)
		}
	}

	// 获取当前同步的端口名列表
	portNameList := make([]string, 0, len(currentPortNames))
	for name := range currentPortNames {
		portNameList = append(portNameList, name)
	}

	// 只标记不在当前同步列表中的端口为已删除
	if err := db.Model(&models.NetworkDevicePort{}).
		Where("device_id = ? AND port_name NOT IN ?", deviceID, portNameList).
		Update("is_deleted", true).Error; err != nil {
		return fmt.Errorf("标记端口为已删除失败: %w", err)
	}

	// 批量创建新端口
	if len(portsToCreate) > 0 {
		if err := db.CreateInBatches(portsToCreate, 20).Error; err != nil {
			return fmt.Errorf("批量创建端口失败: %w", err)
		}
	}

	// 批量更新现有端口
	if len(portsToUpdate) > 0 {
		// 检查是否为虚拟设备，如果是则跳过更新操作
		if (device.Source == "NeoSight" || device.Source == "zabbix") &&
			device.IsPhysical != nil && !*device.IsPhysical {
			logger.Infof(ctx, "跳过虚拟设备端口更新: DeviceID=%d, IP=%s, Source=%s, IsPhysical=%v",
				deviceID, device.ManagementIP, device.Source, *device.IsPhysical)
		} else {
			for _, port := range portsToUpdate {
				if err := db.Model(&port).Updates(map[string]interface{}{
					"port_name": port.PortName,
					//"ip_address":       port.IPAddress,
					"negotiated_speed": port.NegotiatedSpeed,
					//"duplex":           port.Duplex,
					//"description":      port.Description,
					"is_deleted": false,
				}).Error; err != nil {
					return fmt.Errorf("更新端口失败: %w", err)
				}
			}
		}
	}

	//logger.Infof(ctx, "设备ID=%d的端口同步完成: 创建%d个，更新%d个", deviceID, len(portsToCreate), len(portsToUpdate))
	return nil
}

// GetResourceId 获取资源ID
func GetResourceId(serialNumber string) string {
	resourceId := ""
	if serialNumber != "" {
		resourceId = stringutil.Md5(serialNumber)
	}
	return resourceId
}

// GetDeviceType 获取设备类型
func GetDeviceType(category string) string {
	switch category {
	case "ne.category.firewall", "FW":
		return "防火墙"
	case "ne.category.switch", "LSW":
		return "交换机"
	case "ne.category.router", "AR":
		return "路由器"
	case "ne.category.fatap", "AP":
		return "AP"
	case "ne.category.unknown":
		return "其他"
	default:
		return "其他"
	}
}
