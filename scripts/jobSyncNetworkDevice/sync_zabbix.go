package jobSyncNetworkDevice

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/aws/smithy-go/ptr"
)

// SyncFromZabbix 从Zabbix同步网络设备信息
func SyncFromZabbix(ctx context.Context, job *SyncNetworkDevice) ([]*models.NetworkDevice, error) {
	logger := job.Logger()

	// 获取Zabbix配置
	zabbixConfig := config.Global().ZabbixConfig
	if len(zabbixConfig.Config) == 0 {
		return nil, nil
	}

	// 准备登录凭证
	credentials := make([]*ZabbixCredential, 0, len(zabbixConfig.Config))
	for _, cred := range zabbixConfig.Config {
		credentials = append(credentials, &ZabbixCredential{
			Host:     cred.Host,
			User:     cred.User,
			Password: cred.Password,
		})
	}

	// 批量登录Zabbix
	b := tools.NewBatch[*ZabbixCredential, *ZabbixClient](ctx)
	b.Run(credentials, func(ctx context.Context, credential *ZabbixCredential) (*ZabbixClient, error) {
		client := &ZabbixClient{
			Host: credential.Host,
			ID:   1,
			Http: &http.Client{Timeout: 30 * time.Second},
		}
		if err := client.Login(credential.User, credential.Password); err != nil {
			return nil, fmt.Errorf("登录失败: %w", err)
		}
		return client, nil
	})

	zabbixClients := tools.MergeData(b.Outs())
	if len(zabbixClients) == 0 {
		return nil, nil
	}

	// 收集所有网络设备
	var allDevices []*models.NetworkDevice
	allPortMap := make(map[string][]*models.NetworkDevicePort)

	// 从每个Zabbix实例获取网络设备
	for _, client := range zabbixClients {
		devices, portMap, err := getNetworkDevicesFromZabbix(ctx, client, job)
		if err != nil {
			logger.Errorf(ctx, "获取网络设备失败: %v", err)
			continue
		}
		allDevices = append(allDevices, devices...)
		for k, v := range portMap {
			allPortMap[k] = v
		}
	}

	// 保存设备信息
	if len(allDevices) > 0 {
		model := job.Model(ctx)
		if err := CreateOrUpdateNetworkDevices(ctx, model, allDevices, logger); err != nil {
			return nil, fmt.Errorf("保存设备信息失败: %w", err)
		}

		// 保存端口信息
		if len(allPortMap) > 0 {
			db := model.Orm()
			var deviceRecords []models.NetworkDevice
			if err := db.Where("source = ?", "zabbix").Find(&deviceRecords).Error; err != nil {
				return allDevices, nil
			}

			for _, device := range deviceRecords {
				ports := make([]models.NetworkDevicePort, 0, len(allPortMap[device.DeviceCode]))
				for _, port := range allPortMap[device.DeviceCode] {
					port.DeviceID = device.ID
					ports = append(ports, *port)
				}
				if len(ports) > 0 {
					if err := CreateOrUpdateNetworkDevicePorts(ctx, db, ports, logger); err != nil {
						logger.Errorf(ctx, "保存设备 %s 端口信息失败: %v", device.DeviceCode, err)
					}
				}
			}
		}
	}

	return allDevices, nil
}

// Login 登录到Zabbix API
func (c *ZabbixClient) Login(user, password string) error {
	params := map[string]string{
		"user":     user,
		"password": password,
	}

	response, err := c.Call("user.login", params)
	if err != nil {
		return err
	}

	var auth string
	if err := json.Unmarshal(response, &auth); err != nil {
		return fmt.Errorf("解析认证响应失败: %w", err)
	}

	c.Auth = auth
	return nil
}

// Call 调用Zabbix API方法
func (c *ZabbixClient) Call(method string, params interface{}) (json.RawMessage, error) {
	request := ZabbixAPIRequest{
		JsonRpc: "2.0",
		Method:  method,
		Params:  params,
		ID:      c.ID,
	}

	if method != "user.login" {
		request.Auth = c.Auth
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	req, err := http.NewRequest("POST", c.Host, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.Http.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var apiResp ZabbixAPIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if apiResp.Error != nil {
		return nil, fmt.Errorf("API错误: %s", apiResp.Error.Data)
	}

	return apiResp.Result, nil
}

// GetHostItems 获取主机的监控项
func (c *ZabbixClient) GetHostItems(hostID string) ([]ZabbixItem, error) {
	params := map[string]interface{}{
		"output":    "extend",
		"hostids":   []string{hostID},
		"sortfield": "name",
	}

	result, err := c.Call("item.get", params)
	if err != nil {
		return nil, fmt.Errorf("获取监控项失败: %w", err)
	}

	var items []ZabbixItem
	if err := json.Unmarshal(result, &items); err != nil {
		return nil, fmt.Errorf("解析监控项失败: %w", err)
	}

	return items, nil
}

// getNetworkDevicesFromZabbix 从单个Zabbix实例获取网络设备
func getNetworkDevicesFromZabbix(ctx context.Context, client *ZabbixClient, job *SyncNetworkDevice) ([]*models.NetworkDevice, map[string][]*models.NetworkDevicePort, error) {
	logger := job.Logger()

	// 获取所有目标主机组ID
	groupIDs, err := getNetworkDeviceGroups(client)
	if err != nil {
		return nil, nil, fmt.Errorf("获取主机组失败: %w", err)
	}

	if len(groupIDs) == 0 {
		logger.Infof(ctx, "未找到目标主机组")
		return nil, nil, nil
	}

	// 获取主机列表
	params := map[string]interface{}{
		"output":           "extend",
		"groupids":         groupIDs,
		"selectInterfaces": "extend",
	}

	result, err := client.Call("host.get", params)
	if err != nil {
		return nil, nil, fmt.Errorf("获取主机列表失败: %w", err)
	}

	var hosts []ZabbixHostObject
	if err := json.Unmarshal(result, &hosts); err != nil {
		return nil, nil, fmt.Errorf("解析主机列表失败: %w", err)
	}

	// 使用map去重，避免同一设备在多个群组中重复
	processedHosts := make(map[int]bool)                // 记录已处理的主机ID，避免同一主机被重复处理
	deviceMap := make(map[string]*models.NetworkDevice) // 以管理IP为键进行去重，确保相同IP的设备只保留第一个
	portMap := make(map[string][]*models.NetworkDevicePort)

	for _, host := range hosts {
		// 检查是否已处理过此主机
		if processedHosts[host.HostID] {
			logger.Debugf(ctx, "主机 %s (ID:%d) 已处理，跳过重复处理", host.Name, host.HostID)
			continue
		}
		processedHosts[host.HostID] = true

		items, err := client.GetHostItems(fmt.Sprintf("%d", host.HostID))
		if err != nil {
			logger.Warnf(ctx, "获取主机 %s 监控项失败: %v", host.Name, err)
			continue
		}

		device, ports := convertHostToDevice(host, items)
		if device != nil {
			// 使用管理IP作为唯一标识进行去重
			if existingDevice, exists := deviceMap[device.ManagementIP]; exists {
				logger.Debugf(ctx, "设备IP %s 重复，保留第一个 (现有:%s, 当前:%s)",
					device.ManagementIP, existingDevice.AliasName, device.AliasName)
				continue
			}
			deviceMap[device.ManagementIP] = device
			portMap[device.DeviceCode] = ports
		}
	}

	// 将map转换为slice
	devices := make([]*models.NetworkDevice, 0, len(deviceMap))
	for _, device := range deviceMap {
		devices = append(devices, device)
	}

	return devices, portMap, nil
}

// convertHostToDevice 将Zabbix主机转换为网络设备
func convertHostToDevice(host ZabbixHostObject, items []ZabbixItem) (*models.NetworkDevice, []*models.NetworkDevicePort) {
	var hostName, serialNumber, brand, modelField string
	ports := make([]*models.NetworkDevicePort, 0)
	portNameMap := make(map[string]struct{})
	currentTime := time.Now()
	for _, item := range items {
		key := strings.TrimSpace(item.Key_)
		value := strings.TrimSpace(item.LastValue)

		switch key {
		case "system.name":
			hostName = value
		case "device.brand":
			brand = value
		case "device.type":
			modelField = value
		case "system.hw.serialnumber":
			serialNumber = value
		default:
			if strings.Contains(key, "ifHighSpeed") {
				if _, exists := portNameMap[key]; !exists {
					speed, err := ConvertBytesToMB(value)
					if err != nil {
						continue
					}
					portNameMap[key] = struct{}{}
					name, err := getPortName(key)
					if err != nil {
						continue
					}
					if strings.Contains(name, "Ethernet") || strings.Contains(name, "TenGigabitEthernet") ||
						strings.Contains(name, "GigabitEthernet") || strings.Contains(name, "XGigabitEthernet") ||
						strings.Contains(name, "40GE") || strings.Contains(name, "MEth") {
						ports = append(ports, &models.NetworkDevicePort{
							Model: models.Model{
								CreateTime: currentTime,
								UpdateTime: currentTime,
							},
							PortName:        name,
							NegotiatedSpeed: speed,
							IsDeleted:       ptr.Bool(false),
						})
					}
				}
			}
		}
	}

	rawData, _ := json.Marshal(host)
	device := &models.NetworkDevice{
		DeviceCode:   fmt.Sprintf("%d", host.HostID),
		Hostname:     hostName,
		AliasName:    host.Name,
		ResourceId:   GetResourceId(serialNumber),
		ManagementIP: host.Host,
		DeviceType:   "",
		Brand:        brand,
		ModelField:   modelField,
		SerialNumber: serialNumber,
		Status:       "ACTIVE",
		Source:       "zabbix",
		RawApiData:   string(rawData),
		IsPhysical:   ptr.Bool(true),
		IsDeleted:    ptr.Bool(false),
		IsAPIPull:    ptr.Bool(true),
	}

	return device, ports
}

// getPortName 从监控项中提取端口名称
func getPortName(input string) (string, error) {
	start := strings.Index(input, "[")
	end := strings.Index(input, "]")
	if start == -1 || end == -1 || start >= end {
		return "", fmt.Errorf("无效的端口名称格式: %s", input)
	}
	return input[start+1 : end], nil
}

// ConvertBytesToMB 将字节数转换为MB
func ConvertBytesToMB(byteStr string) (int, error) {
	b, err := strconv.ParseInt(byteStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("解析字节数失败: %w", err)
	}
	return int(b / 1_000_000), nil
}

// getNetworkDeviceGroups 获取网络设备主机组ID列表
func getNetworkDeviceGroups(client *ZabbixClient) ([]string, error) {
	targetGroupNames := []string{
		"【网络CMDB】全国广域网设备",
		"【网络CMDB】黄岛数据中心",
		"【网络CMDB】北京数据中心",
	}

	params := map[string]interface{}{
		"output": "extend",
		"filter": map[string]interface{}{
			"name": targetGroupNames,
		},
	}

	result, err := client.Call("hostgroup.get", params)
	if err != nil {
		return nil, fmt.Errorf("获取主机组失败: %w", err)
	}

	var groups []struct {
		GroupID int    `json:"groupid,string"`
		Name    string `json:"name"`
	}

	if err := json.Unmarshal(result, &groups); err != nil {
		return nil, fmt.Errorf("解析主机组失败: %w", err)
	}

	groupIDs := make([]string, 0, len(groups))
	for _, group := range groups {
		groupIDs = append(groupIDs, fmt.Sprintf("%d", group.GroupID))
	}

	return groupIDs, nil
}
