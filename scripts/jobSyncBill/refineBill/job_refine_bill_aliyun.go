package refineBill

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ecs"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewRefineBillAliyun() *RefineBillAliyun {
	return &RefineBillAliyun{
		JobBase: base.NewJobBase("BILL_REFINE_ALIYUN",
			"阿里云账单精细化",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type RefineBillAliyun struct {
	*base.JobBase
}

func (*RefineBillAliyun) Name() string {
	return base.TaskRefineBillAliyun
}

func (r *RefineBillAliyun) Run(ctx context.Context) (map[string]any, error) {
	t := &CloudProductSubstitutionTemplate{s: &AliyunSubstitution{}}
	if err := t.Execute(ctx); err != nil {
		return nil, err
	}
	return nil, nil
}

type AliyunSubstitution struct {
}

func (s *AliyunSubstitution) GetClients(ctx context.Context) ([]client.IClient, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.AliCloud, hbc.AliCloudDedicated},
		hbc.Bill,
		[]string{bizutils.PurposeBill, bizutils.PurposeDedicated},
		actfilter.SetIgnoreVendor(hbc.Private, hbc.TencentCloud, hbc.AWS, hbc.Azure, hbc.JXJG), // 忽略不支持的云
		actfilter.SetRegion(hbc.HuaweiCloud, "cn-north-1"),
		// 忽略不支持的云
		actfilter.SetIgnoreVendor(hbc.Private, hbc.HuaweiCloud, hbc.TencentCloud, hbc.AWS, hbc.Azure, hbc.JXJG),
	)
	if err != nil {
		return nil, err
	}

	return defaultClients, nil
}

func (s *AliyunSubstitution) GetSubstitutionConfigMap() map[string]SubstitutionConfig {
	return map[string]SubstitutionConfig{
		"cbwp": {
			PerPage:          100,
			Concurrent:       10,
			Weight:           50,
			SubstitutionFunc: AliyunCbwpSubstitutor,
		},
		/*"polardb": {
			PerPage:          100,
			Concurrent:       10,
			Weight:           40,
			SubstitutionFunc: AliyunPolarDBSubstitutor,
		},*/
		/*"hdm": {
			PerPage:          100,
			Concurrent:       10,
			Weight:           30,
			SubstitutionFunc: AliyunHdmSubstitutor,
		},*/
		"snapshot": {
			PerPage:          300,
			Concurrent:       5,
			Weight:           20,
			SubstitutionFunc: AliyunSnapshotSubstitutor,
		},
		"yundisk": {
			PerPage:          100,
			Concurrent:       10,
			Weight:           10,
			SubstitutionFunc: AliyunYunDiskSubstitutor,
		},
		"savingplan": {
			PerPage:          100,
			Concurrent:       10,
			Weight:           1,
			SubstitutionFunc: SavingPlanSubstitutor,
		},
	}
}

const EcsPrefix string = "i-"
const RdsPrefix string = "r-"

// AliyunSnapshotSubstitutor SnapshotSubstitutor 快照替换
func AliyunSnapshotSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	substitutionBills := make([]*models.RefinedRawBill, 0)
	for _, bill := range bills {
		var item *aliyun.BillItem
		if err := json.Unmarshal([]byte(bill.Content), &item); err != nil {
			return err
		}
		if strings.HasPrefix(item.SplitItemID, EcsPrefix) {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.SubBillingItem = bill.BillingItem
			bill.InstanceID = item.SplitItemID
			bill.ProductCode = hbc.ECS.String()
			bill.ProductName = "云服务器 ECS"
			substitutionBills = append(substitutionBills, bill)
		} else if strings.HasPrefix(item.SplitItemID, RdsPrefix) {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.SubBillingItem = bill.BillingItem
			bill.InstanceID = item.SplitItemID
			bill.ProductCode = hbc.RDS.String()
			bill.ProductName = "云数据库 RDS"
			substitutionBills = append(substitutionBills, bill)
		}
	}

	log.Info(ctx, "-->snapshot substitutionBills len: ", len(substitutionBills))
	if err := updateRefinedRawBill(ctx, substitutionBills); err != nil {
		return err
	}
	return nil
}

// AliyunYunDiskSubstitutor YunDiskSubstitutor 云盘替换
func AliyunYunDiskSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	if len(bills) < 1 {
		return nil
	}

	var diskIds = tools.NewStringSet()
	for _, bill := range bills {
		diskIds.Add(bill.InstanceID)
	}
	var uniqDiskIds = diskIds.List()

	purpose := ""
	if client.Vendor() == hbc.AliCloud {
		purpose = bizutils.PurposeAdmin
	} else if client.Vendor() == hbc.AliCloudDedicated {
		purpose = bizutils.PurposeDedicated
	}

	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{client.Vendor()}, hbc.ECS, []string{purpose},
		actfilter.SetIgnoreVendor(hbc.Private, hbc.TencentCloud, hbc.AWS, hbc.Azure, hbc.JXJG),
		actfilter.SetIgnoreVendor(hbc.Private, hbc.HuaweiCloud, hbc.TencentCloud, hbc.AWS, hbc.Azure, hbc.JXJG))
	if err != nil {
		return err
	}

	defClient := defaultClients[0].(*aliyun.EcsClient)
	regions, err := defClient.DescribeRegions()

	b := tools.NewBatch[ecs.Region, []ecs.Disk](ctx)
	b.Run(regions, func(ctx context.Context, region ecs.Region) ([]ecs.Disk, error) {
		if region.RegionId == "ap-south-1" || region.RegionId == "ap-southeast-2" {
			return nil, nil
		}
		ecsClient, cliErr := hybrid.GetClient(ctx, client.Vendor(), client.Name(), hbc.ECS, purpose, region.RegionId)
		if cliErr != nil {
			return nil, cliErr
		}

		cli := ecsClient.(*aliyun.EcsClient)

		// 设置每页查询数量
		requestSizeFunc := func(req *ecs.DescribeDisksRequest) {
			req.PageSize = requests.NewInteger(100)
			req.ConnectTimeout = 5000 * time.Millisecond
		}
		return cli.DescribeDisks(ctx, &uniqDiskIds, requestSizeFunc)
	})

	if err := b.Error(); err != nil {
		return err
	}

	disks := tools.MergeData(b.Outs()...)
	diskIdMap := make(map[string]ecs.Disk, len(disks))
	for _, disk := range disks {
		diskIdMap[disk.DiskId] = disk
	}

	refinedBills := make([]*models.RefinedRawBill, 0)
	for _, bill := range bills {
		if disk, ok := diskIdMap[bill.InstanceID]; ok {
			ecsInstanceId := disk.InstanceId

			if bill.SubProductCode == "savingplan" {
				bill.SubSubInstanceId = bill.SubInstanceId
				bill.SubSubProductCode = bill.SubProductCode
				bill.SubSubProductName = bill.SubProductName
				bill.SubSubBillingItem = bill.SubBillingItem
			}

			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.SubBillingItem = bill.BillingItem

			bill.InstanceID = ecsInstanceId
			bill.ProductCode = hbc.ECS.String()
			bill.ProductName = "云服务器 ECS"
			bill.SupplementID = ecsInstanceId

			refinedBills = append(refinedBills, bill)
		}
	}

	log.Info(ctx, "-->yundisk refinedBills len: ", len(refinedBills))

	if err := updateRefinedRawBill(ctx, refinedBills); err != nil {
		return err
	}
	return nil
}

// SavingPlanSubstitutor 节省计划替换
func SavingPlanSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	substitutionBills := make([]*models.RefinedRawBill, 0)
	for _, bill := range bills {
		var item *aliyun.BillItem
		if err := json.Unmarshal([]byte(bill.Content), &item); err != nil {
			return err
		}

		if item.SplitCommodityCode == "ecs" {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.SubBillingItem = bill.BillingItem
			bill.InstanceID = item.SplitItemID
			bill.ProductCode = hbc.ECS.String()
			bill.ProductName = "云服务器 ECS"
			substitutionBills = append(substitutionBills, bill)
		} else if item.SplitCommodityCode == "yundisk" {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.SubBillingItem = bill.BillingItem
			bill.InstanceID = item.SplitItemID
			bill.ProductCode = "yundisk"
			bill.ProductName = "云硬盘"
			substitutionBills = append(substitutionBills, bill)
		}
	}

	log.Info(ctx, "-->savingplan substitutionBills len: ", len(substitutionBills))
	if err := updateRefinedRawBill(ctx, substitutionBills); err != nil {
		return err
	}
	return nil
}

// AliyunHdmSubstitutor 数据库自治服务账单分摊到数据库实例
func AliyunHdmSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	substitutionBills := make([]*models.RefinedRawBill, 0)

	for _, bill := range bills {
		if bill.SubscriptionType == "PayAsYouGo" && len(bill.SupplementID) > 0 {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.SubBillingItem = bill.BillingItem
			bill.InstanceID = bill.SupplementID
			bill.ProductCode = hbc.RDS.String()
			bill.ProductName = "云数据库 RDS"
			substitutionBills = append(substitutionBills, bill)
		}
	}

	if len(substitutionBills) < 1 {
		return nil
	}

	if err := updateRefinedRawBill(ctx, substitutionBills); err != nil {
		return err
	}
	return nil
}

// AliyunPolarDBSubstitutor PolarDB备份账单分摊到数据库实例
func AliyunPolarDBSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	substitutionBills := make([]*models.RefinedRawBill, 0)

	for _, bill := range bills {
		if len(bill.SupplementID) > 0 {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.SubBillingItem = bill.BillingItem
			// todo 不对
			// polardb,pc-m5e0q51gg1qg81r08,pc-m5e0q51gg1qg81r08_bk
			// 拆分时拆分错了 --sql互换
			bill.InstanceID = bill.SupplementID
			bill.ProductCode = "polardb"
			bill.ProductName = "云原生数据库 PolarDB"
			substitutionBills = append(substitutionBills, bill)
		}
	}

	if len(substitutionBills) < 1 {
		return nil
	}

	if err := updateRefinedRawBill(ctx, substitutionBills); err != nil {
		return err
	}
	return nil
}

func AliyunCbwpSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	substitutionBills := make([]*models.RefinedRawBill, 0)

	for _, bill := range bills {
		var item *aliyun.BillItem
		if err := json.Unmarshal([]byte(bill.Content), &item); err != nil {
			return err
		}

		if item.SplitCommodityCode == "eip" && len(bill.SupplementID) > 0 &&
			// haier31的共享带宽需要拆分，所以不做父子关系替换
			bill.AccountName != "<EMAIL>" && bill.SCodeUnverified != "共享带宽" {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.SubBillingItem = bill.BillingItem
			bill.InstanceID = bill.SupplementID
			bill.ProductCode = "eip"
			bill.ProductName = "弹性公网IP"
			substitutionBills = append(substitutionBills, bill)
		}
	}

	if len(substitutionBills) < 1 {
		return nil
	}
	if err := updateRefinedRawBill(ctx, substitutionBills); err != nil {
		return err
	}
	return nil
}
