package refineBill

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewRefineBillAws() *RefineBillAws {
	return &RefineBillAws{
		JobBase: base.NewJobBase("BILL_REFINE_AWS",
			"亚马逊云账单精细化",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource()),
	}
}

type RefineBillAws struct {
	*base.JobBase
}

type AwsSubstitution struct {
}

func (r *RefineBillAws) Name() string {
	return base.TaskRefineBillAws
}

func (r *RefineBillAws) Run(ctx context.Context) (map[string]any, error) {
	t := &CloudProductSubstitutionTemplate{
		s: &AwsSubstitution{},
	}

	if err := t.Execute(ctx); err != nil {
		return nil, err
	}

	return nil, nil
}

func (s *AwsSubstitution) GetClients(ctx context.Context) ([]client.IClient, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.AWS},
		hbc.Bill,
		[]string{bizutils.PurposeBill},
	)
	if err != nil {
		return nil, err
	}

	return defaultClients, nil
}

func (s *AwsSubstitution) GetSubstitutionConfigMap() map[string]SubstitutionConfig {
	return map[string]SubstitutionConfig{}
}
