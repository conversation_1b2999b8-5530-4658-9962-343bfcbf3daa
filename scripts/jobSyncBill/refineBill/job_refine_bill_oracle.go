package refineBill

import (
	"context"
	"fmt"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/sdk/oraclecloud"
	"github.com/oracle/oci-go-sdk/core"
	"github.com/patrickmn/go-cache"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

var (
	oracleVolumeAttachmentCache = cache.New(5*time.Minute, 10*time.Minute)
	oracleVolumeAttachmentLock  = new(sync.Mutex)
)

const (
	oracleCloudDisk = "BLOCK_STORAGE"
	tenantId        = "ocid1.tenancy.oc1..aaaaaaaau5txmmkznypkljyrllnv575cyd6sy7gqg5ticqmssoveeijpjlgq"
	name            = "ocid1.tenancy.oc1..aaaaaaaauvgqmnfx4jbsqd3vp4qica6uccwbzg5iy4u2emobcj5wo2soeqqq"
	purpose         = "RESOURCE_READ"
	region          = "eu-frankfurt-1"
	ocidPrefix      = "ocid1"
)

func NewRefineBillOracle() *RefineBillOracle {
	return &RefineBillOracle{
		base.NewJobBase("BILL_REFINE_ORACLE",
			"Oracle云账单精细化",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource()),
	}
}

type RefineBillOracle struct {
	*base.JobBase
}

func (*RefineBillOracle) Name() string {
	return base.TaskRefineBillOracle
}

func (r *RefineBillOracle) Run(ctx context.Context) (map[string]any, error) {

	t := &CloudProductSubstitutionTemplate{
		s: &OracleSubstitution{},
	}

	if err := t.Execute(ctx); err != nil {
		return nil, err
	}

	return nil, nil
}

type OracleSubstitution struct {
}

func (s *OracleSubstitution) GetClients(ctx context.Context) ([]client.IClient, error) {
	return base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.OracleCloud},
		hbc.Disk,
		//[]string{"RESOURCE_READ"},
		[]string{bizutils.PurposeAdmin},
	)

}

func (s *OracleSubstitution) GetSubstitutionConfigMap() map[string]SubstitutionConfig {
	return map[string]SubstitutionConfig{
		oracleCloudDisk: {
			SubstitutionFunc: OracleCloudDiskSubstitutor,
			PerPage:          100,
			Concurrent:       5,
		},
	}
}

func OracleCloudDiskSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	// 统计不同的实例ID
	instanceIds := make(map[string]int)
	supplementIds := make(map[string]int)
	zones := make(map[string]int)

	for _, bill := range bills {
		instanceIds[bill.InstanceID]++
		supplementIds[bill.SupplementID]++
		zones[bill.Zone]++
	}

	// 记录统计信息
	log.Infof(ctx, "处理账单数: %d, 不同实例ID数: %d, 不同SupplementID数: %d, 不同Zone数: %d",
		len(bills), len(instanceIds), len(supplementIds), len(zones))

	// 打印前10个实例ID的统计信息
	count := 0
	log.Infof(ctx, "实例ID统计前10项:")
	for id, num := range instanceIds {
		if count < 10 {
			log.Infof(ctx, "  实例ID: %s, 对应账单数: %d", id, num)
			count++
		} else {
			break
		}
	}

	refinedBills := make([]*models.RefinedRawBill, 0)
	for _, bill := range bills {
		refined, _ := refine(ctx, bill, client)
		if refined != nil {
			refinedBills = append(refinedBills, refined)
		}
	}

	// 记录处理结果
	log.Infof(ctx, "成功替换账单数: %d/%d (替换率: %.2f%%)",
		len(refinedBills), len(bills), float64(len(refinedBills))/float64(len(bills))*100)

	if len(refinedBills) == 0 {
		return nil
	}

	// 统计成功替换的实例ID
	successInstanceIds := make(map[string]int)
	for _, bill := range refinedBills {
		successInstanceIds[bill.SubInstanceId]++
	}

	count = 0
	log.Infof(ctx, "成功替换的实例ID统计前10项:")
	for id, num := range successInstanceIds {
		if count < 10 {
			log.Infof(ctx, "  实例ID: %s, 成功替换数: %d", id, num)
			count++
		} else {
			break
		}
	}

	return updateRefinedRawBill(ctx, refinedBills)
}

// 使用包级变量跟踪统计信息
var (
	totalBills      int32
	skippedBills    int32
	apiFailedBills  int32
	successfulBills int32
	apiCalls        int32
)

func refine(ctx context.Context, bill *models.RefinedRawBill, client client.IClient) (*models.RefinedRawBill, error) {
	// 增加总处理账单计数
	totalCount := atomic.AddInt32(&totalBills, 1)
	if totalCount%100 == 0 {
		log.Infof(ctx, "处理进度: 已处理账单=%d, 跳过=%d, API失败=%d, 成功替换=%d, API调用=%d",
			totalCount, atomic.LoadInt32(&skippedBills), atomic.LoadInt32(&apiFailedBills),
			atomic.LoadInt32(&successfulBills), atomic.LoadInt32(&apiCalls))
	}

	// 记录当前处理的账单详情
	log.Infof(ctx, "正在处理账单: ID=%d, SupplementID=%s, InstanceID=%s, Zone=%s",
		bill.ID, bill.SupplementID, bill.InstanceID, bill.Zone)

	// 验证SupplementID是否符合OCID格式
	if !strings.HasPrefix(bill.SupplementID, ocidPrefix) {
		atomic.AddInt32(&skippedBills, 1)
		log.Infof(ctx, "账单已跳过: SupplementID=%s 不是以%s开头", bill.SupplementID, ocidPrefix)
		return nil, nil
	}

	// 初始化Oracle卷客户端
	log.Infof(ctx, "--> 创建Oracle卷客户端: name=%s, purpose=%s, tenantId=%s, region=%s",
		name, purpose, tenantId, region)
	volumeClient := hybrid.AccountManager().OracleVolumeClient(ctx, name, purpose, tenantId, region)

	// 增加API调用计数
	atomic.AddInt32(&apiCalls, 1)

	attachment, err := GetOracleVolumeAttachmentsWithCache(ctx, volumeClient, &bill.SupplementID, &bill.InstanceID, &bill.Zone)

	if err != nil {
		atomic.AddInt32(&apiFailedBills, 1)
		log.Errorf(ctx, "获取卷附件失败: billID=%d, error=%v", bill.ID, err)
		return nil, err
	}

	// 如果找到了卷附件，执行替换
	if attachment != nil {
		// 记录替换前后的关键信息
		log.Infof(ctx, "找到卷附件，执行替换: billID=%d, 原InstanceID=%s, 新InstanceID=%s",
			bill.ID, bill.InstanceID, *attachment.InstanceId)

		// 执行账单信息替换
		bill.SubInstanceId = bill.InstanceID
		bill.SubProductCode = bill.ProductCode
		bill.SubProductName = bill.ProductName
		bill.InstanceID = *attachment.InstanceId
		bill.ProductCode = "COMPUTE"
		bill.ProductName = "计算服务"

		// 增加成功替换计数
		atomic.AddInt32(&successfulBills, 1)
		return bill, nil
	}

	return nil, nil
}

// GetOracleVolumeAttachmentsWithCache retrieves Oracle volume attachments from cache or fetches them if not present.
// The key for the cache will be primarily based on volumeId for better hit rate, falling back to other parameters if needed.
func GetOracleVolumeAttachmentsWithCache(
	ctx context.Context,
	volumeClient *oraclecloud.VolumeClient,
	compartmentId, volumeId, availabilityDomain *string,
) (*core.BootVolumeAttachment, error) {
	oracleVolumeAttachmentLock.Lock()
	defer oracleVolumeAttachmentLock.Unlock()

	// 记录传入的参数
	logParams := map[string]string{
		"compartmentId":      "<nil>",
		"volumeId":           "<nil>",
		"availabilityDomain": "<nil>",
	}
	if compartmentId != nil {
		logParams["compartmentId"] = *compartmentId
	}
	if volumeId != nil {
		logParams["volumeId"] = *volumeId
	}
	if availabilityDomain != nil {
		logParams["availabilityDomain"] = *availabilityDomain
	}

	// 构造更简单的缓存键 - 优先使用volumeId
	cacheKey := ""
	if volumeId != nil {
		// 如果有volumeId，优先使用它作为缓存键
		cacheKey = *volumeId
	} else if compartmentId != nil {
		// 否则使用compartmentId
		cacheKey = *compartmentId
	}

	// 如果没有有效的键，记录错误并返回
	if cacheKey == "" {
		log.Errorf(ctx, "Failed to create cache key, no valid volumeId or compartmentId provided")
		return nil, fmt.Errorf("no valid parameters for cache key")
	}

	log.Infof(ctx, "Attempting cache lookup with key: %s (params: compartmentId=%s, volumeId=%s, availabilityDomain=%s)",
		cacheKey, logParams["compartmentId"], logParams["volumeId"], logParams["availabilityDomain"])

	// 尝试从缓存获取
	if attachments, found := oracleVolumeAttachmentCache.Get(cacheKey); found {
		log.Infof(ctx, "Cache hit for Oracle Volume Attachments: key=%s", cacheKey)
		amts := attachments.([]core.BootVolumeAttachment)
		if len(amts) > 0 {
			return &amts[0], nil
		}
		// 缓存命中但数组为空，返回nil而不是错误
		log.Infof(ctx, "Cache hit but empty result for key=%s", cacheKey)
		return nil, nil
	}

	log.Infof(ctx, "Cache miss for Oracle Volume Attachments: key=%s. Fetching from API.", cacheKey)

	// 首次尝试使用所有参数
	attachments, err := volumeClient.ListVolumeAttachments(ctx, compartmentId, volumeId, availabilityDomain)
	if err != nil {
		log.Errorf(ctx, "Primary ListVolumeAttachments failed: %v", err)

		// 如果第一次查询失败，尝试只使用volumeId
		if volumeId != nil {
			log.Infof(ctx, "Trying alternate query with only volumeId=%s", *volumeId)
			attachments, err = volumeClient.ListVolumeAttachments(ctx, nil, volumeId, nil)
			if err != nil {
				log.Errorf(ctx, "Alternate query also failed: %v", err)
				oracleVolumeAttachmentCache.Set(cacheKey, []core.BootVolumeAttachment{}, 30*time.Second) // 缓存空结果，但有更短的过期时间
				return nil, err
			}
		} else {
			// 没有volumeId，无法尝试备用查询
			oracleVolumeAttachmentCache.Set(cacheKey, []core.BootVolumeAttachment{}, 30*time.Second) // 缓存空结果，但有更短的过期时间
			return nil, err
		}
	}

	if len(attachments) == 0 {
		log.Infof(ctx, "API returned empty result for key=%s", cacheKey)
		oracleVolumeAttachmentCache.Set(cacheKey, attachments, cache.DefaultExpiration)
		return nil, nil
	}

	log.Infof(ctx, "API returned %d attachments for key=%s", len(attachments), cacheKey)
	oracleVolumeAttachmentCache.Set(cacheKey, attachments, cache.DefaultExpiration)
	return &attachments[0], nil
}
