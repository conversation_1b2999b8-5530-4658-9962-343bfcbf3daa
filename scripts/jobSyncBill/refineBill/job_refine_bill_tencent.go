package refineBill

import (
	"context"
	"strings"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"

	"git.haier.net/devops/ops-golang-common/sdk/client"
	cbs "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cbs/v20170312"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

var regionCodeNameMap = make(map[string]string)

func NewRefineBillTencent() *RefineBillTencent {
	return &RefineBillTencent{
		JobBase: base.NewJobBase("BILL_REFINE_TENCENT",
			"腾讯云账单精细化",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type RefineBillTencent struct {
	*base.JobBase
}

func (*RefineBillTencent) Name() string {
	return base.TaskRefineBillTencent
}

func (r *RefineBillTencent) Run(ctx context.Context) (map[string]any, error) {
	// 获得地区名称和编码的映射
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.TencentCloud},
		hbc.ECS,
		[]string{bizutils.PurposeAdmin},
	)
	if err != nil {
		return nil, err
	}

	defClient := defaultClients[0].(*tencentcloud.EcsClient)
	regions, err := defClient.DescribeRegions(ctx)
	for _, region := range regions {
		regionCodeNameMap[*region.RegionName] = *region.Region
	}

	t := &CloudProductSubstitutionTemplate{s: &TencentSubstitution{}}
	if err := t.Execute(ctx); err != nil {
		return nil, err
	}
	return nil, nil
}

type TencentSubstitution struct {
}

func (s *TencentSubstitution) GetClients(ctx context.Context) ([]client.IClient, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.TencentCloud},
		hbc.Bill,
		[]string{bizutils.PurposeBill},
	)

	if err != nil {
		return nil, err
	}

	return defaultClients, nil
}

func (s *TencentSubstitution) GetSubstitutionConfigMap() map[string]SubstitutionConfig {

	return map[string]SubstitutionConfig{
		"p_cbs": {
			PerPage:          100,
			Concurrent:       3,
			SubstitutionFunc: TencentSnapshotSubstitutor,
		},
	}
}

// TencentSnapshotSubstitutor 腾讯云盘替换
func TencentSnapshotSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	if len(bills) < 1 {
		return nil
	}

	/* 1、将账单中的地域名称替换为地域编码 */
	b := tools.NewBatch[*models.RefinedRawBill, *models.RefinedRawBill](ctx, batch.WithBatchSize(10))
	b.Run(bills, func(ctx context.Context, bill *models.RefinedRawBill) (*models.RefinedRawBill, error) {
		bill.Region = strings.Replace(bill.Region, "（", "(", -1)
		bill.Region = strings.Replace(bill.Region, "）", ")", -1)
		if regionCode, ok := regionCodeNameMap[bill.Region]; ok {
			bill.Region = regionCode
			return bill, nil
		}

		return nil, nil
	})

	regionReplacedBills := b.Outs()

	/* 2、将账单按照地域进行分组 */
	regionBillMap := make(map[string][]*models.RefinedRawBill)
	for _, bill := range regionReplacedBills {
		billArray, ok := regionBillMap[bill.Region]
		if !ok {
			regionBillMap[bill.Region] = []*models.RefinedRawBill{bill}
		} else {
			billArray = append(billArray, bill)
		}
	}

	/* 3、按照地域分组批量查询硬盘信息，并进行父子关系替换 */
	regions := make([]string, 0, len(regionBillMap))
	for region := range regionBillMap {
		regions = append(regions, region)
	}

	nb := tools.NewBatch[string, []*models.RefinedRawBill](ctx)
	nb.Run(regions, func(ctx context.Context, region string) ([]*models.RefinedRawBill, error) {
		refinedBills := make([]*models.RefinedRawBill, 0)
		regionBills := regionBillMap[region]

		diskIds := make([]*string, 0, len(regionBills))
		for _, bill := range regionBills {
			diskIds = append(diskIds, &bill.InstanceID)
		}

		cbsClient := hybrid.AccountManager().TencentCbsClient(ctx, client.Name(), bizutils.PurposeAdmin, region)
		disks, err := cbsClient.DescribeDisks(ctx, diskIds)
		if err != nil {
			log.Error(ctx, "DescribeDisks failed: %s", err)
			return nil, err
		}

		if len(disks) < 1 {
			return nil, nil
		}

		diskMap := make(map[string]*cbs.Disk)
		for _, disk := range disks {
			diskMap[*disk.DiskId] = disk
		}

		for _, bill := range regionBills {
			if d, ok := diskMap[bill.InstanceID]; ok {
				bill.SubInstanceId = bill.InstanceID
				bill.SubProductCode = bill.ProductCode
				bill.SubProductName = bill.ProductName
				bill.InstanceID = *d.InstanceId
				bill.ProductCode = "p_cvm"
				bill.ProductName = "云服务器CVM"

				refinedBills = append(refinedBills, bill)
			}
		}

		return refinedBills, err
	})

	return updateRefinedRawBill(ctx, tools.MergeData(nb.Outs()...))
}
