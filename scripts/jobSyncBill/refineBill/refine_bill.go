package refineBill

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"gorm.io/gorm"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
)

const DateLayout = "2006-01-02"

// 转义后的 SQL 语句
var instanceIdAggregatedIdQuery = `
		SELECT t.instance_id, t.aggregated_id, t.cost_unit, t.subscription_type, t.product_name
		FROM (SELECT r.instance_id,
					 r.aggregated_id,
					 r.cost_unit,
					 r.subscription_type,
					 r.product_name,
					 row_number() over (partition by r.instance_id, r.aggregated_id ORDER BY t.billing_cycle DESC) as rn
			  FROM bc_bill_task t
					   INNER JOIN bc_refined_raw_bill r ON t.task_id = r.task_id
				  AND t.vendor = ? AND t.stage = 'pull' AND t.status = 1
				  AND t.billing_cycle >= DATE_FORMAT(?, '%Y-%m-01')
				  AND t.billing_cycle < DATE_ADD(DATE_FORMAT(?, '%Y-%m-01'), INTERVAL 1 MONTH)
			  WHERE r.product_code = ?
				AND r.instance_id IN (?)
				and r.sub_instance_id = ''
				and r.supplement_id = ''
			  GROUP BY r.instance_id, r.aggregated_id, cost_unit) t
		WHERE t.rn = 1
	`

// 查询指定产品实例账单
func queryParentProductOfBillingCycle(ctx context.Context, iClient client.IClient, vendor, instanceID string) (
	*models.RefinedRawBill, error) {
	var refinedRawBill models.RefinedRawBill

	m := bizutils.DataSource().Model(ctx)
	err := m.Orm().Model(&models.RefinedRawBill{}).
		Where("select * from bc_refined_raw_bill where vendor = '' and instance_id = '' limit 1", vendor, instanceID).
		Find(&refinedRawBill).Error
	if err != nil {
		return nil, err
	}

	return &refinedRawBill, nil
}

// 查询instanceId对应的aggregatedId、subscriptionType、costUnit、productName
func queryInstanceIdAggregatedIdMapOfBills(ctx context.Context, bills []*models.RefinedRawBill) (map[string]InstanceIdAggregatedIdPair, error) {
	instanceIdAggregatedIdMap := make(map[string]InstanceIdAggregatedIdPair, 0)
	if len(bills) == 0 {
		return nil, nil
	}

	var productCodeInstanceIdsMap = make(map[string]tools.StringSet)
	// 循环结束得到了productCode对应的instanceId集合
	for _, bill := range bills {
		if instanceIdSet, ok := productCodeInstanceIdsMap[bill.ProductCode]; !ok {
			instanceIdSet = tools.NewStringSet()
			instanceIdSet.Add(bill.InstanceID)
			productCodeInstanceIdsMap[bill.ProductCode] = instanceIdSet
		} else {
			instanceIdSet.Add(bill.InstanceID)
		}
	}

	for productCode, instanceIdSet := range productCodeInstanceIdsMap {
		var pairs []InstanceIdAggregatedIdPair
		m := bizutils.DataSource().Model(ctx)
		err := m.Orm().Model(&InstanceIdAggregatedIdPair{}).
			Raw(instanceIdAggregatedIdQuery, bills[0].Vendor, bills[0].BillingCycle, bills[0].BillingCycle, productCode, instanceIdSet.List()).
			Scan(&pairs).
			Error
		if err != nil {
			return nil, err
		}

		for _, pair := range pairs {
			instanceIdAggregatedIdMap[pair.InstanceId] = pair
		}
	}

	return instanceIdAggregatedIdMap, nil
}

type InstanceIdAggregatedIdPair struct {
	InstanceId       string `gorm:"column:instance_id"`
	AggregatedId     string `gorm:"column:aggregated_id"`
	CostUnit         string `gorm:"column:cost_unit"`
	SubscriptionType string `gorm:"column:subscription_type"`
	ProductName      string `gorm:"column:product_name"`
}

func updateRefinedRawBill(ctx context.Context, bills []*models.RefinedRawBill) error {
	var updatingErr error
	m := bizutils.DataSource().Model(ctx)
	var wg sync.WaitGroup
	concurrentController := make(chan struct{}, 20)
	defer close(concurrentController)

	// 从当月的账单中根据产品编码按照InstanceId、AggregatedId分组查询（supplement_id、sub_instance_id为空），
	// 获取value object（InstanceId、AggregatedId、CostUnit、SubscriptionType、ProductName）集合
	instanceIdAggregatedIdMap, err := queryInstanceIdAggregatedIdMapOfBills(ctx, bills)
	if err != nil {
		return err
	}

	for _, bill := range bills {
		wg.Add(1)
		concurrentController <- struct{}{}

		go func(b *models.RefinedRawBill) {
			defer wg.Done()

			/* 云盘的节省计划：直接替换为云盘 */
			if b.Vendor == "aliyun" && b.SubProductCode == "savingplan" && b.ProductCode == "yundisk" && strings.HasPrefix(b.InstanceID, "d-") {
				/* start ==> 替换云硬盘的aggregated_id <== */
				var aggregatedId = b.AggregatedID

				// 能找到父资源对应的aggregatedId，则替换成父资源的aggregatedId
				if pair, ok := instanceIdAggregatedIdMap[b.InstanceID]; ok {
					aggregatedId = pair.AggregatedId
				} else {
					// 当月账单中找不到从cmdb总表获取
					product, _ := bizutils.GetCmdbProductOverviewInstanceByInstanceId(ctx, b.InstanceID)
					if product != nil && product.Vendor == b.Vendor {
						aggregatedId = product.AggregatedID
					}
				}
				/* end ==> 替换云硬盘的aggregated_id <== */

				err = m.Orm().Model(b).UpdateColumns(models.RefinedRawBill{
					RawBill: models.RawBill{
						AggregatedID: aggregatedId,
						ProductCode:  b.ProductCode,
						ProductName:  b.ProductName,
						InstanceID:   b.InstanceID,
						BillingItem:  b.BillingItem,
					},
					SubProductCode: b.SubProductCode,
					SubProductName: b.SubProductName,
					SubInstanceId:  b.SubInstanceId,
					SubBillingItem: b.SubBillingItem,
					ParentFound:    0,
				}).Error
			} else {
				var isParentFound = -1
				// 默认取原来的aggregatedId
				var aggregatedId = b.AggregatedID
				var costUnit = b.CostUnit
				var subscriptionType = b.SubscriptionType
				var productName = b.ProductName

				// 能找到父资源对应的aggregatedId，则替换成父资源的aggregatedId
				if pair, ok := instanceIdAggregatedIdMap[b.InstanceID]; ok {
					aggregatedId = pair.AggregatedId
					costUnit = pair.CostUnit
					subscriptionType = pair.SubscriptionType
					isParentFound = 1
					productName = pair.ProductName
				} else {
					// 当月账单中找不到从cmdb总表获取
					product, _ := bizutils.GetCmdbProductOverviewInstanceByInstanceId(ctx, b.InstanceID)
					if product != nil && product.Vendor == b.Vendor {
						aggregatedId = product.AggregatedID
						costUnit = product.Scode
						subscriptionType = product.SubscriptionType
						isParentFound = 1
						productName = product.ProductName
					}
				}

				if isParentFound > 0 {
					// 找到了父资源，则替换成父资源的aggregatedId、scode、subscriptionType
					err = m.Orm().Model(b).UpdateColumns(models.RefinedRawBill{
						RawBill: models.RawBill{
							ProductCode:      b.ProductCode,
							ProductName:      productName,
							InstanceID:       b.InstanceID,
							AggregatedID:     aggregatedId,
							CostUnit:         costUnit,
							SubscriptionType: subscriptionType,
						},
						SubProductCode:    b.SubProductCode,
						SubProductName:    b.SubProductName,
						SubInstanceId:     b.SubInstanceId,
						SubBillingItem:    b.SubBillingItem,
						ParentFound:       isParentFound,
						SubSubInstanceId:  b.SubSubInstanceId,
						SubSubProductCode: b.SubSubProductCode,
						SubSubProductName: b.SubSubProductName,
						SubSubBillingItem: b.SubSubBillingItem,
					}).Error
				} else {
					// 找不到父资源打标记，用于后期处理。其中“云盘”需要特殊处理
					if b.Vendor == "aliyun" && b.SubProductCode == "yundisk" {
						/* 云盘：将ecs的实例id放到supplement_id中，用于后期根据SupplementId关联到父资源ecs的实例 */
						err = m.Orm().Model(b).UpdateColumns(models.RefinedRawBill{
							RawBill: models.RawBill{
								SupplementID: b.SupplementID,
							},
							SubSubInstanceId:  b.SubSubInstanceId,
							SubSubProductCode: b.SubSubProductCode,
							SubSubProductName: b.SubSubProductName,
							SubSubBillingItem: b.SubSubBillingItem,
							ParentFound:       isParentFound,
						}).Error
					} else {
						/* 其他情况只更新标记，用于后期处理 */
						err = m.Orm().Model(b).UpdateColumns(models.RefinedRawBill{
							ParentFound: isParentFound,
						}).Error
					}
				}
			}
			if err != nil {
				updatingErr = err
			}

			<-concurrentController
		}(bill)
	}

	wg.Wait()

	return updatingErr
}

// 封装分页获取数据方法
func queryPageByTaskIdAndProductCode(ctx context.Context, accountId, taskId, productCode string, perPage, maxId int) ([]*models.RefinedRawBill, error) {
	// 计算数据库中符合条件的总记录数
	var total int64
	m := bizutils.DataSource().Model(ctx)
	err := m.Orm().Model(&models.RefinedRawBill{}).
		Where("account_id = ? and task_id = ? and product_code = ?", accountId, taskId, productCode).
		Count(&total).Error
	if err != nil {
		log.Errorf(ctx, "统计总记录数失败: %v", err)
	} else {
		log.Infof(ctx, "总计符合条件的记录数: %d (accountId=%s, taskId=%s, productCode=%s)",
			total, accountId, taskId, productCode)
	}

	// 验证分页查询是否会丢失数据
	var totalByPaging int64
	err = m.Orm().Model(&models.RefinedRawBill{}).
		Where("account_id = ? and task_id = ? and product_code = ? and id > ?", accountId, taskId, productCode, maxId).
		Count(&totalByPaging).Error
	if err != nil {
		log.Errorf(ctx, "统计分页条件总记录数失败: %v", err)
	} else {
		log.Infof(ctx, "分页条件总记录数: %d (accountId=%s, taskId=%s, productCode=%s, maxId=%d)",
			totalByPaging, accountId, taskId, productCode, maxId)
	}

	// 记录当前的maxId
	log.Infof(ctx, "开始分页查询: maxId=%d", maxId)

	// 查询分页数据
	var bills []*models.RefinedRawBill
	err = m.Orm().Model(&models.RefinedRawBill{}).
		Where("account_id = ? and task_id = ? and product_code = ? and id > ?", accountId, taskId, productCode, maxId).
		Order("id asc").
		Limit(perPage).
		Find(&bills).Error

	if err != nil {
		log.Errorf(ctx, "查询分页数据失败: %v", err)
		return nil, err
	}

	// 记录查询到的记录数和ID范围
	if len(bills) > 0 {
		minID := bills[0].ID
		maxID := bills[len(bills)-1].ID
		log.Infof(ctx, "本页查询到记录数: %d, 范围: %d-%d", len(bills), minID, maxID)
	} else {
		log.Infof(ctx, "本页查询到记录数: 0")
	}

	return bills, nil
}

// 查询账期是否已经精细化处理
func getPendingRefinedSyncLog(ctx context.Context, vendor, accountName, accountId string, billingCycle string) (*models.RawBillSyncLog, error) {
	model := bizutils.DataSource().Model(ctx)

	var syncLog *models.RawBillSyncLog
	if err := model.Orm().Model(&models.RawBillSyncLog{}).
		Where("vendor = ? AND account_name = ? AND account_id = ? AND billing_cycle = ? AND stage = ? AND status = ?",
			vendor, accountName, accountId, billingCycle, jobSyncBill.Pull.String(), "finished").
		Find(&syncLog).Error; err != nil {
		return nil, err
	}

	if syncLog.ID == 0 {
		if err := model.Orm().Model(&models.RawBillSyncLog{}).
			Where("vendor = ? AND account_name = ? AND account_id = ? AND billing_cycle = ? AND stage = ? AND status != ?",
				vendor, accountName, accountId, billingCycle, jobSyncBill.Refinement.String(), "finished").
			Find(&syncLog).Error; err != nil {
			return nil, nil
		}
	}

	if syncLog.ID == 0 {
		syncLog = nil
	}
	return syncLog, nil
}

type Substitutor interface {
	Substitute(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error
}

type SubstitutionFunc func(context.Context, client.IClient, []*models.RefinedRawBill) error

func (s SubstitutionFunc) Substitute(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	return s(ctx, client, bills)
}

type SubstitutionConfig struct {
	PerPage    int // 从数据库取数时每页取多少条
	Concurrent int // 多少个协程同时处理数据
	Weight     int // 优先级
	SubstitutionFunc
}

type CloudProductSubstitution interface {
	GetClients(ctx context.Context) ([]client.IClient, error)
	GetSubstitutionConfigMap() map[string]SubstitutionConfig
}

type CloudProductSubstitutionTemplate struct {
	s CloudProductSubstitution
}

func (t *CloudProductSubstitutionTemplate) Execute(ctx context.Context) error {
	clients, err := t.s.GetClients(ctx)
	if err != nil {
		return err
	}

	// get all dates after the specified starting date, formats as "yyyy-mm-dd"
	billDates := billHandler.GetSyncMonthDate(ctx, 15)
	b := tools.NewBatch[client.IClient, error](ctx)
	b.Run(clients, func(ctx context.Context, client client.IClient) (error, error) {
		vendor := client.Vendor().String()
		accountName := client.Name()
		accountId := client.Identifier()
		for _, date := range billDates {
			syncLog, err := getPendingRefinedSyncLog(ctx, vendor, accountName, accountId, date)
			if err != nil {
				return nil, err
			}

			if syncLog == nil {
				continue
			}
			if err := Substitute(ctx, client, syncLog, t.s.GetSubstitutionConfigMap()); err != nil {
				log.Error(ctx, "substitute vendor: %s, accountName: %s, accountId: %s, billingCycle: %s failed: %s",
					vendor, accountName, accountId, date, err.Error())
				return nil, err
			}
		}
		return nil, nil
	})

	return nil
}

func Substitute(ctx context.Context, cli client.IClient,
	syncLog *models.RawBillSyncLog, productSubstitutionConfigMap map[string]SubstitutionConfig) error {
	m := bizutils.DataSource().Model(ctx)

	// 1、修改同步记录表状态
	billingCycle := syncLog.BillingCycle
	if err := m.Orm().Model(syncLog).
		Updates(map[string]interface{}{"stage": "refinement", "status": "started"}).Error; err != nil {
		log.Error(ctx, "update sync log failed: %s", err.Error())
		return err
	}

	// 2、task任务表新增记录，task_id采用pull任务成功的数据
	var task models.BillTask
	m.Orm().Model(&models.BillTask{}).
		Where("vendor = ? AND account_name = ? AND account_id = ? AND billing_cycle = ? AND stage = ? "+
			"AND status = ? order by create_time desc limit 1",
			cli.Vendor().String(), cli.Name(), cli.Identifier(), billingCycle, jobSyncBill.Pull.String(), 1).
		Find(&task)
	if task.ID == 0 {
		//return errors.New("no finished pull task found")
		log.Error(ctx, "no finished pull task found")
		return nil
	}

	billingCycleDate, _ := time.Parse(DateLayout, billingCycle)
	newTask := &models.BillTask{
		Vendor:       cli.Vendor().String(),
		AccountName:  cli.Name(),
		AccountID:    cli.Identifier(),
		BillingCycle: billingCycleDate,
		TaskID:       task.TaskID,
		Stage:        jobSyncBill.Refinement.String(),
		Status:       0,
	}
	if err := m.Orm().Create(newTask).Error; err != nil {
		log.Error(ctx, "create bill task of stage refinement failed: %s", err.Error())
		return err
	}

	// 3、获取需要处理的产品列表，以及每个产品对应的处理function
	hasWeight := false
	productCodes := make([]string, 0, len(productSubstitutionConfigMap))
	for productCode := range productSubstitutionConfigMap {
		if productSubstitutionConfigMap[productCode].Weight > 0 {
			hasWeight = true
		}

		productCodes = append(productCodes, productCode)
	}

	if hasWeight {
		// 如果有权重排序，按照顺序执行替换
		var sortedKv []kv
		for k, v := range productSubstitutionConfigMap {
			sortedKv = append(sortedKv, kv{k, v.Weight})
		}
		// 根据weight对productCode排序
		sort.Slice(sortedKv, func(i, j int) bool {
			return sortedKv[i].Weight < sortedKv[j].Weight
		})
		var sortedKeys []string
		for _, item := range sortedKv {
			sortedKeys = append(sortedKeys, item.Key)
		}

		for i, key := range sortedKeys {
			log.Info(ctx, "--> start to substitute product %s, weight: %d, index: %d", key, productSubstitutionConfigMap[key].Weight, i)
			err := doSubstitute(ctx, cli, &task, key, productSubstitutionConfigMap[key])
			log.Info(ctx, "--> ending substituting product %s, weight: %d, index: %d", key, productSubstitutionConfigMap[key].Weight, i)
			if err != nil {
				return err
			}
		}
	} else {
		// 多协程并发处理每种产品的替换逻辑
		b := tools.NewBatch[string, error](ctx)
		b.Run(productCodes, func(ctx context.Context, productCode string) (error, error) {
			err := doSubstitute(ctx, cli, &task, productCode, productSubstitutionConfigMap[productCode])
			if err != nil {
				return nil, err
			}
			return nil, nil
		})
	}

	// 3、操作完成后同一事务修改task表和sync表的状态
	if err := m.Orm().Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(syncLog).Update("status", "finished").Error; err != nil {
			return err
		}
		if err := tx.Model(&models.BillTask{}).
			Where("vendor = ? and account_name = ? and billing_cycle = ? and stage = ?",
				cli.Vendor().String(), cli.Name(), billingCycle, jobSyncBill.Refinement.String()).
			Update("status", 4).Error; err != nil {
			return err
		}

		if err := tx.Model(newTask).Update("status", 1).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}

	return nil
}

type kv struct {
	Key    string
	Weight int
}

func doSubstitute(ctx context.Context, cli client.IClient, task *models.BillTask, productCode string,
	substitutionConfig SubstitutionConfig) error {
	var wg sync.WaitGroup
	errChan := make(chan error, substitutionConfig.Concurrent) // 扩大容量
	concurrentController := make(chan struct{}, substitutionConfig.Concurrent)

	var errs []error         // 用于收集所有错误
	var errsMutex sync.Mutex // 保护errs的并发访问

	// 启动错误收集goroutine
	errorsDone := make(chan struct{})
	go func() {
		for err := range errChan {
			errsMutex.Lock()
			errs = append(errs, err)
			errsMutex.Unlock()
			log.Errorf(ctx, "Substitution error: %v", err)
			// 不中断主循环，继续收集错误
		}
		close(errorsDone)
	}()

	totalProcessed := 0
	maxId := 0
	maxIterations := 10000 // 安全措施，避免无限循环

	for i := 0; i < maxIterations; i++ {
		bills, err := queryPageByTaskIdAndProductCode(
			ctx, cli.Identifier(), task.TaskID, productCode, substitutionConfig.PerPage, maxId)
		if err != nil {
			log.Errorf(ctx, "Query failed: %v", err)
			errsMutex.Lock()
			errs = append(errs, err)
			errsMutex.Unlock()
			break
		}

		if len(bills) == 0 {
			log.Infof(ctx, "No more bills to process for product %s", productCode)
			break
		}

		maxId = int(bills[len(bills)-1].ID)
		totalProcessed += len(bills)

		log.Infof(ctx, "Processing page %d with maxId=%d, count=%d, total so far=%d",
			i+1, maxId, len(bills), totalProcessed)

		concurrentController <- struct{}{} // 占用一个并发控制槽
		wg.Add(1)                          // 增加 WaitGroup 计数
		go func(pageNum int, bills []*models.RefinedRawBill) {
			defer wg.Done()
			defer func() { <-concurrentController }()

			startTime := time.Now()
			err := substitutionConfig.Substitute(ctx, cli, bills)
			duration := time.Since(startTime)

			if err != nil {
				log.Errorf(ctx, "Page %d substitution failed after %v: %v",
					pageNum, duration, err)
				errChan <- err // 直接发送，不使用select
			} else {
				log.Infof(ctx, "Page %d substitution completed in %v",
					pageNum, duration)
			}
		}(i+1, bills)

		if len(bills) < substitutionConfig.PerPage {
			log.Infof(ctx, "Last page detected (got %d bills, less than page size %d)",
				len(bills), substitutionConfig.PerPage)
			break
		}
	}

	// 等待所有goroutine完成
	log.Infof(ctx, "Waiting for all %d pages to complete processing...", totalProcessed/substitutionConfig.PerPage+1)
	wg.Wait()

	// 关闭错误通道并等待错误收集完成
	close(errChan)
	<-errorsDone

	// 汇总结果
	if len(errs) > 0 {
		log.Errorf(ctx, "Completed with %d errors out of %d pages",
			len(errs), totalProcessed/substitutionConfig.PerPage+1)
		// 可以选择返回第一个错误，或者聚合所有错误
		return fmt.Errorf("substitution completed with %d errors, first: %v",
			len(errs), errs[0])
	}

	log.Infof(ctx, "Successfully processed all %d bills for product %s", totalProcessed, productCode)

	<-errorsDone

	if len(errs) > 0 {
		return fmt.Errorf("errors during substitution: %v", errs)
	}

	return nil
}
