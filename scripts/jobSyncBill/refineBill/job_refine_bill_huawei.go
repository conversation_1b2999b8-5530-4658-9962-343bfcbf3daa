package refineBill

import (
	"context"
	"encoding/json"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"

	billModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/bss/v2/model"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

const (
	huaweiCloudDisk = "hws.service.type.ebs"
	huaweiEss       = "hws.service.type.ess"     // 华为搜索服务
	huaweiBigData   = "hws.service.type.bigdata" // MapReduce服务
	huaweiDms       = "hws.service.type.dms"     // 分布式消息服务
)

var huaweiClientProjectMap = make(map[string]map[string]model.ProjectResult)

func NewRefineBillHuawei() *RefineBillHuawei {
	return &RefineBillHuawei{
		JobBase: base.NewJobBase("BILL_REFINE_HUAWEI",
			"华为云账单精细化",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type RefineBillHuawei struct {
	*base.JobBase
}

func (*RefineBillHuawei) Name() string {
	return base.TaskRefineBillHuawei
}

func (r *RefineBillHuawei) Run(ctx context.Context) (map[string]any, error) {
	// 初始化project map
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.HuaweiCloud},
		hbc.IAM,
		[]string{bizutils.PurposeAdmin},
		actfilter.SetIgnoreVendor(hbc.Private, hbc.TencentCloud, hbc.AWS, hbc.Azure, hbc.JXJG), // 忽略不支持的云
	)
	if err != nil {
		return nil, err
	}

	b := tools.NewBatch[client.IClient, []model.ProjectResult](ctx)
	b.Run(defaultClients, func(ctx context.Context, cli client.IClient) ([]model.ProjectResult, error) {
		defClient := cli.(*huaweicloud.IamClient)
		projects, err := defClient.ListProjects(ctx)
		if err != nil {
			return nil, bizutils.WarpClientError(defClient, err)
		}

		projectMap, ok := huaweiClientProjectMap[cli.Name()]
		if !ok {
			projectMap = make(map[string]model.ProjectResult)
		}

		for _, project := range projects {
			projectMap[project.Name] = project
		}

		huaweiClientProjectMap[cli.Name()] = projectMap
		return nil, nil
	})

	t := &CloudProductSubstitutionTemplate{s: &HuaweiSubstitution{}}
	if err := t.Execute(ctx); err != nil {
		return nil, err
	}

	return nil, nil
}

type HuaweiSubstitution struct {
}

func (s *HuaweiSubstitution) GetClients(ctx context.Context) ([]client.IClient, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.HuaweiCloud},
		hbc.IAM,
		[]string{bizutils.PurposeAdmin},
		actfilter.SetIgnoreVendor(hbc.Private, hbc.TencentCloud, hbc.AWS, hbc.Azure, hbc.JXJG), // 忽略不支持的云
	)
	if err != nil {
		return nil, err
	}

	return defaultClients, nil
}

func (s *HuaweiSubstitution) GetSubstitutionConfigMap() map[string]SubstitutionConfig {
	return map[string]SubstitutionConfig{
		huaweiCloudDisk: {
			SubstitutionFunc: HuaweiCloudDiskSubstitutor,
			PerPage:          100,
			Concurrent:       10,
		},
		huaweiEss: {
			SubstitutionFunc: HuaweiEssSubstitutor,
			PerPage:          100,
			Concurrent:       10,
		},
		huaweiBigData: {
			SubstitutionFunc: HuaweiBigDataSubstitutor,
			PerPage:          100,
			Concurrent:       10,
		},
		huaweiDms: {
			SubstitutionFunc: HuaweiDmsSubstitutor,
			PerPage:          100,
			Concurrent:       10,
		},
	}
}

func HuaweiDmsSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	substitutionBills := make([]*models.RefinedRawBill, 0)
	for _, bill := range bills {
		var item *billModel.MonthlyBillRes
		if err := json.Unmarshal([]byte(bill.Content), &item); err != nil {
			return err
		}

		if *item.ResourceTypeCode == "hws.resource.type.dms.storage" {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.InstanceID = *item.ParentResourceId
			bill.ProductCode = huaweiDms
			bill.ProductName = "分布式消息服务"
			substitutionBills = append(substitutionBills, bill)
		}
	}
	return updateRefinedRawBill(ctx, substitutionBills)
}

func HuaweiBigDataSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	substitutionBills := make([]*models.RefinedRawBill, 0)
	for _, bill := range bills {
		var item *billModel.MonthlyBillRes
		if err := json.Unmarshal([]byte(bill.Content), &item); err != nil {
			return err
		}

		if *item.ResourceTypeCode == "hws.resource.type.volume" ||
			*item.ResourceTypeCode == "hws.resource.type.vm" ||
			*item.ResourceTypeCode == "hws.resource.type.mrs.lts.sc" ||
			*item.ResourceTypeCode == "hws.resource.type.mrs.basic.sc" {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.InstanceID = *item.ParentResourceId
			bill.ProductCode = huaweiBigData
			bill.ProductName = "MapReduce服务"
			substitutionBills = append(substitutionBills, bill)
		}
	}
	return updateRefinedRawBill(ctx, substitutionBills)
}

func HuaweiEssSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	substitutionBills := make([]*models.RefinedRawBill, 0)
	for _, bill := range bills {
		var item *billModel.MonthlyBillRes
		if err := json.Unmarshal([]byte(bill.Content), &item); err != nil {
			return err
		}

		if *item.ResourceTypeCode == "hws.resource.type.ess.volume" ||
			*item.ResourceTypeCode == "hws.resource.type.ess.bandwidth" {
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.InstanceID = *item.ParentResourceId
			bill.ProductCode = huaweiEss
			bill.ProductName = "云搜索服务"
			substitutionBills = append(substitutionBills, bill)
		}
	}
	return updateRefinedRawBill(ctx, substitutionBills)
}

func HuaweiCloudDiskSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	substitutionBills := make([]*models.RefinedRawBill, 0)
	for _, bill := range bills {
		var item *billModel.MonthlyBillRes
		if err := json.Unmarshal([]byte(bill.Content), &item); err != nil {
			return err
		}

		if *item.ResourceTypeCode == "hws.resource.type.volume" {
			if item.ParentResourceId == nil {
				continue
			}
			bill.SubInstanceId = bill.InstanceID
			bill.SubProductCode = bill.ProductCode
			bill.SubProductName = bill.ProductName
			bill.InstanceID = *item.ParentResourceId
			bill.ProductCode = huaweiCloudDisk
			bill.ProductName = "弹性云服务器"
			substitutionBills = append(substitutionBills, bill)
		}
	}
	return updateRefinedRawBill(ctx, substitutionBills)
}
