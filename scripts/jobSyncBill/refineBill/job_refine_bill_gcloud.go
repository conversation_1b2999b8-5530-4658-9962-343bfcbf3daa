package refineBill

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewRefineBillGcloud() *RefineBillGcloud {
	return &RefineBillGcloud{
		base.NewJobBase("BILL_REFINE_GCLOUD",
			"谷歌云账单精细化",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource()),
	}
}

type RefineBillGcloud struct {
	*base.JobBase
}

type GcloudSubstitution struct{}

func (b *RefineBillGcloud) Name() string {
	return base.TaskRefineBillGcloud
}

func (g *RefineBillGcloud) Run(ctx context.Context) (map[string]any, error) {
	t := &CloudProductSubstitutionTemplate{
		s: &GcloudSubstitution{},
	}

	if err := t.Execute(ctx); err != nil {
		return nil, err
	}

	return nil, nil
}

func (g *GcloudSubstitution) GetClients(ctx context.Context) ([]client.IClient, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.GoogleCloud},
		hbc.Bill,
		[]string{bizutils.PurposeBill},
	)
	if err != nil {
		return nil, err
	}

	return defaultClients, nil
}

func (g *GcloudSubstitution) GetSubstitutionConfigMap() map[string]SubstitutionConfig {
	return map[string]SubstitutionConfig{}
}
