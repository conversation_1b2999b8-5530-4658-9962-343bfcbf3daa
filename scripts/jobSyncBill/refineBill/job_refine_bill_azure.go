package refineBill

import (
	"context"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"strings"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

const (
	azureCloudDisk = "microsoft.computedisks"
)

func NewRefineBillAzure() *RefineBillAzure {
	return &RefineBillAzure{
		base.NewJobBase("BILL_REFINE_AZURE",
			"微软云账单精细化",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource()),
	}
}

type RefineBillAzure struct {
	*base.JobBase
}

func (*RefineBillAzure) Name() string {
	return base.TaskRefineBillAzure
}

func (r *RefineBillAzure) Run(ctx context.Context) (map[string]any, error) {
	t := &CloudProductSubstitutionTemplate{
		s: &AzureSubstitution{},
	}

	if err := t.Execute(ctx); err != nil {
		return nil, err
	}

	return nil, nil
}

type AzureSubstitution struct {
}

func (s *AzureSubstitution) GetClients(ctx context.Context) ([]client.IClient, error) {
	return base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.Azure},
		hbc.Disk,
		[]string{bizutils.PurposeAdmin})
}

func (s *AzureSubstitution) GetSubstitutionConfigMap() map[string]SubstitutionConfig {
	return map[string]SubstitutionConfig{
		azureCloudDisk: {
			SubstitutionFunc: AzureCloudDiskSubstitutor,
			PerPage:          100,
			Concurrent:       5,
		},
	}
}

func AzureCloudDiskSubstitutor(ctx context.Context, client client.IClient, bills []*models.RefinedRawBill) error {
	refinedBills := make([]*models.RefinedRawBill, 0, 100)
	for _, bill := range bills {
		refinedBill, _ := refineAzure(ctx, bill, client)
		if refinedBill != nil {
			refinedBills = append(refinedBills, refinedBill)
		}
	}

	if len(refinedBills) == 0 {
		return nil
	}
	return updateRefinedRawBill(ctx, refinedBills)
}

func refineAzure(ctx context.Context, bill *models.RefinedRawBill, client client.IClient) (*models.RefinedRawBill, error) {
	diskClient := hybrid.AccountManager().AzureDiskClient(
		ctx,
		client.Name(),
		bizutils.PurposeAdmin,
		client.Region(),
		client.TenantId(),
		client.Identifier(),
	)
	disk, err := diskClient.Get(ctx, bill.ResourceGroup, bill.InstanceID)
	if err != nil {
		return nil, err
	}
	if disk == nil || disk.ManagedBy == nil {
		log.Warnf(ctx, "Azure disk %s not found or managedBy is nil", bill.InstanceID)
		return nil, nil
	}
	bill.SubInstanceId = bill.InstanceID
	bill.SubProductCode = bill.ProductCode
	bill.SubProductName = bill.ProductName
	bill.InstanceID = getVmInstanceId(*disk.ManagedBy)
	bill.ProductCode = azureCloudDisk
	bill.ProductName = "云服务器"
	return bill, nil
}

func getVmInstanceId(id string) string {
	///subscriptions/f48e0791-0e8d-4576-a341-82306fe35221/resourceGroups/hdop-prod/providers/Microsoft.Compute/virtualMachines/hdop-vm-prod01
	var instanceId string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "virtualMachines" {
			instanceId = s[n+1]
		}
	}
	return instanceId
}
