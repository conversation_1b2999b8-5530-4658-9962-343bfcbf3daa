package retrieveBill

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"gorm.io/gorm"
)

func getSyncLogLastDay(ctx context.Context, vendor hbc.CloudVendor) (billingCycle string, err error) {
	model := bizutils.DataSource().Model(ctx)
	syncLog := &models.RawBillSyncLog{}
	if err = model.Orm().Model(syncLog).Where("vendor=?", vendor).Last(syncLog).Order("billing_cycle desc").Limit(1).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return "", err
		}
	}
	return syncLog.BillingCycle, nil
}

func updatePrePullStarted(ctx context.Context, lastMonth string, vendor hbc.CloudVendor) error {
	model := bizutils.DataSource().Model(ctx)
	return model.Orm().Model(&models.RawBillSyncLog{}).Where(fmt.Sprintf("vendor =? and billing_cycle like '%s%%'", lastMonth), vendor).
		Updates(models.RawBillSyncLog{Status: billHandler.SYNC_LOG_STATUS_START, Stage: billHandler.SYNC_LOG_STAGE_PRE_PULL}).Error
}

func currentMonth() string {
	return time.Now().Format(time.DateOnly)
}

func lastMonth() string {
	return time.Now().AddDate(0, -1, 0).Format(billHandler.MonthTimeFormat)
}

func matchMonth(ctx context.Context, vendor hbc.CloudVendor) (bool, error) {
	logDay, err := getSyncLogLastDay(ctx, vendor)
	if err != nil {
		return false, err
	}
	if logDay == "" {
		return false, nil
	}
	yearMonth := 7
	return currentMonth()[:yearMonth] == logDay[:yearMonth], nil
}
