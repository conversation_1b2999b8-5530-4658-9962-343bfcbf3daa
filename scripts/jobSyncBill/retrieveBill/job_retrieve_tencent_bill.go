package retrieveBill

import (
	"context"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/ops-golang-common/hbc"
)

func NewTencentRetrieveBill() *SyncTencentBill {
	return &SyncTencentBill{
		JobBase: base.NewJobBase("TENCENT_BILL_RETRIEVE",
			"同步已稳定腾讯云账单",
			base.NewSchedule(
				base.WithDay(2),
				base.WithHour(21),
				base.WithMin(0),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncTencentBill struct {
	*base.JobBase
}

func (s *SyncTencentBill) Name() string {
	return base.TaskRetrieveTencentBill
}

func (s *SyncTencentBill) Run(ctx context.Context) (map[string]any, error) {
	// 重置syncLog腾讯云上个月的账单pre-pull started
	matched, err := matchMonth(ctx, hbc.TencentCloud)
	if err != nil {
		return nil, err
	}
	if !matched {
		s.Infof(ctx, "tencent bill retrieve month not matched")
	}
	return nil, updatePrePullStarted(ctx, lastMonth(), hbc.TencentCloud)
}
