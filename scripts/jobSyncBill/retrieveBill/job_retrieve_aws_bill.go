package retrieveBill

import (
	"context"
	"fmt"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/pullBill"
	"git.haier.net/devops/ops-golang-common/hbc"
)

// 每月7号上月数据稳定,时差9点开始同步.
func NewAwsRetrieveBill() *SyncAwsBill {
	return &SyncAwsBill{
		JobBase: base.NewJobBase("AWS_BILL_RETRIEVE",
			"同步已稳定亚马逊云账单",
			base.NewSchedule(
				base.WithDay(7),
				base.WithHour(9),
				base.WithMin(0),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncAwsBill struct {
	*base.JobBase
}

func (s *SyncAwsBill) Name() string {
	return base.TaskRetrieveAwsBill
}

func (s *SyncAwsBill) Run(ctx context.Context) (map[string]any, error) {
	// 重置syncLog亚马逊上个月的账单pre-pull started
	matched, err := matchMonth(ctx, hbc.AWS)
	if err != nil {
		return nil, err
	}
	if !matched {
		s.Infof(ctx, "aws bill retrieve month not matched")
	}
	// drop last month table & pull aws bill
	lastMonth := lastMonth()
	model := bizutils.DataSource().Model(ctx)
	tables, err := model.Orm().Migrator().GetTables()
	if err != nil {
		return nil, err
	}
	// check table exist
	var drop bool
	for _, table := range tables {
		if table == fmt.Sprintf("bc_aws_bill_%s", lastMonth) {
			drop = true
			break
		}
	}
	if drop {
		if err := model.Orm().Exec(fmt.Sprintf("DELETE FROM `bc_aws_bill_%s`", lastMonth)).Error; err != nil {
			return nil, err
		}
	}
	ctx = context.WithValue(ctx, billHandler.CurrentMonthFlag(billHandler.BillCurrentMonthFlag), billHandler.BillCurrentMonthNotExecute)
	if _, err := pullBill.NewAwsDownloadBill().Run(ctx); err != nil {
		return nil, err
	}
	return nil, updatePrePullStarted(ctx, lastMonth, hbc.AWS)
}
