package retrieveBill

import (
	"context"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/ops-golang-common/hbc"
)

func NewHuaweiRetrieveBill() *SyncHuaweiBill {
	return &SyncHuaweiBill{
		JobBase: base.NewJobBase("HUAWEI_BILL_RETRIEVE",
			"同步已稳定华为云账单",
			base.NewSchedule(
				base.WithDay(4),
				base.WithHour(1),
				base.WithMin(0),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncHuaweiBill struct {
	*base.JobBase
}

func (s *SyncHuaweiBill) Name() string {
	return base.TaskRetrieveHuaweiBill
}

func (s *SyncHuaweiBill) Run(ctx context.Context) (map[string]any, error) {
	// 重置syncLog华为云上个月的账单pre-pull started
	matched, err := matchMonth(ctx, hbc.HuaweiCloud)
	if err != nil {
		return nil, err
	}
	if !matched {
		s.Infof(ctx, "huawei bill retrieve month not matched")
	}
	return nil, updatePrePullStarted(ctx, lastMonth(), hbc.HuaweiCloud)
}
