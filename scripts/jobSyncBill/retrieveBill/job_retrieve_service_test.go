package retrieveBill

import (
	"context"
	"fmt"
	"testing"

	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/stretchr/testify/assert"
)

func TestRules_MatchMonth(t *testing.T) {
	a := assert.New(t)
	rule, err := matchMonth(context.Background(), hbc.AliCloud)
	fmt.Println(utils.JsonString(rule))
	a.NoError(err)
}

func TestRules_UpdatePrePullStarted(t *testing.T) {
	a := assert.New(t)
	err := updatePrePullStarted(context.Background(), "2024-12", hbc.AliCloudDedicated)
	a.NoError(err)
}

func Test_redis_scan(t *testing.T) {
	var cursor uint64
	var count int
	conn := c.RedisRawConn()
	//bill:gcloud:gcloud-1:2025-07-22:*
	prefix := fmt.Sprintf(billHandler.RedisKeyPrefix+"*", "gcloud", "gcloud-1", "2025-07-22")
	for {
		keys, nextCursor, err := conn.Scan(context.Background(), cursor, prefix, 300).Result()
		if err != nil {
			fmt.Println(err)
		}
		count = count + len(keys)
		if nextCursor == 0 {
			break
		}
		cursor = nextCursor
	}
	fmt.Println(count)
}
