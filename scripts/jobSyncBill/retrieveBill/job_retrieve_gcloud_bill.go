package retrieveBill

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/pullBill"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/googlecloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/go-redsync/redsync/v4/redis"
	"golang.org/x/sync/errgroup"
)

const (
	monthSql = "select * " +
		"from `s02166-hcms.billing_data.gcp_billing_export_resource_v1_01EFBF_FE25D9_1F8A1C` \n" +
		"where \n" +
		" invoice.month='%s'\n" +
		" order by usage_start_time asc"
)

var (
	c = config.Global()
)

func NewGoogleCloudRetrieveBill() *SyncGoogleCloudBill {
	return &SyncGoogleCloudBill{
		JobBase: base.NewJobBase("GOOGLECLOUD_BILL_RETRIEVE",
			"同步已稳定谷歌云账单",
			base.NewSchedule(
				base.WithDay(5),
				base.WithHour(1),
				base.WithMin(0),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncGoogleCloudBill struct {
	*base.JobBase
}

func (s *SyncGoogleCloudBill) Name() string {
	return base.TaskRetrieveGoogleCloudBill
}

func (s *SyncGoogleCloudBill) Run(ctx context.Context) (map[string]any, error) {
	// 重置syncLog谷歌云上个月的账单pre-pull started
	matched, err := matchMonth(ctx, hbc.GoogleCloud)
	if err != nil {
		return nil, err
	}
	if !matched {
		s.Infof(ctx, "gcloud bill retrieve month not matched")
	}

	month := lastMonth()
	if err := pullGoogleCloudMonthBill(month, ctx); err != nil { // 拉取上月账单到redis后进行sync_log更新
		return nil, err
	}

	if err := updatePrePullStarted(ctx, month, hbc.GoogleCloud); err != nil {
		return nil, err
	}

	// pull bill
	ctx = context.WithValue(ctx, billHandler.CurrentMonthFlag(billHandler.BillCurrentMonthFlag), billHandler.BillLastMonthExecute)
	return pullBill.NewGoogleCloudSyncBill().Run(ctx)
}

// download last month bill to redis & pull bill from redis
func pullGoogleCloudMonthBill(monthDay string, ctx context.Context) error {

	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.GoogleCloud}, hbc.Bill, []string{bizutils.PurposeBill})
	if err != nil {
		return err
	}

	d := strings.Split(monthDay, "-")
	year, month := d[0], d[1]
	sql := fmt.Sprintf(monthSql, year+month)

	for _, c := range defaultClients {
		receivedChan := make(chan *googlecloud.GoogleScheme)
		done := make(chan struct{})
		defer close(done)

		g, _ := errgroup.WithContext(ctx)
		g.Go(func() error {
			return handle(monthDay, c.Identifier(), receivedChan) // cache redis
		})
		g.Go(func() error {
			return c.(*googlecloud.BillingClient).QueryBills(ctx, sql, done, receivedChan) // query month bill
		})

		if err := g.Wait(); err != nil {
			return err
		}
	}
	return nil
}

// cache redis
func handle(date, accountId string, billChan chan *googlecloud.GoogleScheme) error {
	var total int64
	conn, err := c.RedisConn()
	if err != nil {
		return err
	}
	defer conn.Close()

	// get last day of last month
	t, _ := time.Parse(billHandler.MonthTimeFormat, date)
	lastDay := t.AddDate(0, 1, -1).Format(time.DateOnly)

	for {
		bill, ok := <-billChan
		if !ok {
			return nil
		}

		total = total + 1
		billingCycle := bill.UsageStartTime.Format(time.DateOnly)
		if t.Month() != bill.UsageStartTime.Month() {
			billingCycle = lastDay // 处理会产生在次月的账单设置为当月最后一天
		}

		// set redis
		key := fmt.Sprintf(billHandler.RedisKeyPrefix+"%s", billHandler.GoogleName, accountId, billingCycle, fmt.Sprintf("data%d", total))
		if err := atomicSetWithExpire(conn, key, utils.JsonString(bill), time.Hour*3); err != nil {
			return err
		}
	}
}

var setWithExpireScript = redis.NewScript(1,
	`redis.call('SET', KEYS[1], ARGV[1])
	 redis.call('EXPIRE', KEYS[1], ARGV[2])
	 return 1`)

// atomicSetWithExpire 原子性设置值并更新过期时间
func atomicSetWithExpire(conn redis.Conn, key, value string, expiry time.Duration) error {
	expireSeconds := int(expiry.Seconds())
	_, err := conn.Eval(
		setWithExpireScript,
		key,           // KEYS[1]
		value,         // ARGV[1]
		expireSeconds, // ARGV[2]
	)
	return err
}
