package retrieveBill

import (
	"context"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/ops-golang-common/hbc"
)

func NewAliyunRetrieveBill() *SyncAliyunBill {
	return &SyncAliyunBill{
		JobBase: base.NewJobBase("ALIYUN_BILL_RETRIEVE",
			"同步已稳定阿里云账单",
			base.NewSchedule(
				base.WithDay(7),
				base.WithHour(1),
				base.WithMin(0),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncAliyunBill struct {
	*base.JobBase
}

func (s *SyncAliyunBill) Name() string {
	return base.TaskRetrieveAliyunBill
}

func (s *SyncAliyunBill) Run(ctx context.Context) (map[string]any, error) {
	// 重置syncLog阿里云上个月的账单pre-pull started
	matched, err := matchMonth(ctx, hbc.AliCloud)
	if err != nil {
		return nil, err
	}
	if !matched {
		s.Infof(ctx, "aliyun bill retrieve month not matched")
	}
	lastMonth := lastMonth()
	if err = updatePrePullStarted(ctx, lastMonth, hbc.AliCloud); err != nil {
		return nil, err
	}
	// 重置syncLog阿里专属云上个月的账单pre-pull started
	matched, err = matchMonth(ctx, hbc.AliCloudDedicated)
	if err != nil {
		return nil, err
	}
	if !matched {
		s.Infof(ctx, "aliyun_dedicated bill retrieve month not matched")
	}
	return nil, updatePrePullStarted(ctx, lastMonth, hbc.AliCloudDedicated)
}
