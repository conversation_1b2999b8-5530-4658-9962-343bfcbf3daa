package pullBill

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
)

func NewHuaweicloudSyncBill() *SyncHuaweicloudBill {
	return &SyncHuaweicloudBill{
		JobBase: base.NewJobBase("HUAWEICLOUD_BILL_SYNC",
			"华为云同步账单",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncHuaweicloudBill struct {
	*base.JobBase
}

func (s *SyncHuaweicloudBill) Name() string {
	return base.TaskSyncHuaweiyunBill
}

func (s *SyncHuaweicloudBill) Run(ctx context.Context) (map[string]any, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.HuaweiCloud},
		hbc.Bill, []string{bizutils.PurposeBill}, actfilter.SetRegion(hbc.HuaweiCloud, "cn-north-1"),
	)
	if err != nil {
		return nil, err
	}

	// 同步云账单信息
	b := tools.NewBatch[client.IClient, error](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) (error, error) {
		s.Infof(ctx, "get huaweicloud client %s:%s", input.Name(), input.Product())
		return nil, billHandler.NewSyncBillManager(ctx, input).Handle()
	})

	if err := b.Error(); err != nil {
		s.Errorf(ctx, "huaweicloud sync bill failed, cause: %s", err)
		return nil, err
	}

	return nil, nil
}
