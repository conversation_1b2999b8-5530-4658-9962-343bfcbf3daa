package pullBill

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"github.com/aws/aws-sdk-go/aws/endpoints"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
)

func NewAwsDownloadBill() *SyncAwsBill {
	return &SyncAwsBill{
		JobBase: base.NewJobBase("AWS_BILL_DOWNLOAD",
			"亚马逊下载账单",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncAwsBill struct {
	*base.JobBase
}

func (s *SyncAwsBill) Name() string {
	return base.TaskAwsDownloadBill
}

func (s *SyncAwsBill) Run(ctx context.Context) (map[string]any, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.AWS}, hbc.Bill, []string{bizutils.PurposeBill},
		actfilter.SetRegion(hbc.AWS, endpoints.ApNortheast2RegionID))
	// defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.AWS}, hbc.Bill, []string{bizutils.PurposeBill},
	// 	actfilter.SetRegion(hbc.AWS, endpoints.ApSoutheast1RegionID)) //test账户专用
	if err != nil {
		return nil, err
	}
	// 创建数据库表
	value := ctx.Value(billHandler.CurrentMonthFlag(billHandler.BillCurrentMonthFlag))
	if value == nil {
		value = billHandler.BillCurrentMonthExecute
	}
	if err = billHandler.CreateTables(value.(string)); err != nil {
		s.Errorf(ctx, "AWS sync bill create tables failed, cause: %s", err)
		return nil, err
	}
	// 同步云账单信息
	b := tools.NewBatch[client.IClient, error](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) (error, error) {
		s.Infof(ctx, "get AWS client %s:%s", input.Name(), input.Product())
		return nil, billHandler.NewSyncAwsDownloadBillReport(input)
	})

	if err := b.Error(); err != nil {
		s.Errorf(ctx, "AWS sync bill failed, cause: %s", err)
		return nil, err
	}

	return nil, nil
}
