package pullBill

import (
	"context"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
)

func NewTencentCloudSyncBill() *SyncTencentCloudBill {
	return &SyncTencentCloudBill{
		JobBase: base.NewJobBase("TENCENTCLOUD_BILL_SYNC",
			"腾讯云同步账单",
			base.NewSchedule(
				base.WithHour(21),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncTencentCloudBill struct {
	*base.JobBase
}

func (s *SyncTencentCloudBill) Name() string {
	return base.TaskSyncTencentBill
}

func (s *SyncTencentCloudBill) Run(ctx context.Context) (map[string]any, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.TencentCloud}, hbc.Bill, []string{bizutils.PurposeBill})
	if err != nil {
		return nil, err
	}

	// 同步云账单信息
	b := tools.NewBatch[client.IClient, error](ctx, batch.WithBatchSize(5))
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) (error, error) {
		s.Infof(ctx, "get tencentcloud client %s:%s", input.Name(), input.Product())
		return nil, billHandler.NewSyncBillManager(ctx, input).Handle()
	})

	if err := b.Error(); err != nil {
		s.Errorf(ctx, "tencentcloud sync bill failed, cause: %s", err)
		return nil, err
	}

	return nil, nil
}
