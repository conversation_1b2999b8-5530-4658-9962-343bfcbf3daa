package pullBill

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
)

func NewAzureSyncBill() *SyncAzureBill {
	return &SyncAzureBill{
		JobBase: base.NewJobBase("AZURE_BILL_SYNC",
			"微软云同步账单",
			"0 0 0/2 * * ? *", // 每两小时执行一次
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncAzureBill struct {
	*base.JobBase
}

func (s *SyncAzureBill) Name() string {
	return base.TaskSyncAzureBill
}

func (s *SyncAzureBill) Run(ctx context.Context) (map[string]any, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.Azure}, hbc.Bill, []string{bizutils.PurposeBill})
	if err != nil {
		return nil, err
	}
	// 同步云账单信息
	b := tools.NewBatch[client.IClient, error](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) (error, error) {
		s.Infof(ctx, "get azure client %s:%s", input.Name(), input.Product())
		return nil, billHandler.NewSyncBillManager(ctx, input).Handle()
	})

	if err := b.Error(); err != nil {
		s.Errorf(ctx, "azure sync bill failed, cause: %s", err)
		return nil, err
	}

	return nil, nil
}
