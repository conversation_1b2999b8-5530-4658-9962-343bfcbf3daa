package pullBill

// import (
// 	"context"

// 	"git.haier.net/devops/ops-golang-common/hbc"
// 	"git.haier.net/devops/ops-golang-common/sdk/client"
// 	"github.com/aws/aws-sdk-go/aws/endpoints"

// 	"git.haier.net/devops/hcms-task-center/biz/actfilter"
// 	"git.haier.net/devops/hcms-task-center/biz/bizutils"
// 	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
// 	"git.haier.net/devops/hcms-task-center/core/tools"
// 	"git.haier.net/devops/hcms-task-center/scripts/base"
// 	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
// )

// func NewAwsCompareBill() *CompareAwsBill {
// 	return &CompareAwsBill{
// 		JobBase: base.NewJobBase("AWS_BILL_COMPARE",
// 			"亚马逊比对账单",
// 			base.NewSchedule(
// 				base.WithHour(0),
// 				base.WithMin(0),
// 				base.WithSec(3),
// 			),
// 			taskmodels.TaskCategoryResource,
// 			bizutils.DataSource(),
// 		),
// 	}
// }

// type CompareAwsBill struct {
// 	*base.JobBase
// }

// func (s *CompareAwsBill) Name() string {
// 	return base.TaskAwsCompareBill
// }

// func (s *CompareAwsBill) Run(ctx context.Context) (map[string]any, error) {
// 	// 获取默认环境的云账号
// 	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.AWS}, hbc.Bill, []string{bizutils.PurposeAccountCheck},
// 		actfilter.SetRegion(hbc.AWS, endpoints.ApNortheast2RegionID))
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 同步云账单信息
// 	b := tools.NewBatch[client.IClient, error](ctx)
// 	b.Run(defaultClients, func(ctx context.Context, input client.IClient) (error, error) {
// 		s.Infof(ctx, "get AWS client %s:%s", input.Name(), input.Product())
// 		return nil, billHandler.NewCompareAwsBill(input)
// 	})

// 	if err := b.Error(); err != nil {
// 		s.Errorf(ctx, "AWS compare bill failed, cause: %s", err)
// 		return nil, err
// 	}

// 	return nil, nil
// }
