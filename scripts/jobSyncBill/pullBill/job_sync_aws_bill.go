package pullBill

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
)

func NewAwsSyncBill() *SyncAwsCloudBill {
	return &SyncAwsCloudBill{
		JobBase: base.NewJobBase("AWS_BILL_SYNC",
			"亚马逊云同步账单",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type SyncAwsCloudBill struct {
	*base.JobBase
}

func (s *SyncAwsCloudBill) Name() string {
	return base.TaskSyncAwsBill
}

func (s *SyncAwsCloudBill) Run(ctx context.Context) (map[string]any, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.AWS}, hbc.Bill, []string{bizutils.PurposeBill})
	if err != nil {
		return nil, err
	}

	// 同步云账单信息
	b := tools.NewBatch[client.IClient, error](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) (error, error) {
		s.Infof(ctx, "get AWS client %s:%s", input.Name(), input.Product())
		return nil, billHandler.NewSyncBillManager(ctx, input).Handle()
	})

	if err := b.Error(); err != nil {
		s.Errorf(ctx, "AWS sync bill failed, cause: %s", err)
		return nil, err
	}

	return nil, nil
}
