package pullBill

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncBill/billHandler"
)

func NewAwsPricingBill() *PricingAwsBill {
	return &PricingAwsBill{
		JobBase: base.NewJobBase("AWS_BILL_PRICING",
			"亚马逊EC2价目表更新",
			base.NewSchedule(
				base.WithHour(0),
				base.WithMin(0),
				base.WithSec(3),
			),
			taskmodels.TaskCategoryResource,
			bizutils.DataSource(),
		),
	}
}

type PricingAwsBill struct {
	*base.JobBase
}

func (s *PricingAwsBill) Name() string {
	return base.TaskAwsPricingBill
}

func (s *PricingAwsBill) Run(ctx context.Context) (map[string]any, error) {
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, []hbc.CloudVendor{hbc.AWS}, hbc.Pricing, []string{bizutils.PurposeBill})
	if err != nil {
		return nil, err
	}
	var signalClient []client.IClient
	signalClient = append(signalClient, defaultClients[0])

	// 同步云账单信息
	b := tools.NewBatch[client.IClient, error](ctx)
	b.Run(signalClient, func(ctx context.Context, input client.IClient) (error, error) {
		s.Infof(ctx, "get AWS client %s:%s", input.Name(), input.Product())
		return nil, billHandler.NewSyncAwsDownloadPricingReport(input)
	})

	if err := b.Error(); err != nil {
		s.Errorf(ctx, "AWS download pricing failed, cause: %s", err)
		return nil, err
	}

	return nil, nil
}
