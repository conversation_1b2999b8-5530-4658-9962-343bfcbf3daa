package billHandler

import (
	"context"
	"fmt"
	"os"
	"sync"

	"github.com/google/uuid"
	"github.com/nacos-group/nacos-sdk-go/vo"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
	"golang.org/x/time/rate"
	"gorm.io/gorm"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils"
)

const (
	NACOS_GROUP_FORMAT      = "%s-%s"
	NACOS_ACCOUNT_COROUTINE = "coroutine"

	azure_china_region = "CHINA"
)

var awsRuleOnce sync.Once
var nacosConfig string
var splitRules map[string][]*models.CostUnitSplitRule // 目前只有阿里云在用,后续云不确定
var mtx = new(sync.Mutex)

// var taskMutex sync.Map
var tencentLimiter = rate.NewLimiter(2, 1) // 2 requests every 1 seconds

type SyncBillManager struct {
	ctx context.Context
	cli client.IClient
	// mtx        *sync.Mutex
	logger     *log.Logger
	syncLogMap sync.Map
	rule       *CostUnitComparison
}

func (m *SyncBillManager) once() (err error) {
	// 财富单元规则
	if m.rule, err = GetRule(m.ctx, m.cli.Vendor().String(), m.cli.Name()); err != nil {
		m.logger.Errorf(m.ctx, "sync_bill_manager GetRule failed: %v", err)
		return
	}
	// 财富单元分拆规则
	if splitRules, err = GetSplitRule(m.ctx); err != nil {
		m.logger.Errorf(m.ctx, "sync_bill_manager GetSplitRule failed: %v", err)
		return
	}

	// nacos获取账单数据多的账户启用多协程,错误忽略不开启账户多协程.
	nacosApi := api.NacosApi()
	if nacosApi == nil {
		return
	}
	nacosConfig, _ = nacosApi.GetConfig(vo.ConfigParam{
		Group:  fmt.Sprintf(NACOS_GROUP_FORMAT, config.Global().GetGroupPrefix(), NACOS_ACCOUNT_COROUTINE),
		DataId: m.cli.Vendor().String(),
	})

	return
}

func (m *SyncBillManager) Handle() (err error) {
	var coroutine bool // 是否开启账户多协程处理账单
	if err = m.once(); err != nil {
		return
	}
	// 配置转换失败忽略错误
	names, _ := utils.UnmarshalString[[]string](nacosConfig)
	for _, name := range names {
		if m.cli.Name() == name {
			coroutine = true
			break
		}
	}

	billingCycles := GetSyncMonthDate(m.ctx, 15)
	// aws,oracle,google获取数据只获取到前天
	if len(billingCycles) > 0 {
		if _, ok := beforeYesterdayVendorMap[string(m.cli.Vendor())]; ok {
			billingCycles = billingCycles[:len(billingCycles)-1]
		}
	}
	// 开启多协程拉取
	if coroutine {
		if err = tools.NewBatch[string, error](m.ctx).
			Run(billingCycles, func(ctx context.Context, billingCycle string) (error, error) {
				return nil, m.handle(billingCycle)
			}).Error(); err != nil {
			return
		}
	} else {
		for _, billingCycle := range billingCycles {
			// tencent限流5/s
			if m.cli.Vendor() == hbc.TencentCloud {
				if err = tencentLimiter.Wait(m.ctx); err != nil {
					return
				}
			}
			if err = m.handle(billingCycle); err != nil {
				return
			}
		}
	}
	return
}

func (m *SyncBillManager) handle(billingCycle string) (err error) {
	var syncLog *models.RawBillSyncLog
	if syncLog, err = RecordSyncLog(m.ctx, m.cli, billingCycle); err != nil {
		if errors.Is(err, ErrRecordsAlreadyExist) {
			err = nil
		}
		return
	}
	// pre-pull taskId no change
	var taskId string
	var exist bool
	if task, err := getBillTaskId(m.ctx, syncLog); err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		taskId = uuid.New().String()
	} else {
		taskId = task.TaskID
		exist = true
	}
	m.syncLogMap.Store(taskId, syncLog)
	done := make(chan struct{})

	g, gCtx := errgroup.WithContext(m.ctx)
	m.aliyun(gCtx, taskId, g, done)
	m.huawei(gCtx, taskId, g, done)
	m.tencent(gCtx, taskId, g, done)
	m.google(gCtx, taskId, g, done)
	m.oracle(gCtx, taskId, g, done)
	m.azure(gCtx, taskId, g, done)
	m.aws(gCtx, taskId, g, done)
	m.huawei_dedicated(gCtx, taskId, g, done)

	return m.doneSyncLog(g.Wait(), taskId, exist)
}

func (m *SyncBillManager) aliyun(ctx context.Context, taskId string, g *errgroup.Group, done chan struct{}) {
	if m.cli.Vendor() != hbc.AliCloud && m.cli.Vendor() != hbc.AliCloudDedicated {
		return
	}

	b := NewSyncBillAliyun(taskId, m.syncLog(taskId, false))
	receivedChan := b.ReceivedChan()

	g.Go(func() error {
		return b.CreateRawData(ctx, done, receivedChan)
	})
	g.Go(func() error {
		return b.PullBill(ctx, m.cli, done, receivedChan)
	})
}

func (m *SyncBillManager) huawei(ctx context.Context, taskId string, g *errgroup.Group, done chan struct{}) {
	if m.cli.Vendor() != hbc.HuaweiCloud {
		return
	}

	b := NewSyncBillHuaweicloud(taskId, m.syncLog(taskId, false), m.rule)
	receivedChan := b.ReceivedChan()

	g.Go(func() error {
		return b.CreateRawData(ctx, done, receivedChan)
	})
	g.Go(func() error {
		return b.PullBill(ctx, m.cli, done, receivedChan)
	})
}

func (m *SyncBillManager) huawei_dedicated(ctx context.Context, taskId string, g *errgroup.Group, done chan struct{}) {
	if m.cli.Vendor() != hbc.HuaweiCloudDedicated {
		return
	}

	b := NewSyncBillHuaweiDedicatedCloud(taskId, m.syncLog(taskId, false), m.rule)
	receivedChan := b.ReceivedChan()

	g.Go(func() error {
		return b.CreateRawData(ctx, done, receivedChan)
	})
	g.Go(func() error {
		return b.PullBill(ctx, m.cli, done, receivedChan)
	})

}

func (m *SyncBillManager) tencent(ctx context.Context, taskId string, g *errgroup.Group, done chan struct{}) {
	if m.cli.Vendor() != hbc.TencentCloud {
		return
	}

	b := NewSyncBillTencentCloud(taskId, m.syncLog(taskId, false), m.rule)
	receivedChan := b.ReceivedChan()

	g.Go(func() error {
		return b.CreateRawData(ctx, done, receivedChan)
	})
	g.Go(func() error {
		return b.PullBill(ctx, m.cli, done, receivedChan)
	})
}

func (m *SyncBillManager) google(ctx context.Context, taskId string, g *errgroup.Group, done chan struct{}) {
	if m.cli.Vendor() != hbc.GoogleCloud {
		return
	}

	b := NewSyncBillGoogleCloud(taskId, m.syncLog(taskId, false), m.rule)
	receivedChan := b.ReceivedChan()

	g.Go(func() error {
		return b.CreateRawData(ctx, done, receivedChan)
	})
	g.Go(func() error {
		return b.PullBill(ctx, m.cli, done, receivedChan)
	})
}

func (m *SyncBillManager) azure(ctx context.Context, taskId string, g *errgroup.Group, done chan struct{}) {
	if m.cli.Vendor() != hbc.Azure {
		return
	}
	// 只能获取最近13个月的数据
	// check, _ := time.Parse(DailyTimeFormat, m.syncLog(taskId, false).BillingCycle)
	// now := time.Now()
	// thirteenMonthsAgo := now.AddDate(0, -13, 0)
	// if !(check.After(thirteenMonthsAgo) && check.Before(now)) {
	// 	return
	// }
	if _, err := os.Stat(azure_name); os.IsNotExist(err) {
		os.Mkdir(azure_name, 0755)
	}

	var isChinaRegion bool
	if m.cli.Region() == azure_china_region {
		isChinaRegion = true
	}
	b := NewSyncBillAzureCloud(taskId, isChinaRegion, m.syncLog(taskId, false), m.rule)
	receivedChan := b.ReceivedChan()

	g.Go(func() error {
		return b.CreateRawData(ctx, done, receivedChan)
	})
	g.Go(func() error {
		return b.PullBill(ctx, m.cli, done, receivedChan)
	})
}

func (m *SyncBillManager) oracle(ctx context.Context, taskId string, g *errgroup.Group, done chan struct{}) {
	if m.cli.Vendor() != hbc.OracleCloud {
		return
	}
	b := NewSyncBillOracleCloud(taskId, m.syncLog(taskId, false), m.rule)
	receivedChan := b.ReceivedChan()

	g.Go(func() error {
		return b.CreateRawData(ctx, done, receivedChan)
	})
	g.Go(func() error {
		// data source from db
		if b.syncLogStageAt() == SYNC_LOG_STAGE_PULL {
			return findRawFromDB[models.RawBill](b.BaseSyncHandler, done, receivedChan)
		}
		return b.findOracleBillFromDB(ctx, done, receivedChan, m.cli.Name(), m.syncLog(taskId, false).BillingCycle)
	})
}

func (m *SyncBillManager) aws(ctx context.Context, taskId string, g *errgroup.Group, done chan struct{}) {
	if m.cli.Vendor() != hbc.AWS {
		return
	}
	b := NewSyncBillAwsCloud(taskId, &awsClient{
		Region: m.cli.Region(),
	}, m.syncLog(taskId, false), m.rule)

	awsRuleOnce.Do(func() {
		b.ruleFill()
	})
	receivedChan := b.ReceivedChan()

	g.Go(func() error {
		return b.CreateRawData(ctx, done, receivedChan)
	})
	g.Go(func() error {
		// data source from db
		if b.syncLogStageAt() == SYNC_LOG_STAGE_PULL {
			return findRawFromDB[models.RawBill](b.BaseSyncHandler, done, receivedChan)
		}
		return b.findAwsBillFromDB(ctx, done, receivedChan, m.cli.Identifier(), m.syncLog(taskId, false).BillingCycle)
	})
}

func (m *SyncBillManager) syncLog(taskId string, isDelete bool) *models.RawBillSyncLog {
	var v interface{}
	if isDelete {
		v, _ = m.syncLogMap.LoadAndDelete(taskId)
	} else {
		v, _ = m.syncLogMap.Load(taskId)
	}
	return v.(*models.RawBillSyncLog)
}

func (m *SyncBillManager) doneSyncLog(err error, taskId string, exist bool) error {
	mtx.Lock()
	defer mtx.Unlock()

	// mutex, _ := taskMutex.LoadOrStore(taskId, &sync.Mutex{})
	// mtx := mutex.(*sync.Mutex)
	// mtx.Lock()
	// defer func() {
	// 	mtx.Unlock()
	// 	taskMutex.Delete(taskId) // 清理锁资源
	// }()

	syncLog := m.syncLog(taskId, true)
	if syncLog == nil {
		return fmt.Errorf("syncLog not found for task: %s", taskId)
	}
	if err != nil {
		m.logger.Errorf(m.ctx, "Vendor:[%s] PullBill failed: %s", m.cli.Vendor(), err)
		syncLog.ErrMsg = fmt.Sprintf("%s\n", syncLog.ErrMsg+err.Error())
		return updateSyncLogError(m.ctx, syncLog)
	}
	return updateSyncLogFinishAndCreateBillTask(m.ctx, taskId, syncLog, exist)
}

func NewSyncBillManager(ctx context.Context, cli client.IClient) *SyncBillManager {
	return &SyncBillManager{
		ctx:    ctx,
		cli:    cli,
		logger: log.NewWithOption("SYNC_BILL_MANAGER", log.WithShowCaller(false)),
	}
}
