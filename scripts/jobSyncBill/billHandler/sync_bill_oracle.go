package billHandler

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/pager"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/oraclecloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
	"github.com/gofiber/fiber/v2/log"
)

const (
	oracle_name  = "oracle"
	oracle_table = "bc_oracle_bill"
)

var (
	PRODUCT_CODE_NETWORK       = "NETWORK"
	PRODUCT_CODE_BLOCK_STORAGE = "BLOCK_STORAGE"
	PRODUCT_CODE_TELEMETRY     = "TELEMETRY"

	FILTER_VNIC           = "vnic"
	FILTER_VCN            = "vcn"
	FILTER_DRGATTACHMENT  = "drgattachment"
	INSTANCE_VOLUMEBACKUP = "volumebackup"

	haier10Map = map[string]string{"haier10": "S04076"}

	dictOnce sync.Once
	dict     = make(map[string]string, 0)
)

type SyncBillOracleCloud struct {
	*BaseSyncHandler
	rule        *CostUnitComparison
	oracleScode scode
}

func (s *SyncBillOracleCloud) PullBill(ctx context.Context, c client.IClient, done chan struct{}, receivedChan chan *oraclecloud.OracleBillCSV) (err error) {
	defer s.done(err, done)

	if _, ok := c.(*oraclecloud.BillingClient); !ok {
		return fmt.Errorf("not %s billClient", oracle_name)
	}

	return c.(*oraclecloud.BillingClient).DownloadBillReport(ctx, s.syncLog.BillingCycle)
}

func (s *SyncBillOracleCloud) ToBillRawData(ctx context.Context, item *models.RawBill) []*models.RawBill {
	currency := func(currency string) string {
		if isEmpty(currency) {
			return RMB
		}
		return currency
	}
	bill := &models.RawBill{
		Model:        bizutils.DataSource().Model(ctx),
		TaskId:       s.taskId,
		Vendor:       hbc.OracleCloud.String(),
		AccountName:  s.syncLog.AccountName,
		AccountID:    s.syncLog.AccountID,
		Granularity:  BILL_GRANULARITY,
		BillingCycle: s.billingCycle(),

		ProductCode:       item.ProductCode,
		ProductName:       item.ProductName,
		ProductDetail:     item.ProductDetail,
		CostUnit:          s.scode(item.CostUnit, s.oracleScode.scodeMap),
		InstanceID:        item.InstanceID,
		Usage:             item.Usage,
		UsageUnit:         item.UsageUnit,
		ListPrice:         item.ListPrice,
		ListPriceUnit:     item.ListPriceUnit,
		ServicePeriodUnit: item.ServicePeriodUnit,
		// Cost:              item.Cost,
		PayableAmount: item.PayableAmount,
		// VoucherAmount:      ,
		Currency:      currency(item.Currency),
		BillingItem:   item.BillingItem,
		Region:        item.Region,
		Zone:          item.Zone,
		Content:       item.Content,
		ResourceGroup: item.ResourceGroup,
		// SupplementID:  item.SupplementID,
		AggregatedID: stringutil.Md5(s.syncLog.AccountID +
			item.ProductCode +
			item.InstanceID +
			item.SupplementID),
	}

	return []*models.RawBill{bill}
}

func (s *SyncBillOracleCloud) CreateRawData(ctx context.Context, done chan struct{}, receivedChan chan *models.RawBill) (err error) {
	var batch = []*models.RawBill{}

	for {
		raw, ok := <-receivedChan
		if !ok {
			close(done)
			return s.create(ctx, batch, s.rules)
		}
		batch = append(batch, s.ToBillRawData(ctx, raw)...)
		if len(batch) >= s.batchSize {
			if err = s.create(ctx, batch, s.rules); err != nil {
				close(done)
				return
			}
			batch = nil
		}
	}
}

func (s *SyncBillOracleCloud) rules(raw []*models.RawBill) (refinedRawBills []*models.RefinedRawBill) {
	dictOnce.Do(func() {
		if err := dictItem(); err != nil {
			log.Errorf("sync oracle bill dictItem() failed: %s", err.Error())
		}
	})

	// defer TimeCost("oracle bill rules")()
	refinedRawBills = make([]*models.RefinedRawBill, 0, len(raw))

	s.oracleScode.scodeMap = s.checkSCode(s.oracleScode)

	for _, r := range raw {
		refinedRawBill := &models.RefinedRawBill{TaskId: s.taskId, RawBill: *s.eraseModel(r)}
		if doFilter(refinedRawBill.ProductCode, refinedRawBill.InstanceID) {
			continue
		}
		refinedRawBill.ProductName = dict[refinedRawBill.ProductCode]
		// 费率处理
		// refinedRawBill.PayableAmount, _ = s.rate(refinedRawBill.PayableAmount, refinedRawBill.Currency)

		// 截取不到scode走规则
		var isRule bool
		if s.rule.Priority != nil {
			for _, p := range s.rule.Priority {
				switch p {
				case ORACLE_COMMODITY_CODE:
					refinedRawBill, isRule = s.priority(p, refinedRawBill.ProductCode, s.rule.Rules, refinedRawBill)
				case ORACLE_RULE_VALUE:
					refinedRawBill, isRule = s.priority(p, refinedRawBill.CostUnit, s.rule.Rules, refinedRawBill)
				}
			}
		}

		// 未匹配到规则
		if !isRule {
			// 区间（product/compartmentName）是haier10，haier10的归基础设施，这个规则是基于规则表未匹配上
			// 其它是S01466-hoportal、S00062 这两种，注意截取方式
			if v, ok := haier10Map[refinedRawBill.ResourceGroup]; ok {
				refinedRawBill.SCodeUnverified = v
				refinedRawBill.CostUnit = v
			} else if bizutils.IsSCode(refinedRawBill.ResourceGroup) {
				refinedRawBill.SCodeUnverified = refinedRawBill.ResourceGroup
				refinedRawBill.CostUnit = refinedRawBill.ResourceGroup
			} else if len(refinedRawBill.ResourceGroup) > scodeLen {
				refinedRawBill.SCodeUnverified = refinedRawBill.ResourceGroup[:scodeLen]
				refinedRawBill.CostUnit = refinedRawBill.ResourceGroup[:scodeLen]
			} else {
				if rr, ok := s.scodeRule(s.oracleScode.scodeMap, refinedRawBill); ok {
					refinedRawBills = append(refinedRawBills, rr)
				} else {
					refinedRawBill = scodeRule(refinedRawBill, empty, false)
				}
			}
		}

		// 重设instanceId或者SupplementId recompute aggregateId
		if changed := instanceId(refinedRawBill); changed {
			s.aggregatedId(refinedRawBill)
		}
		// get scode from cmdb
		if scode, ok := s.getSCodeFromCMDB(refinedRawBill.AggregatedID); ok {
			refinedRawBills = append(refinedRawBills, scodeRule(refinedRawBill, scode, true))
			continue
		}

		refinedRawBills = append(refinedRawBills, refinedRawBill)
	}
	return
}

func (s *SyncBillOracleCloud) ReceivedChan() chan *models.RawBill {
	return make(chan *models.RawBill)
}

func (s *SyncBillOracleCloud) findOracleBillFromDB(ctx context.Context, done chan struct{}, receivedChan chan *models.RawBill, accountName, billingCycle string) error {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Errorf(ctx, "findOracleBillFromDB panic: %s", r)
			close(receivedChan)
		}
	}()

	page := 1
	for p, err := findOracleBill(page, BatchSize, accountName, billingCycle); ; p, err = findOracleBill(page, BatchSize, accountName, billingCycle) {
		if err != nil {
			close(receivedChan)
			return err
		}
		if len(p.Data) == 0 {
			break
		}
		s.logger.Infof(ctx, "findOracleBillFromDB page: %d, current: %d, total: %d", p.CurrentPage, p.CurrentPage*BatchSize, p.Total)
		for _, bill := range p.Data {
			select {
			case <-done:
				close(receivedChan)
				return nil
			case receivedChan <- bill:
			}
		}
		page = page + 1
	}

	close(receivedChan)
	return nil
}

func NewSyncBillOracleCloud(taskId string, syncLog *models.RawBillSyncLog, rule *CostUnitComparison) *SyncBillOracleCloud {
	b := &SyncBillOracleCloud{BaseSyncHandler: NewBaseHandler(oracle_name)}
	b.syncLog = syncLog
	b.taskId = taskId
	b.rule = rule
	b.oracleScode.scodeMap = make(map[string]*expectSCode)
	return b
}

// get oracle bill
func findOracleBill(page, size int, accountName, billCycle string) (p *pager.Pager[*models.RawBill], err error) {
	p = pager.NewPager[*models.RawBill](page, size, pager.WithDatasource(bizutils.DataSource()),
		pager.WithCondition("account_name", pager.Equal, accountName),
		pager.WithCondition("billing_cycle", pager.Equal, billCycle))
	return p, p.QueryByTableName(oracle_table)
}

func instanceId(refinedRawBill *models.RefinedRawBill) (changed bool) {
	switch refinedRawBill.ProductCode {
	case PRODUCT_CODE_BLOCK_STORAGE:
		changed = true
		// 设置分拆项
		content, _ := utils.UnmarshalString[oraclecloud.OracleBillCSV](refinedRawBill.Content)
		refinedRawBill.SupplementID = content.CompartmentId
		// 设置实例id
		if middle, isSplit := instanceMiddle(refinedRawBill.InstanceID); isSplit {
			if strings.Contains(middle, INSTANCE_VOLUMEBACKUP) {
				refinedRawBill.InstanceID = refinedRawBill.CostUnit
			}
		}
		return
	case PRODUCT_CODE_TELEMETRY:
		refinedRawBill.InstanceID = refinedRawBill.InstanceID + refinedRawBill.CostUnit
	}

	return
}

func doFilter(productCode, instanceId string) (found bool) {
	switch productCode {
	case PRODUCT_CODE_NETWORK:
		middle, isSplit := instanceMiddle(instanceId)
		if !isSplit {
			return
		}
		return middle == FILTER_VNIC || middle == FILTER_DRGATTACHMENT || middle == FILTER_VCN
	}

	return
}

// 获取instanceId中间值,获取bootvolumebackup这段内容. eg: ocid1.bootvolumebackup.oc1.ap-singapore-1.abzwsljrurqxhzpdxrshz5oudqjizk7gbrvijtstqxt6oomfhbqclvxmd5la
func instanceMiddle(instanceId string) (string, bool) {
	instanceIds := strings.Split(instanceId, spot)
	if len(instanceIds) < 2 {
		return empty, false
	}
	return instanceIds[1], true
}

func dictItem() error {
	// type sysDictItem struct {
	// 	itemText  string `gorm:"column:item_text"`
	// 	itemValue string `gorm:"column:item_value"`
	// }
	var results []map[string]interface{}
	sql := "select item_value,item_text from sys_dict sd left join sys_dict_item sdi on sd.id = sdi.dict_id where sd.dict_code = 'ORACLE_PRODUCT_LIST' and sd.del_flag = 0"
	model := bizutils.DataSource().Model(context.Background())
	if err := model.Orm().Raw(sql).Scan(&results).Error; err != nil {
		return err
	}
	for _, row := range results {
		dict[row["item_value"].(string)] = row["item_text"].(string)
	}
	return nil
}

// func TimeCost(msg string) func() {
// 	start := time.Now()
// 	return func() {
// 		tc := time.Since(start)
// 		fmt.Printf(msg+" time cost = %f s\n", tc.Seconds())
// 	}
// }
