package billHandler

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
)

const (
	COMMODITY_CODE = "CommodityCode"
	RESOURCE_ID    = "ResourceId"
	RESOURCE_TAG   = "ResourceTag"
	RESOURCE_GROUP = "ResourceGroup"
	RESOURCE_NICK  = "ResourceNick"
	ACCOUNT        = "account"
	RULE_VALUE     = "rule_value"

	rule_key_format = "%s-%s"
)

// huawei
const (
	HUAWEI_RULE_VALUE     = RULE_VALUE
	HUAWEI_COMMODITY_CODE = COMMODITY_CODE
	HUAWEI_ACCOUNT        = ACCOUNT
)

// tencent
const (
	TENCENT_COMMODITY_CODE = COMMODITY_CODE
	TENCENT_ACCOUNT        = ACCOUNT
)

// google
const (
	GOOGLE_RULE_VALUE = RULE_VALUE
)

// oracle
const (
	ORACLE_RULE_VALUE     = RULE_VALUE
	ORACLE_COMMODITY_CODE = COMMODITY_CODE
)

// azure
const (
	AZURE_ACCOUNT        = ACCOUNT
	AZURE_COMMODITY_CODE = COMMODITY_CODE
)

func GetRuleKey(priority, value string) string {
	return fmt.Sprintf(rule_key_format, priority, value)
}

// 获取财富单元分拆规则
func GetSplitRule(ctx context.Context) (map[string][]*models.CostUnitSplitRule, error) {
	rules := make([]models.CostUnitSplitRule, 0)
	model := bizutils.DataSource().Model(ctx)

	if err := model.Orm().Find(&rules).Error; err != nil {
		log.Errorf(ctx, "GetSplitRule failed: %s", err.Error())
		return nil, err
	}

	m := map[string][]*models.CostUnitSplitRule{}
	for _, r := range rules {
		r := r
		if r.Percent == "0" {
			continue
		}
		key := r.AccountName + r.CostUnit
		if v, ok := m[key]; ok {
			v = append(v, &r)
			m[key] = v
		} else {
			m[key] = []*models.CostUnitSplitRule{&r}
		}
	}

	// check sum of costUnit percent 100%
	for _, v := range m {
		percent := 0.0
		for _, p := range v {
			f, err := strconv.ParseFloat(p.Percent, 64)
			if err != nil {
				log.Errorf(ctx, "GetSplitRule percent ParseFloat failed: %s", err.Error())
				return nil, err
			}
			percent = percent + f
		}
		if percent != 100 {
			errMsg := "GetSplitRule sum percent of costUnit not 100%%"
			log.Errorf(ctx, errMsg)
			return nil, errors.New(errMsg)
		}
	}
	return m, nil
}

// 获取财富单元规则
func GetRule(ctx context.Context, vendor, account string) (*CostUnitComparison, error) {
	rules := make([]models.CloudCostUnitMoveRule, 0)
	model := bizutils.DataSource().Model(ctx)

	if err := model.Orm().Where("status ='on' and cloud= ? and (account = ? or account is null)", vendor, account).Find(&rules).Error; err != nil {
		log.Errorf(ctx, "vendor: %s account:%s GetRules failed: %s", vendor, account, err.Error())
		return nil, err
	}

	if len(rules) == 0 {
		return &CostUnitComparison{Priority: []string{}}, nil
	}
	return costUnit(rules), nil
}

func costUnit(costUnits []models.CloudCostUnitMoveRule) *CostUnitComparison {
	var account string
	if costUnits[0].Account == "" {
		account = "账户通用"
	} else {
		account = costUnits[0].Account
	}

	cuc := &CostUnitComparison{Account: account, Rules: map[string]models.CloudCostUnitMoveRule{}}
	for _, costUnit := range costUnits {

		cuc.Priority = append(cuc.Priority, costUnit.Priority)

		switch costUnit.Priority {
		case COMMODITY_CODE:
			cuc.Rules[GetRuleKey(costUnit.Priority, costUnit.CommodityCode)] = costUnit
		case RESOURCE_ID:
			cuc.Rules[GetRuleKey(costUnit.Priority, costUnit.ResourceId)] = costUnit
		case RESOURCE_TAG:
			cuc.Rules[GetRuleKey(costUnit.Priority, costUnit.ResourceTag)] = costUnit
		case RESOURCE_GROUP:
			cuc.Rules[GetRuleKey(costUnit.Priority, costUnit.ResourceGroup)] = costUnit
		case RESOURCE_NICK:
			cuc.Rules[GetRuleKey(costUnit.Priority, costUnit.ResourceNick)] = costUnit
		case ACCOUNT:
			cuc.Rules[GetRuleKey(costUnit.Priority, costUnit.Account)] = costUnit
		case RULE_VALUE:
			// 当CommodityCode不为空时,获取CommodityCode值和ruleValue值进行联合判断.
			if costUnit.CommodityCode != "" {
				cuc.Rules[costUnit.CommodityCode+GetRuleKey(costUnit.Priority, costUnit.RuleValue)] = costUnit
			} else {
				cuc.Rules[GetRuleKey(costUnit.Priority, costUnit.RuleValue)] = costUnit
			}
		}
	}

	// 去重
	cuc.Priority = removeDuplicates(cuc.Priority)
	// 按首字母排序
	sort.Strings(cuc.Priority)

	return cuc
}

func removeDuplicates(slice []string) []string {
	seen := make(map[string]struct{})
	result := []string{}

	for _, s := range slice {
		if _, exists := seen[s]; !exists {
			seen[s] = struct{}{}
			result = append(result, s)
		}
	}
	return result
}

type CostUnitComparison struct {
	Account  string                                  // 账号
	Priority []string                                // 规则字段名
	Rules    map[string]models.CloudCostUnitMoveRule // key:ruleValue value:CloudCostUnitMoveRule
}
