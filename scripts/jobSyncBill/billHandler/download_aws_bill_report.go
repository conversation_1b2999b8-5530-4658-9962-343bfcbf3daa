package billHandler

import (
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/util"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/gofiber/fiber/v2/log"
	"golang.org/x/sync/errgroup"
)

const tableName = "bc_aws_bill"

const createTableSql = "CREATE TABLE `bc_aws_bill_%s` (\n" +
	"  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',\n" +
	"  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',\n" +
	"  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',\n" +
	"  `vendor` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '云厂商',\n" +
	"  `account_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录的账号信息-账户名称',\n" +
	"  `account_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录的账号信息-账户ID',\n" +
	"  `granularity` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账单粒度',\n" +
	"  `billing_cycle` date NOT NULL COMMENT '账期',\n" +
	"  `product_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品编码',\n" +
	"  `product_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品名称',\n" +
	"  `resource_group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资源组',\n" +
	"  `instance_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源ID',\n" +
	"  `service_period` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务期',\n" +
	"  `service_period_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务期单位',\n" +
	"  `region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地域',\n" +
	"  `zone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '可用区',\n" +
	"  `billing_item` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计费项',\n" +
	"  `list_price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '定价',\n" +
	"  `list_price_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '定价单位',\n" +
	"  `usage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '使用量',\n" +
	"  `usage_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '使用量单位',\n" +
	"  `cost` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '费用',\n" +
	"  `cost_unit` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算单元',\n" +
	"  `currency` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '币种',\n" +
	"  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始账单内容',\n" +
	"  `subscription_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消费类型; 1: 按量; 2:包年包月',\n" +
	"  `product_detail` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品明细',\n" +
	"  `supplement_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,\n" +
	"  `aggregated_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '聚合id',\n" +
	"  `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务id',\n" +
	"  `payable_amount` decimal(18,9) NOT NULL DEFAULT '0.*********' COMMENT '应付金额',\n" +
	"  `voucher_amount` decimal(18,9) NOT NULL DEFAULT '0.*********' COMMENT '代金券金额',\n" +
	"  `cash_amount` decimal(18,9) NOT NULL DEFAULT '0.*********' COMMENT '现金支付',\n" +
	"  `coupon_amount` decimal(18,9) DEFAULT NULL COMMENT '优惠券金额',\n" +
	"  `prepaid_item` tinyint DEFAULT '0' COMMENT '预付条目0:非预付1:预付款',\n" +
	"  PRIMARY KEY (`id`),\n" +
	"  KEY `idx_vendor_billing_cycle` (`vendor`,`billing_cycle`),\n" +
	"  KEY `idx_create_time` (`create_time`),\n" +
	"  KEY `idx_resource_id` (`instance_id`),\n" +
	"  KEY `idx_account_id` (`account_id`),\n" +
	"  KEY `idx_billing_cycle` (`billing_cycle`),\n" +
	"  KEY `dx_multi_1` (`vendor`,`product_code`,`product_name`,`billing_item`),\n" +
	"  KEY `idx_product_code` (`product_code`),\n" +
	"  KEY `bc_raw_bill_aggregated_id_IDX` (`aggregated_id`),\n" +
	"  KEY `idx_multi` (`vendor`,`product_code`,`product_name`,`billing_item`),\n" +
	"  KEY `idx_task_id` (`task_id`),\n" +
	"  KEY `idx_cost_unit` (`cost_unit`),\n" +
	"  KEY `idx_account_name` (`account_name`),\n" +
	"  KEY `idx_aggregated_id` (`aggregated_id`),\n" +
	"  KEY `idx_vendor` (`vendor`),\n" +
	"  KEY `idx_vendor_cost_unit` (`vendor`,`cost_unit`),\n" +
	"  KEY `idx_task_id_vendor_account_name` (`task_id`,`vendor`,`account_name`)\n" +
	") ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用于存储aws原始账单的表';"

// const stabilize = 7 // 每月7号数据稳定

var awsBillBillingCycleWhereSql = "account_id=? and billing_cycle like '%s%%'"

func NewSyncAwsDownloadBillReport(cli client.IClient) (err error) {
	ctx := context.Background()
	now := time.Now()
	var (
		currentTime = now
		targetTime  = time.Date(2025, time.April, 1, 0, 0, 0, 0, time.UTC) // 只获取2025-04后的账单数据
	)
	// 当前月份和当前月份之前的月份,循环顺序最新月份靠前: 2025-05,2025-04
	for currentTime.After(targetTime) || currentTime.Equal(targetTime) {
		if err = parseDB(ctx, cli, currentTime); err != nil {
			return err
		}
		currentTime = lastMonth(currentTime) // 减少一个月
	}
	return
}

func CreateTables(currentMonthFlag string) error {
	now := time.Now()
	currentTime, currentMonth := now, now
	targetTime := time.Date(2025, time.January, 1, 0, 0, 0, 0, time.UTC) // 只创建到2025-01后的账单数据库表
	model := bizutils.DataSource().Model(context.Background())

outerLoop:
	for currentTime.After(targetTime) || currentTime.Equal(targetTime) {
		ym := yearMonth(currentTime)
		tables, err := model.Orm().Migrator().GetTables()
		if err != nil {
			return err
		}
		for _, table := range tables {
			// 是否需要同步当月数据
			if currentMonthFlag == BillCurrentMonthExecute {
				// 如果是当月 移除当月数据 重置syncLog数据重新拉取
				if currentTime.Year() == currentMonth.Year() && currentTime.Month() == currentMonth.Month() {
					for _, table := range tables {
						cm := yearMonth(currentMonth)
						if table == tableName+underscore+cm {
							// 删除aws数据和重置syncLog数据
							if err := model.Orm().Model(&models.RawBillSyncLog{}).Where(fmt.Sprintf("vendor ='aws' and billing_cycle like '%s%%'", cm)).
								Updates(models.RawBillSyncLog{Status: SYNC_LOG_STATUS_START, Stage: SYNC_LOG_STAGE_PRE_PULL}).Error; err != nil {
								return err
							}
							if err := model.Orm().Exec(fmt.Sprintf("DELETE FROM `bc_aws_bill_%s`", cm)).Error; err != nil {
								return err
							}
							currentTime = lastMonth(currentTime) // 减少一个月
							continue outerLoop
						}
					}
				}
			}
			if table == tableName+underscore+ym { // 格式: bc_aws_bill_2025-01
				currentTime = lastMonth(currentTime) // 减少一个月
				continue outerLoop
			}
		}
		// create table
		if err := model.Orm().Exec(fmt.Sprintf(createTableSql, ym)).Error; err != nil {
			log.Errorf("aws bill create table failed:  %s", err.Error())
			return err
		}
		currentTime = lastMonth(currentTime) // 减少一个月
	}
	return nil
}

func parseDB(ctx context.Context, cli client.IClient, t time.Time) (err error) {
	yearMonth := yearMonth(t)
	prefix, _ := os.Getwd()
	path := path.Join(prefix, string(hbc.AWS), cli.Name())
	eg := errgroup.Group{}
	batch := []*models.RawBill{}
	done := make(chan struct{})
	receivedChan := make(chan *aws.AwsBillCSV)

	// 检查是否存在数据
	count, err := checkBillByYearMonth(ctx, cli.Identifier(), yearMonth)
	if err != nil {
		return err
	}
	if count > 0 {
		return
	}
	if err = NewSyncBillAwsCloud(empty, nil, &models.RawBillSyncLog{BillingCycle: yearMonth}, nil).
		PullBill(ctx, cli, nil, nil); err != nil {
		return err
	}
	log.Infof("%s aws bill download %s csv file", cli.Name(), yearMonth)
	files := trimFiles(path, yearMonth)
	if len(files) == 0 {
		log.Errorf("aws bill download report not found in path: %s/%s", path, yearMonth)
		return
	}

	eg.Go(func() error {
		for {
			record, ok := <-receivedChan
			if !ok {
				close(done)
				return saveAwsBill(batch)
			}
			batch = append(batch, toAwsBillData(ctx, record, yearMonth)...)
			if len(batch) >= 500 {
				if err = saveAwsBill(batch); err != nil {
					close(done)
					log.Errorf("aws bill download client: %s parseDB failed: %s", cli.Name(), err.Error())
					return err
				}
				batch = nil
			}
		}
	})
	eg.Go(func() (csvErr error) {
		defer func() {
			if csvErr != nil {
				if csvErr.Error() == errCSVParseChannelClosed {
					log.Errorf("aws bill done billChan closed")
					return
				}
			}
			close(receivedChan)
		}()
		return awsParseCSV(files, path, done, receivedChan)
	})

	return eg.Wait()
}

func awsParseCSV(files []os.DirEntry, filePath string, done chan struct{}, receivedChan chan *aws.AwsBillCSV) (err error) {
	b := tools.NewBatch[os.DirEntry, error](context.Background())
	b.Run(files, func(ctx context.Context, f os.DirEntry) (err error, _ error) {
		file, err := os.Open(path.Join(filePath, f.Name()))
		if err != nil {
			return
		}
		defer file.Close()

		gr, err := gzip.NewReader(file)
		if err != nil {
			return
		}
		defer gr.Close()

		err = util.BillCsvGz(gr, file.Name(), headLine, done, receivedChan)
		return
	})
	return tools.MergeErrors(b.Outs())
}

func saveAwsBill(batch []*models.RawBill) error {
	if len(batch) == 0 {
		return nil
	}
	model := bizutils.DataSource().Model(context.Background())
	awsBill := models.RawBill{BillingCycle: batch[0].BillingCycle}
	return model.Orm().Table(awsBill.AwsBillTableName()).Create(&batch).Error
}

func checkBillByYearMonth(ctx context.Context, accountId string, yearMonth string) (int64, error) {
	var count int64
	model := bizutils.DataSource().Model(ctx)
	if err := model.Orm().Model(&models.RawBill{}).Raw(fmt.Sprintf("SELECT 1 FROM `bc_aws_bill_%s` where %s LIMIT 1", yearMonth, fmt.Sprintf(awsBillBillingCycleWhereSql, yearMonth)), accountId).Scan(&count).Error; err != nil {
		return count, err
	}
	return count, nil
}

func yearMonth(time time.Time) string {
	return time.Format(MonthTimeFormat)
}

func lastMonth(time time.Time) time.Time {
	return time.AddDate(0, -1, 0)
}

func trimFiles(path, checkName string) []os.DirEntry {
	trimFiles := []os.DirEntry{}
	files, err := os.ReadDir(path)
	if err != nil {
		log.Errorf("aws bill download readDir: %s failed", path)
		return trimFiles
	}
	for _, file := range files {
		if !strings.Contains(file.Name(), checkName) {
			continue
		}
		trimFiles = append(trimFiles, file)
	}
	return trimFiles
}

func toAwsBillData(ctx context.Context, item *aws.AwsBillCSV, yearMonth string) []*models.RawBill {
	type product struct {
		ProductName string `json:"product_name"`
	}
	// 会出现后面月份账单出现在当月的情况,将其归到当月的账单中.
	billingCycle, _ := time.Parse(time.DateOnly, fmt.Sprintf("%s%s%02d", yearMonth, hyphen, item.LineItemUsageStartDate.Day())) //AJ line_item_usage_start_date

	var p product
	json.Unmarshal([]byte(item.Product), &p) //AV product

	return []*models.RawBill{{
		Model:        bizutils.DataSource().Model(ctx),
		AccountName:  item.LineItemUsageAccountName,
		AccountID:    item.LineItemUsageAccountId,
		BillingCycle: billingCycle,

		CostUnit:          item.ResourceTags,                //CS
		InstanceID:        item.LineItemResourceId,          //AB line_item_resource_id
		ProductCode:       item.LineItemProductCode,         //AA line_item_product_code
		ProductName:       p.ProductName,                    //AV product
		ProductDetail:     item.ProductSku,                  //BM product_sku
		Usage:             item.LineItemUsageAmount,         //用量AH line_item_usage_amount
		UsageUnit:         item.LineItemUsageType,           //AK line_item_usage_type
		ListPrice:         item.LineItemNetUnblendedRate,    //单价AE line_item_unblended_rate
		ListPriceUnit:     item.PricingUnit,                 //AU pricing_unit
		ServicePeriod:     item.IdentityTimeInterval,        //N identity_time_interval
		ServicePeriodUnit: item.LineItemLineItemDescription, //T line_item_line_item_description
		BillingItem:       item.LineItemLineItemType,        //U line_item_line_item_type
		ResourceGroup:     item.ProductProductFamily,        //BJ product_product_family
		SubscriptionType:  item.LineItemOperation,           //Z line_item_operation
		// SupplementID:      item.ResourceTags,
		PayableAmount: toFloat(item.LineItemUnblendedCost), //AD line_item_unblended_cost
		Currency:      item.LineItemCurrencyCode,           //R line_item_currency_code
		Region:        item.ProductRegionCode,              //BK product_region_code
		Zone:          item.ProductLocation,                //BF product_location
		Content:       utils.JsonString(item),
	}}
}
