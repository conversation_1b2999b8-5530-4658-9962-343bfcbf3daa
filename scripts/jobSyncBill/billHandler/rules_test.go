package billHandler

import (
	"context"
	"fmt"
	"testing"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestRules_Bill(t *testing.T) {
	a := assert.New(t)
	rule, err := GetRule(context.Background(), "gcloud", "")
	fmt.Println(utils.JsonString(rule))
	a.NoError(err)
}

func TestSplitRules_Bill(t *testing.T) {
	a := assert.New(t)
	rule, err := GetSplitRule(context.Background())
	model := bizutils.DataSource().Model(context.Background())
	bills := []*models.RawBill{}
	model.Orm().Where("vendor='aliyun' and cost_unit  ='共享带宽'").Find(&bills)

	for _, b := range bills {
		if percents, ok := rule[b.CostUnit]; ok {
			var sum decimal.Decimal
			for _, percent := range percents {
				payableAmount := costPercent(b.PayableAmount, percent.Percent)
				cashAmount := costPercent(b.CashAmount, percent.Percent)
				voucherAmount := costPercent(b.VoucherAmount, percent.Percent)

				num1 := decimal.NewFromFloat(payableAmount)
				num2 := decimal.NewFromFloat(cashAmount)
				num3 := decimal.NewFromFloat(voucherAmount)
				sum = sum.Add(num1.Add(num2).Add(num3))
			}

			// num1, _ := strconv.ParseFloat(b.PayableAmount, 64)
			// num2, _ := strconv.ParseFloat(b.CashAmount, 64)
			// num3, _ := strconv.ParseFloat(b.VoucherAmount, 64)

			fmt.Print(b.PayableAmount+b.CashAmount, +b.VoucherAmount)
			fmt.Print(" <> ")
			fmt.Println(sum.String())
		}
	}
	a.NoError(err)
}

func costPercent(cost float64, percent string) float64 {
	percentage := decimal.RequireFromString(percent).Mul(decimal.NewFromFloat(1))
	return decimal.NewFromFloat(cost).Mul(percentage.Div(decimal.NewFromFloat(100))).InexactFloat64()
}

func TestAliyunOceanbaseBill(t *testing.T) {
	model := bizutils.DataSource().Model(context.Background())
	bills := []models.RefinedRawBill{}
	err := model.Orm().Where("vendor='aliyun' and product_code='oceanbase' ").Find(&bills).Error
	if err != nil {
		fmt.Print(err)
		return
	}
	for _, bill := range bills {
		aggregatedID := stringutil.Md5(bill.AccountID + bill.ProductCode + bill.InstanceID + bill.SupplementID)

		err := model.Orm().Model(&models.RefinedRawBill{}).Where("id =? ", bill.ID).Updates(map[string]interface{}{"aggregated_id": aggregatedID}).Error
		if err != nil {
			fmt.Print(err)
			return
		}
	}
}

func TestHuaweiScodeBill(t *testing.T) {
	// type b struct {
	// 	productCode string
	// 	// count       int
	// }

	ctx := context.Background()
	model := bizutils.DataSource().Model(ctx)
	bills := []models.RawBill{}
	err := model.Orm().Where("vendor ='huawei' ").Find(&bills).Error
	if err != nil {
		return
	}
	m := make(map[string]map[string]string)
	for _, bill := range bills {
		if _, ok := m[bill.AggregatedID]; ok {
			am := m[bill.AggregatedID]
			if _, ok := am[bill.CostUnit]; !ok {
				am[bill.CostUnit] = bill.ProductCode
				m[bill.AggregatedID] = am
			}
		} else {
			m[bill.AggregatedID] = map[string]string{
				bill.CostUnit: bill.ProductCode,
			}
		}
	}

	m1 := []map[string]map[string]string{}
	m2 := make(map[string]int)
	for k, v := range m {
		if len(v) > 1 {
			m1 = append(m1, map[string]map[string]string{
				k: v,
			})
			for _, v1 := range v {
				m2[v1] = 0
			}
		}
	}
	products := []string{}
	for k := range m2 {
		products = append(products, k)
	}

	fmt.Println(utils.JsonString(m1))
	fmt.Println(utils.JsonString(products))
}
