package billHandler

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/pager"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

const (
	aws_name                = "aws"
	cost_unit_catch         = unallocated + hyphen
	cost_unit_catch_product = cost_unit_catch + "未梳理的产品"

	s03980Scode      = "S03980"      //9mqo1srq55eig8rgep8vu0t96 特供S码
	userSCode        = "user_s_code" //tags中的key
	usd1             = 1
	PrepaidItemExist = 1 // 预付条目表示存在

	notFountSnapshotErr      = "InvalidSnapshot.NotFound"
	notFountVolumeErr        = "InvalidVolume.NotFound"
	notFountSnapshotVolIdErr = "SnapshotDataVolumeId.NotFound"
	notFountInstanceErr      = "InvalidInstance.NotFound"
	tagsNotFountErr          = "Tags获取不到scode"
	podNotFountErr           = "podNotFoundClusterInstanceId"
)

const (
	// line_item_line_item_type列 paybleAmount=0,cost=账单金额
	credit                  = "Credit"
	savingsPlanNegation     = "SavingsPlanNegation"
	savingsPlanCoveredUsage = "SavingsPlanCoveredUsage"
	//暂时不处理 paybleAmount=账单金额
	savingsPlanRecurringFee = "SavingsPlanRecurringFee"
	// usage tax 最后统一均摊 paybleAmount=账单金额
	usage = "Usage"
	tax   = "Tax"
)

type SyncBillAwsCloud struct {
	*BaseSyncHandler
	rule *CostUnitComparison
	// awsScode scode
	aws *awsClient
}

type awsClient struct {
	Region string
}

func (s *SyncBillAwsCloud) PullBill(ctx context.Context, c client.IClient, done chan struct{}, receivedChan chan *aws.AwsBillCSV) (err error) {
	defer s.done(err, done)

	if _, ok := c.(*aws.BillingClient); !ok {
		return fmt.Errorf("not %s billClient", aws_name)
	}
	return c.(*aws.BillingClient).ListObjectsPages(s.syncLog.BillingCycle)
}

var awsSCodeRegexp = regexp.MustCompile(`"user_s_code"\s*:\s*"\s*([^"]*?)\s*"`)

func (s *SyncBillAwsCloud) ToBillRawData(ctx context.Context, item *models.RawBill) []*models.RawBill {
	tagSCode := func(resourceTag, unable string) (scode string) {
		if result := awsSCodeRegexp.FindStringSubmatch(resourceTag); len(result) > 1 {
			scode = result[1]
			// s.awsScode.scodeMap[resourceTag] = &expectSCode{CheckSubProductsBool: true, SCode: scode}
			// s.awsScode.scodeMap[refinedRawBill.CostUnit] = &expectSCode{CheckSubProductsBool: true, SCode: refinedRawBill.CostUnit}
			return scode
		}
		return unable
	}
	supplementId := empty
	bill := &models.RawBill{
		Model:        bizutils.DataSource().Model(ctx),
		TaskId:       s.taskId,
		Vendor:       hbc.AWS.String(),
		AccountName:  s.syncLog.AccountName,
		AccountID:    s.syncLog.AccountID,
		Granularity:  BILL_GRANULARITY,
		BillingCycle: s.billingCycle(),

		ProductCode:   item.ProductCode,
		ProductName:   item.ProductName,
		ProductDetail: item.ProductDetail,
		CostUnit:      tagSCode(item.CostUnit, empty),
		InstanceID:    item.InstanceID,
		Usage:         item.Usage,
		UsageUnit:     item.UsageUnit,
		// AE是单价line_item_unblended_rate AH是用量line_item_usage_amount
		ListPrice:         item.ListPrice,
		ListPriceUnit:     item.ListPriceUnit,
		ServicePeriod:     item.ServicePeriod,
		ServicePeriodUnit: item.ServicePeriodUnit,
		Cost:              item.PayableAmount,
		PayableAmount:     item.PayableAmount,
		// VoucherAmount:      ,
		SubscriptionType: SUBSCRIPTION_TYPE_PAY_AS_YOU_GO,
		Currency:         item.Currency,
		BillingItem:      item.BillingItem,
		Region:           item.Region,
		Zone:             item.Zone,
		Content:          item.Content,
		ResourceGroup:    item.ResourceGroup,
		SupplementID:     supplementId,
		AggregatedID: stringutil.Md5(item.AccountID +
			item.ProductCode +
			item.InstanceID +
			supplementId),
	}

	return []*models.RawBill{bill}
}

func (s *SyncBillAwsCloud) CreateRawData(ctx context.Context, done chan struct{}, receivedChan chan *models.RawBill) (err error) {
	var batch = []*models.RawBill{}

	for {
		raw, ok := <-receivedChan
		if !ok {
			close(done)
			return s.create(ctx, batch, s.rules)
		}
		batch = append(batch, s.ToBillRawData(ctx, raw)...)
		if len(batch) >= s.batchSize {
			if err = s.create(ctx, batch, s.rules); err != nil {
				close(done)
				return
			}
			batch = nil
		}
	}
}

// var notMarkedProductMap = make(map[string]models.RawBill, 0)
var ruleMap map[string]rule

func (s *SyncBillAwsCloud) rules(raw []*models.RawBill) (refinedRawBills []*models.RefinedRawBill) {
	// defer TimeCost("aws bill rules")()
	refinedRawBills = make([]*models.RefinedRawBill, 0, len(raw))

	for _, r := range raw {
		refinedRawBill := &models.RefinedRawBill{TaskId: s.taskId, RawBill: *s.eraseModel(r)}

		// 处理resourceId为空的数据
		refinedRawBill, found := s.awsEmptyResourceIdItemTypeRule(refinedRawBill)
		if !found {
			if v, ok := ruleMap[refinedRawBill.ProductCode]; ok {
				refinedRawBill.CostUnit, refinedRawBill.InstanceID = v(&refinedRawBill.RawBill)
				refinedRawBill.SCodeUnverified = refinedRawBill.CostUnit
			} else {
				// 未梳理的产品置为未分配
				refinedRawBill.CostUnit, refinedRawBill.InstanceID, refinedRawBill.SCodeUnverified = cost_unit_catch_product, cost_unit_catch_product, cost_unit_catch_product
			}
		}
		refinedRawBill.CostUnit = strings.TrimSpace(refinedRawBill.CostUnit) // 去掉scode空格
		s.aggregatedId(refinedRawBill)

		// SP RI RN Fee
		if refinedRawBill.ProductCode == amazonEC2 || refinedRawBill.ProductCode == amazonRDS {
			if commitRate, found := sprirn(refinedRawBill.InstanceID, refinedRawBill.Content); found {
				refinedRawBill.PayableAmount = commitRate.Mul(decimal.NewFromInt(intervalHours(refinedRawBill.ServicePeriod))).InexactFloat64()
			}
			// if refinedRawBill.BillingItem == riFee && fee(refinedRawBill.Content) {
			// 	// 预付条目存在
			// 	refinedRawBill.PrepaidItem = PrepaidItemExist
			// }
		}

		// get scode from cmdb
		if scode, ok := s.getSCodeFromCMDB(refinedRawBill.AggregatedID); ok {
			refinedRawBills = append(refinedRawBills, scodeRule(refinedRawBill, scode, true))
			continue
		}
		// 低于1USD且S码不正确的账单归为大运维
		if refinedRawBill.PayableAmount < usd1 && !bizutils.IsSCode(refinedRawBill.CostUnit) {
			refinedRawBill = scodeRule(refinedRawBill, s04076Scode, false)
			refinedRawBills = append(refinedRawBills, refinedRawBill)
			continue
		}

		// check scodeMap sub products scode
		// s.awsScode.scodeMap = s.checkSCode(s.awsScode)
		// check scodeMap
		// if rr, ok := s.scodeRule(s.awsScode.scodeMap, refinedRawBill); ok {
		// 	refinedRawBills = append(refinedRawBills, rr)
		// 	continue
		// } else {
		// 	refinedRawBill = scodeRule(refinedRawBill, empty, false)
		// }
		refinedRawBills = append(refinedRawBills, refinedRawBill)
	}
	return
}

func intervalHours(servicePeriod string) int64 {
	// identity_time_interval
	timeParts := strings.Split(servicePeriod, "/")
	startTime, _ := time.Parse(time.RFC3339, timeParts[0])
	endTime, _ := time.Parse(time.RFC3339, timeParts[1])
	return int64(endTime.Sub(startTime).Hours())
}

// ** aws rules start **
type rule func(raw *models.RawBill) (scode, InstanceId string)

func getSaveAWSInstance(ctx context.Context, cli *aws.Ec2Client, id string, isVolOrInstanceId bool, ec2Tags func(tags []types.Tag) string) (scode, instanceId string) {
	var found bool
	defer func() {
		if !found {
			ec2VolInstanceSave(ctx, &models.ResourceSupplement{
				Scode:         scode,
				ResourceId:    id,
				Code:          amazonEC2,
				RawResourceId: instanceId,
				Account:       cli.Name(),
			})
		}
	}()

	// get cache
	instanceId = id
	var err error
	cacheScode, rawResourceId, err := bizutils.GetResourceSupplement(cli.Name(), amazonEC2, instanceId)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			scode = cost_unit_catch + "ec2-GetSaveAWSInstance-Err:" + err.Error()
			return
		}
	}
	if !isEmpty(cacheScode) {
		found = true
		scode = cacheScode
		instanceId = rawResourceId
		return
	}

	// vol
	var instances []types.Instance
	if isVolOrInstanceId {
		var volumes []types.Volume
		volumes, err = cli.DescribeVolumes(ctx, func(input *ec2.DescribeVolumesInput) {
			input.VolumeIds = []string{id}
		})
		if err != nil {
			scode = cost_unit_catch + "ec2-DescribeVolumesErr:" + err.Error()
			return
		}
		if len(volumes) == 0 {
			scode = cost_unit_catch + notFountVolumeErr
			return
		}
		if len(volumes[0].Attachments) == 0 {
			scode = cost_unit_catch + notFountVolumeErr
			return
		}
		instances, err = cli.DescribeInstancesOfAll(ctx, func(input *ec2.DescribeInstancesInput) {
			input.InstanceIds = []string{pointer.String(volumes[0].Attachments[0].InstanceId)}
		})
	} else {
		instances, err = cli.DescribeInstancesOfAll(ctx, func(input *ec2.DescribeInstancesInput) { input.InstanceIds = []string{id} })
	}

	if err != nil {
		scode = cost_unit_catch + "ec2-DescribeInstancesErr:" + err.Error()
		return
	}
	if len(instances) == 0 {
		scode = cost_unit_catch + notFountInstanceErr
		return
	}

	// get scode
	if s := ec2Tags(instances[0].Tags); !isEmpty(s) {
		scode = s
	} else {
		scode = cost_unit_catch + tagsNotFountErr
	}
	// ec2 instanceId
	instanceId = pointer.String(instances[0].InstanceId)
	return
}

//** aws rules end **

func (s *SyncBillAwsCloud) ReceivedChan() chan *models.RawBill {
	return make(chan *models.RawBill)
}

func NewSyncBillAwsCloud(taskId string, aws *awsClient, syncLog *models.RawBillSyncLog, rule *CostUnitComparison) *SyncBillAwsCloud {
	b := &SyncBillAwsCloud{BaseSyncHandler: NewBaseHandler(aws_name)}
	b.syncLog = syncLog
	b.taskId = taskId
	b.rule = rule
	// b.awsScode.scodeMap = make(map[string]*expectSCode)
	b.aws = aws
	return b
}

func (s *SyncBillAwsCloud) findAwsBillFromDB(ctx context.Context, done chan struct{}, receivedChan chan *models.RawBill, accountId, billCycle string) error {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Errorf(ctx, "findAwsBillFromDB panic: %s", r)
			close(receivedChan)
		}
	}()

	page := 1
	for p, err := findAwsBill(page, BatchSize, accountId, billCycle); ; p, err = findAwsBill(page, BatchSize, accountId, billCycle) {
		if err != nil {
			close(receivedChan)
			return err
		}
		if len(p.Data) == 0 {
			break
		}
		s.logger.Infof(ctx, "findAwsBill page: %d, current: %d, total: %d", p.CurrentPage, p.CurrentPage*BatchSize, p.Total)
		for _, bill := range p.Data {
			select {
			case <-done:
				close(receivedChan)
				return nil
			case receivedChan <- bill:
			}
		}
		page = page + 1
	}

	close(receivedChan)
	return nil
}

func isEmpty(s string) bool {
	return s == empty
}

func ec2VolInstanceSave(ctx context.Context, resourceSupplement *models.ResourceSupplement) error {
	var s string
	scode := strings.TrimSpace(resourceSupplement.Scode)
	if bizutils.IsSCode(scode) || scode == unallocated {
		s = scode
	} else if strings.Contains(scode, notFountSnapshotErr) {
		s = cost_unit_catch + notFountSnapshotErr
	} else if strings.Contains(scode, notFountSnapshotVolIdErr) {
		s = cost_unit_catch + notFountSnapshotVolIdErr
	} else if strings.Contains(scode, notFountVolumeErr) {
		s = cost_unit_catch + notFountVolumeErr
	} else if strings.Contains(scode, notFountInstanceErr) {
		s = cost_unit_catch + notFountInstanceErr
	} else if strings.Contains(scode, tagsNotFountErr) {
		s = cost_unit_catch + tagsNotFountErr
	} else {
		s = cost_unit_catch + "DescribeSnapshotsErr:" + scode
	}
	// 替换“;” 防止从redis中取出按照;进行拆分时产生错误
	resourceSupplement.Scode = strings.ReplaceAll(s, semicolon, empty)
	// insert db
	if err := saveAWSSupplement(ctx, resourceSupplement); err != nil {
		return err
	}
	return nil
}

func ec2InstancePricing(ctx context.Context, raw *models.RawBill) {
	product, err := getAwsPricingBySKU(ctx, raw.ProductDetail)
	if err != nil {
		return
	}
	// 计算时间间隔的小时数
	raw.PayableAmount = decimal.NewFromFloat(product.Pricing).
		Mul(decimal.NewFromInt(intervalHours(raw.ServicePeriod))).
		InexactFloat64()
}

// get ecs pricing
func getAwsPricingBySKU(ctx context.Context, sku string) (*models.AwsPricing, error) {
	sf := &models.AwsPricing{}
	model := bizutils.DataSource().Model(ctx)
	if err := model.Orm().Where("instance_id = ? and del_flag = ?", sku, del_flag_undeleted).
		First(sf).Error; err != nil {
		return nil, err
	}
	return sf, nil
}

// aws-rds getSCode
func getAwsRdsSCodeByInstanceName(ctx context.Context, instanceName string) (*models.DatabaseInfo, error) {
	databaseInfo := &models.DatabaseInfo{}
	model := bizutils.DataSource().Model(ctx)
	if err := model.Orm().Where("instance_name = ?", instanceName).First(databaseInfo).Error; err != nil {
		return nil, err
	}
	return databaseInfo, nil
}

// saveAWSSupplement
func saveUpdateAWSSupplement(ctx context.Context, resourceSupplement *models.ResourceSupplement) error {
	cacheScode, _, err := bizutils.GetResourceSupplement(resourceSupplement.Account, resourceSupplement.Code, resourceSupplement.ResourceId)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}
	if isEmpty(cacheScode) {
		// insert db
		if err := saveAWSSupplement(ctx, resourceSupplement); err != nil {
			return err
		}
	}
	return nil
}

func saveAWSSupplement(ctx context.Context, resourceSupplement *models.ResourceSupplement) error {
	model := bizutils.DataSource().Model(ctx)
	resourceSupplement.Model = model
	resourceSupplement.Vendor = hbc.AWS.String()
	if err := model.Orm().Save(resourceSupplement).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return nil
		}
		return err
	}
	return nil
}

// get aws bill
func findAwsBill(page, size int, accountId, billCycle string) (p *pager.Pager[*models.RawBill], err error) {
	p = pager.NewPager[*models.RawBill](page, size, pager.WithDatasource(bizutils.DataSource()),
		pager.WithOrder("id asc"),
		pager.WithCondition("account_id", pager.Equal, accountId),
		pager.WithCondition("billing_cycle", pager.Equal, billCycle))
	b, _ := time.Parse(time.DateOnly, billCycle)
	a := models.RawBill{BillingCycle: b}
	return p, p.QueryByTableName(a.AwsBillTableName())
}

/*
EC2 SavingPlan:
LineItemLineItemType in (SavingsPlanCovewredUsage,Usage)
ProductProductFamily = Compute Instance
LineItemOperation    != Hourly

RDS RI:
LineItemLineItemType in (DiscountedUsage,Usage)
ProductProductFamily Database Instance
LineItemOperation    != Hourly

Redis Reserved nodes:
ProductCode=amazonElastiCache
LineItemLineItemType in (DiscountedUsage,Usage)
ProductProductFamily Cache Instance
LineItemOperation    != Hourly
*/
func sprirn(instanceId string, content string) (rate decimal.Decimal, found bool) {
	discountedUsage := "DiscountedUsage"
	awsBillCSV, _ := utils.UnmarshalString[aws.AwsBillCSV](content)
	var exist bool
	switch awsBillCSV.LineItemProductCode {
	case amazonEC2:
		if awsBillCSV.ProductProductFamily == "Compute Instance" {
			if awsBillCSV.LineItemOperation != hourly && (awsBillCSV.LineItemLineItemType == "SavingsPlanCovewredUsage" || awsBillCSV.LineItemLineItemType == usage) {
				exist = true
			}
		}
	case amazonRDS:
		if awsBillCSV.ProductProductFamily == "Database Instance" {
			if awsBillCSV.LineItemOperation != hourly && (awsBillCSV.LineItemLineItemType == discountedUsage || awsBillCSV.LineItemLineItemType == usage) {
				exist = true
			}
		}
	case amazonElastiCache:
		if awsBillCSV.ProductProductFamily == "Cache Instance" {
			if awsBillCSV.LineItemOperation != hourly && (awsBillCSV.LineItemLineItemType == discountedUsage || awsBillCSV.LineItemLineItemType == usage) {
				exist = true
			}
		}
	}
	if exist {
		// instanceId
		result, err := bizutils.GetResourceUsageAgreement(awsBillCSV.LineItemProductCode, instanceId)
		if err != nil {
			return
		}
		if isEmpty(result) || result == bizutils.None {
			return
		}
		// db存在
		found = true
		rate, _ = decimal.NewFromString(result)
	}
	return
}

/*
upfront
LineItemProductCode AmzonRDS AmazonElastiCache
LineItemLineItemType Fee
LineItemUsageType   contains   HeavyUsage
*/
// func fee(content string) bool {
// 	awsBillCSV, _ := utils.UnmarshalString[aws.AwsBillCSV](content)
// 	return strings.Contains(awsBillCSV.LineItemUsageType, "HeavyUsage")
// }

func accountInstance(accountName, item string) string {
	return fmt.Sprintf(hyphenFormat, accountName, item)
}
