package billHandler

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/util"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
)

const (
	azure_name        = "azure"
	emptyCSVByteCount = 4464
)

type SyncBillAzureCloud struct {
	*BaseSyncHandler
	rule        *CostUnitComparison
	azureScode  scode
	chinaRegion bool // 区分是国际区还是中国区
}

func (s *SyncBillAzureCloud) PullBill(ctx context.Context, c client.IClient, done chan struct{}, receivedChan chan *azure.AzureBillCSV) (err error) {
	defer s.done(err, done)
	defer func() {
		if s.syncLogStageAt() != SYNC_LOG_STAGE_PULL {
			defer close(receivedChan) //放在此处是因为处理还没进入到billReportProcessing函数时的err
		}
	}()

	if _, ok := c.(*azure.BillingClient); !ok {
		return fmt.Errorf("not %s billClient", azure_name)
	}

	// data source from db
	if s.syncLogStageAt() == SYNC_LOG_STAGE_PULL {
		return findRawFromDB[azure.AzureBillCSV](s.BaseSyncHandler, done, receivedChan)
	}
	// data source from api
	bills, err := c.(*azure.BillingClient).NewListPager(ctx, s.syncLog.BillingCycle)
	if err != nil {
		return
	}
	if bills == nil {
		return
	}
	for index, b := range bills {
		if pointer.String(b.BlobLink) == empty {
			continue
		}
		if pointer.Int64(b.ByteCount) <= emptyCSVByteCount {
			continue
		}

		path := fmt.Sprintf("%s/%s_%s_%d.csv", azure_name, c.Identifier(), s.syncLog.BillingCycle, index)
		if err = s.billReportProcessing(pointer.String(b.BlobLink), path, done, receivedChan); err != nil {
			s.logger.Errorf(ctx, "billReportProcessing csv parsing failed: %s", err)
			return
		}
	}

	return
}

var azureSCodeRegexp = regexp.MustCompile(`"(?i:SCode)":\s*"([^"]+)"`)

func (s *SyncBillAzureCloud) ToBillRawData(ctx context.Context, item *azure.AzureBillCSV) []*models.RawBill {
	scode := func(tags string) (scode string) {
		scode = tags

		if result := azureSCodeRegexp.FindStringSubmatch(scode); len(result) > 1 {
			s.azureScode.scodeMap[scode] = &expectSCode{CheckSubProductsBool: true, SCode: result[1]}
		}
		return scode
	}
	// /subscriptions/e751c26b-e4e2-4f2d-b890-77b060991651/resourcegroups/gcc-dev/providers/microsoft.web/sites/d365messagehandler2-gccdev
	resourceIds := strings.Split(item.ResourceId, string(os.PathSeparator))
	var productCode, instanceId string
	if len(resourceIds) > 2 {
		instanceId = resourceIds[len(resourceIds)-1]
		// 预留实例使用ReservationName字段作为instanceId
		if isEmpty(instanceId) {
			instanceId = item.ReservationName
		}
		productCode = resourceIds[len(resourceIds)-3] + resourceIds[len(resourceIds)-2]
	}

	azureBill, v21Bill := splitAzureBillCSV(item)

	resourceGroup := item.ResourceGroup
	quantity := item.Quantity
	unitOfMeasure := item.UnitOfMeasure
	effectivePrice := item.EffectivePrice
	unitPrice := item.UnitPrice
	meterName := item.MeterName
	costInBillingCurrency := item.CostInBillingCurrency
	billingCurrency := item.BillingCurrencyCode
	meterRegion := item.MeterRegion
	location := item.ResourceLocation
	tags := item.Tags
	content := utils.JsonString(azureBill)
	// 中国区账单处理
	if s.chinaRegion {
		if isEmpty(instanceId) {
			instanceId = item.CnReservationName
		}
		resourceGroup = item.CnResourceGroupName
		quantity = item.CnQuantity
		unitOfMeasure = item.CnUnitOfMeasure
		effectivePrice = item.CnEffectivePrice
		unitPrice = item.CnUnitPrice
		meterName = item.CnMeterName
		costInBillingCurrency = item.CnCostInBillingCurrency
		billingCurrency = item.CnBillingCurrency
		meterRegion = item.CnMeterRegion
		location = item.CnResourceLocation
		content = utils.JsonString(v21Bill)
		tags = item.CnTags
	}

	payableAmount, _ := strconv.ParseFloat(costInBillingCurrency, 64)
	bills := []*models.RawBill{{
		Model:            bizutils.DataSource().Model(ctx),
		TaskId:           s.taskId,
		Vendor:           hbc.Azure.String(),
		AccountName:      s.syncLog.AccountName,
		AccountID:        s.syncLog.AccountID,
		Granularity:      BILL_GRANULARITY,
		BillingCycle:     s.billingCycle(),
		ProductCode:      strings.ToLower(productCode),
		ProductName:      item.ProductName,
		InstanceID:       strings.ToLower(instanceId),
		SubscriptionType: SUBSCRIPTION_TYPE_PAY_AS_YOU_GO,

		CostUnit:      scode(tags), //使用Tags字段拆出scode,拆不出存入原始数据.
		ResourceGroup: strings.ToLower(resourceGroup),
		Usage:         quantity,
		UsageUnit:     unitOfMeasure,
		ListPrice:     effectivePrice,
		ListPriceUnit: unitPrice,
		BillingItem:   meterName,
		PayableAmount: payableAmount,
		Currency:      billingCurrency,
		Region:        meterRegion,
		Zone:          location,
		Content:       content,

		AggregatedID: stringutil.Md5(s.syncLog.AccountID +
			strings.ToLower(productCode) +
			strings.ToLower(instanceId)),
	}}
	return bills
}

func (s *SyncBillAzureCloud) CreateRawData(ctx context.Context, done chan struct{}, receivedChan chan *azure.AzureBillCSV) (err error) {
	var batch = []*models.RawBill{}

	for {
		raw, ok := <-receivedChan
		if !ok {
			close(done)
			return s.create(ctx, batch, s.rules)
		}

		batch = append(batch, s.ToBillRawData(ctx, raw)...)
		if len(batch) >= s.batchSize {
			if err = s.create(ctx, batch, s.rules); err != nil {
				close(done)
				return
			}
			batch = nil
		}
	}
}

func (s *SyncBillAzureCloud) billReportProcessing(url, path string, done chan struct{}, receivedChan chan *azure.AzureBillCSV) error {
	if err := downloadFile(url, path); err != nil {
		return err
	}
	file, err := os.Open(path)
	if err != nil {
		return err
	}
	defer file.Close()

	fileName := path[strings.LastIndex(path, PathSeparator)+1:]
	return util.BillCsvGz(bufio.NewReader(file), fileName, headLine, done, receivedChan)
}

func (s *SyncBillAzureCloud) rules(raw []*models.RawBill) (refinedRawBills []*models.RefinedRawBill) {
	refinedRawBills = make([]*models.RefinedRawBill, 0, len(raw))
	s.azureScode.scodeMap = s.checkSCode(s.azureScode)

	for _, r := range raw {
		refinedRawBill := &models.RefinedRawBill{TaskId: s.taskId, RawBill: *s.eraseModel(r)}

		// 费率处理
		// refinedRawBill.PayableAmount, _ = s.rate(refinedRawBill.PayableAmount, refinedRawBill.Currency)

		// get scode from cmdb
		if scode, ok := s.getSCodeFromCMDB(refinedRawBill.AggregatedID); ok {
			refinedRawBills = append(refinedRawBills, scodeRule(refinedRawBill, scode, true))
			continue
		}
		var isRule bool
		if s.rule.Priority != nil {
			for _, p := range s.rule.Priority {
				switch p {
				case AZURE_ACCOUNT:
					refinedRawBill, isRule = s.priority(p, refinedRawBill.AccountName, s.rule.Rules, refinedRawBill)
				case AZURE_COMMODITY_CODE:
					if v, ok := s.rule.Rules[GetRuleKey(p, refinedRawBill.ProductCode)]; ok {
						isRule = true
						refinedRawBill.SCodeRuleMatched = v.ID
						// Haier-Global data warehouse 订阅下的磁盘实例id合并成一条即可，使用“产品编码_S码”的格式:"microsoft.computedisks_S03037"
						refinedRawBill.InstanceID = v.ResourceId
						// 重新计算aggregatedId
						s.aggregatedId(refinedRawBill)
					}
				}
			}
		}
		if !isRule {
			if rr, ok := s.scodeRule(s.azureScode.scodeMap, refinedRawBill); ok {
				refinedRawBills = append(refinedRawBills, rr)
				continue
			} else {
				refinedRawBill = scodeRule(refinedRawBill, empty, false)
			}
		}

		// 低于1USD且S码不正确的账单归为大运维
		if refinedRawBill.PayableAmount < usd1 && !bizutils.IsSCode(refinedRawBill.CostUnit) {
			refinedRawBill = scodeRule(refinedRawBill, s04076Scode, false)
		}

		refinedRawBills = append(refinedRawBills, refinedRawBill)
	}

	return
}

func (s *SyncBillAzureCloud) ReceivedChan() chan *azure.AzureBillCSV {
	return make(chan *azure.AzureBillCSV)
}

func NewSyncBillAzureCloud(taskId string, isChina bool, syncLog *models.RawBillSyncLog, rule *CostUnitComparison) *SyncBillAzureCloud {
	b := &SyncBillAzureCloud{BaseSyncHandler: NewBaseHandler(azure_name)}
	b.syncLog = syncLog
	b.taskId = taskId
	b.rule = rule
	b.chinaRegion = isChina
	b.azureScode.scodeMap = make(map[string]*expectSCode)
	return b
}

// 将完整的csv账单结构体拆成国际和中国区账单结构体
func splitAzureBillCSV(item *azure.AzureBillCSV) (azureBillCSV, azure21VBillCSV) {
	return azureBillCSV{
			InvoiceSectionName:     item.InvoiceSectionName,
			AccountName:            item.AccountName,
			AccountOwnerId:         item.AccountOwnerId,
			SubscriptionId:         item.SubscriptionId,
			SubscriptionName:       item.SubscriptionName,
			ResourceGroup:          item.ResourceGroup,
			ResourceLocation:       item.ResourceLocation,
			Date:                   item.Date,
			ProductName:            item.ProductName,
			MeterCategory:          item.MeterCategory,
			MeterSubCategory:       item.MeterSubCategory,
			MeterId:                item.MeterId,
			MeterName:              item.MeterName,
			MeterRegion:            item.MeterRegion,
			UnitOfMeasure:          item.UnitOfMeasure,
			Quantity:               item.Quantity,
			EffectivePrice:         item.EffectivePrice,
			CostInBillingCurrency:  item.CostInBillingCurrency,
			CostCenter:             item.CostCenter,
			ConsumedService:        item.ConsumedService,
			ResourceId:             item.ResourceId,
			Tags:                   item.Tags,
			OfferId:                item.OfferId,
			AdditionalInfo:         item.AdditionalInfo,
			ServiceInfo1:           item.ServiceInfo1,
			ServiceInfo2:           item.ServiceInfo2,
			ResourceName:           item.ResourceName,
			ReservationId:          item.ReservationId,
			ReservationName:        item.ReservationName,
			UnitPrice:              item.UnitPrice,
			ProductOrderId:         item.ProductOrderId,
			ProductOrderName:       item.ProductOrderName,
			Term:                   item.Term,
			PublisherType:          item.PublisherType,
			PublisherName:          item.PublisherName,
			ChargeType:             item.ChargeType,
			Frequency:              item.Frequency,
			PricingModel:           item.PricingModel,
			AvailabilityZone:       item.AvailabilityZone,
			BillingAccountId:       item.BillingAccountId,
			BillingAccountName:     item.BillingAccountName,
			BillingCurrencyCode:    item.BillingCurrencyCode,
			BillingPeriodStartDate: item.BillingPeriodStartDate,
			BillingPeriodEndDate:   item.BillingPeriodEndDate,
			BillingProfileId:       item.BillingProfileId,
			BillingProfileName:     item.BillingProfileName,
			InvoiceSectionId:       item.InvoiceSectionId,
			IsAzureCreditEligible:  item.IsAzureCreditEligible,
			PartNumber:             item.PartNumber,
			PayGPrice:              item.PayGPrice,
			PlanName:               item.PlanName,
			ServiceFamily:          item.ServiceFamily,
			CostAllocationRuleName: item.CostAllocationRuleName,
			BenefitId:              item.BenefitId,
			BenefitName:            item.BenefitName,
		}, azure21VBillCSV{
			InvoiceId:                    item.CnInvoiceId,
			PreviousInvoiceId:            item.CnPreviousInvoiceId,
			BillingAccountId:             item.CnBillingAccountId,
			BillingAccountName:           item.CnBillingAccountName,
			BillingProfileId:             item.CnBillingProfileId,
			BillingProfileName:           item.CnBillingProfileName,
			InvoiceSectionId:             item.CnInvoiceSectionId,
			InvoiceSectionName:           item.CnInvoiceSectionName,
			ResellerName:                 item.CnResellerName,
			ResellerMpnId:                item.CnResellerMpnId,
			CostCenter:                   item.CnCostCenter,
			BillingPeriodEndDate:         item.CnBillingPeriodEndDate,
			BillingPeriodStartDate:       item.CnBillingPeriodStartDate,
			ServicePeriodEndDate:         item.CnServicePeriodEndDate,
			ServicePeriodStartDate:       item.CnServicePeriodStartDate,
			Date:                         item.CnDate,
			ServiceFamily:                item.CnServiceFamily,
			ProductOrderId:               item.CnProductOrderId,
			ProductOrderName:             item.CnProductOrderName,
			ConsumedService:              item.CnConsumedService,
			MeterId:                      item.CnMeterId,
			MeterName:                    item.CnMeterName,
			MeterCategory:                item.CnMeterCategory,
			MeterSubCategory:             item.CnMeterSubCategory,
			MeterRegion:                  item.CnMeterRegion,
			ProductId:                    item.CnProductId,
			ProductName:                  item.ProductName,
			SubscriptionId:               item.CnSubscriptionId,
			SubscriptionName:             item.CnSubscriptionName,
			PublisherType:                item.CnPublisherType,
			PublisherId:                  item.CnPublisherId,
			PublisherName:                item.CnPublisherName,
			ResourceGroupName:            item.CnResourceGroupName,
			ResourceId:                   item.ResourceId,
			ResourceLocation:             item.CnResourceLocation,
			Location:                     item.CnLocation,
			EffectivePrice:               item.CnEffectivePrice,
			Quantity:                     item.CnQuantity,
			UnitOfMeasure:                item.CnUnitOfMeasure,
			ChargeType:                   item.CnChargeType,
			BillingCurrency:              item.CnBillingCurrency,
			PricingCurrency:              item.CnPricingCurrency,
			CostInBillingCurrency:        item.CnCostInBillingCurrency,
			CostInPricingCurrency:        item.CnCostInPricingCurrency,
			CostInUsd:                    item.CnCostInUsd,
			PaygCostInBillingCurrency:    item.CnPaygCostInBillingCurrency,
			PaygCostInUsd:                item.CnPaygCostInUsd,
			ExchangeRatePricingToBilling: item.CnExchangeRatePricingToBilling,
			ExchangeRateDate:             item.CnExchangeRateDate,
			IsAzureCreditEligible:        item.CnIsAzureCreditEligible,
			ServiceInfo1:                 item.CnServiceInfo1,
			ServiceInfo2:                 item.CnServiceInfo2,
			AdditionalInfo:               item.CnAdditionalInfo,
			Tags:                         item.CnTags,
			PayGPrice:                    item.CnPayGPrice,
			Frequency:                    item.CnFrequency,
			Term:                         item.CnTerm,
			ReservationId:                item.CnReservationId,
			ReservationName:              item.CnReservationName,
			PricingModel:                 item.CnPricingModel,
			UnitPrice:                    item.CnUnitPrice,
			CostAllocationRuleName:       item.CnCostAllocationRuleName,
			BenefitId:                    item.CnBenefitId,
			BenefitName:                  item.CnBenefitName,
			Provider:                     item.CnProvider,
		}
}

// azure国际版csv账单数据
type azureBillCSV struct {
	InvoiceSectionName     string `csv:"InvoiceSectionName"`
	AccountName            string `csv:"AccountName"`
	AccountOwnerId         string `csv:"AccountOwnerId"`
	SubscriptionId         string `csv:"SubscriptionId"`
	SubscriptionName       string `csv:"SubscriptionName"`
	ResourceGroup          string `csv:"ResourceGroup"`
	ResourceLocation       string `csv:"ResourceLocation"`
	Date                   string `csv:"Date"`
	ProductName            string `csv:"ProductName"`
	MeterCategory          string `csv:"MeterCategory"`
	MeterSubCategory       string `csv:"MeterSubCategory"`
	MeterId                string `csv:"MeterId"`
	MeterName              string `csv:"MeterName"`
	MeterRegion            string `csv:"MeterRegion"`
	UnitOfMeasure          string `csv:"UnitOfMeasure"`
	Quantity               string `csv:"Quantity"`
	EffectivePrice         string `csv:"EffectivePrice"`
	CostInBillingCurrency  string `csv:"CostInBillingCurrency"`
	CostCenter             string `csv:"CostCenter"`
	ConsumedService        string `csv:"ConsumedService"`
	ResourceId             string `csv:"ResourceId"`
	Tags                   string `csv:"Tags"`
	OfferId                string `csv:"OfferId"`
	AdditionalInfo         string `csv:"AdditionalInfo"`
	ServiceInfo1           string `csv:"ServiceInfo1"`
	ServiceInfo2           string `csv:"ServiceInfo2"`
	ResourceName           string `csv:"ResourceName"`
	ReservationId          string `csv:"ReservationId"`
	ReservationName        string `csv:"ReservationName"`
	UnitPrice              string `csv:"UnitPrice"`
	ProductOrderId         string `csv:"ProductOrderId"`
	ProductOrderName       string `csv:"ProductOrderName"`
	Term                   string `csv:"Term"`
	PublisherType          string `csv:"PublisherType"`
	PublisherName          string `csv:"PublisherName"`
	ChargeType             string `csv:"ChargeType"`
	Frequency              string `csv:"Frequency"`
	PricingModel           string `csv:"PricingModel"`
	AvailabilityZone       string `csv:"AvailabilityZone"`
	BillingAccountId       string `csv:"BillingAccountId"`
	BillingAccountName     string `csv:"BillingAccountName"`
	BillingCurrencyCode    string `csv:"BillingCurrencyCode"`
	BillingPeriodStartDate string `csv:"BillingPeriodStartDate"`
	BillingPeriodEndDate   string `csv:"BillingPeriodEndDate"`
	BillingProfileId       string `csv:"BillingProfileId"`
	BillingProfileName     string `csv:"BillingProfileName"`
	InvoiceSectionId       string `csv:"InvoiceSectionId"`
	IsAzureCreditEligible  string `csv:"IsAzureCreditEligible"`
	PartNumber             string `csv:"PartNumber"`
	PayGPrice              string `csv:"PayGPrice"`
	PlanName               string `csv:"PlanName"`
	ServiceFamily          string `csv:"ServiceFamily"`
	CostAllocationRuleName string `csv:"CostAllocationRuleName"`
	BenefitId              string `csv:"benefitId"`
	BenefitName            string `csv:"benefitName"`
}

// azure中国区csv账单数据
type azure21VBillCSV struct {
	InvoiceId                    string `csv:"invoiceId"`
	PreviousInvoiceId            string `csv:"previousInvoiceId"`
	BillingAccountId             string `csv:"billingAccountId"`
	BillingAccountName           string `csv:"billingAccountName"`
	BillingProfileId             string `csv:"billingProfileId"`
	BillingProfileName           string `csv:"billingProfileName"`
	InvoiceSectionId             string `csv:"invoiceSectionId"`
	InvoiceSectionName           string `csv:"invoiceSectionName"`
	ResellerName                 string `csv:"resellerName"`
	ResellerMpnId                string `csv:"resellerMpnId"`
	CostCenter                   string `csv:"costCenter"`
	BillingPeriodEndDate         string `csv:"billingPeriodEndDate"`
	BillingPeriodStartDate       string `csv:"billingPeriodStartDate"`
	ServicePeriodEndDate         string `csv:"servicePeriodEndDate"`
	ServicePeriodStartDate       string `csv:"servicePeriodStartDate"`
	Date                         string `csv:"date"`
	ServiceFamily                string `csv:"serviceFamily"`
	ProductOrderId               string `csv:"productOrderId"`
	ProductOrderName             string `csv:"productOrderName"`
	ConsumedService              string `csv:"consumedService"`
	MeterId                      string `csv:"meterId"`
	MeterName                    string `csv:"meterName"`
	MeterCategory                string `csv:"meterCategory"`
	MeterSubCategory             string `csv:"meterSubCategory"`
	MeterRegion                  string `csv:"meterRegion"`
	ProductId                    string `csv:"ProductId"`
	ProductName                  string `csv:"ProductName"`
	SubscriptionId               string `csv:"SubscriptionId"`
	SubscriptionName             string `csv:"subscriptionName"`
	PublisherType                string `csv:"publisherType"`
	PublisherId                  string `csv:"publisherId"`
	PublisherName                string `csv:"publisherName"`
	ResourceGroupName            string `csv:"resourceGroupName"`
	ResourceId                   string `csv:"ResourceId"`
	ResourceLocation             string `csv:"resourceLocation"`
	Location                     string `csv:"location"`
	EffectivePrice               string `csv:"effectivePrice"`
	Quantity                     string `csv:"quantity"`
	UnitOfMeasure                string `csv:"unitOfMeasure"`
	ChargeType                   string `csv:"chargeType"`
	BillingCurrency              string `csv:"billingCurrency"`
	PricingCurrency              string `csv:"pricingCurrency"`
	CostInBillingCurrency        string `csv:"costInBillingCurrency"`
	CostInPricingCurrency        string `csv:"costInPricingCurrency"`
	CostInUsd                    string `csv:"costInUsd"`
	PaygCostInBillingCurrency    string `csv:"paygCostInBillingCurrency"`
	PaygCostInUsd                string `csv:"paygCostInUsd"`
	ExchangeRatePricingToBilling string `csv:"exchangeRatePricingToBilling"`
	ExchangeRateDate             string `csv:"exchangeRateDate"`
	IsAzureCreditEligible        string `csv:"isAzureCreditEligible"`
	ServiceInfo1                 string `csv:"serviceInfo1"`
	ServiceInfo2                 string `csv:"serviceInfo2"`
	AdditionalInfo               string `csv:"additionalInfo"`
	Tags                         string `csv:"tags"`
	PayGPrice                    string `csv:"PayGPrice"`
	Frequency                    string `csv:"frequency"`
	Term                         string `csv:"term	"`
	ReservationId                string `csv:"reservationId"`
	ReservationName              string `csv:"reservationName"`
	PricingModel                 string `csv:"pricingModel"`
	UnitPrice                    string `csv:"unitPrice"`
	CostAllocationRuleName       string `csv:"costAllocationRuleName"`
	BenefitId                    string `csv:"benefitId"`
	BenefitName                  string `csv:"benefitName"`
	Provider                     string `csv:"provider"`
}
