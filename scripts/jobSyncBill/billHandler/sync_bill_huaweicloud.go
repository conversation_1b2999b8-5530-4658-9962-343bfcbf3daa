package billHandler

import (
	"context"
	"fmt"
	"strings"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/bss/v2/model"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
)

const huawei_name = "huaweicloud"

const (
	product_code_antiddos      = "hws.service.type.antiddos"
	product_code_das           = "hws.service.type.das"
	product_code_functionstage = "hws.service.type.functionstage"
	product_code_spot_cse      = "hws.service.type.cse"
	product_code_spot_dds      = "hws.service.type.dds"
	product_code_spot_drs      = "hws.service.type.drs"
	product_code_spot_lts      = "hws.service.type.lts"
	product_code_sport_rtcsms  = "hws.service.type.rtcsms"
	product_code_spot_nosql    = "hws.service.type.nosql"
	product_code_spot_rds      = "hws.service.type.rds"
	product_code_spot_taurus   = "hws.service.type.taurus"
)

var subRdsDict = []string{
	// rds
	"hws.resource.type.rds.obs",
	"hws.resource.type.rds.volume",
	"hws.resource.type.rds.vm",
	"hws.resource.type.rds.proxy",
	// taurus
	"hws.resource.type.taurus.obs",
	"hws.resource.type.taurus.volume",
	"hws.resource.type.taurus.vm",
	// nosql
	"hws.resource.type.nosql.obs",
	"hws.resource.type.nosql.vm",
	"hws.resource.type.nosql.volume",
}

type SyncBillHuaweicloud struct {
	*BaseSyncHandler
	rule        *CostUnitComparison
	huaweiScode scode
}

// api拉取账单数据
func (s *SyncBillHuaweicloud) PullBill(ctx context.Context, c client.IClient, done chan struct{}, receivedChan chan *model.MonthlyBillRes) (err error) {
	defer s.done(err, done)

	if _, ok := c.(*huaweicloud.BssClient); !ok {
		return fmt.Errorf("not %s billClient", huawei_name)
	}

	return c.(*huaweicloud.BssClient).ListCustomerBillsFeeRecordsSolution(ctx, s.syncLog.BillingCycle, done, receivedChan)
}

// 结构体数据转换
func (s *SyncBillHuaweicloud) ToBillRawData(ctx context.Context, data *model.MonthlyBillRes) []*models.RawBill {
	var subscription string
	mode := pointer.Int32(data.ChargeMode)
	if mode == 1 {
		subscription = SUBSCRIPTION_TYPE_SUBSCRIPTION
	} else if mode == 3 {
		subscription = SUBSCRIPTION_TYPE_PAY_AS_YOU_GO
	} else {
		subscription = fmt.Sprint(mode)
	}

	return []*models.RawBill{{
		Model:            bizutils.DataSource().Model(ctx),
		TaskId:           s.taskId,
		Vendor:           hbc.HuaweiCloud.String(),
		AccountName:      s.syncLog.AccountName,
		AccountID:        *data.CustomerId,
		Granularity:      BILL_GRANULARITY,
		BillingCycle:     s.billingCycle(),
		ProductCode:      pointer.String(data.CloudServiceType),
		ProductName:      pointer.String(data.CloudServiceTypeName),
		ProductDetail:    pointer.String(data.ResourceTypeName),
		BillingItem:      pointer.String(data.ResourceTypeCode),
		ResourceGroup:    pointer.String(data.EnterpriseProjectId),
		InstanceID:       pointer.String(data.ResInstanceId),
		CashAmount:       pointer.Value(data.CashAmount).InexactFloat64(),
		PayableAmount:    pointer.Value(data.ConsumeAmount).InexactFloat64(),
		VoucherAmount:    pointer.Value(data.CouponAmount).InexactFloat64(),
		CostUnit:         s.scode(pointer.String(data.EnterpriseProjectName), s.huaweiScode.scodeMap),
		Currency:         RMB,
		Region:           pointer.String(data.RegionName),
		Zone:             pointer.String(data.Region),
		SubscriptionType: subscription,
		Content:          utils.JsonString(data),
		AggregatedID: stringutil.Md5(*data.CustomerId +
			*data.CloudServiceType +
			pointer.String(data.ResInstanceId)),
	}}
}

// 数据通道
func (s *SyncBillHuaweicloud) ReceivedChan() chan *model.MonthlyBillRes {
	return make(chan *model.MonthlyBillRes)
}

// 创建数据
func (s *SyncBillHuaweicloud) CreateRawData(ctx context.Context, done chan struct{}, receivedChan chan *model.MonthlyBillRes) (err error) {
	var batch = []*models.RawBill{}

	for {
		raw, ok := <-receivedChan
		if !ok {
			close(done)
			return s.create(ctx, batch, s.rules)
		}

		batch = append(batch, s.ToBillRawData(ctx, raw)...)
		if len(batch) >= s.batchSize {
			if err = s.create(ctx, batch, s.rules); err != nil {
				close(done)
				return
			}
			batch = nil
		}
	}
}

func (s *SyncBillHuaweicloud) rules(raw []*models.RawBill) (refinedRawBills []*models.RefinedRawBill) {
	refinedRawBills = make([]*models.RefinedRawBill, 0, len(raw))
	s.huaweiScode.scodeMap = s.checkSCode(s.huaweiScode)
	for _, r := range raw {
		refinedRawBill := &models.RefinedRawBill{TaskId: s.taskId, RawBill: *s.eraseModel(r)}
		// split instanceId start ---
		var recompute bool
		firstIndex, secondIndex, thirdIndex := 0, 1, 2
		instanceId := refinedRawBill.InstanceID
		switch refinedRawBill.ProductCode {
		case product_code_antiddos:
			refinedRawBill.InstanceID, _ = instanceIdSupplementID(spot, instanceId, secondIndex, secondIndex)
			recompute = true
		case product_code_das:
			if strings.Contains(instanceId, spot) {
				refinedRawBill.InstanceID, _ = instanceIdSupplementID(spot, instanceId, thirdIndex, thirdIndex)
			}
			if strings.Contains(instanceId, colon) {
				refinedRawBill.InstanceID, _ = instanceIdSupplementID(colon, instanceId, thirdIndex, thirdIndex)
			}
			recompute = true
		case product_code_spot_cse, product_code_spot_dds, product_code_spot_drs, product_code_spot_lts:
			refinedRawBill.InstanceID, _ = instanceIdSupplementID(spot, instanceId, firstIndex, firstIndex)
			recompute = true
		case product_code_spot_nosql, product_code_spot_rds, product_code_spot_taurus:
			refinedRawBill.InstanceID, _ = instanceIdSupplementID(spot, instanceId, firstIndex, firstIndex)
			// 拆出子项的拆分项
			for _, code := range subRdsDict {
				if refinedRawBill.BillingItem == code {
					refinedRawBill.SupplementID = instanceId
					break
				}
			}
			recompute = true
		case product_code_functionstage:
			refinedRawBill.InstanceID, refinedRawBill.SupplementID = instanceIdSupplementID(spot, instanceId, firstIndex, secondIndex)
			recompute = true
		case product_code_sport_rtcsms:
			refinedRawBill.SupplementID = refinedRawBill.CostUnit
			recompute = true
		}
		if recompute {
			s.aggregatedId(refinedRawBill)
		}
		// --- split instanceId end

		// get scode from cmdb
		if scode, ok := s.getSCodeFromCMDB(refinedRawBill.AggregatedID); ok {
			refinedRawBills = append(refinedRawBills, scodeRule(refinedRawBill, scode, true))
			continue
		}

		var isRule bool
		if s.rule.Priority != nil {
			for _, p := range s.rule.Priority {
				switch p {
				case HUAWEI_COMMODITY_CODE:
					refinedRawBill, isRule = s.priority(p, refinedRawBill.ProductCode, s.rule.Rules, refinedRawBill)
				case HUAWEI_RULE_VALUE:
					refinedRawBill, isRule = s.priority(p, refinedRawBill.CostUnit, s.rule.Rules, refinedRawBill)
				case HUAWEI_ACCOUNT:
					refinedRawBill, isRule = s.priority(p, refinedRawBill.AccountName, s.rule.Rules, refinedRawBill)
				}
			}
		}
		if !isRule {
			if rr, ok := s.scodeRule(s.huaweiScode.scodeMap, refinedRawBill); ok {
				refinedRawBills = append(refinedRawBills, rr)
				continue
			} else {
				refinedRawBill = scodeRule(refinedRawBill, empty, false)
			}
		}

		refinedRawBills = append(refinedRawBills, refinedRawBill)
	}
	return
}

func NewSyncBillHuaweicloud(taskId string, syncLog *models.RawBillSyncLog, rule *CostUnitComparison) SyncHandler[model.MonthlyBillRes] {
	b := &SyncBillHuaweicloud{BaseSyncHandler: NewBaseHandler(huawei_name)}
	b.syncLog = syncLog
	b.taskId = taskId
	b.rule = rule
	b.huaweiScode.scodeMap = make(map[string]*expectSCode)
	return b
}
