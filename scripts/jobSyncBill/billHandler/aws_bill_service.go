package billHandler

import (
	"context"
	"fmt"
	"os"
	"strings"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	commonContext "git.haier.net/devops/ops-golang-common/utils/context"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const (
	natGateway         = "natgateway"
	snapshot           = "snapshot"
	snapshot_key_scode = "SCode"
	vol                = "vol"
	dataTransfer       = "DataTransfer"
	cluster            = "cluster"
	pod                = "pod"
	wafv2              = "wafv2"
	boxUsage           = "BoxUsage"
	hourly             = "Hourly"
	runInstances       = "RunInstances"
	fee                = "Fee"
	riFee              = "RIFee"

	// productCode
	amazonEC2                        = "AmazonEC2"
	amazonRDS                        = "AmazonRDS"
	amazonVPC                        = "AmazonVPC"
	awselb                           = "AWSELB"
	um7rif6n97tra9v1l09q16ct4        = "um7rif6n97tra9v1l09q16ct4"
	amazonCloudWatch                 = "AmazonCloudWatch"
	amazonCloudWatchInstanceId       = "CloudWatch-Service"
	amazonECR                        = "AmazonECR"
	amazonElastiCache                = "AmazonElastiCache"
	amazonS3                         = "AmazonS3"
	amazonSNS                        = "AmazonSNS"
	awsDataTransfer                  = "AWSDataTransfer"
	awsDirectConnect                 = "AWSDirectConnect"
	awsEvents                        = "AWSEvents"
	awskms                           = "awskms"
	awsQueueService                  = "AWSQueueService"
	awsSystemsManager                = "AWSSystemsManager"
	amazonEKS                        = "AmazonEKS"
	amazonCloudFront                 = "AmazonCloudFront"
	awsLambda                        = "AWSLambda"
	amazonApiGateway                 = "AmazonApiGateway"
	amazonEFS                        = "AmazonEFS"
	amazonLocationService            = "AmazonLocationService"
	amazonRoute53                    = "AmazonRoute53"
	awsCloudTrail                    = "AWSCloudTrail"
	awsCostExplorer                  = "AWSCostExplorer"
	s03980_9mqo1srq55eig8rgep8vu0t96 = "9mqo1srq55eig8rgep8vu0t96"
	awswaf                           = "awswaf"
	amazonDevOpsGuru                 = "AmazonDevOpsGuru"
	amazonSES                        = "AmazonSES"
	awsCloudShell                    = "AWSCloudShell"
	awsDeveloperSupport              = "AWSDeveloperSupport"
	amazonChimeBusinessCalling       = "AmazonChimeBusinessCalling"
	amazonLightsail                  = "AmazonLightsail"
	amazonFSx                        = "AmazonFSx"
	amazonKinesis                    = "AmazonKinesis"
	amazonDocDB                      = "AmazonDocDB"
	amazonDynamoDB                   = "AmazonDynamoDB"
	amazonKinesisAnalytics           = "AmazonKinesisAnalytics"
	amazonMSK                        = "AmazonMSK"
	awsBackup                        = "AWSBackup"
	awsMigrationHubRefactorSpaces    = "AWSMigrationHubRefactorSpaces"
	awsSecretsManager                = "AWSSecretsManager"
	amazonES                         = "AmazonES"
	amazonRegistrar                  = "AmazonRegistrar"
	susnntmq07e4q1kbkdtgbasvv        = "susnntmq07e4q1kbkdtgbasvv"
	aws_2wqkpek696qhdeo7lbbjncqli    = "2wqkpek696qhdeo7lbbjncqli"
	awsGlue                          = "AWSGlue"
	awsXRay                          = "AWSXRay"
	amazonStates                     = "AmazonStates"
	awsAppSync                       = "AWSAppSync"
	amazonCognito                    = "AmazonCognito"
	amazonGlacier                    = "AmazonGlacier"
	aws_9fj8cq737hqz8dzn4nzdloa5u    = "9fj8cq737hqz8dzn4nzdloa5u"
	awsServiceCatalog                = "AWSServiceCatalog"
	amazonMQ                         = "AmazonMQ"
	awsDatabaseMigrationSvc          = "AWSDatabaseMigrationSvc"
	AWSSupportEnterprise             = "AWSSupportEnterprise"

	awsTransfer         = "AWSTransfer"
	amazonAthena        = "AmazonAthena"
	awsSupportBusiness  = "AWSSupportBusiness"
	amazonPinpoint      = "AmazonPinpoint"
	amazonWorkSpaces    = "AmazonWorkSpaces"
	awsDirectoryService = "AWSDirectoryService"
	ocbAWSSkillBuilder  = "OCBAWSskillbuilder"
	awsCloudFormation   = "AWSCloudFormation"
	amazonSWF           = "AmazonSWF"
	amazonRedshift      = "AmazonRedshift"
	computeSavingsPlans = "ComputeSavingsPlans"
)

// aws支持resourceId为空tag一定为空
func (s *SyncBillAwsCloud) awsEmptyResourceIdItemTypeRule(rr *models.RefinedRawBill) (*models.RefinedRawBill, bool) {
	if !isEmpty(rr.InstanceID) {
		return rr, false
	}
	zero := 0.0
	switch rr.BillingItem {
	case credit, savingsPlanNegation, savingsPlanCoveredUsage:
		rr.PayableAmount = zero
	case savingsPlanRecurringFee, usage, tax:
	case fee: // ri的全预付款情况后续只会存在无预付
	default:
		s.logger.Warnf(s.syncLog.Ctx, "awsEmptyResourceIdItemTypeRule other line_item_line_item_type: %s", rr.BillingItem)
	}
	rr.InstanceID = accountInstance(rr.AccountName, rr.BillingItem)
	rr.CostUnit = s04076Scode
	rr.SCodeUnverified = s04076Scode
	return rr, true
}

// 亚马逊云获取账单消费 https://ihaier.feishu.cn/wiki/V7w8wy7bMiZTP8kKjX2cc0OTnCh
func (s *SyncBillAwsCloud) ruleFill() {
	ruleMap = make(map[string]rule, 0)
	var ( // sub string index
		first        = 0
		last         = 1
		secondToLast = 2
		thirdToLast  = 3
	)

	// funcs
	part := func(resourceId string, index int, front, isSeparator bool) string {
		defer func() {
			if e := recover(); e != nil {
				s.logger.Warnf(commonContext.NewContext(), "ruleFill part() error: %s", e)
				return
			}
		}()
		if isEmpty(resourceId) {
			return resourceId
		}
		var parts []string
		if isSeparator {
			parts = strings.Split(resourceId, string(os.PathSeparator)) // /
		} else {
			parts = strings.Split(resourceId, string(os.PathListSeparator)) // :
		}
		if len(parts) < index {
			return empty
		}
		if front {
			// 从前往后截取
			return parts[index]
		} else {
			// 从后往前截取
			return parts[len(parts)-index]
		}
	}
	ec2Tags := func(tags []types.Tag) (scode string) {
		for _, t := range tags {
			if pointer.String(t.Key) == snapshot_key_scode {
				scode = pointer.String(t.Value)
				break
			}
		}
		return
	}
	tagSCode := func(resourceTag, unable string) (scode string) {
		if isEmpty(resourceTag) {
			return unable
		}
		return strings.TrimSpace(resourceTag)
	}
	s04076 := func(accountName, product string) (string, string) {
		return s04076Scode, accountInstance(accountName, product)
	}
	instance := func(instanceId, accountName, product string, part func() string) string {
		if isEmpty(instanceId) {
			// 获取不到拼起来[账号-产品编码]
			return accountInstance(accountName, product)
		}
		return part()
	}

	// filling
	ruleMap[amazonEC2] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 1
		// resourceId为空且是RunInstances金额负数的 忽略
		if isEmpty(raw.InstanceID) && strings.Contains(raw.BillingItem, runInstances) && raw.Cost < 0 {
			return
		}
		// resourceId为空 大运维:账号-EC2
		if isEmpty(raw.InstanceID) {
			return s04076Scode, accountInstance(raw.AccountName, amazonEC2)
		}
		scode = tagSCode(raw.CostUnit, unallocated)

		ctx := context.Background()
		region := raw.Region
		if isEmpty(raw.Region) {
			region = s.aws.Region
		}
		cloudAccount := bizutils.GetCloudAccount(ctx, hbc.AWS.String(), raw.AccountID)
		cli := aws.NewEc2Client(region, cloudAccount.AccountName, cloudAccount.AccessKey, cloudAccount.AccessKeySecret, cloudAccount.PurposeType, false)
		// vol&instance
		if strings.Contains(raw.InstanceID, vol+hyphen) {
			return getSaveAWSInstance(ctx, cli, raw.InstanceID, true, ec2Tags)
		}
		if strings.Contains(raw.InstanceID, "i"+hyphen) {
			// 重新计算,使用价目表中的单价作为原始账单数据单价.
			if strings.Contains(raw.UsageUnit, boxUsage) && strings.Contains(raw.BillingItem, runInstances) {
				ec2InstancePricing(ctx, raw)
			}
			return getSaveAWSInstance(ctx, cli, raw.InstanceID, false, ec2Tags)
		}

		// natGateway snapshot
		productParts := strings.Split(part(raw.InstanceID, last, false, false), string(os.PathSeparator))
		if len(productParts) < 2 {
			s.logger.Errorf(ctx, "instanceId: %s split product parts failed", raw.InstanceID)
			return scode, "Ec2splitErr"
		}
		name := productParts[0]
		snapshotId := productParts[1]
		switch name {
		case natGateway:
			return s04076Scode, snapshotId
		case snapshot:
			// cache hit
			cacheScode, rawResourceId, err := bizutils.GetResourceSupplement(raw.AccountName, amazonEC2, snapshotId)
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					s.logger.Error(commonContext.NewContext(), fmt.Sprintf("snapshot getResourceSupplement failed: instanceID: %s, error: %s", raw.InstanceID, err.Error()))
				}
			}
			if !isEmpty(cacheScode) {
				return cacheScode, rawResourceId
			}

			// snapshot request start ---
			snapshots, err := cli.DescribeSnapshots(ctx, func(input *ec2.DescribeSnapshotsInput) { input.SnapshotIds = []string{snapshotId} })
			instanceId = snapshotId
			if err == nil {
				if len(snapshots) == 0 {
					scode = notFountSnapshotVolIdErr // found snapshot not found volumeId
				} else {
					// ec2 instanceId
					scode, instanceId = getSaveAWSInstance(ctx, cli, pointer.String(snapshots[0].VolumeId), true, ec2Tags)
					if strings.Contains(scode, unallocated) {
						if s := ec2Tags(snapshots[0].Tags); !isEmpty(s) {
							scode = s
						}
					}
				}
			} else {
				scode = err.Error()
			}
			// --- snapshot request end
			if err := ec2VolInstanceSave(ctx, &models.ResourceSupplement{
				Scode:         scode,
				ResourceId:    snapshotId,
				RawResourceId: instanceId,
				Code:          amazonEC2,
				Account:       raw.AccountName,
			}); err != nil {
				s.logger.Warnf(ctx, "amazonEC2 saveAWSSupplement failed: %s , instanceId: %s", err, snapshotId)
			}
		}
		return
	}

	ruleMap[amazonRDS] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 2
		if isEmpty(raw.InstanceID) {
			return s04076(raw.AccountName, amazonRDS)
		}
		// AK列包含DataTransfer单独查询CMDB获取scode
		// if strings.Contains(raw.UsageUnit, dataTransfer) {
		ctx := context.Background()
		instanceId = part(raw.InstanceID, last, false, false)
		// 查询rc_database_info
		databaseInfo, err := getAwsRdsSCodeByInstanceName(ctx, instanceId)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				scode = cost_unit_catch + gorm.ErrRecordNotFound.Error()
				return
			}
			scode = cost_unit_catch + "getAwsRdsSCodeByInstanceName Err:" + err.Error()
			return
		}
		scode = databaseInfo.Scode
		return
		// }
		// 非DataTransfer的情况
		// return tagSCode(raw.CostUnit, unallocated), part(raw.InstanceID, last, false, false)
	}
	ruleMap[amazonVPC] = func(raw *models.RawBill) (scode, instanceId string) { return s04076(raw.AccountName, amazonVPC) } // aws rule 3
	ruleMap[awselb] = func(raw *models.RawBill) (scode, instanceId string) {                                                // aws rule 4
		return tagSCode(raw.CostUnit, s04076Scode), part(raw.InstanceID, secondToLast, false, true)
	}
	ruleMap[um7rif6n97tra9v1l09q16ct4] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 5
		return tagSCode(raw.CostUnit, s04076Scode), part(raw.InstanceID, last, false, true)
	}
	ruleMap[amazonCloudWatch] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 6
		return s04076(raw.AccountName, amazonCloudWatchInstanceId)
	}
	ruleMap[amazonECR] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 7
		if isEmpty(raw.InstanceID) {
			return s04076(raw.AccountName, amazonECR)
		}
		return tagSCode(raw.CostUnit, s04076Scode), fmt.Sprintf(hyphenFormat, part(raw.InstanceID, secondToLast, false, true), part(raw.InstanceID, last, false, true))
	}
	ruleMap[amazonElastiCache] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 8
		if isEmpty(raw.InstanceID) {
			return s04076(raw.AccountName, amazonElastiCache)
		}
		return tagSCode(raw.CostUnit, s04076Scode), part(raw.InstanceID, last, false, false)
	}
	ruleMap[amazonS3] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 9
		if isEmpty(raw.InstanceID) {
			return s04076(raw.AccountName, amazonS3)
		}
		return tagSCode(raw.CostUnit, s04076Scode), raw.InstanceID
	}
	ruleMap[amazonSNS] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 10
		if isEmpty(raw.InstanceID) {
			return s04076(raw.AccountName, amazonSNS)
		}
		return tagSCode(raw.CostUnit, s04076Scode), part(raw.InstanceID, last, false, false)
	}
	ruleMap[awsDataTransfer] = func(raw *models.RawBill) (scode, instanceId string) { return s04076(raw.AccountName, awsDataTransfer) }   // aws rule 11
	ruleMap[awsDirectConnect] = func(raw *models.RawBill) (scode, instanceId string) { return s04076(raw.AccountName, awsDirectConnect) } // aws rule 12
	ruleMap[awsEvents] = func(raw *models.RawBill) (scode, instanceId string) { return s04076(raw.AccountName, awsEvents) }               // aws rule 13
	ruleMap[awskms] = func(raw *models.RawBill) (scode, instanceId string) {                                                              // aws rule 14
		return tagSCode(raw.CostUnit, s04076Scode), part(raw.InstanceID, last, false, true)
	}
	ruleMap[awsQueueService] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 15
		if isEmpty(raw.InstanceID) {
			return s04076(raw.AccountName, awsQueueService)
		}
		instanceId = part(raw.InstanceID, last, false, false)
		scode = tagSCode(raw.CostUnit, s04076Scode)
		return
	}
	ruleMap[awsSystemsManager] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 16
		return s04076(raw.AccountName, awsSystemsManager)
	}
	ruleMap[amazonEKS] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 17
		ctx := context.Background()
		scode = tagSCode(raw.CostUnit, s04076Scode)

		// 为空的情况 实例为：账号-AmazonEKS，费用的话全部按照0处理，不要按照原始账单进行取
		if isEmpty(raw.InstanceID) {
			raw.Cost = 0
			instanceId = accountInstance(raw.AccountName, amazonEKS)
			return
		}

		lastPart := part(raw.InstanceID, last, false, false)
		name := part(lastPart, first, true, true)
		instanceId = part(lastPart, last, true, true)
		// 可以打标签从tag里面进行获取 cluster可以进行打标签
		if name == cluster {
			// get from cache
			if err := saveUpdateAWSSupplement(ctx, &models.ResourceSupplement{
				Scode:      scode,
				ResourceId: instanceId,
				Account:    raw.AccountName,
				Code:       amazonEKS,
			}); err != nil {
				s.logger.Warnf(ctx, "saveUpdateAWSSupplement failed: %s , instanceId: %s", err, instanceId)
			}
			return
		}
		// pod 不可以打标签，截取id后根据cluster的id进行分配
		if name == pod {
			cacheScode, _, err := bizutils.GetResourceSupplement(raw.AccountName, amazonEKS, raw.InstanceID)
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					s.logger.Error(commonContext.NewContext(), fmt.Sprintf("pod getResourceSupplement failed: instanceID: %s, error: %s", raw.InstanceID, err.Error()))
					scode = cost_unit_catch + err.Error()
					return
				}
			}
			if !isEmpty(cacheScode) {
				scode = cacheScode
				return
			}
			scode = cost_unit_catch + podNotFountErr
			return
		}
		scode = cost_unit_catch
		return
	}
	ruleMap[amazonCloudFront] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 18
		return tagSCode(raw.CostUnit, s04076Scode), part(raw.InstanceID, last, false, true)
	}
	ruleMap[awsLambda] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 19
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, awsLambda, func() string {
			return part(raw.InstanceID, last, false, false)
		})
	}
	ruleMap[amazonApiGateway] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 20
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonApiGateway, func() string {
			return part(raw.InstanceID, thirdToLast, false, true)
		})
	}
	ruleMap[amazonEFS] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 21
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonEFS, func() string {
			return part(raw.InstanceID, last, false, true)
		})
	}
	ruleMap[amazonLocationService] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 22
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonLocationService, func() string {
			return part(raw.InstanceID, last, false, true)
		})
	}
	ruleMap[amazonRoute53] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 23
		return s04076Scode, part(raw.InstanceID, last, false, false)
	}
	ruleMap[awsCloudTrail] = func(raw *models.RawBill) (scode, instanceId string) { return s04076(raw.AccountName, awsCloudTrail) }     // aws rule 24
	ruleMap[awsCostExplorer] = func(raw *models.RawBill) (scode, instanceId string) { return s04076(raw.AccountName, awsCostExplorer) } // aws rule 25
	ruleMap[s03980_9mqo1srq55eig8rgep8vu0t96] = func(raw *models.RawBill) (scode, instanceId string) {                                  // aws rule 26
		return s03980Scode, accountInstance(raw.AccountName, wafv2)
	}
	ruleMap[awswaf] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 27
		return s03980Scode, accountInstance(raw.AccountName, wafv2)
	}
	ruleMap[amazonDevOpsGuru] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 28
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonDevOpsGuru)
	}
	ruleMap[amazonSES] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 29
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonSES)
	}
	ruleMap[awsCloudShell] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 30
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsCloudShell)
	}
	ruleMap[awsDeveloperSupport] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 31
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsDeveloperSupport)
	}
	ruleMap[amazonChimeBusinessCalling] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 32
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonChimeBusinessCalling)
	}
	ruleMap[amazonLightsail] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 33
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonLightsail)
	}
	ruleMap[amazonFSx] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 34
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonFSx, func() string {
			return part(raw.InstanceID, last, false, true)
		})
	}
	ruleMap[amazonKinesis] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 35
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonKinesis, func() string {
			return part(raw.InstanceID, last, false, true)
		})
	}
	ruleMap[amazonDocDB] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 36
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonDocDB, func() string {
			return part(raw.InstanceID, last, false, false)
		})
	}
	ruleMap[amazonDynamoDB] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 37
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonDynamoDB, func() string {
			return part(raw.InstanceID, last, false, true)
		})
	}
	ruleMap[amazonKinesisAnalytics] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 38
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonKinesisAnalytics, func() string {
			return part(raw.InstanceID, last, false, true)
		})
	}
	ruleMap[amazonMSK] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 39
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonMSK, func() string {
			return part(raw.InstanceID, secondToLast, false, true)
		})
	}
	ruleMap[awsBackup] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 40
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, awsBackup, func() string {
			return part(raw.InstanceID, last, false, false)
		})
	}
	ruleMap[awsMigrationHubRefactorSpaces] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 41
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsMigrationHubRefactorSpaces)
	}
	ruleMap[awsSecretsManager] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 42
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsSecretsManager)
	}
	ruleMap[amazonES] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 43
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonES)
	}
	ruleMap[amazonRegistrar] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 44
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonRegistrar)
	}
	ruleMap[susnntmq07e4q1kbkdtgbasvv] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 45
		return tagSCode(raw.CostUnit, cost_unit_catch), instance(raw.InstanceID, raw.AccountName, susnntmq07e4q1kbkdtgbasvv, func() string {
			return part(raw.InstanceID, last, false, true)
		})
	}
	ruleMap[aws_2wqkpek696qhdeo7lbbjncqli] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 46
		return tagSCode(raw.CostUnit, cost_unit_catch), instance(raw.InstanceID, raw.AccountName, aws_2wqkpek696qhdeo7lbbjncqli, func() string {
			return part(raw.InstanceID, last, false, true)
		})
	}
	ruleMap[awsGlue] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 47
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsGlue)
	}
	ruleMap[awsXRay] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 48
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsXRay)
	}
	ruleMap[amazonStates] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 49
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonStates)
	}
	ruleMap[awsAppSync] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 50
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsAppSync)
	}
	ruleMap[amazonCognito] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 51
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonCognito)
	}
	ruleMap[amazonGlacier] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 52
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonGlacier)
	}
	ruleMap[aws_9fj8cq737hqz8dzn4nzdloa5u] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 53
		return tagSCode(raw.CostUnit, cost_unit_catch), accountInstance(raw.AccountName, aws_9fj8cq737hqz8dzn4nzdloa5u)
	}
	ruleMap[awsServiceCatalog] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 54
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsServiceCatalog)
	}
	ruleMap[awsTransfer] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 55
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsTransfer)
	}
	ruleMap[amazonAthena] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 56
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonAthena)
	}
	ruleMap[awsSupportBusiness] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 57
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsSupportBusiness)
	}
	ruleMap[amazonPinpoint] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 58
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonPinpoint)
	}
	ruleMap[amazonWorkSpaces] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 59
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonWorkSpaces)
	}
	ruleMap[awsDirectoryService] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 60
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsDirectoryService)
	}
	ruleMap[ocbAWSSkillBuilder] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 61
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, ocbAWSSkillBuilder)
	}
	ruleMap[awsCloudFormation] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 62
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, awsCloudFormation)
	}
	ruleMap[amazonSWF] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 63
		return tagSCode(raw.CostUnit, s04076Scode), instance(raw.InstanceID, raw.AccountName, amazonSWF, func() string {
			return part(raw.InstanceID, last, false, false)
		})
	}
	ruleMap[amazonRedshift] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 64
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, amazonRedshift)
	}
	ruleMap[amazonMQ] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 65
		return tagSCode(raw.CostUnit, empty), instance(raw.InstanceID, raw.AccountName, amazonMQ, func() string {
			return part(raw.InstanceID, thirdToLast, false, false)
		})
	}
	ruleMap[awsDatabaseMigrationSvc] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 66
		return tagSCode(raw.CostUnit, empty), instance(raw.InstanceID, raw.AccountName, awsDatabaseMigrationSvc, func() string {
			return part(raw.InstanceID, last, false, false)
		})
	}
	ruleMap[computeSavingsPlans] = func(raw *models.RawBill) (scode, instanceId string) { // aws rule 67
		return tagSCode(raw.CostUnit, s04076Scode), accountInstance(raw.AccountName, computeSavingsPlans)
	}
}
