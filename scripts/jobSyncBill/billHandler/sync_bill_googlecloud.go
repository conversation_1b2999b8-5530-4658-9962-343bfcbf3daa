package billHandler

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/googlecloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
)

const (
	GoogleName = "gcloud"

	daySql = "select * " +
		"from `s02166-hcms.billing_data.gcp_billing_export_resource_v1_01EFBF_FE25D9_1F8A1C` \n" +
		"where \n" +
		" invoice.month='%s'\n" +
		" and FORMAT_TIMESTAMP(\"%%D\", TIMESTAMP_TRUNC(usage_start_time, DAY, \"America/Los_Angeles\"))='%s/%s/%s'\n" +
		" order by usage_start_time asc"
)

type SyncBillGoogleCloud struct {
	*BaseSyncHandler
	rule        *CostUnitComparison
	googleScode scode
}

func (s *SyncBillGoogleCloud) PullBill(ctx context.Context, c client.IClient, done chan struct{}, receivedChan chan *googlecloud.GoogleScheme) (err error) {
	defer s.done(err, done)

	if _, ok := c.(*googlecloud.BillingClient); !ok {
		return fmt.Errorf("not %s billClient", GoogleName)
	}
	// data source from db
	if s.syncLogStageAt() == SYNC_LOG_STAGE_PULL {
		return findRawFromDB[googlecloud.GoogleScheme](s.BaseSyncHandler, done, receivedChan)
	}
	// data source from redis
	if ctx.Value(CurrentMonthFlag(BillCurrentMonthFlag)) == BillLastMonthExecute {
		// 判断是否是上个月的账期
		date, _ := time.Parse(time.DateOnly, s.syncLog.BillingCycle)
		prevMonth := time.Now().AddDate(0, -1, 0)
		if date.Year() == prevMonth.Year() && date.Month() == prevMonth.Month() {
			return findRawFromRedis[googlecloud.GoogleScheme](s.BaseSyncHandler, done, receivedChan)
		}
	}
	// data source from api
	date := strings.Split(s.syncLog.BillingCycle, hyphen)
	year, month, day := date[0], date[1], date[2]
	sql := fmt.Sprintf(daySql, year+month, month, day, year[len(year)-2:])
	return c.(*googlecloud.BillingClient).QueryBills(ctx, sql, done, receivedChan)
}

func (s *SyncBillGoogleCloud) ToBillRawData(ctx context.Context, item *googlecloud.GoogleScheme) []*models.RawBill {
	var voucherAmount float64
	for _, c := range item.Credits {
		voucherAmount = voucherAmount + c.Amount
	}
	isNegativeDecimal := func(n float64) bool {
		// 检查是否是数字且是负数
		return !math.IsNaN(n) && n < 0
	}
	if isNegativeDecimal(voucherAmount) {
		voucherAmount = math.Abs(voucherAmount)
	}
	bill := &models.RawBill{
		Model:             bizutils.DataSource().Model(ctx),
		TaskId:            s.taskId,
		Vendor:            hbc.GoogleCloud.String(),
		AccountName:       s.syncLog.AccountName,
		AccountID:         s.syncLog.AccountID,
		Granularity:       BILL_GRANULARITY,
		BillingCycle:      s.billingCycle(),
		ProductCode:       pointer.String(item.Service.Id),
		ProductName:       pointer.String(item.Service.Description),
		CostUnit:          s.scode(pointer.String(item.Project.Id), s.googleScode.scodeMap),
		InstanceID:        pointer.String(item.Sku.Id),
		Usage:             fmt.Sprint(item.Usage.Amount),
		UsageUnit:         pointer.String(item.Usage.PricingUnit),
		ListPrice:         item.Price.EffectivePrice.FloatString(0),
		ListPriceUnit:     pointer.String(item.Usage.Unit),
		ServicePeriod:     item.Price.PricingUnitQuantity.FloatString(0),
		ServicePeriodUnit: pointer.String(item.Price.Unit),
		BillingItem:       pointer.String(item.CostType),
		PayableAmount:     item.Cost,
		VoucherAmount:     voucherAmount,
		Currency:          pointer.String(item.Currency),
		Region:            pointer.String(item.Location.Region),
		Zone:              pointer.String(item.Location.Zone),
		Content:           utils.JsonString(item),
		SubscriptionType:  SUBSCRIPTION_TYPE_PAY_AS_YOU_GO,
		AggregatedID: stringutil.Md5(pointer.String(item.BillingAccountId) +
			pointer.String(item.Service.Id) +
			pointer.String(item.Sku.Id)),
	}
	return []*models.RawBill{bill}
}

func (s *SyncBillGoogleCloud) CreateRawData(ctx context.Context, done chan struct{}, receivedChan chan *googlecloud.GoogleScheme) (err error) {
	var batch = []*models.RawBill{}

	for {
		raw, ok := <-receivedChan
		if !ok {
			close(done)
			return s.create(ctx, batch, s.rules)
		}

		batch = append(batch, s.ToBillRawData(ctx, raw)...)
		if len(batch) >= s.batchSize {
			if err = s.create(ctx, batch, s.rules); err != nil {
				close(done)
				return
			}
			batch = nil
		}
	}
}

func (s *SyncBillGoogleCloud) rules(raw []*models.RawBill) (refinedRawBills []*models.RefinedRawBill) {
	refinedRawBills = make([]*models.RefinedRawBill, 0, len(raw))
	s.googleScode.scodeMap = s.checkSCode(s.googleScode)
	for _, r := range raw {
		refinedRawBill := &models.RefinedRawBill{TaskId: s.taskId, RawBill: *s.eraseModel(r)}

		// get scode from cmdb
		if scode, ok := s.getSCodeFromCMDB(refinedRawBill.AggregatedID); ok {
			refinedRawBills = append(refinedRawBills, scodeRule(refinedRawBill, scode, true))
			continue
		}

		var isRule bool
		if s.rule.Priority != nil {
			for _, p := range s.rule.Priority {
				switch p {
				case GOOGLE_RULE_VALUE:
					refinedRawBill, isRule = s.priority(p, refinedRawBill.CostUnit, s.rule.Rules, refinedRawBill)
				}
			}
		}
		if !isRule {
			if rr, ok := s.scodeRule(s.googleScode.scodeMap, refinedRawBill); ok {
				refinedRawBills = append(refinedRawBills, rr)
				continue
			} else {
				refinedRawBill = scodeRule(refinedRawBill, empty, false)
			}
		}

		refinedRawBills = append(refinedRawBills, refinedRawBill)
	}
	return
}

func (s *SyncBillGoogleCloud) ReceivedChan() chan *googlecloud.GoogleScheme {
	return make(chan *googlecloud.GoogleScheme)
}

func NewSyncBillGoogleCloud(taskId string, syncLog *models.RawBillSyncLog, rule *CostUnitComparison) *SyncBillGoogleCloud {
	b := &SyncBillGoogleCloud{BaseSyncHandler: NewBaseHandler(GoogleName)}
	b.syncLog = syncLog
	b.taskId = taskId
	b.rule = rule
	b.googleScode.scodeMap = make(map[string]*expectSCode)
	return b
}
