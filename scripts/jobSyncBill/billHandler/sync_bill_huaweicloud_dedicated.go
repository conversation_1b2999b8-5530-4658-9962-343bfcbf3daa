package billHandler

import (
	"context"
	"fmt"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/pager"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
)

const huawei_dedicated_name = "huawei_dedicated"

type SyncBillHuaweiDedicatedCloud struct {
	*BaseSyncHandler
	rule *CostUnitComparison
}

func (s *SyncBillHuaweiDedicatedCloud) PullBill(ctx context.Context, c client.IClient, done chan struct{}, receivedChan chan *models.ResourceApplicationInstanceBillMetadata) (err error) {
	defer s.done(err, done)

	if _, ok := c.(*huaweicloud.DedicatedClient); !ok {
		return fmt.Errorf("not %s billClient", huawei_dedicated_name)
	}
	// data source from db
	if s.syncLogStageAt() == SYNC_LOG_STAGE_PULL {
		return findRawFromDB[models.ResourceApplicationInstanceBillMetadata](s.BaseSyncHandler, done, receivedChan)
	}
	// data source from api
	return s.findHuaweiDedicatedBillFromDB(ctx, done, receivedChan, c.Name(), s.syncLog.BillingCycle)
}

// 结构体数据转换
func (s *SyncBillHuaweiDedicatedCloud) ToBillRawData(ctx context.Context, data *models.ResourceApplicationInstanceBillMetadata) []*models.RawBill {
	return []*models.RawBill{{
		Model:         bizutils.DataSource().Model(ctx),
		TaskId:        s.taskId,
		Vendor:        hbc.HuaweiCloudDedicated.String(),
		AccountName:   s.syncLog.AccountName,
		AccountID:     s.syncLog.AccountID,
		Granularity:   BILL_GRANULARITY,
		BillingCycle:  s.billingCycle(),
		ProductCode:   data.ProductCode,
		InstanceID:    data.InstanceID,
		PayableAmount: data.UnitPrice,
		CostUnit:      data.SCode,
		Currency:      RMB,
		Content:       utils.JsonString(data),
		AggregatedID:  stringutil.Md5(data.ProductCode + data.InstanceID),
	}}
}

// 数据通道
func (s *SyncBillHuaweiDedicatedCloud) ReceivedChan() chan *models.ResourceApplicationInstanceBillMetadata {
	return make(chan *models.ResourceApplicationInstanceBillMetadata)
}

// 创建数据
func (s *SyncBillHuaweiDedicatedCloud) CreateRawData(ctx context.Context, done chan struct{}, receivedChan chan *models.ResourceApplicationInstanceBillMetadata) (err error) {
	var batch = []*models.RawBill{}

	for {
		raw, ok := <-receivedChan
		if !ok {
			close(done)
			return s.create(ctx, batch, s.rules)
		}

		batch = append(batch, s.ToBillRawData(ctx, raw)...)
		if len(batch) >= s.batchSize {
			if err = s.create(ctx, batch, s.rules); err != nil {
				close(done)
				return
			}
			batch = nil
		}
	}
}

func (s *SyncBillHuaweiDedicatedCloud) findHuaweiDedicatedBillFromDB(ctx context.Context, done chan struct{}, receivedChan chan *models.ResourceApplicationInstanceBillMetadata, accountName, billCycle string) error {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Errorf(ctx, "findHuaweiDedicatedBillFromDB panic: %s", r)
			close(receivedChan)
		}
	}()

	page := 1
	for p, err := findBill(page, BatchSize, accountName, billCycle); ; p, err = findBill(page, BatchSize, accountName, billCycle) {
		if err != nil {
			close(receivedChan)
			return err
		}
		if len(p.Data) == 0 {
			break
		}
		for _, bill := range p.Data {
			select {
			case <-done:
				close(receivedChan)
				return nil
			case receivedChan <- bill:
			}
		}
		page = page + 1
	}

	close(receivedChan)
	return nil
}

// get aws bill
func findBill(page, size int, accountName, billCycle string) (p *pager.Pager[*models.ResourceApplicationInstanceBillMetadata], err error) {
	p = pager.NewPager[*models.ResourceApplicationInstanceBillMetadata](page, size, pager.WithDatasource(bizutils.DataSource()),
		pager.WithOrder("id asc"),
		pager.WithCondition("account_name", pager.Equal, accountName),
		pager.WithCondition("bill_date", pager.Equal, billCycle))
	return p, p.Query()
}

func (s *SyncBillHuaweiDedicatedCloud) rules(raw []*models.RawBill) (refinedRawBills []*models.RefinedRawBill) {
	refinedRawBills = make([]*models.RefinedRawBill, 0, len(raw))
	for _, r := range raw {
		refinedRawBills = append(refinedRawBills, &models.RefinedRawBill{TaskId: s.taskId, RawBill: *s.eraseModel(r)})
	}
	return
}

func NewSyncBillHuaweiDedicatedCloud(taskId string, syncLog *models.RawBillSyncLog, rule *CostUnitComparison) *SyncBillHuaweiDedicatedCloud {
	b := &SyncBillHuaweiDedicatedCloud{BaseSyncHandler: NewBaseHandler(huawei_dedicated_name)}
	b.syncLog = syncLog
	b.taskId = taskId
	b.rule = rule
	return b
}
