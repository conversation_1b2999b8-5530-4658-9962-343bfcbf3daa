package billHandler

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/stretchr/testify/assert"
)

func TestBillSyncLog_Log(t *testing.T) {
	a := assert.New(t)
	log, err := RecordSyncLog(context.Background(), nil, "2024-09-01")
	fmt.Println(utils.JsonString(log))
	a.NoError(err)
}

func TestGetTables(t *testing.T) {
	sql := "CREATE TABLE `bc_aws_bill_%s` (\n" +
		" `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',\n" +
		" `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',\n" +
		" `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',\n" +
		" `account_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录的账号信息-账户名称',\n" +
		" `account_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录的账号信息-账户ID',\n" +
		" `billing_cycle` varchar(19) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账期',\n" +
		" `product_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品编码',\n" +
		" `product_name` varchar(128) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '产品名称',\n" +
		" `product_detail` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品明细',\n" +
		" `usage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '使用量',\n" +
		" `usage_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '使用量单位',\n" +
		" `list_price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '定价',\n" +
		" `list_price_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '定价单位',\n" +
		" `cost` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '费用',\n" +
		" `cost_unit` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算单元',\n" +
		" `service_period_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务期单位',\n" +
		" `currency` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '币种',\n" +
		" `billing_item` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计费项',\n" +
		" `region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地域',\n" +
		" `zone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '可用区',\n" +
		" `resource_group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资源组',\n" +
		" `supplement_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '对实例id的补充',\n" +
		" `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始账单内容',\n" +
		" `month` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '月份',\n" +
		" `instance_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '实例id',\n" +
		" `csv_line` int DEFAULT NULL COMMENT 'csv文件行号',\n" +
		" `service_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日期',\n" +
		" `file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '文件名称',\n" +
		" PRIMARY KEY (`id`),\n" +
		" KEY `idx_account_id` (`account_id`),\n" +
		" KEY `idx_billing_cycle` (`billing_cycle`),\n" +
		" KEY `bc_aws_bill_account_id_IDX` (`account_id`,`billing_cycle`)\n" +
		") ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用于存储aws原始账单的表';"

	a := assert.New(t)
	model := bizutils.DataSource().Model(context.Background())
	mg := model.Orm().Migrator()
	tables, err := mg.GetTables()
	for _, table := range tables {
		if strings.HasPrefix(table, "bc_aws_bill") {
			fmt.Println(table)
		}
	}
	if err := model.Orm().Exec(fmt.Sprintf(sql, "2025_01")).Error; err != nil {
		fmt.Println(err)
		return
	}
	a.NoError(err)
}
