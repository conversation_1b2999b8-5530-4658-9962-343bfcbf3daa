package billHandler

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"sort"
	"time"

	"github.com/hashicorp/go-retryablehttp"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
)

var (
	// ErrRecordsAlreadyExist 记录已存在
	ErrRecordsAlreadyExist = errors.New("records already exist")
)

const (
	BatchSize       = 300
	MonthTimeFormat = "2006-01"
	// DailyTimeFormat  = "2006-01-02"
	FolderTimeFormat = "**************"
	PathSeparator    = string(os.PathSeparator)
	RedisKeyPrefix   = "bill:%s:%s:%s:" // "bill:vendor:accountId:billingCycle"
)

const (
	SYNC_LOG_STATUS_START  = "started"
	SYNC_LOG_STATUS_FINISH = "finished"
	SYNC_LOG_STATUS_ERROR  = "error"

	SYNC_LOG_STAGE_PRE_PULL = "pre-pull"
	SYNC_LOG_STAGE_PULL     = "pull"

	OFFICIAL_DATA = 1
	BACKUP_DATA   = 2
	FAILED_DATA   = 3
	HISTORY_DATA  = 4

	SUBSCRIPTION_TYPE_PAY_AS_YOU_GO = "1" //按量
	SUBSCRIPTION_TYPE_SUBSCRIPTION  = "2" //包年包月
)

const (
	BILL_GRANULARITY = "DAILY"
)

const (
	RMB = "CNY"
	USD = "USD"
)

// 获取账期列表
func GetSyncMonthDate(ctx context.Context, threshold int) []string {
	startTimeStr, _ := bizutils.GetDBConfig(bizutils.DataSource().Model(ctx), "bill_start_time")
	startTimeMonth, _ := time.Parse(MonthTimeFormat, startTimeStr)

	getSyncMonth := func(threshold int) string {
		t := time.Now()
		if t.Day() < threshold {
			t = t.AddDate(0, -2, 0)
		} else {
			t = t.AddDate(0, -1, 0)
		}
		return t.Format(MonthTimeFormat)
	}

	entTimeStr := getSyncMonth(threshold)
	endTimeMonth, _ := time.Parse(MonthTimeFormat, entTimeStr)
	if startTimeStr == "" {
		startTimeMonth = endTimeMonth
	}

	firstDay := time.Date(startTimeMonth.Year(), startTimeMonth.Month(), 1, 0, 0, 0, 0, time.UTC)
	endDay := time.Now().AddDate(0, 0, -1)

	var dates []string
	delta := time.Hour * 24
	for firstDay.Before(endDay) {
		dates = append(dates, firstDay.Format(time.DateOnly))
		firstDay = firstDay.Add(delta)
	}

	sort.Slice(dates, func(i, j int) bool {
		return dates[i] < dates[j]
	})
	return dates
}

func RecordSyncLog(ctx context.Context, cli client.IClient, billingCycle string) (*models.RawBillSyncLog, error) {
	model := bizutils.DataSource().Model(ctx)
	syncLog := &models.RawBillSyncLog{
		Model:        model,
		Vendor:       cli.Vendor().String(),
		AccountName:  cli.Name(),
		AccountID:    cli.Identifier(),
		BillingCycle: billingCycle,
		Status:       SYNC_LOG_STATUS_START,
		Stage:        SYNC_LOG_STAGE_PRE_PULL,
	}
	return syncLog, model.Orm().Transaction(func(tx *gorm.DB) (err error) {
		sql := "vendor = ? AND account_id = ? AND billing_cycle = ?"
		result := tx.Where(sql, syncLog.Vendor, syncLog.AccountID, syncLog.BillingCycle).
			FirstOrCreate(syncLog)
		if err = result.Error; err != nil {
			return
		}
		if result.RowsAffected > 0 {
			return
		}
		// 检查是否不需要重新拉取
		if shouldSkipSync(syncLog) {
			return ErrRecordsAlreadyExist
		}
		// update exist sync log init
		updateExistSyncLog := func(tx *gorm.DB) error {
			syncLog.Status, syncLog.ErrMsg = SYNC_LOG_STATUS_START, empty
			return tx.Save(syncLog).Error
		}
		// 需要完整重置的情况
		if requiresFullReset(syncLog) {
			//删除相关数据
			if err = deleteRelatedData(ctx, tx, sql, syncLog); err != nil {
				return err
			}
			//更新bill_task中正式数据为备份数据
			where := fmt.Sprintf("vendor = ? AND account_name = ? AND account_id = ? AND status = ? AND stage = 'pull' AND billing_cycle like '%s%%'", syncLog.BillingCycle)
			if err = tx.Where(where, syncLog.Vendor, syncLog.AccountName, syncLog.AccountID, OFFICIAL_DATA).Updates(&models.BillTask{Status: BACKUP_DATA}).Error; err != nil {
				return
			}
			syncLog.Stage = SYNC_LOG_STAGE_PRE_PULL
		} else {
			if err = tx.Where(sql, syncLog.Vendor, syncLog.AccountID, syncLog.BillingCycle).Delete(&models.RefinedRawBill{}).Error; err != nil {
				return
			}
			syncLog.Stage = SYNC_LOG_STAGE_PULL
		}
		//更新syncLog
		return updateExistSyncLog(tx)
	})
}

func deleteRelatedData(ctx context.Context, tx *gorm.DB, condition string, syncLog *models.RawBillSyncLog) error {
	g, _ := errgroup.WithContext(ctx)

	g.Go(func() error {
		if err := tx.Where(condition, syncLog.Vendor, syncLog.AccountID, syncLog.BillingCycle).Delete(&models.RawBill{}).Error; err != nil {
			return fmt.Errorf("failed to delete raw bills: %w", err)
		}
		return nil
	})

	g.Go(func() error {
		if err := tx.Where(condition, syncLog.Vendor, syncLog.AccountID, syncLog.BillingCycle).Delete(&models.RefinedRawBill{}).Error; err != nil {
			return fmt.Errorf("failed to delete refined raw bills: %w", err)
		}
		return nil
	})

	return g.Wait()
}

func shouldSkipSync(syncLog *models.RawBillSyncLog) bool {
	// 不需要重新拉取的情况
	if syncLog.Stage != SYNC_LOG_STAGE_PRE_PULL && syncLog.Stage != SYNC_LOG_STAGE_PULL {
		return true
	}
	if syncLog.Stage == SYNC_LOG_STAGE_PULL && syncLog.Status == SYNC_LOG_STATUS_FINISH {
		return true
	}
	return false
}

func requiresFullReset(syncLog *models.RawBillSyncLog) bool {
	// 需要完整重置的情况
	return (syncLog.Stage == SYNC_LOG_STAGE_PRE_PULL || syncLog.Stage == SYNC_LOG_STAGE_PULL) && syncLog.Status != SYNC_LOG_STATUS_FINISH
}

// 更新同步日志状态
func updateSyncLogError(ctx context.Context, syncLog *models.RawBillSyncLog) error {
	model := bizutils.DataSource().Model(ctx)
	syncLog.Status = SYNC_LOG_STATUS_ERROR
	if err := model.Orm().Save(syncLog).Error; err != nil {
		log.Errorf(ctx, "updateSyncLogError failed: %s", err.Error())
		return err
	}
	return nil
}

// 更新同步日志状态并新增任务
func updateSyncLogFinishAndCreateBillTask(ctx context.Context, taskId string, syncLog *models.RawBillSyncLog, exist bool) error {
	model := bizutils.DataSource().Model(ctx)
	syncLog.Stage = SYNC_LOG_STAGE_PULL
	syncLog.Status = SYNC_LOG_STATUS_FINISH
	// pre-pull情况下账单任务taskId不更新
	if exist {
		return model.Orm().Save(syncLog).Error
	}
	// taskId不存在的情况下进行更新
	billingCycleDateTime, _ := time.Parse(time.DateOnly, syncLog.BillingCycle)
	task := &models.BillTask{
		TaskID:       taskId,
		Status:       OFFICIAL_DATA, //正式数据
		Stage:        SYNC_LOG_STAGE_PULL,
		Vendor:       syncLog.Vendor,
		AccountName:  syncLog.AccountName,
		AccountID:    syncLog.AccountID,
		BillingCycle: billingCycleDateTime,
	}

	err := model.Orm().Transaction(func(tx *gorm.DB) error {
		if err := tx.Save(syncLog).Error; err != nil {
			return err
		}
		updateFn := func(updatedStatus, beforeStatus int) error {
			return tx.Where("vendor = ? AND account_id = ? AND status = ? AND billing_cycle =? AND stage = 'pull'",
				task.Vendor, task.AccountID, beforeStatus, billingCycleDateTime).
				Updates(&models.BillTask{Status: updatedStatus}).Error
		}
		// 锁定需要修改的记录
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).Where("vendor = ? AND account_id = ? AND billing_cycle = ? AND stage = 'pull'",
			task.Vendor, task.AccountID, billingCycleDateTime,
		).Find(&models.BillTask{}).Error; err != nil {
			return err
		}
		// 更新已备份数据为历史数据
		if err := updateFn(HISTORY_DATA, BACKUP_DATA); err != nil {
			return err
		}
		// 更新正式数据为备份数据
		if err := updateFn(BACKUP_DATA, OFFICIAL_DATA); err != nil {
			return err
		}
		// 插入新的任务数据
		return tx.Save(task).Error
	})

	if err != nil {
		log.Errorf(ctx, "updateSyncLogFinishAndCreateBillTask failed: %s", err.Error())
		return err
	}

	return nil
}

// 获取账单任务表中已存在的taskId
func getBillTaskId(ctx context.Context, syncLog *models.RawBillSyncLog) (models.BillTask, error) {
	model := bizutils.DataSource().Model(ctx)
	billTask := models.BillTask{}
	where := fmt.Sprintf("vendor = ? AND account_name = ? AND account_id = ? AND status = 1 AND stage = 'pull' AND billing_cycle like '%s%%'", syncLog.BillingCycle)
	return billTask, model.Orm().Where(where, syncLog.Vendor, syncLog.AccountName, syncLog.AccountID).Order("update_time asc").
		Take(&billTask).Error
}

var cli = retryablehttp.NewClient()

func downloadFile(url, path string) error {
	resp, err := cli.Get(url)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("url: %s, statusCode: %d", url, resp.StatusCode)
	}
	out, err := os.Create(path)
	if err != nil {
		return err
	}
	if _, err = io.Copy(out, resp.Body); err != nil {
		return err
	}
	resp.Body.Close()
	out.Close()

	return nil
}
