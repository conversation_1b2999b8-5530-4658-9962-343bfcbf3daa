package billHandler

// import (
// 	"context"
// 	"fmt"
// 	"math"
// 	"os"
// 	"path"
// 	"time"

// 	"git.haier.net/devops/hcms-task-center/biz/bizutils"
// 	"git.haier.net/devops/ops-golang-common/hbc"
// 	"git.haier.net/devops/ops-golang-common/log"
// 	"git.haier.net/devops/ops-golang-common/models/models"
// 	"git.haier.net/devops/ops-golang-common/sdk/aws"
// 	"git.haier.net/devops/ops-golang-common/sdk/client"
// 	"golang.org/x/sync/errgroup"
// 	"gorm.io/gorm"
// )

// // aws账单数据比对
// // 总行数比对和一个月累加的总金额比对
// func NewCompareAwsBill(cli client.IClient) (err error) {
// 	ctx := context.Background()
// 	currentTime := time.Now()

// 	// 当月7号前比对上个月数据完整性
// 	if currentTime.Day() <= stabilize {
// 		if err = compare(ctx, cli, lastMonth(currentTime)); err != nil {
// 			return
// 		}
// 	}
// 	// 比对当月数据完整性
// 	return compare(ctx, cli, currentTime)
// }

// func compare(ctx context.Context, cli client.IClient, t time.Time) (err error) {
// 	eg := errgroup.Group{}
// 	prefix, _ := os.Getwd()
// 	path := path.Join(prefix, string(hbc.AWS), cli.Name())
// 	yearMonth := yearMonth(t)
// 	monthFormat := t.Format(MonthTimeFormat)
// 	done := make(chan struct{})
// 	awsCh := make(chan *aws.AwsBillCSV)

// 	var (
// 		csvLineCount int64
// 		csvCostSum   float64
// 		dbLineCount  int64
// 		dbCostSum    float64
// 	)

// 	files := trimFiles(path, monthFormat)
// 	if len(files) == 0 {
// 		log.Errorf(ctx, "aws bill compare report not found in path: %s/%s", path, monthFormat)
// 		return
// 	}

// 	// csv
// 	eg.Go(func() error {
// 		for record := range awsCh {
// 			csvLineCount = csvLineCount + 1
// 			csvCostSum = csvCostSum + toFloat(record.LineItemUnblendedCost)
// 		}
// 		return nil
// 	})
// 	eg.Go(func() error {
// 		if err = NewSyncBillAwsCloud(empty, nil, &models.RawBillSyncLog{BillingCycle: monthFormat}, nil).
// 			PullBill(ctx, cli, nil, nil); err != nil {
// 			return err
// 		}
// 		return awsParseCSV(files, monthFormat, done, awsCh)
// 	})
// 	// db
// 	eg.Go(func() error {
// 		dbLineCount, err = countBillByMonth(ctx, cli.Identifier(), yearMonth)
// 		if err != nil {
// 			return err
// 		}
// 		return nil
// 	})
// 	eg.Go(func() error {
// 		dbCostSum, err = sumCostBillByMonth(ctx, cli.Identifier(), yearMonth)
// 		if err != nil {
// 			return err
// 		}
// 		output := math.Pow(10, float64(9))
// 		dbCostSum = math.Round(dbCostSum*output) / output //保留9位小数点与csv一致
// 		return nil
// 	})
// 	if eg.Wait() != nil {
// 		return
// 	}
// 	close(done)

// 	// compare
// 	if (csvLineCount != dbLineCount) || (csvCostSum != dbCostSum) {
// 		// 1.remove db data
// 		if err = removeAwsBillAndResetSyncLogByMonth(ctx, cli.Identifier(), yearMonth); err != nil {
// 			return
// 		}
// 		// 2.insert db
// 		if err = parseDB(ctx, cli, t); err != nil {
// 			return
// 		}
// 		// 3.update sync_log and bc_bill_task
// 		model := bizutils.DataSource().Model(ctx)
// 		if err = model.Orm().Transaction(func(tx *gorm.DB) error {
// 			return updateAwsSyncLogAndBillTask(tx, cli.Identifier(), fmt.Sprintf("%s%%", monthFormat))
// 		}); err != nil {
// 			return
// 		}
// 	}

// 	return
// }

// // count aws bill
// func countBillByMonth(ctx context.Context, accountId string, yearMonth string) (int64, error) {
// 	var count int64
// 	model := bizutils.DataSource().Model(ctx)
// 	result := model.Orm().Model(&models.AwsBill{}).Select("count(1)").Where(fmt.Sprintf(awsBillBillingCycleWhereSql, yearMonth), accountId).Row()
// 	if err := result.Err(); err != nil {
// 		return count, err
// 	}
// 	result.Scan(&count)

// 	return count, nil
// }

// // sum aws bill
// func sumCostBillByMonth(ctx context.Context, accountId string, yearMonth string) (float64, error) {
// 	var sum float64
// 	model := bizutils.DataSource().Model(ctx)
// 	result := model.Orm().Model(&models.AwsBill{}).Select("sum(cost)").Where(fmt.Sprintf(awsBillBillingCycleWhereSql, yearMonth), accountId).Row()
// 	if err := result.Err(); err != nil {
// 		return sum, err
// 	}
// 	result.Scan(&sum)

// 	return sum, nil
// }

// func updateAwsSyncLogAndBillTask(tx *gorm.DB, accountId string, monthLike string) error {
// 	sql := "vendor = 'aws' AND account_id = ? AND billing_cycle like ?"
// 	if err := tx.Where(sql, accountId, monthLike).Updates(&models.RawBillSyncLog{Status: SYNC_LOG_STATUS_START}).Error; err != nil {
// 		return err
// 	}
// 	return tx.Where(sql, accountId, monthLike).Updates(&models.BillTask{Status: FAILED_DATA}).Error
// }

// func removeAwsBillAndResetSyncLogByMonth(ctx context.Context, accountId string, yearMonth string) error {
// 	model := bizutils.DataSource().Model(ctx)
// 	// 删除aws数据和重置syncLog数据
// 	return model.Orm().Transaction(func(tx *gorm.DB) error {
// 		if err := tx.Model(&models.RawBillSyncLog{}).Where(fmt.Sprintf(awsBillBillingCycleWhereSql, yearMonth), accountId).
// 			Updates(models.RawBillSyncLog{Status: SYNC_LOG_STATUS_START, Stage: SYNC_LOG_STAGE_PRE_PULL}).Error; err != nil {
// 			return err
// 		}
// 		return tx.Exec(fmt.Sprintf("truncate bc_aws_bill_%s", yearMonth)).Error
// 		// return tx.Where(fmt.Sprintf(awsBillBillingCycleWhereSql, yearMonth), accountId).Delete(&models.AwsBill{BillingCycle: yearMonth}).Error
// 	})
// }
