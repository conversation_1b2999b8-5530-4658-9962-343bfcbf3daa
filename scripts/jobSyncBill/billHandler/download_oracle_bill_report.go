package billHandler

import (
	"compress/gzip"
	"context"
	"errors"
	"io/fs"
	"os"
	"path"
	"sort"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/oraclecloud"
	"git.haier.net/devops/ops-golang-common/sdk/util"
	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/gofiber/fiber/v2/log"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var prefix, _ = os.Getwd()
var csv_gz_suffix = ".csv.gz"

func NewSyncOracleDownloadBillReport(cli client.IClient) (err error) {
	ctx := context.Background()
	eg := errgroup.Group{}
	done := make(chan struct{})
	receivedChan := make(chan *oraclecloud.OracleBillCSV)
	path := path.Join(prefix, string(hbc.OracleCloud), cli.Name())
	fileCursor := "0001000001377002-00001.csv.gz" //25年1月 20250101043256 会从当前这个文件(包含)进行下载

	files := sortFiles(path)
	if len(files) > 0 {
		// get fileName
		if deepFiles := sortFiles(path + PathSeparator + files[len(files)-1].Name()); len(deepFiles) > 0 {
			for _, df := range deepFiles {
				if strings.LastIndex(df.Name(), csv_gz_suffix) != -1 {
					fileCursor = df.Name()
					break
				}
			}
		}
	}

	// download the latest files
	if err = downloadBill(cli, fileCursor); err != nil {
		return
	}

	// last folder name from db
	cursor, err := lastBillingCycleFileCursor(ctx, cli.Name())
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}
	incrementFiles := []os.DirEntry{}
	if !isEmpty(cursor.Cursor) {
		// increment
		var lastIndex int
		files := sortFiles(path)
		for index, f := range files {
			if !f.Type().IsDir() {
				continue
			}
			if f.Name() == cursor.Cursor {
				// incrementFiles = append(incrementFiles, f)
				lastIndex = index
				break
			}
		}
		// get one more file
		if lastIndex != 0 {
			incrementFiles = append(incrementFiles, files[lastIndex+1:]...)
		}
	} else {
		// all bill
		incrementFiles = sortFiles(path)
		cursor.AccountId = cli.Identifier()
		cursor.AccountName = cli.Name()
	}
	if len(incrementFiles) == 0 {
		return
	}

	//update oracle_bill_mark table
	cursor.Cursor = incrementFiles[len(incrementFiles)-1].Name()
	if err = saveOracleBillMark(ctx, cursor); err != nil {
		return err
	}

	// parse db
	eg.Go(func() error {
		dbBatch := []*models.RawBill{}
		for {
			record, ok := <-receivedChan
			if !ok {
				close(done)
				return saveOracleBill(dbBatch)
			}

			dbBatch = append(dbBatch, toOracleBillData(ctx, record)...)
			if len(dbBatch) >= 500 {
				if err = saveOracleBill(dbBatch); err != nil {
					close(done)
					log.Errorf("oracle bill download client: %s parseDB failed: %s", cli.Name(), err.Error())
					return err
				}
				dbBatch = nil
			}
		}
	})
	// parse csv
	eg.Go(func() (csvErr error) {
		defer func() {
			if csvErr != nil {
				if csvErr.Error() == errCSVParseChannelClosed {
					log.Errorf("oracle bill done billChan closed")
					return
				}
			}
			close(receivedChan)
		}()
		return oracleParseCSV(path, incrementFiles, done, receivedChan)
	})

	return eg.Wait()
}

func oracleParseCSV(rootPath string, incrementFiles []os.DirEntry, done chan struct{}, receivedChan chan *oraclecloud.OracleBillCSV) (err error) {
	b := tools.NewBatch[os.DirEntry, error](context.Background(),
		batch.WithShowLog(true),
		batch.WithLogPrintStep(50))
	b.Run(incrementFiles, func(ctx context.Context, f os.DirEntry) (err error, _ error) {
		filePath := path.Join(rootPath, f.Name())
		files, _ := os.ReadDir(filePath)
		if len(files) == 0 {
			return
		}
		// file to bill
		for _, f := range files {
			if strings.LastIndex(f.Name(), csv_gz_suffix) == -1 {
				continue
			}
			file, fileErr := os.Open(path.Join(filePath, f.Name()))
			if err != nil {
				return fileErr, nil
			}
			defer file.Close()
			gr, grErr := gzip.NewReader(file)
			if err != nil {
				return grErr, nil
			}
			defer gr.Close()

			if err = util.BillCsvGz(gr, file.Name(), headLine, done, receivedChan); err != nil {
				return
			}
		}
		return
	})
	return tools.MergeErrors(b.Outs())
}

func sortFiles(path string) []fs.DirEntry {
	// read file
	files, _ := os.ReadDir(path)
	// sort folder
	sort.Slice(files, func(i, j int) bool {
		fileI, _ := files[i].Info()
		fileJ, _ := files[j].Info()
		return fileI.Name() < fileJ.Name()
	})
	return files
}

// oracle download bill reports
func downloadBill(cli client.IClient, fileCursor string) error {
	return NewSyncBillOracleCloud(empty, &models.RawBillSyncLog{BillingCycle: fileCursor}, nil).PullBill(context.Background(), cli, nil, nil)
}

func lastBillingCycleFileCursor(ctx context.Context, accountName string) (*models.OracleBillMark, error) {
	o := &models.OracleBillMark{}
	model := bizutils.DataSource().Model(ctx)
	if err := model.Orm().Where("account_name = ?", accountName).First(o).Error; err != nil {
		return o, err
	}
	return o, nil
}

func saveOracleBillMark(ctx context.Context, mark *models.OracleBillMark) error {
	model := bizutils.DataSource().Model(ctx)
	return model.Orm().Save(mark).Error
}

func saveOracleBill(batch []*models.RawBill) error {
	if len(batch) == 0 {
		return nil
	}
	model := bizutils.DataSource().Model(context.Background())
	return model.Orm().Table(oracle_table).Create(&batch).Error // save to oracle_bill table
}

func toOracleBillData(ctx context.Context, item *oraclecloud.OracleBillCSV) []*models.RawBill {
	billingCycle := item.IntervalUsageStart.Format(time.DateOnly)
	billingCycleTime, _ := time.Parse(time.DateOnly, billingCycle)
	bill := &models.RawBill{
		Model:        bizutils.DataSource().Model(ctx),
		AccountName:  item.TenantId,
		BillingCycle: billingCycleTime,

		ProductCode:       item.Service,     // E
		ProductName:       item.ProductSku,  // N
		ProductDetail:     item.Description, // O
		CostUnit:          item.CreatedBy,   // AB
		InstanceID:        item.ResourceId,  // J
		Usage:             item.BilledQuantity,
		UsageUnit:         item.BillingUnitReadable,
		ListPrice:         item.UnitPrice,
		ListPriceUnit:     item.BillingUnitReadable, // U
		ServicePeriodUnit: item.SkuUnitDescription,  // V
		// Cost:              toFloat(item.MyCost),     // Z
		PayableAmount: toFloat(item.MyCost), // Z
		Currency:      item.CurrencyCode,
		BillingItem:   item.BillingUnitReadable,
		Region:        item.Region,
		Zone:          item.AvailabilityDomain,
		Content:       utils.JsonString(item),
		ResourceGroup: item.CompartmentName, // G
		// SupplementID:      item.ReferenceNo + hyphen + item.CompartmentId, // A - F
	}
	return []*models.RawBill{bill}
}
