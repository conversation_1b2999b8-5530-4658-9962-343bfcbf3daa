package billHandler

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	billing "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/billing/v20180709"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
)

const tencent_name = "tencent"

type SyncBillTencentCloud struct {
	*BaseSyncHandler
	rule         *CostUnitComparison
	tencentScode scode
}

func (s *SyncBillTencentCloud) PullBill(ctx context.Context, c client.IClient, done chan struct{}, receivedChan chan *billing.BillDetail) (err error) {
	defer s.done(err, done)

	if _, ok := c.(*tencentcloud.BillingClient); !ok {
		return fmt.Errorf("not %s billClient", tencent_name)
	}
	// data source from db
	if s.syncLogStageAt() == SYNC_LOG_STAGE_PULL {
		return findRawFromDB[billing.BillDetail](s.BaseSyncHandler, done, receivedChan)
	}
	return c.(*tencentcloud.BillingClient).DescribeBillDetail(ctx, s.syncLog.BillingCycle, done, receivedChan)
}

func (s *SyncBillTencentCloud) ToBillRawData(ctx context.Context, item *billing.BillDetail) []*models.RawBill {
	scode := func(tags []*billing.BillTagInfo, projectName string) (scode string) {
		var tag string
		if len(tags) == 0 {
			if strings.HasPrefix(strings.ToUpper(projectName), scodePrefix) {
				tag = projectName
			}
		} else {
			tag = pointer.String(tags[0].TagValue)
		}
		if !strings.HasPrefix(strings.ToUpper(tag), scodePrefix) {
			tag = s04076Scode
		}
		return s.scode(tag, s.tencentScode.scodeMap)
	}

	var subscription string
	mode := pointer.String(item.PayModeName)
	if mode == "按量计费" {
		subscription = SUBSCRIPTION_TYPE_PAY_AS_YOU_GO
	} else if mode == "包年包月" {
		subscription = SUBSCRIPTION_TYPE_SUBSCRIPTION
	} else {
		subscription = mode
	}

	bills := []*models.RawBill{}
	for _, data := range item.ComponentSet {
		cashAmount, _ := strconv.ParseFloat(pointer.String(data.CashPayAmount), 64)
		payableAmount, _ := strconv.ParseFloat(pointer.String(data.RealCost), 64)
		voucherAmount, _ := strconv.ParseFloat(pointer.String(data.VoucherPayAmount), 64)

		bill := &models.RawBill{
			Model:             bizutils.DataSource().Model(ctx),
			TaskId:            s.taskId,
			Vendor:            hbc.TencentCloud.String(),
			AccountName:       s.syncLog.AccountName,
			AccountID:         pointer.String(item.OwnerUin),
			Granularity:       BILL_GRANULARITY,
			BillingCycle:      s.billingCycle(),
			ProductCode:       pointer.String(item.BusinessCode),
			ProductName:       pointer.String(item.ProductCodeName),
			ResourceGroup:     fmt.Sprint(pointer.Int64(item.ProjectId)),
			CostUnit:          scode(item.Tags, *item.ProjectName), //使用Tags字段拆出scode,拆不出存入原始数据.
			InstanceID:        pointer.String(item.ResourceId),
			Usage:             pointer.String(data.UsedAmount),
			UsageUnit:         pointer.String(data.UsedAmountUnit),
			ListPrice:         pointer.String(data.SinglePrice),
			ListPriceUnit:     pointer.String(data.PriceUnit),
			ServicePeriod:     pointer.String(data.TimeSpan),
			ServicePeriodUnit: pointer.String(data.TimeUnitName),
			BillingItem:       pointer.String(data.ItemCodeName),
			CashAmount:        cashAmount,
			PayableAmount:     payableAmount,
			VoucherAmount:     voucherAmount,
			Currency:          RMB,
			Region:            pointer.String(item.RegionName),
			Zone:              pointer.String(item.ZoneName),
			Content:           utils.JsonString(item),
			SubscriptionType:  subscription,
			ProductDetail:     pointer.String(item.ProductCode) + ":" + pointer.String(item.ProductCodeName),
			AggregatedID: stringutil.Md5(*item.OwnerUin +
				pointer.String(item.BusinessCode) +
				*item.ResourceId),
		}
		bills = append(bills, bill)
	}
	return bills
}

func (s *SyncBillTencentCloud) CreateRawData(ctx context.Context, done chan struct{}, receivedChan chan *billing.BillDetail) (err error) {
	var batch = []*models.RawBill{}

	for {
		raw, ok := <-receivedChan
		if !ok {
			close(done)
			return s.create(ctx, batch, s.rules)
		}

		batch = append(batch, s.ToBillRawData(ctx, raw)...)
		if len(batch) >= s.batchSize {
			if err = s.create(ctx, batch, s.rules); err != nil {
				close(done)
				return
			}
			batch = nil
		}
	}
}

func (s *SyncBillTencentCloud) rules(raw []*models.RawBill) (refinedRawBills []*models.RefinedRawBill) {
	refinedRawBills = make([]*models.RefinedRawBill, 0, len(raw))
	s.tencentScode.scodeMap = s.checkSCode(s.tencentScode)
	for _, r := range raw {
		refinedRawBill := &models.RefinedRawBill{TaskId: s.taskId, RawBill: *s.eraseModel(r)}
		// get scode from cmdb
		if scode, ok := s.getSCodeFromCMDB(refinedRawBill.AggregatedID); ok {
			refinedRawBills = append(refinedRawBills, scodeRule(refinedRawBill, scode, true))
			continue
		}

		// 截取不到scode走规则
		var isRule bool
		if s.rule.Priority != nil {
			for _, p := range s.rule.Priority {
				switch p {
				case TENCENT_ACCOUNT:
					refinedRawBill, isRule = s.priority(p, refinedRawBill.AccountName, s.rule.Rules, refinedRawBill)
				case TENCENT_COMMODITY_CODE:
					refinedRawBill, isRule = s.priority(p, refinedRawBill.ProductCode, s.rule.Rules, refinedRawBill)
				}
			}
		}
		// 未匹配到规则
		if !isRule {
			if rr, ok := s.scodeRule(s.tencentScode.scodeMap, refinedRawBill); ok {
				refinedRawBills = append(refinedRawBills, rr)
				continue
			} else {
				refinedRawBill = scodeRule(refinedRawBill, empty, false)
			}
		}

		refinedRawBills = append(refinedRawBills, refinedRawBill)
	}

	return
}

func (s *SyncBillTencentCloud) ReceivedChan() chan *billing.BillDetail {
	return make(chan *billing.BillDetail)
}

func NewSyncBillTencentCloud(taskId string, syncLog *models.RawBillSyncLog, rule *CostUnitComparison) *SyncBillTencentCloud {
	b := &SyncBillTencentCloud{BaseSyncHandler: NewBaseHandler(tencent_name)}
	b.syncLog = syncLog
	b.taskId = taskId
	b.rule = rule
	b.tencentScode.scodeMap = make(map[string]*expectSCode)
	return b
}
