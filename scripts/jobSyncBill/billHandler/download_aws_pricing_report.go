package billHandler

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/util"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/aws/aws-sdk-go-v2/service/pricing"
	"github.com/aws/aws-sdk-go-v2/service/pricing/types"
	awsSDK "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/endpoints"
	"github.com/gofiber/fiber/v2/log"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

const (
	csv      = "csv"
	onDemand = "OnDemand"
	createBy = "PRICING"

	del_flag_undeleted    = "0" // 0：有效
	del_flag_deleted      = "1" // 1：无效
	prince_type           = 3
	skipPrinceCSVHeadLine = 6
)

func NewSyncAwsDownloadPricingReport(cli client.IClient) (err error) {
	ctx := context.Background()
	pricingCli := aws.NewPricingClient(endpoints.EuCentral1RegionID, cli.Name(), cli.Key().AccessKey(), cli.Key().SecureKey(), cli.Tag(), false)
	prefix, _ := os.Getwd()
	awsFilePath := path.Join(prefix, string(hbc.AWS))
	if _, err = os.Stat(awsFilePath); os.IsNotExist(err) { // create aws folder
		os.Mkdir(awsFilePath, 0755)
	}
	filePath := path.Join(awsFilePath, hbc.Pricing.String()) // {osPath}/aws/pricing
	if _, err = os.Stat(filePath); os.IsNotExist(err) {      // create pricing folder
		os.Mkdir(filePath, 0755)
	}

	// 1.find region from bc_aws_bill_xxxx_xx
	products, err := findEC2ProductRegionSKU(ctx)
	if err != nil {
		return err
	}
	pricingMap := make(map[string]skuPrice, 0)
	for _, product := range products {
		if _, ok := pricingMap[product.Region]; ok {
			pricingMap[product.Region].sku[product.ProductDetail] = nil
		} else {
			pricingMap[product.Region] = skuPrice{sku: map[string]*float64{
				product.ProductDetail: nil,
			}}
		}
	}
	// 2.many goroutine download pricing csv file
	eg := errgroup.Group{}
	var pricingList []types.PriceList
	if pricingList, err = pricingCli.ListPriceLists(ctx,
		func(input *pricing.ListPriceListsInput) {
			input.ServiceCode = awsSDK.String(amazonEC2)
			input.CurrencyCode = awsSDK.String(USD)
			input.EffectiveDate = awsSDK.Time(time.Now())
		}); err != nil {
		return err
	}
	for region := range pricingMap { // preparing download
		for _, awsPrice := range pricingList {
			if region != pointer.String(awsPrice.RegionCode) {
				continue
			}
			// extract version
			v := pricingMap[region]
			arns := strings.Split(*awsPrice.PriceListArn, PathSeparator)
			v.version = arns[len(arns)-2]
			pricingMap[region] = v
			// get download url
			fileUrls, err := pricingCli.GetPriceListFileUrl(ctx,
				func(input *pricing.GetPriceListFileUrlInput) {
					input.FileFormat = awsSDK.String(csv)
					input.PriceListArn = awsPrice.PriceListArn
				})
			if err != nil {
				return err
			}
			// download
			for index, url := range fileUrls {
				fileName := region + spot + csv //eu-central-1.csv
				eg.Go(func() error {
					return downloadFile(*url, path.Join(filePath, fileName))
				})
				if index > 0 {
					log.Warn("aws GetPriceListFileUrl more than one")
				}
			}
			break
		}
	}
	if err = eg.Wait(); err != nil {
		return err
	}
	// 3.read PricePerUnit of pricing csv
	done := make(chan struct{})
	receivedChan := make(chan *awsPriceCSV)

	eg.Go(func() error {
		for {
			record, ok := <-receivedChan
			if !ok {
				close(done)
				return nil
			}
			if sku, ok := pricingMap[record.RegionCode]; ok {
				if _, ok := sku.sku[record.SKU]; ok && record.TermType == onDemand {
					sku.sku[record.SKU] = &record.PricePerUnit
				}
			}
		}
	})

	egCSV := errgroup.Group{}
	for regionCode := range pricingMap {
		regionCode := regionCode
		file, err := os.Open(path.Join(filePath, regionCode+spot+csv))
		if err != nil {
			return err
		}
		defer file.Close()

		egCSV.Go(func() error {
			return util.BillCsvGz(bufio.NewReader(file), regionCode, skipPrinceCSVHeadLine, done, receivedChan)
		})
	}
	if err := egCSV.Wait(); err != nil {
		return err
	}
	close(receivedChan)
	if err := eg.Wait(); err != nil {
		return err
	}

	// 4.check bc_aws_pricing update latest data
	instances, err := findAwsPricing(ctx)
	if err != nil {
		return
	}
	pricingCSV := make(map[string]instancePrice, 0)
	for _, sku := range pricingMap {
		for k, v := range sku.sku {
			i := instancePrice{
				version: sku.version,
				price:   v,
			}
			pricingCSV[k] = i
		}
	}
	// transaction
	model := bizutils.DataSource().Model(ctx)
	return model.Orm().Transaction(func(tx *gorm.DB) error {
		for _, instance := range instances {
			price, ok := pricingCSV[instance.InstanceID]
			if ok {
				// check instance update or delete
				if price.price == nil {
					log.Warnf("not found price of sku: %s", instance.InstanceID)
					break
				}
				// update for pricing,version
				if instance.Version != price.version {
					instance.Pricing, instance.Version = *price.price, price.version
					if err := updateAwsPricing(tx, instance); err != nil {
						return err
					}
				}
				delete(pricingCSV, instance.InstanceID)
			} else {
				// delete
				instance.DelFlag = del_flag_deleted
				if err := updateAwsPricing(tx, instance); err != nil {
					return err
				}
			}
		}
		awsPricing := []*models.AwsPricing{}
		for sku, v := range pricingCSV {
			// save
			awsPricing = append(awsPricing, &models.AwsPricing{
				InstanceID: sku,
				Pricing:    *v.price,
				Version:    v.version,
				Currency:   USD,
				DelFlag:    del_flag_undeleted,
			})
		}
		if len(awsPricing) > 0 {
			return createAwsPricing(tx, awsPricing)
		}
		return nil
	})
}

type skuPrice struct {
	version string
	sku     map[string]*float64
}

type instancePrice struct {
	price   *float64
	version string
}

type awsPriceCSV struct {
	RegionCode   string  `csv:"Region Code"`
	SKU          string  `csv:"SKU"`
	PricePerUnit float64 `csv:"PricePerUnit"`
	TermType     string  `csv:"TermType"`
}

func findEC2ProductRegionSKU(ctx context.Context) ([]models.RawBill, error) {
	model := bizutils.DataSource().Model(ctx)
	tables, err := model.Orm().Migrator().GetTables()
	if err != nil {
		return nil, err
	}
	var tableNames []string
	for _, table := range tables {
		if strings.Contains(table, "bc_aws_bill_") {
			tableNames = append(tableNames, table)
		}
	}
	// 获取区域信息
	b := tools.NewBatch[string, []models.RawBill](ctx)
	b.Run(tableNames, func(ctx context.Context, tableName string) ([]models.RawBill, error) {
		bills := []models.RawBill{}
		if err := model.Orm().Table(tableName).Select("product_detail,region").
			Where(fmt.Sprintf("instance_id like 'i-%%' and usage_unit like '%%%s%%' and billing_item like '%s%%' ", boxUsage, runInstances)).
			Group("product_detail").Group("region").Find(&bills).Error; err != nil {
			return nil, err
		}
		return bills, nil
	})
	if err := b.Error(); err != nil {
		return nil, err
	}
	return tools.MergeData(b.Outs()...), err
}

func findAwsPricing(ctx context.Context) ([]*models.AwsPricing, error) {
	instanceIds := make([]*models.AwsPricing, 0)
	model := bizutils.DataSource().Model(ctx)
	if err := model.Orm().Where("del_flag = 0").Find(&instanceIds).Error; err != nil {
		return nil, err
	}
	return instanceIds, nil
}
func createAwsPricing(tx *gorm.DB, sf []*models.AwsPricing) error {
	return tx.Create(sf).Error
}
func updateAwsPricing(tx *gorm.DB, sf *models.AwsPricing) error {
	return tx.Save(sf).Error
}
