package billHandler

import (
	"context"
	"fmt"
	"strings"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils"
	commonContext "git.haier.net/devops/ops-golang-common/utils/context"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
	"github.com/shopspring/decimal"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
)

const (
	ali_name = "aliyun"

	dbms_scode = "S03467"

	product_code_oceanbase = "oceanbase"
	product_code_arms      = "arms"
	product_code_sc        = "sc"
	product_code_pts       = "pts"
	product_code_nat       = "nat"
	product_code_sls       = "sls"
	product_code_mns       = "mns"
	product_code_cdn       = "cdn"

	product_code_semicolon_ons          = "ons"
	product_code_semicolon_hdm          = "hdm"
	product_code_semicolon_cbwp         = "cbwp"
	product_code_semicolon_dts          = "dts"
	product_code_semicolon_nat_gw       = "nat_gw"
	product_code_semicolon_ecs_mt9_dt41 = "ecs-mt9-dt41"
	product_code_semicolon_hitsdb       = "hitsdb"
	product_code_semicolon_nas          = "nas"
	product_code_semicolon_ntr          = "ntr"
	product_code_semicolon_odps         = "odps"
	product_code_semicolon_polardb      = "polardb"
	product_code_semicolon_rds          = "rds"

	product_code_second_emapreduce = "emapreduce"
	product_code_second_gws        = "gws"
)

type SyncBillAliyun struct {
	*BaseSyncHandler
	aliyunScode scode
}

func (s *SyncBillAliyun) PullBill(ctx context.Context, c client.IClient, done chan struct{}, receivedChan chan *aliyun.BillItem) (err error) {
	defer s.done(err, done)

	if _, ok := c.(*aliyun.BillClient); !ok {
		return fmt.Errorf("not %s billClient", ali_name)
	}
	// data source from db
	if s.syncLogStageAt() == SYNC_LOG_STAGE_PULL {
		return findRawFromDB[aliyun.BillItem](s.BaseSyncHandler, done, receivedChan)
	}
	// data source from api
	return c.(*aliyun.BillClient).DescribeSplitItemBillWithChan(ctx, BILL_GRANULARITY, s.syncLog.BillingCycle, done, receivedChan)
}

func (s *SyncBillAliyun) ToBillRawData(ctx context.Context, item *aliyun.BillItem) []*models.RawBill {
	var subscription string
	if item.SubscriptionType == "PayAsYouGo" {
		subscription = SUBSCRIPTION_TYPE_PAY_AS_YOU_GO
	} else if item.SubscriptionType == "Subscription" {
		subscription = SUBSCRIPTION_TYPE_SUBSCRIPTION
	} else {
		subscription = item.SubscriptionType
	}

	scode := s.scode(item.CostUnit, s.aliyunScode.scodeMap)
	instanceId := s.instanceId(item, scode)
	return []*models.RawBill{{
		Model:             bizutils.DataSource().Model(ctx),
		TaskId:            s.taskId,
		Vendor:            s.syncLog.Vendor,
		AccountName:       item.BillAccountName,
		AccountID:         item.BillAccountID,
		Granularity:       BILL_GRANULARITY,
		BillingCycle:      s.billingCycle(),
		ProductCode:       item.ProductCode,
		ProductName:       item.ProductName,
		ResourceGroup:     item.ResourceGroup,
		InstanceID:        instanceId,
		Usage:             item.Usage,
		UsageUnit:         item.UsageUnit,
		ListPrice:         item.ListPrice,
		ListPriceUnit:     item.ListPriceUnit,
		ServicePeriod:     item.ServicePeriod,
		ServicePeriodUnit: item.ServicePeriodUnit,
		BillingItem:       item.BillingItem,
		CashAmount:        item.PaymentAmount,         // 现金支付
		PayableAmount:     item.PretaxAmount,          // 应付金额
		CouponAmount:      item.DeductedByCoupons,     // 优惠券金额
		VoucherAmount:     item.DeductedByCashCoupons, // 代金券抵扣金额
		CostUnit:          scode,
		Currency:          item.Currency,
		Region:            item.Region,
		Zone:              item.Zone,
		Content:           utils.JsonString(item),
		SubscriptionType:  subscription,
		ProductDetail:     item.ProductDetail,
		SupplementID:      item.SplitItemID,
		AggregatedID:      stringutil.Md5(item.BillAccountID + item.ProductCode + instanceId + item.SplitItemID),
	}}
}

func (s *SyncBillAliyun) CreateRawData(ctx context.Context, done chan struct{}, receivedChan chan *aliyun.BillItem) (err error) {
	var batch = []*models.RawBill{}

	for {
		raw, ok := <-receivedChan
		if !ok {
			close(done)
			return s.create(ctx, batch, s.rules)
		}

		batch = append(batch, s.ToBillRawData(ctx, raw)...)
		if len(batch) >= s.batchSize {
			if err = s.create(ctx, batch, s.rules); err != nil {
				close(done)
				return
			}
			batch = nil
		}
	}
}

func (s *SyncBillAliyun) rules(raw []*models.RawBill) (refinedRawBills []*models.RefinedRawBill) {
	refinedRawBills = make([]*models.RefinedRawBill, 0, len(raw))
	s.aliyunScode.scodeMap = s.checkSCode(s.aliyunScode)
	for _, r := range raw {
		refinedRawBill := &models.RefinedRawBill{TaskId: s.taskId, RawBill: *s.eraseModel(r)}

		// oceanbase rule
		switch refinedRawBill.ProductCode {
		case product_code_oceanbase:
			backupFlag := "bk_"
			if strings.HasPrefix(refinedRawBill.InstanceID, backupFlag) {
				start := strings.Index(refinedRawBill.InstanceID, backupFlag) + len(backupFlag)
				end := strings.Index(refinedRawBill.InstanceID, semicolon)

				refinedRawBill.InstanceID = refinedRawBill.InstanceID[start:end]
				s.aggregatedId(refinedRawBill)
			}
		}

		// split instanceId start ---
		// 按流量
		firstIndex, secondIndex, thirdIndex := 0, 1, 2
		if refinedRawBill.SubscriptionType == SUBSCRIPTION_TYPE_PAY_AS_YOU_GO {
			var recompute bool
			instanceId := refinedRawBill.InstanceID
			switch refinedRawBill.ProductCode {
			case product_code_sc:
				refinedRawBill.InstanceID = s.sc(instanceId)
				recompute = true
			case product_code_sls:
				refinedRawBill.InstanceID, refinedRawBill.SupplementID = instanceIdSupplementID(semicolon, instanceId, secondIndex, thirdIndex)
				recompute = true
			case product_code_semicolon_hdm:
				refinedRawBill.InstanceID, refinedRawBill.SupplementID = instanceIdSupplementID(semicolon, instanceId, firstIndex, secondIndex)
				recompute = true
			case product_code_semicolon_cbwp, product_code_semicolon_dts,
				product_code_semicolon_nat_gw, product_code_semicolon_ecs_mt9_dt41,
				product_code_semicolon_hitsdb, product_code_semicolon_nas,
				product_code_semicolon_ntr, product_code_semicolon_odps,
				product_code_semicolon_rds, product_code_semicolon_polardb:
				refinedRawBill.InstanceID, _ = instanceIdSupplementID(semicolon, instanceId, firstIndex, firstIndex)
				recompute = true
			case product_code_pts:
				refinedRawBill.InstanceID = fmt.Sprintf(hyphenFormat, refinedRawBill.AccountID, product_code_pts)
				recompute = true
			case product_code_second_emapreduce, product_code_second_gws:
				refinedRawBill.InstanceID, _ = instanceIdSupplementID(semicolon, instanceId, secondIndex, secondIndex)
				recompute = true
			case product_code_mns:
				refinedRawBill.InstanceID, _ = instanceIdSupplementID(semicolon, instanceId, firstIndex, firstIndex)
				refinedRawBill.InstanceID = strings.ReplaceAll(refinedRawBill.InstanceID, colon, underscore)
				recompute = true
			case product_code_cdn:
				refinedRawBill.InstanceID = refinedRawBill.SupplementID
				refinedRawBill.SupplementID = instanceId
				recompute = true
			}
			if recompute {
				s.aggregatedId(refinedRawBill)
			}
		}
		// 包年包月
		if refinedRawBill.SubscriptionType == SUBSCRIPTION_TYPE_SUBSCRIPTION {
			var recompute bool
			switch refinedRawBill.ProductCode {
			case product_code_nat:
				refinedRawBill.InstanceID = fmt.Sprintf(hyphenFormat, refinedRawBill.AccountID, product_code_nat)
				recompute = true
			}
			if recompute {
				s.aggregatedId(refinedRawBill)
			}
		}
		// --- split instanceId end

		// set scode start ---
		// 产品是hdm的hr690x hr690n账号的scode归到dbms下面scode,不走总表规则.
		if strings.Contains("hr690x hr690n", s.syncLog.AccountName) && refinedRawBill.ProductCode == product_code_semicolon_hdm {
			refinedRawBill.CostUnit = dbms_scode
			refinedRawBills = append(refinedRawBills, refinedRawBill)
			continue
		}
		// --- set scode end

		var defaultScode *models.DefaultScode
		var defaultScodeStr string
		// 获取账号兜底的S码
		defaultScode = bizutils.GetDefaultScode(context.Background(), s.syncLog.Vendor, s.syncLog.AccountName)
		if defaultScode != nil {
			defaultScodeStr = defaultScode.Scode
		}
		// get scode from cmdb
		if scode, ok := s.getSCodeFromCMDB(refinedRawBill.AggregatedID); ok {
			// 从cmdb总表中获取scode时判断scode是否与兜底默认规则相同
			if scode == defaultScodeStr { //相同: 使用cost_unit中的scode
				if rr, ok := s.scodeRule(s.aliyunScode.scodeMap, refinedRawBill); ok {
					refinedRawBills = append(refinedRawBills, rr)
					continue
				}
			} else {
				// 不相同: 使用cmdb总表中的scode
				refinedRawBills = append(refinedRawBills, scodeRule(refinedRawBill, scode, false))
				continue
			}
		}
		// cost_unit split rule 财务单元拆分规则
		if rules, ok := splitRules[refinedRawBill.AccountName+refinedRawBill.CostUnit]; ok {
			for _, r := range rules {
				refinedRawBills = append(refinedRawBills, s.amountProportion(refinedRawBill, r))
			}
			continue
		}
		// cut scode rule 财务单元分割规则
		if rr, ok := s.scodeRule(s.aliyunScode.scodeMap, refinedRawBill); ok {
			refinedRawBills = append(refinedRawBills, rr)
			continue
		}
		// default scode rule 兜底默认规则
		if defaultScode == nil {
			refinedRawBills = append(refinedRawBills, scodeRule(refinedRawBill, empty, false))
			continue
		}
		refinedRawBill.SCodeRuleMatched = defaultScode.ID        // 设置已走规则ID
		refinedRawBill.SCodeUnverified = refinedRawBill.CostUnit // 账号默认scode规则SCodeUnverified为原始costUnit
		if bizutils.IsSCode(defaultScodeStr) {
			refinedRawBill.CostUnit = defaultScodeStr
		} else {
			refinedRawBill.CostUnit = empty //  账号默认scode不合法则置为空
		}
		refinedRawBills = append(refinedRawBills, refinedRawBill)
	}

	return
}

// 按照百分比计算金额
func (s *SyncBillAliyun) amountProportion(refinedRawBill *models.RefinedRawBill, rule *models.CostUnitSplitRule) (amountProportionRefinedRawBill *models.RefinedRawBill) {
	p := func(amount float64) float64 {
		return decimal.NewFromFloat(amount).Mul(decimal.RequireFromString(rule.Percent).Mul(decimal.NewFromFloat(1)).
			Div(decimal.NewFromFloat(100))).InexactFloat64()
	}

	amountProportionRefinedRawBill = new(models.RefinedRawBill)
	if err := utils.Copy(refinedRawBill, amountProportionRefinedRawBill); err != nil {
		s.logger.Errorf(commonContext.NewContext(), "aliyun bill amountProportion failed: %s", err.Error())
	}

	amountProportionRefinedRawBill.PayableAmount = p(refinedRawBill.PayableAmount)
	amountProportionRefinedRawBill.CashAmount = p(refinedRawBill.CashAmount)
	amountProportionRefinedRawBill.VoucherAmount = p(refinedRawBill.VoucherAmount)
	amountProportionRefinedRawBill.SupplementID = rule.AppScode
	amountProportionRefinedRawBill.SCodeRuleMatched = rule.ID
	amountProportionRefinedRawBill = scodeRule(amountProportionRefinedRawBill, rule.AppScode, false)

	s.aggregatedId(amountProportionRefinedRawBill)
	return amountProportionRefinedRawBill
}

func (s *SyncBillAliyun) sc(rawBillInstanceId string) (instanceId string) {
	//1079601739310326;1704110400;ap-southeast-1;f-cn-zvp2qbp7902
	instanceId = rawBillInstanceId
	parts := strings.Split(instanceId, ";")

	for _, part := range parts {
		if strings.Contains(part, "f-cn") {
			instanceId = part
			return
		}
	}
	return
}

func (s *SyncBillAliyun) instanceId(item *aliyun.BillItem, costUnit string) string {
	type instanceIdHandle struct {
		b bool
		f func(c, s string) string
		c string
	}
	hyphenInstanceId := func(content, costUnit string) string {
		if v, ok := s.aliyunScode.scodeMap[costUnit]; ok {
			return content + hyphen + v.SCode
		}
		return content
	}
	secondLast := func(content, character string) string {
		secondLastIndex := 2 //倒数第二部分
		part := strings.Split(content, character)
		if c := len(part); c > 1 {
			return part[c-secondLastIndex]
		}
		return content
	}
	last := func(content, character string) string {
		lastIndex := 1 //最后一部分
		part := strings.Split(content, character)
		if c := len(part); c > 0 {
			return part[c-lastIndex]
		}
		return content
	}

	concat, split := true, false
	commodityCodeInstanceIdDict := map[string]instanceIdHandle{
		"arms_web_post页面浏览次数":                    {concat, hyphenInstanceId, item.CommodityCode},
		"arms_alert_public_cn短信通知数量":             {concat, hyphenInstanceId, item.CommodityCode},
		"arms_promethues_public_cn指标上报次数（百万次）":   {concat, hyphenInstanceId, item.CommodityCode},
		product_code_semicolon_ons + "公网流量费":     {concat, hyphenInstanceId, item.CommodityCode},
		product_code_semicolon_ons + "API调用次数":   {concat, hyphenInstanceId, item.CommodityCode},
		product_code_semicolon_ons + "Topic资源占用": {split, last, item.InstanceID},
		"arms_app_postAgent小时":                   {split, last, item.InstanceID},
		"onsproxy_post队列占用费":                     {split, secondLast, strings.ReplaceAll(item.InstanceID, separator, semicolon)},
		"oceanbase_oceanbasepre_public_cn数据库代理":  {split, secondLast, item.InstanceID},
		"arms_serverless_public_cn应用监控 - 指标用量":   {split, secondLast, item.InstanceID},
		"arms_serverless_public_cn应用监控 - 链路用量":   {split, secondLast, item.InstanceID},
		"arms_serverless_public_cn应用监控 - 剖析用量":   {split, secondLast, item.InstanceID},
	}
	var v instanceIdHandle
	var ok bool
	if v, ok = commodityCodeInstanceIdDict[item.CommodityCode+item.BillingItem]; !ok {
		return item.InstanceID
	}

	// v := commodityCodeInstanceIdDict[item.CommodityCode+item.BillingItem]
	switch item.ProductCode {
	case product_code_oceanbase:
		return v.f(v.c, semicolon)
	case product_code_arms, product_code_semicolon_ons:
		if v.b == concat {
			return v.f(v.c, costUnit)
		}
		if v.b == split {
			return v.f(v.c, semicolon)
		}
	}

	return item.InstanceID
}

func (s *SyncBillAliyun) ReceivedChan() chan *aliyun.BillItem {
	return make(chan *aliyun.BillItem)
}

func NewSyncBillAliyun(taskId string, syncLog *models.RawBillSyncLog) *SyncBillAliyun {
	b := &SyncBillAliyun{BaseSyncHandler: NewBaseHandler(ali_name)}
	b.syncLog = syncLog
	b.taskId = taskId
	b.aliyunScode.scodeMap = make(map[string]*expectSCode)
	return b
}
