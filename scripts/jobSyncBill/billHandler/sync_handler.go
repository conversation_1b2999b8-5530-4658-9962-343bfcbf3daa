package billHandler

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/pager"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils"
	commonContext "git.haier.net/devops/ops-golang-common/utils/context"
	"git.haier.net/devops/ops-golang-common/utils/stringutil"
	"gorm.io/gorm"

	"git.haier.net/devops/hcms-task-center/biz/api"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
)

const (
	empty       = ""
	unallocated = "未分配"
	scodeLen    = 6

	headLine                 = 1
	errCSVParseChannelClosed = "done billChan closed"

	SERVICE_FREE_TYPE_SERVICE_RATE  = 1 // 服务费
	SERVICE_FREE_TYPE_EXCHANGE_RATE = 2 // 汇率

	spot         = "."
	colon        = ":"
	hyphen       = "-"
	underscore   = "_"
	semicolon    = ";"
	separator    = "/"
	asterisk     = "*"
	hyphenFormat = "%s" + hyphen + "%s"
)

var (
	c                        = config.Global()
	beforeYesterdayVendorMap = map[string]bool{oracle_name: true, aws_name: true, GoogleName: true}
)

type SyncHandler[T any] interface {
	// api拉取账单数据
	PullBill(ctx context.Context, c client.IClient, done chan struct{}, receivedChan chan *T) error
	// 结构体数据转换
	ToBillRawData(ctx context.Context, data *T) []*models.RawBill
	// 数据通道
	ReceivedChan() chan *T
	// 创建数据
	CreateRawData(ctx context.Context, done chan struct{}, receivedChan chan *T) error
	// 按规则转S码
	rules([]*models.RawBill) []*models.RefinedRawBill
}

type BaseSyncHandler struct {
	logger    *log.Logger
	taskId    string
	batchSize int
	syncLog   *models.RawBillSyncLog
}

type scode struct {
	checkLastLen int //上一次检查后map的数量
	scodeMap     map[string]*expectSCode
}
type expectSCode struct {
	CheckSubProductsBool bool
	SCode                string
}

func NewBaseHandler(name string) *BaseSyncHandler {
	return &BaseSyncHandler{
		batchSize: BatchSize,
		logger:    log.NewWithOption(name, log.WithShowCaller(false)),
	}
}

type convert func([]*models.RawBill) []*models.RefinedRawBill

func (b *BaseSyncHandler) create(ctx context.Context, batch []*models.RawBill, convert convert) (err error) {
	if len(batch) == 0 {
		return
	}

	model := bizutils.DataSource().Model(ctx)
	if b.syncLogStageAt() == SYNC_LOG_STAGE_PRE_PULL {
		if err = b.rawBillCreate(model, batch); err != nil {
			b.logger.Errorf(ctx, "saving raw bill failed: %s", err.Error())
			return
		}
	}
	if middleBills := convert(batch); len(middleBills) > 0 {
		if err = b.MiddleBillCreate(model, middleBills); err != nil {
			b.logger.Errorf(ctx, "saving middle bill failed: %s", err.Error())
			return
		}
	}
	return
}

func (b *BaseSyncHandler) rawBillCreate(model models.Model, batch []*models.RawBill) error {
	return model.Orm().Create(&batch).Error
}

func (b *BaseSyncHandler) MiddleBillCreate(model models.Model, batch []*models.RefinedRawBill) (err error) {
	return model.Orm().Create(&batch).Error
}

func (b *BaseSyncHandler) done(err error, done chan struct{}) {
	if err != nil && done != nil {
		close(done)
	}
}

// 服务费和费率处理: 落库的金额 = 原始账单金额 x 服务费费率 x 汇率
// return: 转化后的金额和是否已经成功转换
// func (b *BaseSyncHandler) rate(cost float64, currency string) (complete float64, completeFlag bool) {
// 	complete = cost
// 	if cost == 0 {
// 		return
// 	}
// 	var serviceRate float64
// 	// var serviceRate, exchangeRate float64
// 	// var serviceFlag, exchangeFlag bool
// 	billingCycle, _ := time.Parse(DailyTimeFormat, b.syncLog.BillingCycle)
// 	// cache
// 	serviceFreeMap := bizutils.GetServiceFree(context.Background(), b.syncLog.Vendor)
// 	// 服务
// 	service := serviceFreeMap[fmt.Sprintf("%s%d", b.syncLog.Vendor, SERVICE_FREE_TYPE_SERVICE_RATE)]
// 	// 汇率
// 	// exchange := serviceFreeMap[fmt.Sprintf("%s%d%s", b.syncLog.Vendor, SERVICE_FREE_TYPE_EXCHANGE_RATE, strings.ToUpper(currency))]

// 	if len(service) > 0 {
// 		var serviceFlag bool
// 		for _, s := range service {
// 			// 使用此账单周期的费率
// 			if billingCycle.After(s.StartDate) && billingCycle.Before(s.EndDate) {
// 				serviceRate = s.Rate
// 				serviceFlag = true
// 				break
// 			}
// 		}
// 		// 账单周期无法匹配则使用最近一次周期费率
// 		if !serviceFlag {
// 			serviceRate = service[len(service)-1].Rate
// 		}
// 		complete = decimal.NewFromFloat(complete).Mul(decimal.NewFromFloat(serviceRate)).InexactFloat64()
// 	}

// if len(exchange) > 0 {
// 	for _, s := range exchange {
// 		// 使用此账单周期的汇率
// 		if billingCycle.After(s.StartDate) && billingCycle.Before(s.EndDate) {
// 			exchangeRate = s.Rate
// 			exchangeFlag = true
// 			break
// 		}
// 	}
// 	// 账单周期无法匹配则使用最近一次周期汇率
// 	if !exchangeFlag {
// 		exchangeRate = exchange[len(exchange)-1].Rate
// 	}
// 	complete = decimal.NewFromFloat(complete).Mul(decimal.NewFromFloat(exchangeRate)).InexactFloat64()
// 	completeFlag = true
// }

// 	return
// }

// 截取scode 校验scode
func (b *BaseSyncHandler) scode(costUnit string, scodeMap map[string]*expectSCode) (scode string) {
	scode = costUnit

	if len(scode) < scodeLen {
		return
	}

	subScode := scode[len(scode)-scodeLen:]
	switch b.syncLog.Vendor {
	case tencent_name:
		subScode = scode[:scodeLen] // tencent scode
	case GoogleName:
		subScode = strings.ToUpper(scode[:scodeLen]) // google scode
	}

	if bizutils.IsSCode(subScode) {
		scodeMap[scode] = &expectSCode{CheckSubProductsBool: hasPrefixForScode(subScode), SCode: subScode}
	}

	return
}

// 规则处理
func (b *BaseSyncHandler) priority(priority, key string, rules map[string]models.CloudCostUnitMoveRule, refinedRawBill *models.RefinedRawBill) (*models.RefinedRawBill, bool) {
	if v, ok := rules[GetRuleKey(priority, key)]; ok {
		refinedRawBill.SCodeRuleMatched = v.ID
		refinedRawBill = scodeRule(refinedRawBill, v.Appscode, false)
		return refinedRawBill, true
	}
	return refinedRawBill, false
}

// 检查cmdb总表缓存
func (b *BaseSyncHandler) getSCodeFromCMDB(aggregatedId string) (string, bool) {
	// get scode from cmdb
	scode, err := bizutils.SCodeFromCMDB(aggregatedId)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			// redis超时从db中获取
			if strings.Contains(err.Error(), "timeout") {
				cmdb, cmdbErr := bizutils.GetInstanceByAggregatedId(context.Background(), aggregatedId)
				if cmdbErr == nil {
					return cmdb.Scode, true
				}
				b.logger.Error(commonContext.NewContext(), fmt.Sprintf("getSCodeFromCMDB redis timeout get cmdb failed: %s, error: %s", aggregatedId, cmdbErr.Error()))
			}
			b.logger.Error(commonContext.NewContext(), fmt.Sprintf("getSCodeFromCMDB failed: aggregatedId: %s, error: %s", aggregatedId, err.Error()))
		}
	}
	if !isEmpty(scode) {
		return scode, true
	}

	return empty, false
}

// 子产品S码缓存
func (b *BaseSyncHandler) checkSCode(scode scode) map[string]*expectSCode {
	// 判断map检查后是否发生变化
	if scode.checkLastLen != 0 {
		if scode.checkLastLen == len(scode.scodeMap) {
			return scode.scodeMap
		}
	}

	for k, s := range scode.scodeMap {
		// 只检查S码,W和G吗其他等不做检查,表里也不存在.
		if !hasPrefixForScode(s.SCode) {
			continue
		}
		// 修改alm接口校验scode
		if _, err := api.HdsClient().QueryAlmProject(s.SCode); err != nil {
			// 测试过不存在的S码会在error里面返回NotFound
			b.logger.Error(commonContext.NewContext(), fmt.Sprintf("checkSCode: %s QueryAlmProject failed: %s", s.SCode, err.Error()))
			s.CheckSubProductsBool = false
			scode.scodeMap[k] = s
		}
	}

	scode.checkLastLen = len(scode.scodeMap)

	return scode.scodeMap
}

// 原始S码截取规则
func (b *BaseSyncHandler) scodeRule(scode_map map[string]*expectSCode, refined *models.RefinedRawBill) (*models.RefinedRawBill, bool) {
	if _, ok := scode_map[refined.CostUnit]; !ok {
		return nil, false
	} else {
		scode := scode_map[refined.CostUnit].SCode
		refined.SCodeUnverified = scode
		if !scode_map[refined.CostUnit].CheckSubProductsBool {
			return nil, false
		}
		refined.CostUnit = scode
		return refined, true
	}
}

// recompute aggregatedId
func (b *BaseSyncHandler) aggregatedId(refinedRawBill *models.RefinedRawBill) {
	refinedRawBill.AggregatedID = stringutil.Md5(refinedRawBill.AccountID + refinedRawBill.ProductCode + refinedRawBill.InstanceID + refinedRawBill.SupplementID)
}

func (b *BaseSyncHandler) eraseModel(rawBill *models.RawBill) *models.RawBill {
	rawBill.Model = models.Model{}
	return rawBill
}

func scodeRule(refinedRawBill *models.RefinedRawBill, costUnit string, equal bool) *models.RefinedRawBill {
	if equal {
		refinedRawBill.SCodeUnverified = costUnit
	} else {
		refinedRawBill.SCodeUnverified = refinedRawBill.CostUnit
	}
	refinedRawBill.CostUnit = costUnit
	return refinedRawBill
}

func instanceIdSupplementID(sep, rawBillInstanceId string, instanceIdIndex, supplementIdIndex int) (instanceId string, supplementId string) {
	instanceId, supplementId = rawBillInstanceId, rawBillInstanceId
	length := instanceIdIndex
	if instanceIdIndex < supplementIdIndex {
		length = supplementIdIndex
	}
	length = length + 1

	parts := strings.Split(rawBillInstanceId, sep)
	if len(parts) >= length {
		instanceId, supplementId = parts[instanceIdIndex], parts[supplementIdIndex]
	}
	return
}

func toFloat(s string) float64 {
	f, _ := strconv.ParseFloat(s, 64)
	return f
}

func hasPrefixForScode(scode string) bool {
	return strings.HasPrefix(strings.ToUpper(scode), scodePrefix)
}

func (b *BaseSyncHandler) syncLogStageAt() string {
	// 在prePull拉取未成功时从api拉取数据同步raw和refined
	if b.syncLog.Stage == SYNC_LOG_STAGE_PRE_PULL && b.syncLog.Status != SYNC_LOG_STATUS_FINISH {
		return SYNC_LOG_STAGE_PRE_PULL
	}
	// 其他情况下只同步refined表
	return SYNC_LOG_STAGE_PULL
}

func findRawFromDB[T any](b *BaseSyncHandler, done chan struct{}, receivedChan chan *T) error {
	var fromRawBillFlag bool
	if b, ok := beforeYesterdayVendorMap[b.syncLog.Vendor]; ok {
		fromRawBillFlag = b
	}
	page := 1
	for p, err := b.prePullBill(page); ; p, err = b.prePullBill(page) {
		if err != nil {
			close(receivedChan)
			return err
		}
		if len(p.Data) == 0 {
			break
		}

		b.logger.Infof(b.syncLog.Ctx, "find raw bill from DB, accountName: %s, billingCycle: %s, currentPage: %d, pages: %d, total: %d", b.syncLog.AccountName, b.billingCycle(), p.CurrentPage, p.Pages, p.Total)
		for _, bill := range p.Data {
			var t T
			if fromRawBillFlag {
				if err = utils.Copy(bill, &t); err != nil {
					return err
				}
			} else {
				if t, err = utils.UnmarshalString[T](bill.Content); err != nil {
					b.logger.Errorf(b.syncLog.Ctx, "findRawFromDB UnmarshalString failed: %s", err.Error())
					return err
				}
			}
			// content unmarshal failed changed rawBill return
			// if reflect.DeepEqual(t, emptyT) {
			// pre download bill data.  e.g. aws,oracle.
			// 	if err = utils.Copy(bill, &t); err != nil {
			// 		return err
			// 	}
			// }

			select {
			case <-done:
				close(receivedChan)
				return nil
			case receivedChan <- &t:
			}
		}
		page = page + 1
	}
	close(receivedChan)
	return nil
}

// 从redis获取账单数据
func findRawFromRedis[T any](b *BaseSyncHandler, done chan struct{}, receivedChan chan *T) (err error) {
	defer func() {
		if err != nil {
			close(receivedChan)
		}
	}()
	conn := c.RedisRawConn()

	prefix := fmt.Sprintf(RedisKeyPrefix+asterisk, GoogleName, b.syncLog.AccountID, b.syncLog.BillingCycle)
	var cursor uint64
	for {
		keys, nextCursor, err := conn.Scan(b.syncLog.Ctx, cursor, prefix, BatchSize).Result()
		if err != nil {
			return err
		}
		if len(keys) > 0 {
			for _, key := range keys {
				content, err := conn.Get(b.syncLog.Ctx, key).Result()
				if err != nil {
					return err
				}
				t, err := utils.UnmarshalString[T](content)
				if err != nil {
					b.logger.Errorf(b.syncLog.Ctx, "findRawFromRedis UnmarshalString failed: %s", err.Error())
					return err
				}
				select {
				case <-done:
					close(receivedChan)
					return nil
				case receivedChan <- &t:
				}
			}
			// 删除已获取的键释放内存;set时已设置超时时间,所以此处不做删除返回的处理.
			conn.Del(b.syncLog.Ctx, keys...).Result()
		}
		if nextCursor == 0 {
			break
		}
		cursor = nextCursor
	}
	close(receivedChan)
	return nil
}

// 查询raw_data数据库数据
func (b *BaseSyncHandler) prePullBill(page int) (*pager.Pager[*models.RawBill], error) {
	// find page from db
	p := pager.NewPager[*models.RawBill](page, 2000, pager.WithDatasource(bizutils.DataSource()),
		pager.WithCondition("vendor", pager.Equal, b.syncLog.Vendor),
		// pager.WithCondition("account_name", pager.Equal, b.syncLog.AccountName),
		pager.WithCondition("account_id", pager.Equal, b.syncLog.AccountID),
		pager.WithCondition("billing_cycle", pager.Equal, b.syncLog.BillingCycle))
	return p, p.Query()
}

func (b *BaseSyncHandler) billingCycle() time.Time {
	t, _ := time.Parse(time.DateOnly, b.syncLog.BillingCycle)
	return t
}
