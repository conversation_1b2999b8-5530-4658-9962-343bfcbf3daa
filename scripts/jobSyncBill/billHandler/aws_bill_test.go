package billHandler

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

// func Test_Sum_Bill(t *testing.T) {
// 	a := assert.New(t)
// 	sum, err := sumCostBillByMonth(context.Background(), "088595811235", "04")
// 	fmt.Println(sum)
// 	a.NoError(err)
// }

// func Test_Update_Bill(t *testing.T) {
// 	a := assert.New(t)
// 	model := bizutils.DataSource().Model(context.Background())
// 	err := model.Orm().Transaction(func(tx *gorm.DB) error {
// 		return updateAwsSyncLogAndBillTask(tx, "088595811235", "2024-04%")
// 	})
// 	a.NoError(err)
// }

// func Test_countBillByMonth(t *testing.T) {
// 	a := assert.New(t)
// 	c, err := countBillByMonth(context.Background(), "088595811235", "05")
// 	fmt.Println(c)
// 	a.NoError(err)
// }

func Test_findEC2InstanceProductSKU(t *testing.T) {
	a := assert.New(t)
	bills, err := findEC2ProductRegionSKU(context.Background())
	fmt.Println(bills)
	a.NoError(err)
}
