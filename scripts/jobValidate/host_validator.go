package jobValidate

import (
	"context"
	"fmt"
	"strings"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"

	"git.haier.net/devops/hcms-task-center/biz/api"
)

func checkHostSCodeProject(ctx context.Context, host *models.HostInfo) error {
	if host.Scode == "" {
		if host.Project != "" && host.Project != "default" {
			return fmt.Errorf("主机所属项目为：%s, S码为空", host.Project)
		}
		return fmt.Errorf("主机所属项目为空，S码为空")
	}
	if host.Scode != "default" && !strings.Contains(host.Scode, "基础设施") {
		project, _ := api.HdsClient().QueryAlmProject(host.Scode)
		if project == nil {
			return fmt.Errorf("主机S码%s项目不存在", host.Scode)
		}
		if project.AlmSCode != host.Scode {
			return fmt.Errorf("主机S码不匹配：%s -> %s(%s)", host.Scode, project.Id, project.AlmSCode)
		}
	}
	if host.PrivateIp == "" && host.Vendor != hbc.JXJG {
		return fmt.Errorf("主机私网IP为空")
	}
	hostInfo, _ := api.HcmsClient().QueryHaierHostInfoByIp(host.PrivateIp)
	if hostInfo == nil {
		return fmt.Errorf("资源API无法获取主机信息:%s", host.PrivateIp)
	}
	return nil
}
