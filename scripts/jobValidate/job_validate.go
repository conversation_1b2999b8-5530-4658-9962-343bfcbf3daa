package jobValidate

import (
	"context"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewJobValidate() *JobValidate {
	return &JobValidate{
		hostValidateFuncList: []hostValidateFunc{
			checkHostSCodeProject,
		},
		JobBase: base.NewJobBase(
			"RESOURCE_VALIDATE",
			"资源数据校验",
			"0 0 1 * * *",
			taskmodels.TaskCategoryOps,
			bizutils.DataSource()),
	}
}

type JobValidate struct {
	hostValidateFuncList []hostValidateFunc
	*base.JobBase
}

func (j *JobValidate) Run(ctx context.Context) (map[string]any, error) {
	errors, err := j.validateHosts(ctx)
	if err != nil {
		return nil, err
	}

	for _, err := range errors {
		if err == nil {
			continue
		}
		for _, e := range err.Errors {
			j.Logger().<PERSON>rro<PERSON>(ctx, "主机校验失败：%s", e.<PERSON>rror())
		}
	}

	return nil, nil
}
