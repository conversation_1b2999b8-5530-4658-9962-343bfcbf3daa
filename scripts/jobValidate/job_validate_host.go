package jobValidate

import (
	"context"

	"git.haier.net/devops/ops-golang-common/models/models"

	"git.haier.net/devops/hcms-task-center/core/tools"
)

type hostValidateFunc func(ctx context.Context, host *models.HostInfo) error

func (j *JobValidate) validateHosts(ctx context.Context) ([]*ResourceError, error) {
	hosts := make([]*models.HostInfo, 0)
	err := j.Orm(ctx).Table("rc_host_info").Where("is_deleted = '0'").Find(&hosts).Error
	if err != nil {
		return nil, err
	}

	validateProcess := tools.NewBatch[*models.HostInfo, *ResourceError](ctx)
	validateProcess.Run(hosts, func(ctx context.Context, host *models.HostInfo) (*ResourceError, error) {
		j.Logger().Infof(ctx, "开始校验主机：%s", host.InstanceId)
		if host == nil {
			return nil, nil
		}
		info := &ResourceError{
			Vendor:       host.Vendor.String(),
			Account:      host.AccountName,
			ResourceType: "host",
			Id:           host.InstanceId,
			Errors:       make([]error, 0),
		}
		for _, f := range j.hostValidateFuncList {
			if err := f(ctx, host); err != nil {
				info.Errors = append(info.Errors, err)
			}
		}
		if len(info.Errors) > 0 {
			return info, nil
		}
		return nil, nil
	})

	return tools.MergeData(validateProcess.Outs()), validateProcess.Error()
}
