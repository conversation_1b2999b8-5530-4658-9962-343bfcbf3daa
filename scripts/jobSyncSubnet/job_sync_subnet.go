package jobSyncSubnet

import (
	"context"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func New() *SubnetSync {
	c := config.Global()
	return &SubnetSync{
		JobBase: base.NewJobBase(
			"SUBNET",
			"同步子网信息",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type SubnetSync struct {
	*base.JobBase
}

func (s *SubnetSync) Name() string {
	return base.TaskCloudSyncSubnet
}

func (s *SubnetSync) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(s.Model(ctx), hbc.GoogleCloud)
	if err != nil {
		return nil, err
	}
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(
		ctx, vendors,
		hbc.Vpc,
		[]string{bizutils.PurposeAdmin},
		actfilter.SetCustomProduct(hbc.HuaweiCloud, hbc.IAM),              // 华为云默认使用IamClient
		actfilter.SetCustomProduct(hbc.TencentCloud, hbc.ECS),             // 腾讯云默认使用EcsClient
		actfilter.SetCustomProduct(hbc.AWS, hbc.ECS),                      // AwsVpc接口在EC2产品中
		actfilter.SetCustomPurpose(hbc.AWS, bizutils.PurposeAccountCheck), // AWS复用 ACCOUNT_CHECK 的账号
		actfilter.SetIgnoreVendor(hbc.Private),
	)
	if err != nil {
		return nil, err
	}

	// 获取云资源信息
	b := tools.NewBatch[client.IClient, []*models.SubnetInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.SubnetInfo, error) {
		switch defClient := input.(type) {
		case *aliyun.VpcClient:
			return s.syncAliyunSubnetInfo(ctx, defClient)
		case *huaweicloud.IamClient:
			return s.synHuaweiSubnetInfo(ctx, defClient)
		case *tencentcloud.EcsClient:
			return s.syncTencentSubnetInfo(ctx, defClient)
		case *aws.Ec2Client:
			return s.syncAwsSubnetInfo(ctx, defClient)
		case *azure.VirtualNetworkClient:
			return s.syncAzureSubnetInfo(ctx, defClient)
		}
		return nil, nil
	})
	if err := b.Error(); err != nil {
		s.Errorf(ctx, "sync subnet failed, cause: %s", err)
		return nil, err
	}

	return nil, bizutils.CreateOrUpdateSubnet(ctx, tools.MergeData(b.Outs()...)...)
}
