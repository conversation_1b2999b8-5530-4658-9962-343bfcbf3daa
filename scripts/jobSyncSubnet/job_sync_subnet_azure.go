package jobSyncSubnet

import (
	"context"
	"strings"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"github.com/aws/smithy-go/ptr"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
)

func (s *SubnetSync) syncAzureSubnetInfo(ctx context.Context, cli *azure.VirtualNetworkClient) ([]*models.SubnetInfo, error) {
	defer func() {
		if err := recover(); err != nil {
			s.Errorf(ctx, "syncAzureSubnetInfo recover error: %v", err)
			panic(err)
		}
	}()

	virtualNetworkList, err := cli.NewListAllPager(ctx)
	if err != nil {
		s.Errorf(ctx, "syncAzureSubnetInfo NewListAllPager error: %v", err)
		return nil, bizutils.WarpClientError(cli, err)
	}

	subnetInfoSlice := make([]*models.SubnetInfo, 0)

	for _, virtualNetwork := range virtualNetworkList {
		location := *virtualNetwork.Location
		for _, subnet := range virtualNetwork.Properties.Subnets {
			resourceGroupName, vpcId, subnetId := ParseVpcIdAndSubnetId(*subnet.ID)
			cidr := ""
			if subnet.Properties.AddressPrefix != nil {
				cidr = *subnet.Properties.AddressPrefix
			}
			subnetInfoSlice = append(subnetInfoSlice,
				&models.SubnetInfo{
					Model:         s.Model(ctx),
					Vendor:        cli.Vendor(),
					AccountName:   cli.Name(),
					CreationTime:  nil,
					VpcId:         vpcId,
					SubnetId:      subnetId,
					ResourceGroup: resourceGroupName,
					Region:        location,
					Zone:          location,
					Cidr:          cidr,
					State:         common.StatusAvailable,
					IsDeleted:     ptr.Bool(false),
				})
		}
	}

	return subnetInfoSlice, nil
}

func ParseVpcIdAndSubnetId(id string) (resourceGroupName string, vpcId string, subnetId string) {
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			resourceGroupName = s[n+1]
		}
		if k == "virtualNetworks" {
			vpcId = s[n+1]
		}
		if k == "subnets" {
			subnetId = s[n+1]
		}
	}
	return
}
