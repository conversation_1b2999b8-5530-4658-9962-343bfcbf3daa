package jobSyncSubnet

import (
	"context"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"time"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"github.com/aws/smithy-go/ptr"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func (s *SubnetSync) synHuaweiSubnetInfo(ctx context.Context, cli *huaweicloud.IamClient) ([]*models.SubnetInfo, error) {
	projects, err := cli.ListProjects(ctx)
	if err != nil {
		return nil, err
	}
	regions, err := cli.ListRegions(ctx)
	if err != nil {
		return nil, err
	}

	b := tools.NewBatch[model.ProjectResult, []*models.SubnetInfo](ctx)
	b.Run(projects, func(ctx context.Context, project model.ProjectResult) ([]*models.SubnetInfo, error) {
		return s.syncHuaweiProjectSubnetInfo(ctx, project, regions, cli)
	})

	return tools.MergeData(b.Outs()...), bizutils.WarpClientError(cli, b.Error())
}

func (s *SubnetSync) syncHuaweiProjectSubnetInfo(
	ctx context.Context, project model.ProjectResult, regions []model.Region,
	cli *huaweicloud.IamClient) ([]*models.SubnetInfo, error) {
	b := tools.NewBatch[model.Region, []*models.SubnetInfo](ctx)
	b.Run(regions, func(ctx context.Context, region model.Region) ([]*models.SubnetInfo, error) {
		vpcCli := hybrid.AccountManager().HuaweiVpcClient(ctx, cli.Name(), cli.Tag(), region.Id, project.Id)
		return s.syncHuaweiRegionSubnetInfo(ctx, vpcCli)
	})

	return tools.MergeData(b.Outs()...), bizutils.WarpClientError(cli, b.Error())
}

func (s *SubnetSync) syncHuaweiRegionSubnetInfo(ctx context.Context, cli *huaweicloud.VpcClient) ([]*models.SubnetInfo, error) {
	if cli == nil || cli.Client() == nil {
		return nil, nil
	}

	subnets, err := cli.ListSubnetsOfAll()
	if err != nil {
		return nil, nil
	}

	subnetInfos := make([]*models.SubnetInfo, len(subnets))
	for i, subnet := range subnets {
		var creationTime time.Time
		if subnet.CreatedAt != nil {
			creationTime, _ = time.Parse(bizutils.HuaweiEcsTimeFormat, subnet.CreatedAt.String())
		}
		subnetInfos[i] = &models.SubnetInfo{
			Model:        s.Model(ctx),
			Vendor:       cli.Vendor(),
			AccountName:  cli.Name(),
			CreationTime: timeutil.ZeroTime(creationTime),
			VpcId:        subnet.VpcId,
			SubnetId:     subnet.Id,
			Region:       cli.Region(),
			Zone:         subnet.AvailabilityZone,
			State:        common.StatusAvailable,
			Cidr:         subnet.Cidr,
			IsDeleted:    ptr.Bool(false),
		}
	}
	return subnetInfos, nil
}
