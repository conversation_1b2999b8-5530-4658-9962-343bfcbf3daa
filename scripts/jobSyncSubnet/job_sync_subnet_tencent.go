package jobSyncSubnet

import (
	"context"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"time"

	"git.haier.net/devops/ops-golang-common/common"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/LPX3F8/orderedmap"
	"github.com/aws/smithy-go/ptr"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func (s *SubnetSync) syncTencentSubnetInfo(ctx context.Context, cli *tencentcloud.EcsClient) ([]*models.SubnetInfo, error) {
	regions, err := cli.DescribeRegions(ctx)
	if err != nil {
		s.Errorf(ctx, "tencent 查询区域出错：%v", err.Error())
		return nil, bizutils.WarpClientError(cli, err)
	}
	regionsIds := orderedmap.New[string, string]()
	for _, region := range regions {
		regionsIds.Store(pointer.String(region.Region), pointer.String(region.Region))
	}

	b := tools.NewBatch[string, []*models.SubnetInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, region string) ([]*models.SubnetInfo, error) {
		cli := hybrid.AccountManager().TencentVpcClient(ctx, cli.Name(), cli.Tag(), region)
		return s.syncTencentRegionSubnetInfo(ctx, cli)
	})

	return tools.MergeData(b.Outs()...), bizutils.WarpClientError(cli, b.Error())
}

func (s *SubnetSync) syncTencentRegionSubnetInfo(ctx context.Context, cli *tencentcloud.VpcClient) ([]*models.SubnetInfo, error) {
	subnets, err := cli.DescribeSubnetsOfAll(ctx)
	if err != nil {
		s.Errorf(ctx, "tencent 查询子网出错：%v", err.Error())
		return nil, bizutils.WarpClientError(cli, err)
	}

	subnetInfos := make([]*models.SubnetInfo, len(subnets))
	for i, subnet := range subnets {
		creationTime, _ := time.Parse(bizutils.HuaweiEcsTimeFormat, *subnet.CreatedTime)
		subnetInfos[i] = &models.SubnetInfo{
			Model:        s.Model(ctx),
			Vendor:       cli.Vendor(),
			AccountName:  cli.Name(),
			CreationTime: timeutil.ZeroTime(creationTime),
			VpcId:        pointer.String(subnet.VpcId),
			SubnetId:     pointer.String(subnet.SubnetId),
			Region:       cli.Region(),
			Zone:         pointer.String(subnet.Zone),
			State:        common.StatusAvailable,
			Cidr:         pointer.String(subnet.CidrBlock),
			IsDeleted:    ptr.Bool(false),
		}
	}
	return subnetInfos, nil
}
