package jobSyncSubnet

import (
	"context"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/LPX3F8/orderedmap"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func (s *SubnetSync) syncAwsSubnetInfo(ctx context.Context, cli *aws.Ec2Client) ([]*models.SubnetInfo, error) {
	regions, err := cli.DescribeAvailableRegions(ctx)
	if err != nil {
		return nil, bizutils.WarpClientError(cli, err)
	}
	regionsIds := orderedmap.New[string, string]()
	for _, region := range regions {
		regionsIds.Store(pointer.String(region.RegionName), pointer.String(region.RegionName))
	}

	b := tools.NewBatch[string, []*models.SubnetInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, regionName string) ([]*models.SubnetInfo, error) {
		regionCli := hybrid.AccountManager().AwsEc2Client(ctx, cli.Name(), cli.Tag(), regionName)
		return s.syncAwsRegionSubnetInfo(ctx, regionCli)
	})

	return tools.MergeData(b.Outs()...), bizutils.WarpClientError(cli, b.Error())
}

func (s *SubnetSync) syncAwsRegionSubnetInfo(ctx context.Context, cli *aws.Ec2Client) ([]*models.SubnetInfo, error) {
	subnets, err := bizutils.WithRetry(
		3,
		func() ([]types.Subnet, error) {
			return cli.DescribeSubnetsOfAll(ctx)
		},
	)

	if err != nil {
		return nil, bizutils.WarpClientError(cli, err)
	}

	subnetInfos := make([]*models.SubnetInfo, len(subnets))
	for i, subnet := range subnets {
		subnetInfos[i] = &models.SubnetInfo{
			Model:        s.Model(ctx),
			Vendor:       cli.Vendor(),
			AccountName:  cli.Name(),
			CreationTime: nil,
			VpcId:        pointer.String(subnet.VpcId),
			SubnetId:     pointer.String(subnet.SubnetId),
			Region:       cli.Region(),
			Zone:         pointer.String(subnet.AvailabilityZoneId),
			State:        string(subnet.State),
			Cidr:         pointer.String(subnet.CidrBlock),
		}
	}
	return subnetInfos, nil

}
