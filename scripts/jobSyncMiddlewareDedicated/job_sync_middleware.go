package jobSyncMiddlewareDedicated

import (
	"context"
	"sync"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func NewSyncMiddlewareDedicated() *SyncMiddlewareDedicated {
	c := config.Global()
	return &SyncMiddlewareDedicated{
		mtx: new(sync.Mutex),
		rg:  orderedmap.New[string, *resourcemanager.ResourceGroup](),
		ep:  orderedmap.New[string, *eps.EpDetail](),
		JobBase: base.NewJobBase("DEDICATED_MIDDLEWARE_SYNC",
			"同步专属云中间件实例",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type SyncMiddlewareDedicated struct {
	rg  *orderedmap.OrderedMap[string, *resourcemanager.ResourceGroup]
	ep  *orderedmap.OrderedMap[string, *eps.EpDetail]
	mtx *sync.Mutex
	*base.JobBase
}

func (j *SyncMiddlewareDedicated) Name() string {
	return base.TaskCloudSyncMiddlewareDedicated
}

func (j *SyncMiddlewareDedicated) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(j.Model(ctx), hbc.GoogleCloud, hbc.Private, hbc.HuaweiCloudDedicated, hbc.AliCloud, hbc.HuaweiCloud, hbc.TencentCloud, hbc.AWS, hbc.Azure)
	if err != nil {
		return nil, err
	}

	defaultClients, err := base.GetDefaultIClients(ctx, vendors, hbc.RocketMQ, []string{bizutils.PurposeDedicated})
	if err != nil {
		return nil, err
	}

	b := tools.NewBatch[client.IClient, []*models.MiddlewareInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.MiddlewareInfo, error) {
		switch defaultClient := input.(type) {
		case *aliyun.RocketMQClient:
			return j.syncAliyunDedicatedRocketMQInfo(ctx, defaultClient)
		}
		return nil, nil
	})

	if err := b.Error(); err != nil {
		j.Errorf(ctx, "sync dedicated middleware failed, cause: %s", err)
		return nil, err
	}

	// 将信息存入数据库
	return nil, bizutils.CreateOrUpdateMiddleware(ctx, tools.MergeData(b.Outs()...)...)
}

func (j *SyncMiddlewareDedicated) GetAliyunResourceGroup(client *aliyun.ResourceClient, groupId string) *resourcemanager.ResourceGroup {
	return bizutils.LoadFromMap[string, *resourcemanager.ResourceGroup](j.mtx, j.rg, groupId, func() (*resourcemanager.ResourceGroup, error) {
		return client.GetResourceGroup(groupId)
	})
}

func (j *SyncMiddlewareDedicated) GetHuaweiEnterpriseProject(client *huaweicloud.EpsClient, ctx context.Context, projectId string) *eps.EpDetail {
	return bizutils.LoadFromMap[string, *eps.EpDetail](j.mtx, j.ep, projectId, func() (*eps.EpDetail, error) {
		return client.ShowEnterpriseProject(ctx, projectId)
	})
}
