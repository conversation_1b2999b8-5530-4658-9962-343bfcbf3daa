package jobSyncMiddlewareDedicated

import (
	"context"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/utils/timeutil"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/models/ormtype"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	rocketmq "github.com/alibabacloud-go/rocketmq-20220801/client"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var aliyun_product_code_ons = []string{"ons"}

func (j *SyncMiddlewareDedicated) syncAliyunDedicatedRocketMQInfo(ctx context.Context, cli client.IClient) ([]*models.MiddlewareInfo, error) {
	regions := []string{"cn-qingdao-acdr-ut-1"}

	process := tools.NewBatch[string, []*models.MiddlewareInfo](ctx)
	process.Run(regions, func(ctx context.Context, regionId string) ([]*models.MiddlewareInfo, error) {
		rocketMQClient := hybrid.AccountManager().AliyunDedicatedRocketMQClient(ctx, cli.Name(), cli.Tag(), regionId)
		resCli := hybrid.AccountManager().AliyunDedicatedResourceManagerClient(ctx, cli.Name(), cli.Tag(), regionId)
		instanceList, err := rocketMQClient.ListInstances(ctx)
		if err != nil {
			return nil, err
		}

		getMongoAttrProcess := tools.NewBatch[*rocketmq.ListInstancesResponseBodyDataList, *models.MiddlewareInfo](ctx)
		getMongoAttrProcess.Run(instanceList.List, func(ctx context.Context, ins *rocketmq.ListInstancesResponseBodyDataList) (*models.MiddlewareInfo, error) {
			attr, err := rocketMQClient.GetInstance(ctx, pointer.Value(ins.InstanceId))
			if err != nil {
				return nil, err
			}

			var topicQuota int
			var groupQuota int
			for _, quota := range attr.InstanceQuotas {
				switch pointer.Value(quota.QuotaName) {
				case "TOPIC_COUNT":
					topicQuota = int(pointer.Value(quota.TotalCount))
				case "GROUP_COUNT":
					groupQuota = int(pointer.Value(quota.TotalCount))
				}
			}

			publicEndpoints := make(ormtype.StringSlice, 0)
			privateEndpoints := make(ormtype.StringSlice, 0)
			for _, endpoint := range attr.NetworkInfo.Endpoints {
				if pointer.Value(endpoint.EndpointType) == "TCP_VPC" {
					privateEndpoints = append(privateEndpoints, pointer.Value(endpoint.EndpointUrl))
				} else {
					publicEndpoints = append(publicEndpoints, pointer.Value(endpoint.EndpointUrl))
				}
			}

			createAt, _ := time.Parse(bizutils.DefaultTimeFormatter, pointer.Value(attr.CreateTime))
			expireAt, _ := time.Parse(bizutils.DefaultTimeFormatter, pointer.Value(attr.ExpireTime))
			env, scode, project := bizutils.ParseAliyunEnvProject(resCli, j, pointer.Value(attr.ResourceGroupId))
			cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.Value(attr.InstanceId), aliyun_product_code_ons)
			return &models.MiddlewareInfo{
				Vendor:           cli.Vendor(),
				AccountName:      cli.Name(),
				InstanceId:       pointer.Value(attr.InstanceId),
				InstanceName:     pointer.Value(attr.InstanceName),
				CreationTime:     timeutil.ZeroTime(createAt),
				ExpiredTime:      timeutil.ZeroTime(expireAt),
				IsDeleted:        nil,
				InsType:          string(hbc.RocketMQ),
				InsVersion:       pointer.Value(attr.Software.SoftwareVersion),
				InsCategory:      strings.Join([]string{pointer.Value(attr.SeriesCode), pointer.Value(attr.SubSeriesCode)}, "-"),
				InsStatus:        pointer.Value(attr.Status),
				ClassCode:        pointer.Value(attr.ProductInfo.MsgProcessSpec),
				ResourceGroup:    pointer.Value(attr.ResourceGroupId),
				Region:           cli.Region(),
				PrivateEndpoints: &privateEndpoints,
				PublicEndpoints:  &publicEndpoints,
				PayType:          pointer.Value(attr.PaymentType),
				Description:      pointer.Value(ins.Remark),
				TopicNum:         int(pointer.Value(attr.TopicCount)),
				TopicQuota:       topicQuota,
				GroupNum:         int(pointer.Value(attr.GroupCount)),
				GroupQuota:       groupQuota,
				VpcId:            pointer.Value(attr.NetworkInfo.VpcInfo.VpcId),
				SubnetId:         pointer.Value(attr.NetworkInfo.VpcInfo.VSwitchId),
				Env:              env,
				Project:          project,
				SCode:            bizutils.SwapSCode(scode, cmdb.Scode),
				AggregatedId:     cmdb.AggregatedID,
			}, nil

		})

		return getMongoAttrProcess.Outs(), getMongoAttrProcess.Error()
	})

	return tools.MergeData(process.Outs()...), bizutils.WarpClientError(cli, process.Error())
}
