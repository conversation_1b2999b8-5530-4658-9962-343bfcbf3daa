package jobCleanupTask

import (
	"context"
	"time"

	"gorm.io/gorm"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

func New() *CleanupTask {
	return &CleanupTask{
		JobBase: base.NewJobBase(
			"TASK_CLEAN",
			"清理过期异常任务",
			base.NewSchedule(
				base.WithMin(0),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryOps,
			bizutils.DataSource(),
		),
	}
}

type CleanupTask struct {
	*base.JobBase
}

func (c *CleanupTask) Name() string {
	return base.TaskJobClean
}

func (c *CleanupTask) Run(ctx context.Context) (map[string]any, error) {
	now := time.Now()
	logs := make([]*taskmodels.TcTaskExecLog, 0)
	err := c.Orm(ctx).Table("tc_task_exec_log").
		Where("status = 'running' and env = ?", config.Global().Env).
		Find(&logs).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	process := tools.NewBatch[*taskmodels.TcTaskExecLog, *taskmodels.TcTaskExecLog](ctx)
	process.Run(logs, func(ctx context.Context, input *taskmodels.TcTaskExecLog) (*taskmodels.TcTaskExecLog, error) {
		task := new(taskmodels.TcTaskSchedule)
		err := c.Orm(ctx).Table("tc_task_schedule").Where("name = ? and env = ?", input.Name, input.Env).First(task).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}

		durationStr := "1h"
		if task != nil && task.MaxExecTimeout != "" {
			durationStr = task.MaxExecTimeout
		}
		duration, err := time.ParseDuration(durationStr)
		if err != nil {
			return nil, err
		}
		if !now.After(input.CreateTime.Add(duration).Add(time.Minute)) {
			return nil, nil
		}

		input.Status = taskcenter.TaskStatusException
		input.Error = "task timeout with max_duration: " + durationStr + ", closed by task cleaner"
		return input, c.Orm(ctx).Table("tc_task_exec_log").Save(input).Error
	})
	closedTask := process.Outs()

	return map[string]any{
		"total":  len(closedTask),
		"detail": closedTask,
	}, process.Error()
}
