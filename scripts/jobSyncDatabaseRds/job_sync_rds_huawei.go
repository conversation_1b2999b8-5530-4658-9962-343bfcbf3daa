package jobSyncDatabaseRds

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	epsModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"
	iamModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/rds/v3/model"
	"golang.org/x/time/rate"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var huawei_product_code_rds = []string{"hws.service.type.taurus", "hws.service.type.rds", "hws.service.type.das"}

func (r *RdsSync) syncHuaweiRdsInfo(defClient *huaweicloud.IamClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	projects, err := defClient.ListProjects(ctx)
	if err != nil {
		return nil, err
	}
	regions, err := defClient.ListRegions(ctx)
	if err != nil {
		return nil, err
	}

	projectBatch := tools.NewBatch[iamModel.ProjectResult, []*models.DatabaseInfo](ctx)
	projectBatch.Run(projects, func(ctx context.Context, project iamModel.ProjectResult) ([]*models.DatabaseInfo, error) {
		var targetRegion *iamModel.Region
		for _, region := range regions {
			if region.Id == project.Name {
				targetRegion = &region
				break
			}
		}
		if targetRegion == nil {
			r.Warnf(ctx, "unexpected regionId: %s, skip ...", project.Name)
			return nil, nil
		}
		infoList, err := r.getHuaweiRegionRds(ctx, defClient, *targetRegion, project)
		r.Infof(ctx, "get %d records for all regions", len(infoList))
		return infoList, err
	})

	return tools.MergeData(projectBatch.Outs()...), projectBatch.Error()
}

func (r *RdsSync) getHuaweiRegionRds(ctx context.Context, defClient client.IClient, region iamModel.Region, project iamModel.ProjectResult) (infoList []*models.DatabaseInfo, err error) {
	rdsClient := hybrid.AccountManager().HuaweiRdsClient(ctx, defClient.Name(), defClient.Tag(), region.Id, project.Id)
	if rdsClient == nil {
		return nil, nil
	}
	instanceList, listServerError := rdsClient.ListInstancesOfAll(ctx)
	if listServerError != nil {
		r.Errorf(ctx, "getHuaweiRegionRds: %s, project: %s", listServerError, utils.JsonString(project))
		return nil, nil
	}

	epsClient := hybrid.AccountManager().HuaweiEpsClient(ctx, defClient.Name(), defClient.Tag(), region.Id)
	infoList = make([]*models.DatabaseInfo, len(instanceList))
	// 1 requests every 1 seconds
	limiter := rate.NewLimiter(1, 1)
	for i, ins := range instanceList {
		// 限流
		if err := limiter.Wait(ctx); err != nil {
			return nil, err
		}
		infoList[i] = r.tranceHuaweiRds2RdsInfo(ctx, rdsClient, epsClient, ins)
	}

	return infoList, nil
}

// func (r *RdsSync) handleHuaweiProjectPanic(panicError any, ctx context.Context, region iamModel.Region) error {
// 	var msg string
// 	switch pe := panicError.(type) {
// 	case error:
// 		msg = pe.Error()
// 	case string:
// 		msg = pe
// 	}

// 	if strings.HasPrefix(msg, "unexpected regionId") {
// 		r.Warnf(ctx, "%s getHuaweiRegionEcs: %s", region.Id, msg)
// 	} else if strings.HasPrefix(msg, "failed to get project id, No project id found") {
// 		r.Warnf(ctx, "%s getHuaweiRegionEcs: %s", region.Id, msg)
// 	} else {
// 		return errors.New(msg)
// 	}
// 	return nil
// }

func (r *RdsSync) tranceHuaweiRds2RdsInfo(
	ctx context.Context,
	rdsClient *huaweicloud.RdsClient,
	epsClient *huaweicloud.EpsClient,
	rds model.InstanceResponse,
) *models.DatabaseInfo {

	_, scode, project := bizutils.ParseHuaweiEnvProject(ctx, rds.Name, rds.EnterpriseProjectId, epsClient, r)
	creationTime, _ := time.Parse(bizutils.HuaweiRdsTimeFormat, rds.Created)
	expiredTime, _ := time.Parse(time.RFC3339, pointer.String(rds.ExpirationTime))
	cpu, _ := strconv.Atoi(pointer.String(rds.Cpu))
	ram, _ := strconv.Atoi(pointer.String(rds.Mem))

	// get env from tag
	var env string
	if len(rds.Tags) > 0 {
	outerLoop:
		for _, tag := range rds.Tags {
			switch strings.ToLower(strings.TrimSpace(tag.Key)) {
			case RDS_ENV, RDS_ENV_CH:
				env = tag.Value
				break outerLoop
			}
		}
	}
	cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(rdsClient.Vendor()), rdsClient.Identifier(), rds.Id, huawei_product_code_rds)
	return &models.DatabaseInfo{
		Model:         r.Model(ctx),
		Vendor:        rdsClient.Vendor(),
		AccountName:   rdsClient.Name(),
		InstanceId:    rds.Id,
		InstanceName:  rds.Name,
		InstanceType:  getHuaweiRdsType(rds),
		InstanceRole:  getHuaweiRdsRole(rds),
		Category:      getHuaweiRdsCategory(rds),
		ResourceGroup: rds.EnterpriseProjectId,
		Status:        rds.Status,
		ClassCode:     rds.FlavorRef,
		ChargeType:    rds.ChargeInfo.ChargeMode.Value(),
		CreationTime:  timeutil.ZeroTime(creationTime),
		ExpiredTime:   timeutil.ZeroTime(expiredTime),
		Host:          getHuaweiRdsPrivateConnAddr(rds),
		Port:          int(rds.Port),
		EngineType:    rds.Datastore.Type.Value(),
		EngineVersion: rds.Datastore.Version,
		Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
		Project:       project,
		Env:           env,
		Cpu:           cpu,
		Memory:        uint64(ram) * 1024,
		DiskSize:      float64(rds.Volume.Size) * 1024,
		DiskType:      rds.Volume.Type.Value(),
		VpcId:         rds.VpcId,
		SubnetId:      rds.SubnetId,
		Region:        rds.Region,
		Zone:          getHuaweiMasterZone(rds),
		Content:       utils.JsonString(rds),
		PrivateIp:     getHuaweiRdsPrivateIP(rds),
		PublicIp:      getHuaweiRdsPublicIP(rds),
		AggregatedId:  cmdb.AggregatedID,
	}
}

func getHuaweiRdsPrivateConnAddr(rds model.InstanceResponse) string {
	for _, dns := range *rds.PrivateDnsNames {
		return dns
	}
	for _, ip := range rds.PrivateIps {
		return ip
	}
	return ""
}

func getHuaweiRdsPrivateIP(rds model.InstanceResponse) string {
	for _, ip := range rds.PrivateIps {
		return ip
	}
	return ""
}

func getHuaweiRdsPublicIP(rds model.InstanceResponse) string {
	for _, ip := range rds.PublicIps {
		return ip
	}
	return ""
}

func getHuaweiMasterZone(rds model.InstanceResponse) string {
	for _, n := range rds.Nodes {
		if n.Role == "master" {
			return n.AvailabilityZone
		}
	}
	return ""
}

func getHuaweiRdsType(rds model.InstanceResponse) string {
	switch strings.ToLower(rds.Type) {
	case "replica":
		return RDSTypeReadonly
	default:
		return RDSTypeNormal
	}
}

func getHuaweiRdsRole(rds model.InstanceResponse) string {
	switch getHuaweiRdsType(rds) {
	case RDSTypeNormal:
		return RdsRoleMaster
	default:
		return RdsRoleSlave
	}
}

func getHuaweiRdsCategory(rds model.InstanceResponse) string {
	switch strings.ToLower(rds.Type) {
	case "replica", "single":
		return RdsCategoryBasic
	default:
		return RdsCategoryHA
	}
}

func (r *RdsSync) GetHuaweiEnterpriseProject(client *huaweicloud.EpsClient, ctx context.Context, projectId string) *epsModel.EpDetail {
	if client == nil {
		return nil
	}
	return bizutils.LoadFromMap[string, *epsModel.EpDetail](r.mtx, r.ep, projectId, func() (*epsModel.EpDetail, error) {
		return client.ShowEnterpriseProject(ctx, projectId)
	})
}
