package jobSyncDatabaseRds

const (
	RDS_ENV    = "env"
	RDS_ENV_CH = "环境"
)

const (
	RDSTypeNormal   = "Primary"  // 普通实例
	RDSTypeGuard    = "Guard"    // 灾备实例
	RDSTypeReadonly = "Readonly" // 只读实例
	RdsTypeTemp     = "Temp"     // 临时实例

	RdsCategoryHA         = "HighAvailability"
	RdsCategoryBasic      = "Basic"
	RdsCategoryEnterprise = "Enterprise"
	RdsCategoryCluster    = "Cluster"

	RdsRoleMaster    = "Master"       // 主节点
	RdsRoleSecMaster = "SecondMaster" // 备主
	RdsRoleSlave     = "Slave"        // 备节点
	RdsRoleLogger    = "Logger"       // 日志节点
)
