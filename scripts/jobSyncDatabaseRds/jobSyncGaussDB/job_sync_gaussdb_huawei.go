package jobSyncGaussDB

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/jobSyncDatabaseRds"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	epsModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/gaussdb/v3/model"
	iamModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
)

var huawei_product_code_gaussdb = []string{"hws.service.type.taurus"}

func (r *RdsSync) syncHuaweiGaussDBInfoAllRegion(defClient *huaweicloud.IamClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	projects, err := defClient.ListProjects(ctx)
	if err != nil {
		return nil, err
	}
	regions, err := defClient.ListRegions(ctx)
	if err != nil {
		return nil, err
	}
	projectBatch := tools.NewBatch[iamModel.ProjectResult, []*models.DatabaseInfo](ctx)
	projectBatch.Run(projects, func(ctx context.Context, project iamModel.ProjectResult) ([]*models.DatabaseInfo, error) {
		var targetRegion *iamModel.Region
		for _, region := range regions {
			if region.Id == project.Name {
				targetRegion = &region
				break
			}
		}
		if targetRegion == nil {
			r.Warnf(ctx, "unexpected regionId: %s, skip ...", project.Name)
			return nil, nil
		}
		infoList, err := r.syncHuaweiGaussDBInfoRegion(defClient, ctx, *targetRegion, project)
		r.Infof(ctx, "get %d records for all regions", len(infoList))
		return infoList, err
	})

	return tools.MergeData(projectBatch.Outs()...), projectBatch.Error()
}

func (r *RdsSync) syncHuaweiGaussDBInfoRegion(defClient client.IClient, ctx context.Context, region iamModel.Region, project iamModel.ProjectResult) (dabaseInfo []*models.DatabaseInfo, err error) {
	defer func() {
		// 获取到非预期的region时会发生panic
		if panicError := recover(); panicError != nil {
			r.Warnf(ctx, "Panic occurred: %v", panicError)
			err = r.handleHuaweiProjectPanic(panicError, ctx, region)
		}
	}()

	rdsClient := hybrid.AccountManager().HuaweiGaussDBClient(ctx, defClient.Name(), defClient.Tag(), region.Id, project.Id)
	if rdsClient == nil {
		return nil, nil
	}
	rsp, err := rdsClient.ListGaussMySqlInstancesUnifyStatus()
	if err != nil {
		if strings.Contains(err.Error(), "\"status_code\":401") {
			return nil, nil
		}
		if strings.Contains(err.Error(), "\"status_code\":403") {
			return nil, nil
		}
		if strings.Contains(err.Error(), "Timeout") {
			return nil, nil
		}
		return nil, err
	}
	dabaseInfo = make([]*models.DatabaseInfo, len(*rsp.Instances))
	for _, data := range *rsp.Instances {
		epsClient := hybrid.AccountManager().HuaweiEpsClient(ctx, defClient.Name(), defClient.Tag(), defClient.Region())
		d := r.tranceHuaweiGaussDB2RdsInfo(ctx, rdsClient, epsClient, &data)
		if d != nil {
			dabaseInfo = append(dabaseInfo, d)
		}
	}
	return dabaseInfo, nil
}

func (r *RdsSync) tranceHuaweiGaussDB2RdsInfo(
	ctx context.Context,
	gaussDBClient *huaweicloud.GaussDBClient,
	epsClient *huaweicloud.EpsClient,
	gaussData *model.MysqlInstanceListInfoUnifyStatus,
) *models.DatabaseInfo {

	_, scode, project := bizutils.ParseHuaweiEnvProject(ctx, gaussData.Name, pointer.String(gaussData.EnterpriseProjectId), epsClient, r)
	creationTime, _ := time.Parse(bizutils.HuaweiEcsTimeFormat, pointer.String(gaussData.Created))
	// get host from detail
	var host string
	detail, err := gaussDBClient.ListGaussMySqlInstanceDetailInfoUnifyStatus(gaussData.Id)
	if err != nil {
		return nil
	}
	details := *detail.Instances
	if len(details) > 0 {
		dnsNames := *details[0].PrivateDnsNames
		if len(dnsNames) > 0 {
			host = dnsNames[0]
		}

		// handle endpoint
		err = handleGaussDBEndpoint(gaussData.Id, *details[0].Nodes)
		if err != nil {
			return nil
		}
		// handle node
		err = handleGaussDBNode(gaussData.Id, *details[0].Nodes)
		if err != nil {
			return nil
		}
	}
	if strings.TrimSpace(host) == "" {
		for _, ip := range *gaussData.PrivateIps {
			host = ip
			break
		}
	}

	// get env from tag
	var env string
	tags, err := gaussDBClient.ListInstanceTags(gaussData.Id)
	if err != nil {
		return nil
	}

	if len(*tags.Tags) > 0 {
	outerLoop:
		for _, tag := range *tags.Tags {
			switch strings.ToLower(strings.TrimSpace(tag.Key)) {
			case jobSyncDatabaseRds.RDS_ENV, jobSyncDatabaseRds.RDS_ENV_CH:
				env = tag.Value
				break outerLoop
			}
		}
	}

	port, _ := strconv.Atoi(pointer.String(gaussData.Port))
	cpus, _ := strconv.Atoi(gaussData.FlavorInfo.Vcpus)
	ram, _ := strconv.Atoi(gaussData.FlavorInfo.Ram)
	diskSize, _ := strconv.Atoi(gaussData.Volume.Size)
	contextInfo := map[string]any{
		"instance": gaussData,
		"detail":   detail,
	}
	cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(gaussDBClient.Vendor()), gaussDBClient.Identifier(), gaussData.Id, huawei_product_code_gaussdb)
	return &models.DatabaseInfo{
		Model:         r.Model(ctx),
		Vendor:        gaussDBClient.Vendor(),
		AccountName:   gaussDBClient.Name(),
		InstanceId:    gaussData.Id,
		InstanceName:  gaussData.Name,
		InstanceType:  pointer.String(gaussData.Type),
		InstanceRole:  jobSyncDatabaseRds.RdsRoleMaster,
		Category:      pointer.String(gaussData.Type),
		ResourceGroup: pointer.String(gaussData.EnterpriseProjectId),
		Status:        pointer.String(gaussData.Status),
		ClassCode:     pointer.String(gaussData.FlavorRef),
		ChargeType:    gaussData.ChargeInfo.ChargeMode.Value(),
		CreationTime:  timeutil.ZeroTime(creationTime),
		Host:          host,
		Port:          port,
		EngineType:    gaussData.Datastore.Type,
		EngineVersion: gaussData.Datastore.Version,
		Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
		Project:       project,
		Env:           env,
		Cpu:           cpus,
		Memory:        uint64(ram) * 1024,
		DiskSize:      float64(diskSize) * 1024,
		DiskType:      gaussData.Volume.Type,
		VpcId:         pointer.String(gaussData.VpcId),
		SubnetId:      pointer.String(gaussData.SubnetId),
		Region:        pointer.String(gaussData.Region),
		Content:       utils.JsonString(contextInfo),
		AggregatedId:  cmdb.AggregatedID,
	}
}

func (r *RdsSync) GetHuaweiEnterpriseProject(client *huaweicloud.EpsClient, ctx context.Context, projectId string) *epsModel.EpDetail {
	if client == nil {
		return nil
	}
	return bizutils.LoadFromMap[string, *epsModel.EpDetail](r.mtx, r.ep, projectId, func() (*epsModel.EpDetail, error) {
		return client.ShowEnterpriseProject(ctx, projectId)
	})
}

func (r *RdsSync) handleHuaweiProjectPanic(panicError any, ctx context.Context, region iamModel.Region) error {
	var msg string
	switch pe := panicError.(type) {
	case error:
		msg = pe.Error()
	case string:
		msg = pe
	}

	if strings.Contains(msg, "not in the following supported regions") {
		r.Logger().Warnf(ctx, "%s getHuaweiRegionGaussDB: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, "unexpected regionId") {
		r.Logger().Warnf(ctx, "%s getHuaweiRegionGaussDB: %s", region.Id, msg)
	} else if strings.HasPrefix(msg, "failed to get project id, No project id found") {
		r.Logger().Warnf(ctx, "%s getHuaweiRegionGaussDB: %s", region.Id, msg)
	} else {
		return errors.New(msg)
	}
	return nil
}

func handleGaussDBNode(instanceId string, nodes []model.MysqlInstanceNodeInfo) error {
	if len(nodes) == 0 {
		return nil
	}
	data := make([]*models.DatabaseNode, 0, len(nodes))
	for _, node := range nodes {
		cpu, _ := strconv.Atoi(*node.Vcpus)
		mem, _ := strconv.Atoi(*node.Ram)
		data = append(data, &models.DatabaseNode{
			InstanceId:      instanceId,
			Cpu:             cpu,
			Memory:          mem,
			NodeId:          node.Id,
			NodeClass:       pointer.Value(node.FlavorRef),
			NodeDescription: "",
			NodeRole:        pointer.Value(node.Type),
			NodeStatus:      pointer.Value(node.Status),
			Zone:            pointer.Value(node.RegionCode),
			CreateTime:      timeutil.ZeroTime(time.Now()),
			UpdateTime:      timeutil.ZeroTime(time.Now()),
		})
	}
	if err := bizutils.CreateOrUpdateNode(context.Background(), data...); err != nil {
		return err
	}
	return nil
}

func handleGaussDBEndpoint(instanceId string, endpoints []model.MysqlInstanceNodeInfo) error {
	if len(endpoints) == 0 {
		return nil
	}
	data := make([]*models.DatabaseEndpoint, 0, len(endpoints))
	for _, endpoint := range endpoints {
		if len(pointer.Value(endpoint.PrivateReadIps)) == 0 {
			continue
		}
		for _, ip := range pointer.Value(endpoint.PrivateReadIps) {
			data = append(data, &models.DatabaseEndpoint{
				InstanceId:   instanceId,
				EndpointType: pointer.Value(endpoint.Type),
				Host:         ip,
				Port:         int(pointer.Int32(endpoint.Port)),
				Description:  "",
				CreateTime:   timeutil.ZeroTime(time.Now()),
				UpdateTime:   timeutil.ZeroTime(time.Now()),
				NodeId:       endpoint.Id,
			})
		}
	}
	if err := bizutils.CreateOrUpdateEndpoint(context.Background(), data...); err != nil {
		return err
	}
	return nil
}
