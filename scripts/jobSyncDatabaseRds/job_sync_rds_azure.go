package jobSyncDatabaseRds

import (
	"context"
	"strings"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v2"

	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func (r *RdsSync) syncAzureRdsInfo(client *azure.MysqlClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	sqlServerClient := hybrid.AccountManager().AzureSqlServerClient(ctx, client.Name(), client.Tag(), client.Region(), client.TenantId(), client.Identifier())
	flexibleMysqlClient := hybrid.AccountManager().AzureMysqlFlexibleClient(ctx, client.Name(), client.Tag(), client.Region(), client.TenantId(), client.Identifier())
	//sqlDatabaseClient := hybrid.AccountManager().AzureSQLDatabaseClient(ctx, client.Name(), client.Tag(), client.Region(), client.TenantId(), client.Identifier())
	//mySQLDabaseClient := hybrid.AccountManager().AzureMySQLDatabaseClient(ctx, client.Name(), client.Tag(), client.Region(), client.TenantId(), client.Identifier())
	privateEndpointsClient := hybrid.AccountManager().AzurePrivateEndpointsClient(ctx, client.Name(), client.Tag(), client.Region(), client.TenantId(), client.Identifier())

	mysqlInfos, mysqlErr := r.syncAzureMySqlInfo(flexibleMysqlClient, ctx)
	if mysqlErr != nil {
		if strings.Contains(mysqlErr.Error(), "not be found") {
			return nil, nil
		}
		r.Errorf(ctx, "syncAzureMySqlInfo error: %v", mysqlErr)
	}

	sqlServerInfos, sqlServerErr := r.syncAzureSqlserverInfo(sqlServerClient, privateEndpointsClient, ctx)
	if sqlServerErr != nil {
		r.Errorf(ctx, "syncAzureSqlserverInfo error: %v", sqlServerErr)
	}

	return tools.MergeData(append(mysqlInfos, sqlServerInfos...)), nil
}

func GetResourceGroup(id string) string {
	var groupName string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			groupName = s[n+1]
		}
	}

	return groupName
}

func GetResourceGroupAndServerName(id string) (string, string) {
	var groupName, serverName string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			groupName = s[n+1]
		}
		if k == "servers" {
			serverName = s[n+1]
		}
	}
	return groupName, serverName
}

func ParseResourceGroupAndPrivateEndpoint(id string) (string, string) {
	var groupName, privateEndpoint string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "resourceGroups" {
			groupName = s[n+1]
		}
		if k == "privateEndpoints" {
			privateEndpoint = s[n+1]
		}
	}
	return groupName, privateEndpoint
}

func ParseVpcIdAndSubnetIdFromPrivateEndpoint(e *armnetwork.PrivateEndpoint) (string, string) {
	var vpcId, subnetId string

	s := strings.Split(*e.Properties.Subnet.ID, "/")
	for n, k := range s {
		if k == "virtualNetworks" {
			vpcId = s[n+1]
		}
		if k == "subnets" {
			subnetId = s[n+1]
		}
	}
	return vpcId, subnetId
}
