package jobSyncDatabaseRds

import (
	"context"
	"strings"

	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/mysql/armmysqlflexibleservers"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"git.haier.net/devops/ops-golang-common/utils/pointer"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var azure_product_code_mysql = []string{"microsoft.dbformysqlflexibleservers"}

func (r *RdsSync) syncAzureMySqlInfo(client *azure.MysqlFlexibleClient,
	ctx context.Context) ([]*models.DatabaseInfo, error) {
	mysqlServers, err := client.NewListPager(ctx)
	if err != nil {
		if strings.Contains(err.Error(), "SubscriptionNotFound") {
			return nil, nil
		}
		r.Errorf(ctx, "syncAzureMySqlInfo NewListPager error: %v", err)
		return nil, err
	}

	b := tools.NewBatch[*armmysqlflexibleservers.Server, *models.DatabaseInfo](ctx)
	b.Run(mysqlServers, func(ctx context.Context, m *armmysqlflexibleservers.Server) (*models.DatabaseInfo, error) {
		var vpcId, subnetId string

		scode, env, project := ParseScodeAndEnvAndProjectFromMySQLServer(m)
		resourceGroupName := GetResourceGroup(*m.ID)

		instanceId := ParseInstanceIdFromMySQLDatabase(*m.ID)
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(client.Vendor()), client.Identifier(), instanceId, azure_product_code_mysql)
		return &models.DatabaseInfo{
			Model:         r.Model(ctx),
			Vendor:        client.Vendor(),
			AccountName:   client.Name(),
			CreationTime:  m.SystemData.CreatedAt,
			ExpiredTime:   nil,
			InstanceId:    instanceId,
			InstanceName:  pointer.String(m.Name),
			InstanceType:  pointer.String(m.Type),
			InstanceRole:  RdsRoleMaster,
			Category:      RdsCategoryCluster,
			Status:        parseServerState(*m.Properties.State),
			ClassCode:     pointer.String(m.SKU.Name),
			Host:          pointer.String(m.Properties.FullyQualifiedDomainName),
			Port:          3306,
			EngineType:    bizutils.DBTypeMySQL,
			EngineVersion: string(*m.Properties.Version),
			DiskSize:      float64(*m.Properties.Storage.StorageSizeGB),
			Region:        *m.Location,
			Zone:          *m.Location,
			VpcId:         vpcId,
			SubnetId:      subnetId,
			Project:       project,
			Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
			Env:           env,
			ResourceGroup: resourceGroupName,
			Content:       utils.JsonString(m),
			AggregatedId:  cmdb.AggregatedID,
		}, nil

	})

	return tools.MergeData(b.Outs()), b.Error()
}

func parseServerState(state armmysqlflexibleservers.ServerState) string {
	switch state {
	case armmysqlflexibleservers.ServerStateDisabled:
		return "Disabled"
	case armmysqlflexibleservers.ServerStateDropping:
		return "Dropping"
	case armmysqlflexibleservers.ServerStateReady:
		return "Ready"
	case armmysqlflexibleservers.ServerStateStarting:
		return "Starting"
	case armmysqlflexibleservers.ServerStateStopped:
		return "Stopped"
	case armmysqlflexibleservers.ServerStateStopping:
		return "Stopping"
	case armmysqlflexibleservers.ServerStateUpdating:
		return "Updating"
	default:
		return "Unknown"
	}
}
func ParseScodeAndEnvAndProjectFromMySQLServer(s *armmysqlflexibleservers.Server) (scode string, env string, project string) {
	if s.Tags == nil {
		return
	}
	scode = pointer.String(s.Tags["SCode"])
	project = pointer.String(s.Tags["Project"])
	env = pointer.String(s.Tags[RDS_ENV])
	if env == "" {
		env = pointer.String(s.Tags[RDS_ENV_CH])
	}

	return
}

func ParseInstanceIdFromMySQLDatabase(id string) string {
	var databaseName string
	s := strings.Split(id, "/")
	for n, k := range s {
		if k == "flexibleServers" {
			databaseName = s[n+1]
		}
	}
	return databaseName

}
