package jobSyncDatabaseRds

import (
	"context"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	v20170312 "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/postgres/v20170312"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
)

var tencent_product_code_postgresql = []string{"p_postgresql"}

func (r *RdsSync) syncTencentRegionPgInfo(cli *tencentcloud.PostgresqlClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	pgs, err := cli.DescribeDBInstancesOfAll(ctx)
	if err != nil {
		return nil, err
	}
	rdsInfos := make([]*models.DatabaseInfo, len(pgs))
	for i, pg := range pgs {
		ip, port := getTencentPgInstanceIp(pg)
		env, scode, project := tryParseTencentRdsPgEnvProjects(pg)
		creationTime, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.String(pg.CreateTime))
		expiredTime, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.String(pg.ExpireTime))
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(pg.DBInstanceId), tencent_product_code_postgresql)
		rdsInfos[i] = &models.DatabaseInfo{
			Model:             r.Model(ctx),
			Vendor:            cli.Vendor(),
			AccountName:       cli.Name(),
			InstanceId:        pointer.String(pg.DBInstanceId),
			InstanceName:      pointer.String(pg.DBInstanceName),
			InstanceType:      getTencentPgInstanceType(pg),
			InstanceRole:      getTencentPgInstanceRole(pg),
			PrimaryInstanceId: pointer.String(pg.MasterDBInstanceId),
			ResourceGroup:     scode,
			Category:          getTencentPgInstanceCategory(pg),
			Status:            pointer.String(pg.DBInstanceStatus),
			ClassCode:         pointer.String(pg.DBInstanceClass),
			ChargeType:        pointer.String(pg.PayType),
			CreationTime:      timeutil.ZeroTime(creationTime),
			ExpiredTime:       timeutil.ZeroTime(expiredTime),
			Host:              ip,
			Port:              port,
			EngineType:        bizutils.DBTypePostgres,
			EngineVersion:     pointer.String(pg.DBVersion),
			Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
			Project:           project,
			Env:               env,
			Cpu:               int(*pg.DBInstanceCpu),
			Memory:            *pg.DBInstanceMemory * 1024,
			DiskSize:          float64(*pg.DBInstanceStorage * 1024),
			VpcId:             pointer.String(pg.VpcId),
			SubnetId:          pointer.String(pg.SubnetId),
			Region:            cli.Region(),
			Zone:              pointer.String(pg.Zone),
			Content:           utils.JsonString(pg),
			AggregatedId:      cmdb.AggregatedID,
		}
	}
	return rdsInfos, nil
}

func getTencentPgInstanceCategory(pg *v20170312.DBInstance) string {
	switch pointer.String(pg.DBInstanceVersion) {
	case "standard":
		return "HighAbility"
	}
	return ""
}

func getTencentPgInstanceIp(pg *v20170312.DBInstance) (string, int) {
	for _, net := range pg.DBInstanceNetInfo {
		if *net.NetType == "public" && net.Ip != nil && len(*net.Ip) > 0 {
			return *net.Ip, int(*net.Port)
		}
	}

	for _, net := range pg.DBInstanceNetInfo {
		if *net.NetType == "private" {
			return *net.Ip, int(*net.Port)
		}
	}
	return "", 0
}

func getTencentPgInstanceType(rds *v20170312.DBInstance) string {
	switch pointer.String(rds.DBInstanceType) {
	case "1":
		return RDSTypeNormal
	case "2":
		return RDSTypeGuard
	case "3":
		return RDSTypeReadonly
	case "4":
		return RdsTypeTemp
	default:
		return ""
	}
}

func getTencentPgInstanceRole(rds *v20170312.DBInstance) string {
	switch pointer.String(rds.DBInstanceType) {
	case "1":
		return RdsRoleMaster
	default:
		return RdsRoleSlave
	}
}

func tryParseTencentRdsPgEnvProjects(rds *v20170312.DBInstance) (env, code, project string) {
	for _, tag := range rds.TagList {
		if pointer.String(tag.TagKey) != "腾讯云标签" {
			continue
		}
		tmp := strings.Split(pointer.String(tag.TagValue), "_")
		if strings.HasPrefix(strings.ToLower(tmp[0]), "s") {
			code = tmp[0]
		}
		if len(tmp) > 1 {
			if strings.HasPrefix(strings.ToLower(tmp[0]), "s") {
				code = tmp[0]
				project = tmp[1]
			} else if strings.HasPrefix(strings.ToLower(tmp[1]), "s") {
				code = tmp[1]
				project = tmp[0]
			}
		}
		break
	}

outerLoop:
	for _, tag := range rds.TagList {
		switch strings.ToLower(strings.TrimSpace(pointer.String(tag.TagKey))) {
		case RDS_ENV, RDS_ENV_CH:
			env = pointer.String(tag.TagValue)
			break outerLoop
		}
	}
	if env == "" {
		env = bizutils.TryParseEnv(pointer.String(rds.DBInstanceName))
	}
	return
}
