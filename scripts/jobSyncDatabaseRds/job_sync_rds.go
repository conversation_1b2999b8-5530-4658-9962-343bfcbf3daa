package jobSyncDatabaseRds

import (
	"context"
	"sync"

	"git.haier.net/devops/ops-golang-common/sdk/aliyun"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/sdk/huaweicloud"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"

	"git.haier.net/devops/ops-golang-common/hbc"
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"git.haier.net/devops/ops-golang-common/sdk/client"
	"github.com/LPX3F8/orderedmap"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"

	"git.haier.net/devops/hcms-task-center/biz/actfilter"
	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/tools"
	"git.haier.net/devops/hcms-task-center/scripts/base"
)

const region_moscow = "ru-moscow-1"

func New() *RdsSync {
	c := config.Global()
	return &RdsSync{
		mtx: new(sync.Mutex),
		rg:  orderedmap.New[string, *resourcemanager.ResourceGroup](),
		ep:  orderedmap.New[string, *eps.EpDetail](),
		JobBase: base.NewJobBase(
			"RDS_SYNC",
			"同步RDS资源信息",
			base.NewSchedule(
				base.WithHour(0),
				base.WithRandMin(),
				base.WithSec(0),
			),
			taskmodels.TaskCategoryResource,
			c.GetStore(c.DefaultStoreName),
		),
	}
}

type RdsSync struct {
	rg  *orderedmap.OrderedMap[string, *resourcemanager.ResourceGroup]
	ep  *orderedmap.OrderedMap[string, *eps.EpDetail]
	mtx *sync.Mutex
	*base.JobBase
}

func (r *RdsSync) Name() string {
	return base.TaskCloudSyncRds
}

func (r *RdsSync) Run(ctx context.Context) (map[string]any, error) {
	// 获取云列表
	vendors, err := hybrid.GetVendors(r.Model(ctx), hbc.GoogleCloud)
	if err != nil {
		return nil, err
	}
	// 获取默认环境的云账号
	defaultClients, err := base.GetDefaultIClients(ctx, vendors,
		hbc.RDS, []string{bizutils.PurposeAdmin},
		actfilter.SetCustomProduct(hbc.AWS, hbc.ECS),                      // aws默认使用ecs client，获取地区
		actfilter.SetCustomProduct(hbc.HuaweiCloud, hbc.IAM),              // 华为云默认client使用 IAM client
		actfilter.SetCustomProduct(hbc.TencentCloud, hbc.ECS),             // 腾讯云默认client使用 Ecs client
		actfilter.SetCustomPurpose(hbc.AWS, bizutils.PurposeAccountCheck), // AWS复用 ACCOUNT_CHECK 的账号
		actfilter.SetCustomProduct(hbc.Azure, hbc.MySQL),
		actfilter.SetIgnoreVendor(hbc.Private),
	)
	if err != nil {
		return nil, err
	}
	// 增加华为云莫斯科地区client
	for _, c := range defaultClients {
		if c.Name() == "hr690g" && c.Vendor() == hbc.HuaweiCloud {
			defClient := hybrid.AccountManager().HuaweiIamClient(ctx, c.Name(), c.Tag(), region_moscow)
			defaultClients = append(defaultClients, defClient)
			break
		}
	}
	// 获取云资源信息
	b := tools.NewBatch[client.IClient, []*models.DatabaseInfo](ctx)
	b.Run(defaultClients, func(ctx context.Context, input client.IClient) ([]*models.DatabaseInfo, error) {
		var result []*models.DatabaseInfo
		var syncError error
		switch defClient := input.(type) {
		case *aliyun.RDSClient:
			result, syncError = r.syncAliyunRdsInfo(defClient, ctx)
		case *huaweicloud.IamClient:
			result, syncError = r.syncHuaweiRdsInfo(defClient, ctx)
		case *tencentcloud.EcsClient:
			result, syncError = r.syncTencentRdsInfo(defClient, ctx)
		case *aws.Ec2Client:
			result, syncError = r.syncAwsRdsInfo(defClient, ctx)
		case *azure.MysqlClient:
			result, syncError = r.syncAzureRdsInfo(defClient, ctx)
		}
		return result, bizutils.WarpClientError(input, syncError)
	})
	if err := b.Error(); err != nil {
		r.Errorf(ctx, "sync rds failed, cause: %s", err)
		return nil, err
	}

	// 将信息存入数据库
	return nil, bizutils.CreateOrUpdateRds(ctx, tools.MergeData(b.Outs()...)...)
}
