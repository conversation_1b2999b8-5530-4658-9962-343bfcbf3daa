package jobSyncDatabaseRds

import (
	"context"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/LPX3F8/orderedmap"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"

	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func (r *RdsSync) syncTencentRdsInfo(defClient *tencentcloud.EcsClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	regions, err := defClient.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionsIds := orderedmap.New[string, string]()
	for _, region := range regions {
		regionsIds.Store(pointer.String(region.Region), pointer.String(region.Region))
	}
	b := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, region string) ([]*models.DatabaseInfo, error) {
		mysqlCli := hybrid.AccountManager().TencentMySQLClient(ctx, defClient.Name(), defClient.Tag(), region)
		mysqlInfos, err := r.syncTencentRegionMysqlInfo(mysqlCli, ctx)
		if err != nil {
			te, ok := err.(*errors.TencentCloudSDKError)
			if !ok || te.Code != "UnsupportedRegion" {
				return nil, err
			}
		}
		pgCli := hybrid.AccountManager().TencentPostgresqlClient(ctx, defClient.Name(), defClient.Tag(), region)
		pgInfos, err := r.syncTencentRegionPgInfo(pgCli, ctx)
		if err != nil {
			te, ok := err.(*errors.TencentCloudSDKError)
			if !ok || te.Code != "UnsupportedRegion" {
				return nil, err
			}
		}
		return tools.MergeData(append(mysqlInfos, pgInfos...)), nil
	})
	return tools.MergeData(b.Outs()...), b.Error()
}
