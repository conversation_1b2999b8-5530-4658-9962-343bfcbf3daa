package jobSyncDatabaseRds

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/tencentcloud"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cdb/v20170320"
	mysql "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cdb/v20170320"


	"git.haier.net/devops/hcms-task-center/biz/bizutils"
)

var tencent_product_code_rds = []string{"p_cdb"}

func (r *RdsSync) syncTencentRegionMysqlInfo(cli *tencentcloud.MysqlClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	infos, err := bizutils.WithRetry(
		3,
		func() ([]*mysql.InstanceInfo, error) {
			return cli.DescribeInstancesOfAll(ctx)
		},
	)

	if err != nil {
		return nil, bizutils.WarpClientError(cli, err)
	}

	rdsInfos := make([]*models.DatabaseInfo, len(infos))
	for i, rds := range infos {
		env, scode, project := tryParseTencentRdsEnvProjects(rds)
		creationTime, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.String(rds.CreateTime))
		expiredTime, _ := time.Parse(bizutils.NormalDatetimeFmt, pointer.String(rds.DeadlineTime))
		host := pointer.String(rds.Vip)
		if *rds.WanStatus == 1 && rds.WanDomain != nil && len(*rds.WanDomain) > 0 {
			host = pointer.String(rds.WanDomain)
		}
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.String(rds.InstanceId), tencent_product_code_rds)
		rdsInfos[i] = &models.DatabaseInfo{
			Model:             r.Model(ctx),
			Vendor:            cli.Vendor(),
			AccountName:       cli.Name(),
			InstanceId:        pointer.String(rds.InstanceId),
			InstanceName:      pointer.String(rds.InstanceName),
			InstanceType:      getTencentInstanceType(rds),
			InstanceRole:      getTencentInstanceRole(rds),
			Category:          getTencentInstanceCategory(rds),
			PrimaryInstanceId: getTencentMasterInstance(rds),
			Status:            getTencentRdsStatus(rds),
			ClassCode:         pointer.String(rds.DeviceClass),
			ChargeType:        getTencentRdsPayType(rds),
			CreationTime:      timeutil.ZeroTime(creationTime),
			ExpiredTime:       timeutil.ZeroTime(expiredTime),
			ResourceGroup:     scode,
			Host:              host,
			Port:              int(pointer.Int64(rds.Vport)),
			EngineType:        bizutils.DBTypeMySQL,
			EngineVersion:     pointer.String(rds.EngineVersion),
			HostInsId:         pointer.String(rds.PhysicalId),
			Scode:             bizutils.SwapSCode(scode, cmdb.Scode),
			Project:           project,
			Env:               env,
			Cpu:               int(pointer.Int64(rds.Cpu)),
			Memory:            uint64(pointer.Int64(rds.Memory)),
			DiskType:          pointer.String(rds.DiskType),
			VpcId:             strconv.FormatInt(pointer.Int64(rds.VpcId), 10),
			SubnetId:          strconv.FormatInt(pointer.Int64(rds.SubnetId), 10),
			Region:            cli.Region(),
			Zone:              pointer.String(rds.Zone),
			Content:           utils.JsonString(rds),
			PrivateIp:         pointer.String(rds.Vip),
			AggregatedId:      cmdb.AggregatedID,
		}
	}
	return rdsInfos, nil
}

func getTencentRdsPayType(rds *v20170320.InstanceInfo) string {
	if *rds.PayType == 0 {
		return "PrePay"
	}
	return "PostPay"
}

func getTencentRdsStatus(rds *v20170320.InstanceInfo) string {
	switch *rds.Status {
	case 0:
		return "Creating"
	case 1:
		return "Running"
	case 4:
		return "Isolating"
	case 5:
		return "Isolated"
	}
	return ""
}

func getTencentMasterInstance(rds *v20170320.InstanceInfo) string {
	if rds.MasterInfo == nil {
		return ""
	}
	return pointer.String(rds.MasterInfo.InstanceId)
}

func getTencentInstanceCategory(rds *v20170320.InstanceInfo) string {
	if pointer.Int64(rds.InstanceNodes) == 1 {
		return RdsCategoryBasic
	}
	if pointer.Int64(rds.InstanceNodes) == 2 {
		return RdsCategoryHA
	}
	return RdsCategoryEnterprise
}

func getTencentInstanceType(rds *v20170320.InstanceInfo) string {
	switch pointer.Int64(rds.InstanceType) {
	case 1:
		return RDSTypeNormal
	case 2:
		return RDSTypeGuard
	case 3:
		return RDSTypeReadonly
	default:
		return ""
	}
}

func getTencentInstanceRole(rds *v20170320.InstanceInfo) string {
	switch pointer.Int64(rds.InstanceType) {
	case 1:
		return RdsRoleMaster
	default:
		return RdsRoleSlave
	}
}

func tryParseTencentRdsEnvProjects(rds *v20170320.InstanceInfo) (env, code, project string) {
	for _, tag := range rds.TagList {
		if pointer.String(tag.TagKey) != "腾讯云标签" {
			continue
		}
		tmp := strings.Split(pointer.String(tag.TagValue), "_")
		if strings.HasPrefix(strings.ToLower(tmp[0]), "s") {
			code = tmp[0]
		}
		if len(tmp) > 1 {
			if strings.HasPrefix(strings.ToLower(tmp[0]), "s") {
				code = tmp[0]
				project = tmp[1]
			} else if strings.HasPrefix(strings.ToLower(tmp[1]), "s") {
				code = tmp[1]
				project = tmp[0]
			}
		}
		break
	}

	env = bizutils.TryParseEnv(pointer.String(rds.InstanceName))
	return
}
