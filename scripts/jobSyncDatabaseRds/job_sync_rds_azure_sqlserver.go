package jobSyncDatabaseRds

import (
	"context"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/azure"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/sql/armsql"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var azure_product_code_sqlServer = []string{"microsoft.networkprivateendpoints", "microsoft.sqlservers", "cmhvacdbdatabases"}

func (r *RdsSync) syncAzureSqlserverInfo(client *azure.SqlServerClient,
	peClient *azure.PrivateEndpointsClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	sqlServers, err := client.NewListPager(ctx)
	if err != nil {
		r.<PERSON>(ctx, "syncAzureSqlserverInfo NewListPager error: %v", err)
		return nil, err
	}

	b := tools.NewBatch[*armsql.Server, *models.DatabaseInfo](ctx)
	b.Run(sqlServers, func(ctx context.Context, s *armsql.Server) (*models.DatabaseInfo, error) {
		var vpcId, subnetId string

		connections := s.Properties.PrivateEndpointConnections
		if len(connections) > 0 {
			id := *connections[0].Properties.PrivateEndpoint.ID
			groupName, privateEndpoint := ParseResourceGroupAndPrivateEndpoint(id)

			pe, peErr := peClient.Get(ctx, groupName, privateEndpoint)
			if peErr != nil {
				r.Errorf(ctx, "syncAzureSqlserverInfo  PrivateEndpoint error: %v", err)
				return nil, err
			}

			vpcId, subnetId = ParseVpcIdAndSubnetIdFromPrivateEndpoint(pe)
		}

		scode, env, project := ParseScodeAndEnvAndProjectFromSQLServer(s)
		resourceGroupName, instanceId := GetResourceGroupAndServerName(*s.ID)
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(client.Vendor()), client.Identifier(), instanceId, azure_product_code_sqlServer)
		return &models.DatabaseInfo{
			Model:         r.Model(ctx),
			Vendor:        client.Vendor(),
			AccountName:   client.Name(),
			InstanceId:    instanceId,
			CreationTime:  nil,
			ExpiredTime:   nil,
			InstanceName:  pointer.String(s.Name),
			InstanceType:  pointer.String(s.Type),
			InstanceRole:  RdsRoleMaster,
			Category:      RdsCategoryCluster,
			Status:        pointer.String(s.Properties.State),
			ClassCode:     pointer.String(s.Kind),
			Host:          pointer.String(s.Properties.FullyQualifiedDomainName),
			Port:          1433,
			VpcId:         vpcId,
			SubnetId:      subnetId,
			EngineType:    bizutils.DBTypeSQLServer,
			EngineVersion: *s.Properties.Version,
			Region:        *s.Location,
			Zone:          *s.Location,
			Project:       project,
			Scode:         bizutils.SwapSCode(scode, cmdb.Scode),
			Env:           env,
			ResourceGroup: resourceGroupName,
			Content:       utils.JsonString(s),
			AggregatedId:  cmdb.AggregatedID,
		}, nil
	})

	return tools.MergeData(b.Outs()), b.Error()
}

func ParseScodeAndEnvAndProjectFromSQLServer(s *armsql.Server) (scode string, env string, project string) {
	if s.Tags == nil {
		return
	}
	scode = pointer.String(s.Tags["SCode"])
	project = pointer.String(s.Tags["Project"])
	env = pointer.String(s.Tags[RDS_ENV])
	if env == "" {
		env = pointer.String(s.Tags[RDS_ENV_CH])
	}

	return
}
