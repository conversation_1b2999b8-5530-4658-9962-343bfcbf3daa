package jobSyncDatabaseRds

import (
	"context"
	"strings"

	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/sdk/aws"
	"git.haier.net/devops/ops-golang-common/utils"
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"git.haier.net/devops/ops-golang-common/utils/timeutil"
	"github.com/LPX3F8/orderedmap"
	"github.com/aws/aws-sdk-go-v2/service/rds/types"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"

	"git.haier.net/devops/hcms-task-center/biz/hybrid"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

var aws_product_code_rds = []string{"AmazonRDS"}

func (r *RdsSync) syncAwsRdsInfo(defClient *aws.Ec2Client, ctx context.Context) ([]*models.DatabaseInfo, error) {
	regions, err := defClient.DescribeRegions(ctx)
	if err != nil {
		return nil, err
	}
	regionsIds := orderedmap.New[string, string]()
	for _, region := range regions {
		regionsIds.Store(pointer.Value(region.RegionName), pointer.Value(region.RegionName))
	}

	b := tools.NewBatch[string, []*models.DatabaseInfo](ctx)
	b.Run(regionsIds.Slice(), func(ctx context.Context, region string) ([]*models.DatabaseInfo, error) {
		cli := hybrid.AccountManager().AwsRdsClient(ctx, defClient.Name(), defClient.Tag(), region)
		return r.syncAwsRegionRdsInfo(cli, ctx)
	})

	return tools.MergeData(b.Outs()...), b.Error()
}
func (r *RdsSync) syncAwsRegionRdsInfo(cli *aws.RdsClient, ctx context.Context) ([]*models.DatabaseInfo, error) {
	rdsList, err := cli.DescribeDBInstancesOfAll(ctx)
	if err != nil {
		if strings.Contains(err.Error(), bizutils.TimeoutErr) {
			return nil, nil
		}
		if strings.Contains(err.Error(), "no such host") {
			return nil, nil
		}
		return nil, err
	}

	rdsInfos := make([]*models.DatabaseInfo, len(rdsList))
	for i, rds := range rdsList {
		cpu, mem := cli.GetClassCpuMemory(pointer.Value(rds.DBInstanceClass))
		primaryInstanceId := ""
		if instanceName := pointer.Value(rds.ReadReplicaSourceDBInstanceIdentifier); instanceName != "" {
			rdsInfo, err := bizutils.GetRdsDetailByInstanceName(ctx, instanceName)
			if err != nil {
				return nil, err
			}
			if rdsInfo != nil {
				primaryInstanceId = rdsInfo.InstanceId
			}
		}
		cmdb := bizutils.GetCmdbProductOverviewByProductInfo(ctx, string(cli.Vendor()), cli.Identifier(), pointer.Value(rds.DBInstanceIdentifier), aws_product_code_rds)
		rdsInfos[i] = &models.DatabaseInfo{
			Model:             r.Model(ctx),
			Vendor:            cli.Vendor(),
			AccountName:       cli.Name(),
			InstanceId:        pointer.Value(rds.DBInstanceIdentifier),
			InstanceName:      pointer.Value(rds.DBInstanceArn),
			InstanceType:      getAwsRdsType(rds),
			InstanceRole:      getInstanceRole(rds),
			Category:          getCategory(rds),
			PrimaryInstanceId: primaryInstanceId,
			Status:            pointer.Value(rds.DBInstanceStatus),
			ResourceGroup:     getTagValue(rds, "Scode"),
			ClassCode:         pointer.Value(rds.DBInstanceClass),
			CreationTime:      timeutil.ZeroTimePtr(rds.InstanceCreateTime),
			ExpiredTime:       nil,
			Host:              pointer.Value(rds.Endpoint.Address),
			Port:              int(pointer.Int32(rds.Endpoint.Port)),
			EngineType:        pointer.Value(rds.Engine),
			EngineVersion:     pointer.Value(rds.EngineVersion),
			Scode:             bizutils.SwapSCode(getTagValue(rds, "Scode"), cmdb.Scode),
			Project:           getTagValue(rds, "Project"),
			Env:               getTagValue(rds, RDS_ENV, RDS_ENV_CH),
			Cpu:               cpu,
			Memory:            mem,
			DiskSize:          float64(pointer.Int32(rds.AllocatedStorage) * 1024),
			DiskType:          pointer.Value(rds.StorageType),
			VpcId:             pointer.Value(rds.DBSubnetGroup.VpcId),
			SubnetId:          pointer.Value(rds.DBSubnetGroup.DBSubnetGroupName),
			Region:            cli.Region(),
			Zone:              pointer.Value(rds.AvailabilityZone),
			Description:       pointer.Value(rds.DBName),
			Content:           utils.JsonString(rds),
			AggregatedId:      cmdb.AggregatedID,
		}
	}

	return rdsInfos, nil
}

func getAwsRdsType(rds types.DBInstance) string {
	switch rds.ReplicaMode {
	case types.ReplicaModeOpenReadOnly, types.ReplicaModeMounted:
		return RDSTypeReadonly
	default:
		return RDSTypeNormal
	}
}

func getCategory(rds types.DBInstance) string {
	if *rds.MultiAZ {
		return RdsCategoryHA
	}
	return RdsCategoryBasic
}

func getInstanceRole(rds types.DBInstance) string {
	if rds.ReadReplicaSourceDBInstanceIdentifier != nil {
		return RdsRoleSlave
	}
	return RdsRoleMaster
}

func getTagValue(rds types.DBInstance, keys ...string) string {
	for _, tag := range rds.TagList {
		for _, key := range keys {
			if strings.EqualFold(pointer.Value(tag.Key), key) {
				return pointer.Value(tag.Value)
			}
		}
	}
	return ""
}
