package taskcenter

import (
	"context"
	"errors"
	"fmt"
	"path"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/utils"
	myCtx "git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/LPX3F8/orderedmap"
	"github.com/gofiber/fiber/v2"
	"github.com/hashicorp/raft"
	"github.com/imroc/req/v3"

	"git.haier.net/devops/hcms-task-center/core/gofiber/engine"
	"git.haier.net/devops/hcms-task-center/core/gofiber/request"
	"git.haier.net/devops/hcms-task-center/core/gofiber/response"
	"git.haier.net/devops/hcms-task-center/core/raftserver"
)

const (
	apiRaftClusterAction     = "/raft/cluster"
	apiRaftClusterNodeAction = "/raft/cluster/node"
)

var httpClient = req.NewClient().SetTimeout(time.Second)

func (tc *TaskCenter) initTaskCenterRouter() *TaskCenter {
	tc.httpserver.Group("", func(raftApi engine.FiberRouter) {
		raftApi.Register(fiber.MethodGet, apiRaftClusterAction, tc.HandleClusterInfoQuery)  // 获取集群信息
		raftApi.Register(fiber.MethodPost, apiRaftClusterAction, tc.HandleAddRaftNode)      // 添加节点
		raftApi.Register(fiber.MethodDelete, apiRaftClusterAction, tc.HandleRemoveRaftNode) // 删除节点
		raftApi.Register(fiber.MethodGet, apiRaftClusterNodeAction, tc.HandleNodeInfoQuery) // 获取节点信息
	})
	return tc
}

func (tc *TaskCenter) HandleClusterInfoQuery(req *request.FiberReq) *response.FiberResp {
	// check ready
	if !tc.Ready() {
		return response.Fail(req.Ctx, response.RequestErr, "current node service not ready")
	}
	// check leader
	if !tc.Leader() {
		return tc.proxyToLeader(req)
	}

	servers := tc.raft.Server().GetConfiguration().Configuration().Servers
	leaderHttpAddr, _ := ParseRaftNodeHttpAddr(tc.raft.LeaderAddr())
	nodeMap := orderedmap.New[string, *RaftNodeInfo]()
	wg := new(sync.WaitGroup)
	wg.Add(len(servers))
	for _, server := range servers {
		go func(s raft.Server) {
			defer wg.Done()
			httpAddr, _ := ParseRaftNodeHttpAddr(string(s.Address))
			nodeInfo, _ := tc.getNodeInfo(req.Context(), httpAddr)
			nodeMap.Store(httpAddr, nodeInfo)
		}(server)
	}

	wg.Wait()
	return response.Ok(req.Ctx, RaftClusterInfo{
		Leader:         tc.raft.LeaderID(),
		LeaderRaftAddr: tc.raft.LeaderAddr(),
		LeaderHttpAddr: leaderHttpAddr,
		NodeCount:      len(servers),
		Nodes:          nodeMap.Slice(),
	})
}

func (tc *TaskCenter) HandleNodeInfoQuery(req *request.FiberReq) *response.FiberResp {
	if !tc.Ready() {
		return response.Fail(req.Ctx, response.RequestErr, "current node service not ready")
	}
	return response.Ok(req.Ctx, tc.NodeInfo(req.Context()))
}

func (tc *TaskCenter) HandleAddRaftNode(req *request.FiberReq) *response.FiberResp {
	// check ready
	if !tc.Ready() {
		return response.Fail(req.Ctx, response.RequestErr, "current node service not ready")
	}
	// check leader
	if !tc.Leader() {
		return tc.proxyToLeader(req)
	}

	// check node id
	addNodeReq := new(raftserver.NodeActionReq)
	if err := req.Ctx.BodyParser(addNodeReq); err != nil {
		return response.Error(req.Ctx, response.RequestErr, err)
	}
	if addNodeReq.NodeId == raft.ServerID(tc.raft.LeaderID()) {
		return response.Fail(req.Ctx, response.RequestErr, "can not add self")
	}

	// add node
	err := tc.raft.Server().AddVoter(addNodeReq.NodeId, addNodeReq.NodeAddr, 0, 0).Error()
	if err != nil {
		return response.Error(req.Ctx, response.RequestErr, err)
	}
	return response.Ok(req.Ctx)
}

func (tc *TaskCenter) HandleRemoveRaftNode(req *request.FiberReq) *response.FiberResp {
	// check ready
	if !tc.Ready() {
		return response.Fail(req.Ctx, response.RequestErr, "current node service not ready")
	}
	// check leader
	if !tc.Leader() {
		return tc.proxyToLeader(req)
	}

	// check node id
	nodeAction := new(raftserver.NodeActionReq)
	if err := req.Ctx.BodyParser(nodeAction); err != nil {
		return response.Error(req.Ctx, response.RequestErr, err)
	}
	if nodeAction.NodeId == raft.ServerID(tc.raft.LeaderID()) {
		return response.Fail(req.Ctx, response.RequestErr, "can not add self")
	}

	// remove node
	err := tc.raft.Server().RemoveServer(nodeAction.NodeId, 0, 0).Error()
	if err != nil {
		return response.Error(req.Ctx, response.RequestErr, err)
	}
	return response.Ok(req.Ctx)
}

func (tc *TaskCenter) getNodeInfo(ctx context.Context, serverHttpAddr string) (*RaftNodeInfo, error) {
	url := fmt.Sprintf("http://%s%s", serverHttpAddr, path.Join(apiPrefix, apiRaftClusterNodeAction))
	resp, err := httpClient.R().SetHeader("X-Request-Id", myCtx.GetTraceId(ctx)).Get(url)
	if err != nil {
		tc.logger.Errorf(ctx, "%s ==> get node info failed: %s", url, err)
		return nil, err
	}

	hrResp, _ := utils.Unmarshal[*response.BaseResponse](resp.Bytes())
	if hrResp.Code != response.Success.Code {
		tc.logger.Errorf(ctx, "%s ==> get node info failed: %s", url, hrResp.Detail)
		return nil, errors.New(fmt.Sprintf("%s, %s", hrResp.Message, hrResp.Detail))
	}

	result := new(RaftNodeInfo)
	_ = utils.Copy(hrResp.Data, result)
	return result, nil
}
