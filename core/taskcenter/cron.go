package taskcenter

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/gorhill/cronexpr"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
)

type Cron<PERSON>ob struct {
	env        string
	scheduleId int64
	lastServer string
	schedule   string
	expr       *cronexpr.Expression
	next       time.Time
	prev       time.Time
	task       ITask
	enable     bool

	// 保证修改跟读取数据的原子的
	*sync.Mutex
}

func NewJob(task ITask, env string) *CronJob {
	return &CronJob{
		env:      env,
		schedule: task.Schedule(),
		expr:     cronexpr.MustParse(task.Schedule()),
		task:     task,
		enable:   task.Enable(),
		Mutex:    new(sync.Mutex),
	}
}

func (c *CronJob) Name() string {
	return c.task.Name()
}

func (c *CronJob) setTask(t ITask) {
	c.Lock()
	defer c.Unlock()
	c.task = t
}

func (c *<PERSON><PERSON><PERSON>ob) getTask() ITask {
	return c.task
}

func (c *<PERSON><PERSON><PERSON>ob) ToSchedule(ctx context.Context) *taskmodels.TcTaskSchedule {
	c.Lock()
	defer c.Unlock()

	m := &taskmodels.TcTaskSchedule{
		Model:          NewModel(ctx),
		Env:            c.env,
		Name:           c.task.Name(),
		Schedule:       c.task.Schedule(),
		Prev:           c.prev,
		Next:           c.next,
		Enable:         c.enable,
		LastServer:     c.lastServer,
		MaxExecTimeout: c.task.Timeout().String(),
		Category:       c.task.Category(),
		Comment:        c.task.Comment(),
	}
	m.ID = c.scheduleId
	return m
}

func (c *CronJob) setExecTime(now time.Time) {
	c.Lock()
	defer c.Unlock()

	c.prev = now
	c.next = c.expr.Next(now)
}

func (c *CronJob) String() string {
	c.Lock()
	defer c.Unlock()

	return fmt.Sprintf("cronjob: %s, schedule: %s, prev: %s, next: %s", c.task.Name(), c.task.Schedule(), c.prev, c.next)
}
