package taskcenter

import (
	"sync"
)

type Named interface {
	Name() string
}

type Repository[T Named] struct {
	mtx  *sync.Mutex
	data map[string]T
}

func NewRepository[T Named]() *Repository[T] {
	return &Repository[T]{
		mtx:  new(sync.Mutex),
		data: make(map[string]T),
	}
}

func (m *Repository[T]) Register(t T) {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	if _, ok := m.data[t.Name()]; ok {
		panic("already registered: " + t.Name())
	}
	m.data[t.Name()] = t
}

func (m *Repository[T]) Get(name string) T {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	if t, ok := m.data[name]; ok {
		return t
	}
	return *new(T)
}

func (m *Repository[T]) GetAll() []T {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	data := make([]T, 0, len(m.data))
	for _, t := range m.data {
		data = append(data, t)
	}
	return data
}

func (m *Repository[T]) GetNames() []string {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	names := make([]string, 0, len(m.data))
	for name := range m.data {
		names = append(names, name)
	}
	return names
}

func (m *Repository[T]) Delete(name string) {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	delete(m.data, name)
}

func (m *Repository[T]) DeleteAll() {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	for name := range m.data {
		delete(m.data, name)
	}
}

func (m *Repository[T]) Len() int {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	return len(m.data)
}

func (m *Repository[T]) IsEmpty() bool {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	return len(m.data) == 0
}

func (m *Repository[T]) Exists(name string) bool {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	_, ok := m.data[name]
	return ok
}

func (m *Repository[T]) ForEach(f func(T)) {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	for _, t := range m.data {
		f(t)
	}
}

func (m *Repository[T]) ForEachWithBreak(f func(T) bool) {
	m.mtx.Lock()
	defer m.mtx.Unlock()

	for _, t := range m.data {
		if f(t) {
			break
		}
	}
}

func (m *Repository[T]) Lock() {
	m.mtx.Lock()
}

func (m *Repository[T]) Unlock() {
	m.mtx.Unlock()
}
