package taskcenter

import (
	"context"
	"fmt"
)

func (tc *TaskCenter) init() *TaskCenter {
	// 初始化配置
	tc.initRedisConn()
	// 初始化服务
	tc.initDependService()
	// 初始化分布式锁信息
	tc.initSyncMutex()
	// 注册路由
	tc.initTaskCenterRouter()
	// 初始化调度器
	tc.initDispatcher()
	return tc
}

func (tc *TaskCenter) initDependService() {
	tc.httpserver = buildHttpServer(tc.cnf)
	tc.raft = buildRaftNode(tc.cnf)
}

func (tc *TaskCenter) initSyncMutex() {
	tc.clusterName = fmt.Sprintf("%s:taskcenter", tc.cnf.Env)
	tc.mtx = tc.generateSyncMtx(tc.cnf)
}

func (tc *TaskCenter) initRedisConn() {
	var err error
	tc.redisConn, err = tc.cnf.RedisConn()
	if err != nil {
		tc.logger.Error(tc.ctx, "init redis conn error: ", err.Error())
		panic(err)
	}
}

func (tc *TaskCenter) initDispatcher() {
	dispatcherCtx, dispatcherCancelFunc := context.WithCancel(tc.ctx)
	tc.dispatcher.tc = tc
	tc.dispatcher.ctx = dispatcherCtx
	tc.dispatcher.cancelFun = dispatcherCancelFunc
	tc.dispatcher.initCronJobs()
	tc.dispatcher.registerHandler()
}
