package taskcenter

func (tc *TaskCenter) Leader() bool {
	return tc.raft.IsLeader()
}

func (tc *TaskCenter) Ready() bool {
	httpReady := tc.httpserver.Running()
	raftReady := tc.raft.Ready()
	lockReady := tc.lockStatus.Load()
	ready := httpReady && raftReady && lockReady
	if !ready {
		tc.logger.Errorf(tc.ctx, "httpReady: %v, raftReady: %v, lockReady: %v", httpReady, raftReady, lockReady)
	}
	return ready

}
