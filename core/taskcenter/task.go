package taskcenter

import (
	"context"
	"time"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
)

type ITask interface {
	Name() string
	Schedule() string
	SetSchedule(schedule string)
	Run(ctx context.Context) (map[string]any, error)
	Enable() bool
	Timeout() time.Duration
	SetTimeout(timeout time.Duration)
	Comment() string
	SetComment(str string)
	Category() taskmodels.TaskCategory
	SetCategory(category taskmodels.TaskCategory)
}

func ITaskToCron(t ITask, env string) *CronJob {
	return NewJob(t, env)
}
