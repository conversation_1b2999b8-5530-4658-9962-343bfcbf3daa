package taskcenter

import (
	"errors"

	"github.com/hashicorp/raft"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
)

var (
	c            = config.Global()
	defaultStore = c.GetDefaultStore()
	timeFmt      = "2006-01-02 15:04:05"
	timeOnlyFmt  = "15:04:05"
)

var (
	ErrCronJobNotExist = errors.New("cron job not exist")
	ErrInvalidCronJob  = errors.New("invalid cron job")
)

const (
	TaskStatusRunning   = "running"
	TaskStatusSuccess   = "done"
	TaskStatusException = "exception"
)

type RaftClusterInfo struct {
	Leader         string          `json:"leader"`
	LeaderRaftAddr string          `json:"leader_raft_addr"`
	LeaderHttpAddr string          `json:"leader_http_addr"`
	NodeCount      int             `json:"node_count"`
	Nodes          []*RaftNodeInfo `json:"nodes"`
}

type RaftNodeInfo struct {
	NodeId        raft.ServerID      `json:"node_id"`
	NodeName      string             `json:"node_name"`
	RaftAddr      raft.ServerAddress `json:"node_addr"`
	HttpAddr      string             `json:"http_addr"`
	Role          string             `json:"role"`
	TaskTotal     int64              `json:"task_total"`
	NodeStartTime string             `json:"node_start_time"`
}

type ModifyTaskRequest struct {
	Task     string                  `json:"task"`
	Schedule string                  `json:"schedule"`
	Duration string                  `json:"duration"`
	Comment  string                  `json:"comment"`
	Category taskmodels.TaskCategory `json:"category"`
	Enable   bool                    `json:"enable"`
}

type GetTaskResponse struct {
	Task       string                  `json:"task"`
	Comment    string                  `json:"comment"`
	Schedule   string                  `json:"schedule"`
	Prev       string                  `json:"prev"`
	Next       string                  `json:"next"`
	LastServer string                  `json:"last_server"`
	Enable     bool                    `json:"enable"`
	MaxTimeout string                  `json:"max_timeout"`
	Category   taskmodels.TaskCategory `json:"category"`
	Running    bool                    `json:"running"`
}

type TaskActionResponse struct {
	Task string `json:"task"`
	Info string `json:"info"`
}

type TaskExecResponse struct {
	Result map[string]any
	Err    error
}

type TaskServer struct {
	Address string
	Role    string
}
