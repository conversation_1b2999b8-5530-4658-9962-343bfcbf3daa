package taskmodels

import (
	"git.haier.net/devops/ops-golang-common/models/models"
	"git.haier.net/devops/ops-golang-common/utils"
)

type TcTaskExecLog struct {
	models.Model

	Env     string `gorm:"type:varchar(32);not null;index:idx_task_name" json:"env"`
	Name    string `gorm:"type:varchar(128);not null;index:idx_task_name" json:"task_id"`
	TraceId string `gorm:"type:varchar(64);not null" json:"trace_id"`
	Status  string `gorm:"type:varchar(8);not null" json:"status"`
	Worker  string `gorm:"type:varchar(32);not null" json:"worker"`
	Result  string `gorm:"type:text;default null" json:"result"`
	Error   string `gorm:"type:text;default null" json:"error"`
}

func NewTcTaskExecLog(model models.Model, name string, status string) *TcTaskExecLog {
	t := &TcTaskExecLog{Model: model, Name: name}
	t.Orm().Model(t).
		Where("name = ? and status = ?", name, status).
		Order("id desc").First(t)
	return t
}

func (t *TcTaskExecLog) Data() (map[string]any, error) {
	if t.Result == "" {
		return map[string]any{}, nil
	}
	return utils.UnmarshalString[map[string]any](t.Result)
}
