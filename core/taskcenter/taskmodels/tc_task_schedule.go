package taskmodels

import (
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
	"github.com/gorhill/cronexpr"
)

const (
	TaskCategoryOps      TaskCategory = "ops"      // 运维类任务
	TaskCategoryResource TaskCategory = "resource" // 资源类任务
	TaskCategoryDBMS     TaskCategory = "dbms"     // DBMS任务
	TaskCategoryTest     TaskCategory = "test"     // 测试类任务
	TaskCategoryOther    TaskCategory = "other"    // 其他类任务
	TaskCategoryUnknown  TaskCategory = "unknown"  // 未知类任务
)

type TaskCategory string

type TcTaskSchedule struct {
	models.Model

	Env            string               `gorm:"column:env" json:"env"`
	Name           string               `gorm:"column:name" json:"name"`
	Comment        string               `gorm:"column:comment" json:"comment"`
	Category       TaskCategory         `gorm:"column:category" json:"category"`
	Schedule       string               `gorm:"column:schedule" json:"schedule"`
	Prev           time.Time            `gorm:"column:prev" json:"prev"`
	Next           time.Time            `gorm:"column:next" json:"next"`
	LastServer     string               `gorm:"column:last_server" json:"last_server"`
	Enable         bool                 `gorm:"column:enable" json:"enable"`
	MaxExecTimeout string               `gorm:"column:max_exec_timeout" json:"max_exec_timeout"`
	Expr           *cronexpr.Expression `gorm:"-" json:"-"`
}
