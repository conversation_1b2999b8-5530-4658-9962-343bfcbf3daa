package taskmodels

import (
	"git.haier.net/devops/ops-golang-common/models/models"
)

type TcTaskDispatchLog struct {
	models.Model

	Name    string `gorm:"type:varchar(128);not null;index:idx_task_name" json:"task_id"`
	TraceId string `gorm:"type:varchar(64);not null" json:"trace_id"`
	Status  string `gorm:"type:varchar(8);not null" json:"status"`
	Service string `gorm:"type:varchar(32);not null" json:"service"`
	Client  string `gorm:"type:varchar(32);not null" json:"client"`
	Error   string `gorm:"type:text;default null" json:"error"`
}
