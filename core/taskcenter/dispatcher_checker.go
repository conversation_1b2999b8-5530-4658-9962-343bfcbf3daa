package taskcenter

import (
	"context"
	"time"
)

// 检测任务是否可运行
func (d *Dispatcher) ifJobRunnable(now time.Time, cj *CronJob) bool {
	if cj.scheduleId == 0 {
		return false
	}
	if !cj.enable {
		return false
	}
	if now.Before(cj.next) {
		return false
	}
	return true
}

// 检测任务是否已结束
func (d *Dispatcher) ifJobFinished(ctx context.Context, cj *CronJob) bool {
	// 跳过还未结束运行的任务
	if cj.lastServer == "" {
		return true
	}

	exists := false
	for _, s := range d.tc.raft.RaftServers() {
		addr, _ := ParseRaftNodeHttpAddr(string(s.Address))
		if addr == cj.lastServer {
			exists = true
			break
		}
	}

	// 如果上次执行的服务器已经不在集群中，则可以重新执行
	if !exists {
		d.failedExecTask(ctx, cj)
		return true
	}

	resp, err := d.getTargetNodeTaskInfo(ctx, cj.task.Name(), cj.lastServer)
	if err != nil {
		d.logger.Errorf(ctx, "[%s|%s] get target node task info excepted: %s", cj.env, cj.task.Name(), err.Error())
		return false
	}
	if resp.Running {
		return false
	}

	d.failedExecTask(ctx, cj)
	return true
}

func (d *Dispatcher) failedExecTask(ctx context.Context, cj *CronJob) {
	if err := failedRunningTaskLog(ctx, cj); err != nil {
		d.logger.Errorf(ctx, "[%s|%s] failed running task log error: %s", cj.env, cj.task.Name(), err.Error())
	}
}

func (d *Dispatcher) couldDispatchTaskToOther() bool {
	if !d.tc.Leader() {
		return false
	}

	if !d.tc.Ready() {
		d.Warnf("task center is not ready, could not dispatch task to server")
		return false
	}

	return true
}
