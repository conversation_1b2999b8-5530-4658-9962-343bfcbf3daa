package taskcenter

import (
	"context"
	"fmt"

	"github.com/gorhill/cronexpr"

	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
)

func getOrCreateSchedule(ctx context.Context, cron *CronJob) (*taskmodels.TcTaskSchedule, error) {
	schedule := cron.ToSchedule(ctx)
	err := schedule.Orm().Where("env = ? and name = ?", cron.env, cron.task.Name()).FirstOrCreate(schedule).Error
	if err != nil {
		return nil, err
	}
	expr, parseErr := cronexpr.Parse(schedule.Schedule)
	if parseErr != nil {
		return nil, parseErr
	}
	schedule.Expr = expr
	return schedule, err
}

func failedRunningTaskLog(ctx context.Context, cron *CronJob) error {
	query := fmt.Sprintf("update tc_task_exec_log set status = 'failed', result = 'closed by task dispatcher', "+
		"error = 'closed by task dispatcher' where env = '%s' and name = '%s' and worker = '%s' and status = 'running'",
		cron.env, cron.task.Name(), cron.lastServer)

	task := &taskmodels.TcTaskExecLog{Model: NewModel(ctx)}
	return task.Orm().Exec(query).Error
}

func updateScheduleByCron(ctx context.Context, cron *CronJob, lastServer *TaskServer) error {
	schedule := cron.ToSchedule(ctx)
	return updateSchedule(schedule, lastServer)
}

func updateSchedule(schedule *taskmodels.TcTaskSchedule, lastServer *TaskServer) error {
	if lastServer != nil && lastServer.Address != "" {
		schedule.LastServer = lastServer.Address
	}
	if err := schedule.Orm().
		Model(schedule).
		Select("prev", "next", "last_server", "max_exec_timeout", "category", "comment").
		Where("env = ? and name = ?", schedule.Env, schedule.Name).
		Updates(schedule).Error; err != nil {
		return err
	}
	return nil
}
