package taskcenter

import (
	"fmt"

	"github.com/gofiber/fiber/v2/middleware/proxy"

	"git.haier.net/devops/hcms-task-center/core/gofiber/request"
	"git.haier.net/devops/hcms-task-center/core/gofiber/response"
)

// proxyToLeader 代理到 leader 节点
func (tc *TaskCenter) proxyToLeader(req *request.FiberReq) *response.FiberResp {
	if !tc.Ready() {
		return response.Fail(req.Ctx, response.RequestErr, "current node service not ready")
	}
	if tc.Leader() {
		return response.Fail(req.Ctx, response.RequestErr, "current node is leader")
	}

	leaderAddr := tc.raft.LeaderAddr()
	if leaderAddr == "" {
		return response.Fail(req.Ctx, response.RequestErr, "leader not found")
	}
	httpAddr, err := ParseRaftNodeHttpAddr(leaderAddr)
	if err != nil {
		return response.Error(req.Ctx, response.RequestErr, err)
	}
	return tc.proxyRaftServer(req, httpAddr)
}

// proxyRaftServer 代理到 raft 节点
func (tc *TaskCenter) proxyRaftServer(req *request.FiberReq, addr string) *response.FiberResp {
	targetHost := addr
	ctx := req.Context()
	tc.logger.Infof(ctx, "request proxy to %s", targetHost)

	uri := req.Ctx.Request().URI()
	uri.SetHost(targetHost)
	if err := proxy.Forward(uri.String())(req.Ctx); err != nil {
		return response.Error(req.Ctx, response.InternalCallErr, fmt.Errorf("proxy to %s failed: %s", uri.String(), err.Error()))
	}
	return nil
}
