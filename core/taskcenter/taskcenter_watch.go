package taskcenter

import (
	"net"
	"time"

	"github.com/hashicorp/raft"

	"git.haier.net/devops/hcms-task-center/core/tools"
)

func (tc *TaskCenter) watchHttpServiceReady() {
	tc.wg.Add(1)
	go func() {
		defer tc.logger.Infoln("httpserver ready reporter exit.")
		defer tc.wg.Done()
		defer tc.cancel()
		lastReadyStatus := tc.Ready()
		nowReadyStatus := false
		for {
			if !tc.httpserver.Running() {
				return
			}

			tools.StandardSleep()
			nowReadyStatus = tc.Ready()
			if lastReadyStatus == nowReadyStatus {
				continue
			}
			if nowReadyStatus {
				tc.logger.Infoln("task center ready to service.")
			} else {
				tc.logger.Warnln("task center status not ready, waiting...")
			}
			lastReadyStatus = nowReadyStatus
		}
	}()
}

func (tc *TaskCenter) watchRaftNodeStatus() {
	tc.wg.Add(1)
	go func() {
		defer tc.wg.Done()
		for {
			if tc.raft.Closed() {
				return
			}
			if tc.raft.IsLeader() {
				cfg := tc.raft.Server().GetConfiguration().Configuration()
				cpy := cfg.Clone()
				for _, server := range cpy.Servers {
					if checkNodeStatus(server.Address) {
						continue
					}
					err := tc.raft.Server().RemoveServer(server.ID, 0, 0).Error()
					if err != nil {
						tc.raft.Errorf("remove server %s failed: %s", server.Address, err)
					}
				}
			}
			time.Sleep(time.Second)
		}
	}()
}

func checkNodeStatus(addr raft.ServerAddress) bool {
	conn, err := net.DialTimeout("tcp", string(addr), time.Second)
	if conn != nil {
		_ = conn.Close()
	}
	return err == nil
}
