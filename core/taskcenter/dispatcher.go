package taskcenter

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/batch"
	"git.haier.net/devops/ops-golang-common/log"
	"git.haier.net/devops/ops-golang-common/utils"
	myCtx "git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/gorhill/cronexpr"

	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/pool"
	"git.haier.net/devops/hcms-task-center/core/taskcenter/taskmodels"
)

type Dispatcher struct {
	mtx *sync.Mutex

	env                string
	ctx                context.Context
	cancelFun          context.CancelFunc
	dispatchMu         *sync.Mutex
	dispatchLastServer string
	taskRepo           *Repository[ITask]
	runningTask        *Repository[ITask]
	cronRepo           *Repository[*CronJob]
	logger             *log.Logger
	tc                 *TaskCenter
	pool               batch.RoutinePool
	dryRun             bool
}

func NewDispatcher(env string) *Dispatcher {
	d := &Dispatcher{
		env:         env,
		taskRepo:    NewRepository[ITask](),
		runningTask: NewRepository[ITask](),
		cronRepo:    NewRepository[*CronJob](),
		logger:      log.NewWithOption("DISPATCHER", log.WithShowCaller(false)),
		dispatchMu:  new(sync.Mutex),
		mtx:         new(sync.Mutex),
		pool:        pool.Pool(),
	}
	return d
}

func (d *Dispatcher) UpdateTask(ctx context.Context, name string, req *ModifyTaskRequest) error {
	d.mtx.Lock()
	defer d.mtx.Unlock()
	if !d.cronRepo.Exists(name) {
		return nil
	}

	params := map[string]any{
		"enable": req.Enable,
	}
	if req.Schedule != "" {
		if _, err := cronexpr.Parse(req.Schedule); err != nil {
			return err
		}
		params["schedule"] = req.Schedule
	}
	if req.Duration != "" {
		duration, err := time.ParseDuration(req.Duration)
		if err != nil {
			return err
		}
		if duration < time.Second {
			return fmt.Errorf("duration must be greater than 1 second")
		}
		params["max_exec_timeout"] = req.Duration
	}
	if req.Category != "" {
		params["category"] = req.Category
	}
	if req.Comment != "" {
		params["comment"] = req.Comment
	}

	cron := d.cronRepo.Get(name)
	schedule := cron.ToSchedule(ctx)
	return schedule.Orm().
		Model(schedule).
		Select("enable", "schedule", "max_exec_timeout", "category", "comment").
		Where("env = ? and name = ?", schedule.Env, name).
		Updates(params).
		Error
}

func (d *Dispatcher) DeleteTask(ctx context.Context, name string) error {
	d.mtx.Lock()
	defer d.mtx.Unlock()

	if !d.cronRepo.Exists(name) {
		return nil
	}

	cron := d.cronRepo.Get(name)
	schedule := cron.ToSchedule(ctx)
	return schedule.Orm().
		Table("tc_task_schedule").
		Where("env = ? and name = ?", schedule.Env, name).
		Updates(map[string]interface{}{
			"enable": false,
		}).
		Error
}

func (d *Dispatcher) RunTask(ctx context.Context, name string, duration time.Duration) error {
	// 防止并发执行任务冲突
	d.mtx.Lock()
	defer d.mtx.Unlock()

	if !d.taskRepo.Exists(name) {
		return ErrCronJobNotExist
	}
	if d.runningTask.Exists(name) {
		d.Infof("task %s is running, skip", name)
		return nil
	}

	task := d.taskRepo.Get(name)
	d.runningTask.Register(task)

	ormModel := NewModel(ctx)
	execLog := &taskmodels.TcTaskExecLog{
		Env:     d.env,
		Model:   ormModel,
		Name:    task.Name(),
		TraceId: myCtx.GetTraceId(ctx),
		Worker:  d.tc.HttpServer().Address(),
		Status:  TaskStatusRunning,
	}
	if err := ormModel.Orm().Create(execLog).Error; err != nil {
		d.logger.Errorf(ctx, "create task %s error: %s", task.Name(), err.Error())
		d.runningTask.Delete(task.Name())
		return err
	}

	return d.submitTaskWithPool(execLog, task, ctx, duration)
}

func (d *Dispatcher) submitTaskWithPool(execLog *taskmodels.TcTaskExecLog, task ITask, ctx context.Context, duration time.Duration) error {
	err := d.pool.Submit(func() {
		ctxWithTimeout, cancelFun := context.WithTimeout(ctx, duration)
		defer cancelFun()
		defer d.runningTask.Delete(task.Name())

		ch := make(chan *TaskExecResponse)
		go d.taskProcess(ctx, task, ch)

		var resp *TaskExecResponse
		select {
		case resp = <-ch:
		case <-ctxWithTimeout.Done():
			resp = &TaskExecResponse{
				Result: nil,
				Err:    fmt.Errorf("task %s timeout: %s", task.Name(), duration.String()),
			}
		}

		res := resp.Result
		err := resp.Err
		execLog.Status = TaskStatusSuccess
		if res != nil && len(res) > 0 {
			execLog.Result = utils.JsonString(res)
		}
		if err != nil {
			execLog.Status = TaskStatusException
			execLog.Error = err.Error()
			title := fmt.Sprintf("任务执行失败: %s", task.Name())
			notice.SendWarnMessage(title, err.Error())
		}

		if err := execLog.Model.Orm().Updates(execLog).Error; err != nil {
			d.logger.Errorf(execLog.Ctx, "save task exec log %s error: %s", task.Name(), err.Error())
		}
	})

	return err
}

func (d *Dispatcher) taskProcess(ctx context.Context, task ITask, resultChan chan *TaskExecResponse) {
	defer func() {
		r := recover()
		if r == nil {
			return
		}

		var pic error
		if err, ok := r.(error); ok {
			pic = err
		} else {
			pic = fmt.Errorf("%v", r)
		}

		stack := debug.Stack()
		d.logger.Errorf(ctx, "task %s panic: %s\n%s", task.Name(), pic.Error(), debug.Stack())
		notice.SendErrorMessage("TASK PANIC", fmt.Sprintf("task %s panic: %s\n%s", task.Name(), pic.Error(), stack))
		resultChan <- &TaskExecResponse{
			Err: pic,
			Result: map[string]any{
				"panic": string(debug.Stack()),
			},
		}
	}()

	if ctx.Err() != nil {
		return
	}

	res, err := task.Run(ctx)
	resultChan <- &TaskExecResponse{
		Err:    err,
		Result: res,
	}
}
