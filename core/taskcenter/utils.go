package taskcenter

import (
	"context"
	"fmt"
	"math/rand"
	"net"
	"strconv"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/models/models"
)

func NewModel(ctx context.Context) models.Model {
	return defaultStore.Model(ctx)
}

func MustParseRaftNodeHttpAddr(raftAddr string) string {
	httpAddr, err := ParseRaftNodeHttpAddr(raftAddr)
	if err != nil {
		panic(err)
	}
	return httpAddr
}

func ParseRaftNodeHttpAddr(raftAddr string) (string, error) {
	ip, port, err := net.SplitHostPort(raftAddr)
	if err != nil {
		return "", fmt.Errorf("invalid raft addr %s: %s", raftAddr, err.Error())
	}
	portInt, _ := strconv.Atoi(port)
	httpAddr := fmt.Sprintf("%s:%d", ip, portInt-1000)
	return httpAddr, nil
}

func GetRandomAddress(ipList map[string]int, lastIP string) string {
	totalWeight := 0
	for _, weight := range ipList {
		totalWeight += weight
	}

	if totalWeight == 0 {
		// 尝试返回除 lastIP 外的第一个可用 IP
		for ip := range ipList {
			if ip != lastIP {
				return ip
			}
		}
		// 如果只有 lastIP 可用，则返回它
		return lastIP
	}

	randomNum := getRand(totalWeight)
	cumWeight := 0
	for ip, weight := range ipList {
		if ip == lastIP {
			continue
		}
		cumWeight += weight
		if cumWeight > randomNum {
			return ip
		}
	}

	// 如果所有的ip地址都相同，则返回最后一个ip地址
	return lastIP
}

var randSource = rand.New(rand.NewSource(time.Now().UnixNano()))
var randSourceMutex = new(sync.Mutex)

func getRand(totalWeight int) int {
	if totalWeight == 0 {
		return 0
	}
	randSourceMutex.Lock()
	defer randSourceMutex.Unlock()
	randSource.Seed(time.Now().UnixNano())
	randomNum := randSource.Intn(totalWeight)
	return randomNum
}
