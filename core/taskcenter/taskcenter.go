package taskcenter

import (
	"context"
	"sync"
	"sync/atomic"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
	myCtx "git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/gofiber/engine"
	"git.haier.net/devops/hcms-task-center/core/raftserver"
)

const (
	apiPrefix = "/api/v1/hcms/task"
)

type TaskCenter struct {
	startTime time.Time
	wg        *sync.WaitGroup
	logger    *log.Logger
	ctx       context.Context
	cancel    context.CancelFunc
	cnf       config.Config

	clusterName string
	redisConn   redis.Conn
	mtx         *redsync.Mutex

	lockStatus *atomic.Bool
	raft       *raftserver.Raft
	httpserver *engine.FiberEngine
	dispatcher *Dispatcher
}

func NewTaskCenter(cnf config.Config, opt ...Option) *TaskCenter {
	ctx, cancel := context.WithCancel(myCtx.NewContextWithTraceId(""))
	tc := &TaskCenter{
		wg:         new(sync.WaitGroup),
		logger:     log.NewWithOption("TASK_CENT", log.WithShowCaller(false)),
		cnf:        cnf,
		ctx:        ctx,
		cancel:     cancel,
		lockStatus: new(atomic.Bool),
		dispatcher: NewDispatcher(cnf.Env),
	}

	for _, o := range opt {
		o(tc)
	}

	return tc.init()
}

func (tc *TaskCenter) HttpServer() *engine.FiberEngine {
	return tc.httpserver
}

func (tc *TaskCenter) RedisConn() redis.Conn {
	return tc.redisConn
}

func (tc *TaskCenter) Run() {
	tc.startTime = time.Now()
	tc.watchHttpServiceReady()
	tc.watchRaftNodeStatus()
	tc.startRaftServer()
	tc.startHttpServer()
	tc.dispatcher.start()
}

func (tc *TaskCenter) Wait() {
	tc.wg.Wait()
}
