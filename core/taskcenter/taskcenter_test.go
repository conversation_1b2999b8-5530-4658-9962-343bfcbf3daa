package taskcenter

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.haier.net/devops/hcms-task-center/core/config"
)

func TestNewTaskCenter(t *testing.T) {
	a := assert.New(t)
	tc := NewTaskCenter(config.Global())
	a.NotEmpty(tc)
	tc.Run()
	tc.Wait()
}

func TestNewTaskCenter2(t *testing.T) {
	a := assert.New(t)
	oc := config.Global()
	c := &oc
	c.ServerPort = 8001
	c.RaftPort = 9001

	tc := NewTaskCenter(*c)
	a.NotEmpty(tc)
	tc.Run()
	tc.Wait()
}
