package taskcenter

import (
	"context"
	"errors"
	"fmt"
	"path"

	"git.haier.net/devops/ops-golang-common/utils"
	myCtx "git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/imroc/req/v3"

	"git.haier.net/devops/hcms-task-center/core/gofiber/response"
)

func (d *Dispatcher) getTargetNodeTaskInfo(ctx context.Context, taskName, serverHttpAddr string) (*GetTaskResponse, error) {
	if serverHttpAddr == "" {
		return nil, errors.New("server addr is empty")
	}
	if taskName == "" {
		return nil, errors.New("task name is empty")
	}

	url := fmt.Sprintf("http://%s%s", serverHttpAddr, path.Join(apiPrefix, taskName))
	resp, err := req.R().SetHeader("X-Request-Id", myCtx.GetTraceId(ctx)).Get(url)
	if err != nil {
		d.logger.Errorf(ctx, "%s ==> get task %s failed: %s", url, taskName, err)
		return nil, err
	}

	hrResp, _ := utils.Unmarshal[*response.BaseResponse](resp.Bytes())
	if hrResp.Code != response.Success.Code {
		d.logger.Errorf(ctx, "%s ==> get task %s failed: %s", url, taskName, hrResp.Detail)
		return nil, errors.New(fmt.Sprintf("%s, %s", hrResp.Message, hrResp.Detail))
	}

	result := new(GetTaskResponse)
	_ = utils.Copy(hrResp.Data, result)
	return result, nil
}

func (d *Dispatcher) runTaskOnTargetNode(ctx context.Context, taskName, duration string, server *TaskServer) error {
	if server == nil || server.Address == "" {
		return errors.New("server addr is empty")
	}
	if taskName == "" {
		return errors.New("task name is empty")
	}

	url := fmt.Sprintf("http://%s%s", server.Address, path.Join(apiPrefix, taskName))
	resp, err := req.R().
		SetHeader("X-Request-Id", myCtx.GetTraceId(ctx)).
		SetQueryParam("duration", duration).
		Put(url)
	if err != nil {
		d.logger.Errorf(ctx, "%s ==> exec task %s http request failed: %s", url, taskName, err)
		return err
	}

	hrResp, _ := utils.Unmarshal[*response.BaseResponse](resp.Bytes())
	if hrResp.Code != response.Success.Code {
		d.logger.Errorf(ctx, "%s ==> exec task %s failed: %s", url, taskName, hrResp.Detail)
		return errors.New(fmt.Sprintf("%s, %s", hrResp.Message, hrResp.Detail))
	}

	return nil
}
