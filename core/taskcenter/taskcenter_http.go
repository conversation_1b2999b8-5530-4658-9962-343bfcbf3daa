package taskcenter

import (
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/gofiber/engine"
	"git.haier.net/devops/hcms-task-center/core/gofiber/middleware/limiter"
)

func buildHttpServer(cnf config.Config) *engine.FiberEngine {
	serverOptions := []engine.ConfigOption{
		engine.WithAppName("TASK_CENTER"),
		engine.WithApiPrefix(apiPrefix),
		engine.WithCaseSensitive(),
		engine.WithStrictRouting(),
		engine.WithEnableHealthCheck(),
		engine.WithEnableLimiter(),
		engine.WithEnableMonitor(),
		engine.WithMonitorApiOnly(),
		engine.WithEnableMetrics(),
		engine.WithReadBufferSize(4096 * 8), // hds请求header太大，增加buffer到32k
		engine.WithPort(cnf.ServerPort),
		engine.WithLimitType(limiter.LimitByToken),
		engine.WithMaxReqInSec(cnf.MaxRequestPerSec),
	}

	switch cnf.Env {
	case "local":
		serverOptions = append(serverOptions, engine.WithEnablePrintRoutes())
	default:
	}

	return engine.NewFiber(serverOptions...)
}

func (tc *TaskCenter) startHttpServer() {
	tc.wg.Add(1)
	go func() {
		defer tc.wg.Done()
		if err := tc.httpserver.Run(); err != nil {
			panic(err)
		}
	}()
}
