package taskcenter

import (
	"context"
	"path"
	"strconv"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/hashicorp/raft"

	"git.haier.net/devops/hcms-task-center/biz/bizutils"
	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/raftserver"
	"git.haier.net/devops/hcms-task-center/core/tools"
)

func buildRaftNode(cnf config.Config) *raftserver.Raft {
	api := path.Join(apiPrefix, apiRaftClusterAction)
	return raftserver.NewNode(
		cnf.RaftPort, "",
		raftserver.WithJoinClusterAPI(api),
		raftserver.WithLeaveClusterAPI(api),
	)
}

func (tc *TaskCenter) startRaftServer() {
	tc.wg.Add(2)
	go tc.syncMtxWatchdog()
	go tc.raftServerRun()
}

func (tc *TaskCenter) raftServerRun() {
	defer tc.wg.Done()
	for {
		if tc.httpserver.HttpReady() {
			goto start
		}
		time.Sleep(time.Second)
	}

start:
	bootstrapInfo := tc.getBootstrapCluster()
	tc.raft.SetRaftBootstrapCluster(bootstrapInfo)
	tc.raft.Start()
	tc.raft.Wait()
}

// tryLockLeaderShip 尝试获取leader权限
func (tc *TaskCenter) tryLockLeaderShip() bool {
	if err := tc.mtx.LockContext(tc.ctx); err != nil {
		tc.raft.Warnf("tryLockLeaderShip failed: %s", err.Error())
		return false
	}
	return true
}

func (tc *TaskCenter) getBootstrapCluster() string {
	if tc.tryLockLeaderShip() {
		tc.raft.Infof("lock leader success, try getBootstrapCluster as single cluster")
		return ""
	}

	addr, err := tc.RedisConn().Get(tc.clusterName)
	if err != nil {
		panic(err)
	}
	tc.raft.Infof("lock leader failed, try getBootstrapCluster as cluster: %s", addr)
	return addr
}

func (tc *TaskCenter) syncMtxWatchdog() {
	defer tc.wg.Done()
	for tc.httpserver.Running() {
		tc.tryExtendLock()
	}
}

func (tc *TaskCenter) tryExtendLock() {
	defer tools.StandardSleep()

	if !tc.raft.IsLeader() {
		tc.checkIfRebootRaftServer()
		return
	}

	v, err := tc.redisConn.Get(tc.mtx.Name())
	if err != nil {
		tc.raft.Warnf("get lock failed: %s", err.Error())
		tc.lockStatus.Store(false)
		return
	}

	if v == "" {
		if err := tc.mtx.Lock(); err != nil {
			tc.raft.Warnf("lock failed: %s", err.Error())
			tc.lockStatus.Store(false)
		}
		return
	}
	if v != tc.mtx.Value() {
		tc.raft.Warnf("lock not match [%s] <-> [%s], wait auto unlock ...", v, tc.getSyncMtxValue(tc.cnf))
		tc.lockStatus.Store(false)
		return
	}

	ok, err := tc.mtx.ExtendContext(tc.ctx)
	if ok {
		tc.lockStatus.Store(true)
		return
	}

	if err != nil {
		tc.raft.Warnf("extend lock failed: %s", err.Error())
		tc.lockStatus.Store(false)
		return
	}
}

// checkIfRebootRaftServer 检查raft server状态，如果不是follower则重启raft server
func (tc *TaskCenter) checkIfRebootRaftServer() {
	defer tools.StandardSleep()

	tc.wg.Add(1)
	defer tc.wg.Done()

	if tc.raft.Server() == nil {
		return
	}
	if tc.raft.Server().State() == raft.Follower {
		tc.lockStatus.Store(true)
		return
	}

	// 等待raft状态机自行恢复，如果超时则重启raft server
	timer := time.NewTimer(10 * time.Second).C
	for {
		if !tc.httpserver.Running() {
			return
		}

		select {
		case <-timer:
			tc.raft.Info("raft server state recovery timeout, try to reboot")
			goto reboot
		default:
			state := tc.raft.Server().State()
			if state == raft.Leader || state == raft.Follower {
				tc.raft.Info("raft server state is recovery, no need to reboot")
				return
			}
			time.Sleep(time.Second)
		}
	}

reboot:
	if !tc.httpserver.Running() {
		tc.raft.Infof("http server is not running, no need to reboot, quit")
		return
	}

	// 重启raft server
	tc.raft.Info("try to reboot raft server")
	tc.raft.Shutdown()
	tc.raft.Wait()
	tc.raft = buildRaftNode(tc.cnf)
	tc.startRaftServer()

	// 等待raft server启动
	c := time.NewTimer(time.Minute).C
	for {
		select {
		case <-c:
			tc.raft.Error("reboot raft server timeout, exit")
			return
		default:
			if !tc.httpserver.Running() {
				tc.raft.Infof("http server is not running, no need to reboot, quit")
				return
			}
			if tc.raft.Ready() {
				tc.raft.Info("reboot raft server success")
				return
			}
		}
	}
}

func (tc *TaskCenter) generateSyncMtx(cnf config.Config) *redsync.Mutex {
	return redsync.New(cnf.RedisPool()).NewMutex(tc.clusterName,
		redsync.WithExpiry(2*time.Second),
		redsync.WithTries(5),
		redsync.WithGenValueFunc(func() (string, error) {
			return tc.getSyncMtxValue(cnf), nil
		}),
	)
}

func (tc *TaskCenter) getSyncMtxValue(cnf config.Config) string {
	return "http://" + tc.raft.RaftBindIp() + ":" + strconv.Itoa(cnf.ServerPort)
}

func (tc *TaskCenter) NodeName() string {
	return tc.raft.NodeName()
}

func (tc *TaskCenter) NodeInfo(ctx context.Context) *RaftNodeInfo {
	httpAddr, _ := ParseRaftNodeHttpAddr(tc.raft.RaftBindAddr())
	model := bizutils.DataSource().Model(tc.ctx)
	var taskCount int64
	err := model.Orm().Table("tc_task_exec_log").
		Where("worker = ? and create_time >= ?", httpAddr, tc.startTime).
		Count(&taskCount).
		Error
	if err != nil {
		tc.logger.Errorf(ctx, "get task count failed: %s", err.Error())
	}

	return &RaftNodeInfo{
		NodeId:        tc.raft.RaftID(),
		NodeName:      tc.raft.NodeName(),
		RaftAddr:      raft.ServerAddress(tc.raft.RaftBindAddr()),
		HttpAddr:      httpAddr,
		Role:          tc.raft.Server().State().String(),
		TaskTotal:     taskCount,
		NodeStartTime: tc.startTime.Format("2006-01-02 15:04:05"),
	}
}
