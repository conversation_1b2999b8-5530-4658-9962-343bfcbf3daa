package taskcenter

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	myCtx "git.haier.net/devops/ops-golang-common/utils/context"

	"git.haier.net/devops/hcms-task-center/biz/notice"
)

func (d *Dispatcher) start() {
	if d.tc.cnf.DisableDispatcher {
		d.Infof("task dispatcher disabled")
		return
	}

	d.Infof("task dispatcher started")
	defer d.Infof("task dispatcher stopped")

	d.tc.wg.Add(1)
	defer d.tc.wg.Done()
	defer d.cancelFun()

	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-d.ctx.Done():
			d.Infof("task dispatcher ctx done")
			return
		case <-ticker.C:
			// 保证秒级调度
			func() {
				defer func() {
					if r := recover(); r != nil {
						stack := debug.Stack()
						d<PERSON>("dispatch panic: %v\n%s", r, string(stack))
					}
				}()
				d.dispatch()
			}()
		}
	}
}

func (d *Dispatcher) dispatch() {
	if !d.couldDispatchTaskToOther() {
		return
	}
	wg := new(sync.WaitGroup)

	// 所有任务使用相同的时间来判断是否执行
	now := time.Now()
	// 从数据库中获取所有任务
	cronJobs := d.cronRepo.GetAll()
	wg.Add(len(cronJobs))
	// 控制下发任务的并发数
	ctrlChan := make(chan struct{}, 10)
	for _, cronJob := range cronJobs {
		// 每个任务使用独立的上下文
		ctx := myCtx.NewContext()
		ctrlChan <- struct{}{}
		d.checkAndDispatch(ctx, wg, ctrlChan, now, cronJob)
	}
	// 等待所有任务下发完成
	wg.Wait()
	// 记录下发任务的耗时
	since := time.Since(now)
	if since > time.Second {
		d.Warnf("[%s] dispatch all task cost: %s", now.Format(timeOnlyFmt), since)
	}
}

func (d *Dispatcher) checkAndDispatch(ctx context.Context, wg *sync.WaitGroup, ctrlChan <-chan struct{}, now time.Time, cronJob *CronJob) {
	defer func() {
		<-ctrlChan
		wg.Done()
	}()

	// 预处理任务，跳过预处理失败的任务
	if err := d.prepareCronjob(ctx, now, cronJob); err != nil {
		d.logger.Errorf(ctx, " [%s|%s] prepare cronjob error: %s", cronJob.env, cronJob.task.Name(), err.Error())
		return
	}

	// 跳过不可执行的任务
	if !d.ifJobRunnable(now, cronJob) {
		return
	}

	// 跳过还在执行的任务
	if !d.ifJobFinished(ctx, cronJob) {
		d.logger.Warnf(ctx, " [%s|%s] task is running, skip", cronJob.env, cronJob.task.Name())
		cronJob.setExecTime(now) // 调整下次时间
		return
	}

	// 选择执行服务器
	serverHttpAddr := d.selectServer()
	go func(cj *CronJob, address *TaskServer) {
		// 下发任务
		err := d.sendTaskToSerer(ctx, now, cj, address)
		if err == nil {
			return
		}
		msg := fmt.Sprintf(" [%s|%s] send task to [%s][%s] excepted: %s",
			cj.env, cj.task.Name(), address.Role, address.Address, err.Error())
		d.logger.Errorf(ctx, msg)
		notice.SendErrorMessage("任务调度失败", msg)
	}(cronJob, serverHttpAddr)
}

func (d *Dispatcher) sendTaskToSerer(ctx context.Context, now time.Time, cj *CronJob, address *TaskServer) error {
	// 更新调度信息
	cj.setExecTime(now)
	schedule := cj.ToSchedule(ctx)
	if err := updateSchedule(schedule, address); err != nil {
		d.logger.Errorf(ctx, "[%s|%s] update schedule excepted: %s", cj.env, cj.task.Name(), err.Error())
		return err
	}

	// 下发任务
	d.Infof("[%s|%s] dispatch task to [%s][%s]", cj.env, cj.task.Name(), address.Role, address.Address)
	return d.runTaskOnTargetNode(ctx, cj.task.Name(), cj.task.Timeout().String(), address)
}

func (d *Dispatcher) doDispatch(ctx context.Context, task ITask) error {
	if d.dryRun {
		d.logger.Infof(ctx, "dry run, skip dispatch task %s", task.Name())
		return nil
	}

	return nil
}

func (d *Dispatcher) selectServer() *TaskServer {
	servers := d.tc.raft.RaftServers()
	_, leaderId := d.tc.raft.Server().LeaderWithID()
	addressMap := make(map[string]int)
	addressRoleMap := make(map[string]string)
	// leader不分配任务， 防止负载过高导致集群频繁切换重启
	for _, s := range servers {
		if s.ID == leaderId {
			addressMap[string(s.Address)] = 0
			addressRoleMap[string(s.Address)] = "leader"
		} else {
			addressMap[string(s.Address)] = 100
			addressRoleMap[string(s.Address)] = "follower"
		}
	}

	d.dispatchMu.Lock()
	defer d.dispatchMu.Unlock()
	serverAddr := GetRandomAddress(addressMap, d.dispatchLastServer)
	d.dispatchLastServer = serverAddr
	httpServerAddr, _ := ParseRaftNodeHttpAddr(serverAddr)
	return &TaskServer{
		Address: httpServerAddr,
		Role:    addressRoleMap[serverAddr],
	}
}

func (d *Dispatcher) prepareCronjob(ctx context.Context, now time.Time, cj *CronJob) error {
	defaultTimeout := time.Hour
	// 合理性检查
	if cj == nil || cj.task == nil || cj.task.Name() == "" {
		return ErrInvalidCronJob
	}
	// 从数据库中获取任务调度信息
	schedule, err := getOrCreateSchedule(ctx, cj)
	if err != nil {
		return err
	}

	// 以数据库中的信息作为最高优先级
	cj.scheduleId = schedule.ID
	cj.lastServer = schedule.LastServer
	if cj.prev.IsZero() {
		cj.prev = schedule.Prev
	}
	if cj.next.IsZero() {
		cj.next = schedule.Next
	}
	if schedule.Comment != "" {
		cj.task.SetComment(schedule.Comment)
	}
	if schedule.Category != "" {
		cj.task.SetCategory(schedule.Category)
	}

	// 更新调度信息
	if cj.enable != schedule.Enable {
		d.logger.Infof(ctx, " [%s|%s] enable changed, update", cj.env, cj.task.Name())
		cj.enable = schedule.Enable
	}
	// 更新调度信息
	if !schedule.Next.IsZero() && !schedule.Next.Equal(cj.next) {
		d.logger.Infof(ctx, " [%s|%s] next time changed, update", cj.env, cj.task.Name())
		cj.next = schedule.Next
	}
	// 更新调度信息
	if cj.task.Schedule() != schedule.Schedule || cj.next.IsZero() {
		d.logger.Infof(ctx, " [%s|%s] schedule changed, update", cj.env, cj.task.Name())
		cj.expr = schedule.Expr
		cj.next = cj.expr.Next(now)
	}
	// 更新最大执行时间
	if schedule.MaxExecTimeout == "" && cj.task.Timeout() == 0 {
		cj.task.SetTimeout(defaultTimeout)
	}
	if schedule.MaxExecTimeout != "" {
		timeout, err := time.ParseDuration(schedule.MaxExecTimeout)
		if err != nil {
			d.logger.Errorf(ctx, " [%s|%s] parse max exec timeout error: %s, reset to default",
				cj.env, cj.task.Name(), err.Error())
			timeout = defaultTimeout
		} else {
			if cj.task.Timeout() != timeout {
				d.logger.Infof(ctx, " [%s|%s] max exec timeout changed, update %s -> %s",
					cj.env, cj.task.Name(), cj.task.Timeout().String(), timeout.String())
				cj.task.SetTimeout(timeout)
			}
		}
	}

	cj.task.SetSchedule(schedule.Schedule)
	// 更新调度信息
	if err := updateScheduleByCron(ctx, cj, nil); err != nil {
		d.logger.Errorf(ctx, "update schedule %s-%s error: %s", cj.env, cj.task.Name(), err.Error())
		return err
	}
	return nil
}
