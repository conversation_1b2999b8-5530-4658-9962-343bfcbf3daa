package taskcenter

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.haier.net/devops/ops-golang-common/utils"
	"github.com/gofiber/fiber/v2"
	"github.com/hashicorp/raft"

	"git.haier.net/devops/hcms-task-center/core/gofiber/engine"
	"git.haier.net/devops/hcms-task-center/core/gofiber/request"
	"git.haier.net/devops/hcms-task-center/core/gofiber/response"
)

func (d *Dispatcher) registerHandler() {
	d.tc.HttpServer().Group("", func(api engine.FiberRouter) {
		api.Register(fiber.MethodGet, "", d.<PERSON>leTaskList)                // 获取任务列表
		api.Register(fiber.MethodGet, "/:taskName", d.HandleQueryTaskInfo) // 获取任务信息
		api.Register(fiber.MethodPut, "/:taskName", d.<PERSON>leExecuteTask)   // 执行任务
		api.Register(fiber.MethodPost, "/:taskName", d.<PERSON>)   // 更新任务
		api.Register(fiber.MethodDelete, "/:taskName", d.Handle<PERSON>eleteTask) // 关闭任务调度
		api.Register(fiber.MethodGet, "/node/ready", d.HandleHealthCheck)  // 节点状态检查
	})
}

// HandleDeleteTask 删除任务
func (d *Dispatcher) HandleDeleteTask(req *request.FiberReq) *response.FiberResp {
	taskName := req.FiberParams("taskName")
	if taskName == "" {
		return response.Fail(req.Ctx, response.RequestErr, "task name is empty")
	}
	if !d.cronRepo.Exists(taskName) {
		return response.Fail(req.Ctx, response.RequestErr, "task not exists")
	}

	if err := d.DeleteTask(req.Context(), taskName); err != nil {
		return response.Error(req.Ctx, response.RequestErr, err)
	}

	return response.Ok(req.Ctx, TaskActionResponse{
		Task: taskName,
		Info: fmt.Sprintf("Task %s disabled", taskName),
	})
}

// HandleUpdateTask 更新任务
func (d *Dispatcher) HandleUpdateTask(req *request.FiberReq) *response.FiberResp {
	if !d.tc.Leader() {
		return d.tc.proxyToLeader(req)
	}

	taskName := req.FiberParams("taskName")
	if taskName == "" {
		return response.Fail(req.Ctx, response.RequestErr, "task name is empty")
	}
	if !d.cronRepo.Exists(taskName) {
		return response.Fail(req.Ctx, response.RequestErr, "task not exists")
	}

	reqData, err := utils.Unmarshal[*ModifyTaskRequest](req.FiberCtx().Body())
	if err != nil {
		return response.Fail(req.Ctx, response.RequestErr, err.Error())
	}

	if err := d.UpdateTask(req.Context(), taskName, reqData); err != nil {
		return response.Error(req.Ctx, response.RequestErr, err)
	}

	return response.Ok(req.Ctx, TaskActionResponse{
		Task: taskName,
		Info: fmt.Sprintf("Task %s updated on %s", taskName, d.tc.HttpServer().Address()),
	})
}

// HandleExecuteTask 执行任务
func (d *Dispatcher) HandleExecuteTask(req *request.FiberReq) *response.FiberResp {
	taskName := req.FiberParams("taskName")
	if taskName == "" {
		return response.Fail(req.Ctx, response.RequestErr, "task name is empty")
	}
	if !d.cronRepo.Exists(taskName) {
		return response.Fail(req.Ctx, response.RequestErr, "task not exists")
	}
	if d.runningTask.Exists(taskName) {
		return response.Fail(req.Ctx, response.RequestErr, "task is running")
	}

	durationString := req.FiberQuery("duration", "1h")
	duration, err := time.ParseDuration(durationString)
	if err != nil {
		return response.Fail(req.Ctx, response.RequestErr, "duration is invalid")
	}

	// 传递请求参数
	ctx := req.Context()
	for k, v := range req.FiberQueries() {
		if k == "duration" {
			continue
		}
		ctx = context.WithValue(ctx, k, v)
	}
	if err := d.RunTask(ctx, taskName, duration); err != nil {
		return response.Error(req.Ctx, response.RequestErr, err)
	}

	return response.Ok(req.Ctx, TaskActionResponse{
		Task: taskName,
		Info: fmt.Sprintf("Task %s triggered on %s", taskName, d.tc.HttpServer().Address()),
	})
}

// HandleQueryTaskInfo 获取任务信息
func (d *Dispatcher) HandleQueryTaskInfo(req *request.FiberReq) *response.FiberResp {
	taskName := req.FiberParams("taskName")
	if taskName == "" {
		return response.Fail(req.Ctx, response.RequestErr, "task name is empty")
	}

	if !d.cronRepo.Exists(taskName) {
		return response.Fail(req.Ctx, response.RequestErr, "task not exists")
	}

	taskInfo := d.cronRepo.Get(taskName)
	running := d.runningTask.Exists(taskName)
	return response.Ok(req.Ctx, GetTaskResponse{
		Task:       taskInfo.Name(),
		Schedule:   taskInfo.getTask().Schedule(),
		Prev:       taskInfo.prev.Format(timeFmt),
		Next:       taskInfo.next.Format(timeFmt),
		LastServer: taskInfo.lastServer,
		Enable:     taskInfo.enable,
		MaxTimeout: taskInfo.task.Timeout().String(),
		Comment:    taskInfo.task.Comment(),
		Category:   taskInfo.task.Category(),
		Running:    running,
	})
}

// HandleTaskList 获取任务列表
func (d *Dispatcher) HandleTaskList(req *request.FiberReq) *response.FiberResp {
	if !d.tc.Ready() {
		return response.Fail(req.Ctx, response.RequestErr, "server not ready")
	}
	if !d.tc.Leader() {
		return d.tc.proxyToLeader(req)
	}

	wg := new(sync.WaitGroup)
	tasks := d.cronRepo.GetAll()
	resp := make([]GetTaskResponse, len(tasks))
	raftServerMap := map[string]struct{}{}
	for _, server := range d.tc.raft.RaftServers() {
		raftServerMap[MustParseRaftNodeHttpAddr(string(server.Address))] = struct{}{}
	}

	wg.Add(len(tasks))
	for i, task := range tasks {
		go func(task *CronJob, i int) {
			defer wg.Done()
			running := d.checkTaskRunning(req.Context(), task.Name())
			resp[i] = GetTaskResponse{
				Task:       task.Name(),
				Schedule:   task.getTask().Schedule(),
				Prev:       task.prev.Format(timeFmt),
				Next:       task.next.Format(timeFmt),
				LastServer: task.lastServer,
				Enable:     task.enable,
				MaxTimeout: task.task.Timeout().String(),
				Comment:    task.task.Comment(),
				Category:   task.task.Category(),
				Running:    running,
			}
		}(task, i)
	}

	wg.Wait()
	return response.Ok(req.Ctx, resp)
}

// HandleHealthCheck 节点状态检查
func (d *Dispatcher) HandleHealthCheck(req *request.FiberReq) *response.FiberResp {
	if !d.tc.Ready() {
		return response.Fail(req.Ctx, response.RequestErr, "server not ready")
	}
	return response.Ok(req.Ctx)
}

func (d *Dispatcher) checkTaskRunning(ctx context.Context, taskName string) bool {
	servers := d.tc.raft.RaftServers()
	wg := new(sync.WaitGroup)
	wg.Add(len(servers))

	b := make([]bool, len(servers))
	for i, server := range servers {
		go func(i int, server raft.Server) {
			defer wg.Done()
			info, _ := d.getTargetNodeTaskInfo(ctx, taskName, MustParseRaftNodeHttpAddr(string(server.Address)))
			if info != nil {
				b[i] = info.Running
			}
		}(i, server)
	}

	wg.Wait()
	for _, b := range b {
		if b {
			return true
		}
	}
	return false
}
