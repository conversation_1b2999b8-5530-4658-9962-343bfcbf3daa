package taskcenter

func (d *Dispatcher) Debugf(format string, args ...interface{}) {
	if d.logger != nil {
		d.logger.Debugf(d.ctx, format, args...)
	}
}

func (d *Dispatcher) Infof(format string, args ...interface{}) {
	if d.logger != nil {
		d.logger.Infof(d.ctx, format, args...)
	}
}

func (d *Dispatcher) Warnf(format string, args ...interface{}) {
	if d.logger != nil {
		d.logger.Warnf(d.ctx, format, args...)
	}
}

func (d *Dispatcher) Errorf(format string, args ...interface{}) {
	if d.logger != nil {
		d.logger.Errorf(d.ctx, format, args...)
	}
}
