package response

var (
	Success            = RespCode{Code: 10000, Status: 200, Message: "success"}
	RequestErr         = RespCode{Code: 10001, Status: 400, Message: "请求错误，请更正后重试"}
	RequestParamErr    = RespCode{Code: 10002, Status: 400, Message: "请求参数错误，请更正后重试"}
	RequestParamLost   = RespCode{Code: 10003, Status: 400, Message: "请求参数缺失，请补充后重试"}
	ResourceNotFound   = RespCode{Code: 10004, Status: 404, Message: "未找到请求资源，请稍后重试"}
	TooManyRequests    = RespCode{Code: 10005, Status: 429, Message: "请求受限，请稍后重试"}
	RequestConflicts   = RespCode{Code: 10006, Status: 500, Message: "请求冲突，请稍后重试"}
	InternalErr        = RespCode{Code: 15000, Status: 500, Message: "内部错误，请稍后重试"}
	InternalCallErr    = RespCode{Code: 15001, Status: 500, Message: "内部调用错误，请稍后重试"}
	InternalCallNoResp = RespCode{Code: 15002, Status: 500, Message: "内部调用无响应，请稍后重试"}
	DatabaseErr        = RespCode{Code: 15003, Status: 500, Message: "内部调用数据库错误，请稍后重试"}
	GatewayErr         = RespCode{Code: 15004, Status: 500, Message: "网关错误，请稍后重试"}
	RequestAuthFailed  = RespCode{Code: 20001, Status: 403, Message: "请求未授权"}
)

type RespCode struct {
	Code    int
	Status  int
	Message string
}

var BuiltInRespCode = map[int]RespCode{
	Success.Code:            Success,
	RequestErr.Code:         RequestErr,
	RequestParamErr.Code:    RequestParamErr,
	RequestParamLost.Code:   RequestParamLost,
	ResourceNotFound.Code:   ResourceNotFound,
	TooManyRequests.Code:    TooManyRequests,
	RequestConflicts.Code:   RequestConflicts,
	InternalErr.Code:        InternalErr,
	InternalCallErr.Code:    InternalCallErr,
	InternalCallNoResp.Code: InternalCallNoResp,
	DatabaseErr.Code:        DatabaseErr,
	GatewayErr.Code:         GatewayErr,
	RequestAuthFailed.Code:  RequestAuthFailed,
}
