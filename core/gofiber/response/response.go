package response

import (
	"github.com/gofiber/fiber/v2"

	"git.haier.net/devops/hcms-task-center/core/gofiber/futils"
)

type FiberResp struct {
	ctx  *fiber.Ctx
	data *BaseResponse
}

type BaseResponse struct {
	HttpCode  int    `json:"-"` // http 状态码
	Code      int    `json:"code" example:"10000"`
	Message   string `json:"msg" example:"success"`
	RequestId string `json:"requestId" example:"abcdefg-hijklmn-opqrst-uvwxyz"`
	Detail    string `json:"detail,omitempty" example:""`
	Data      any    `json:"data,omitempty"` // 任意类型
}

// Resp 结构具体看规范：https://ihaier.feishu.cn/wiki/wikcnE7QaMjn2FD55RC3PZoHwTf#
func Resp(ctx *fiber.Ctx, opts ...Option) *FiberResp {
	resp := &FiberResp{
		ctx: ctx,
		data: &BaseResponse{
			HttpCode:  Success.Status,
			Code:      Success.Code,
			Message:   Success.Message,
			RequestId: futils.RequestID(ctx),
		}}
	for _, opt := range opts {
		opt(resp)
	}

	return resp
}

func (r *FiberResp) Next() error {
	return r.ctx.Next()
}

func (r *FiberResp) Result() error {
	r.ctx.Status(r.data.HttpCode)
	return r.ctx.JSON(r.data)
}

func Ok(ctx *fiber.Ctx, data ...interface{}) *FiberResp {
	var d interface{}
	if len(data) > 0 {
		d = data[0]
	}
	return Resp(ctx, WithData(d))
}

func Fail(ctx *fiber.Ctx, code RespCode, detail ...string) *FiberResp {
	return Resp(ctx).Fail(code, detail...)
}

func Error(ctx *fiber.Ctx, code RespCode, err error) *FiberResp {
	return Resp(ctx).Fail(code, err.Error())
}

func (r *FiberResp) Ok(detail ...string) error {
	return r.Standard(Success, detail...).Result()
}

func (r *FiberResp) Fail(code RespCode, detail ...string) *FiberResp {
	if code.Status == Success.Status {
		code = InternalErr
	}
	return r.Standard(code, detail...)
}

func (r *FiberResp) Standard(code RespCode, detail ...string) *FiberResp {
	r.data.HttpCode = code.Status
	r.data.Code = code.Code
	r.data.Message = code.Message
	if len(detail) > 0 {
		r.data.Detail = detail[0]
	}
	return r
}

func (r *FiberResp) String(data string) error {
	r.data.Data = map[string]interface{}{"data": data}
	return r.Result()
}

func (r *FiberResp) Byte(data []byte) error {
	r.data.Data = map[string]interface{}{"data": data}
	return r.Result()
}
