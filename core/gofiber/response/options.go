package response

type Option func(resp *FiberResp)

func WithCode(code RespCode) Option {
	return func(resp *FiberResp) {
		resp.data.Code = code.Code
		resp.data.Message = code.Message
	}
}

func WithString(data string) Option {
	return func(resp *FiberResp) {
		resp.data.Message = data
	}
}

func withInterface[T any](data T) Option {
	return func(resp *FiberResp) {
		resp.data.Data = data
	}
}

func WithData(data interface{}) Option {
	switch v := data.(type) {
	case RespCode:
		return WithCode(v)
	case string:
		return WithString(v)
	case nil:
		return func(resp *FiberResp) {}
	default:
		return withInterface(data)
	}
}
