package engine

import (
	"context"
	"os"
	"os/signal"
	"path"
	"strconv"
	"sync"
	"sync/atomic"
	"syscall"

	"git.haier.net/devops/ops-golang-common/log"
	myCtx "git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/gofiber/fiber/v2"
	"github.com/imroc/req/v3"

	"git.haier.net/devops/hcms-task-center/core/tools"
)

var emptyCtx = myCtx.NewContextWithTraceId("")

type FiberEngine struct {
	done       *atomic.Bool
	wg         *sync.WaitGroup
	stopOnce   *sync.Once
	context    context.Context
	cancelFunc context.CancelFunc

	sysLogger *log.Logger
	logger    *log.Logger

	cfg *Config
	app *fiber.App
}

func NewFiber(opts ...ConfigOption) *FiberEngine {
	cfg := NewFiberConfig(opts...)

	ctx, cancelFunc := context.WithCancel(emptyCtx)
	engine := &FiberEngine{
		cfg:        cfg,
		app:        fiber.New(*cfg.Config),
		done:       new(atomic.Bool),
		wg:         new(sync.WaitGroup),
		context:    ctx,
		cancelFunc: cancelFunc,
		sysLogger:  log.NewWithOption("FIBER", log.WithShowCaller(false)),
		logger:     log.NewWithOption("SERVER", log.WithShowCaller(true)),
		stopOnce:   new(sync.Once),
	}

	return engine.initMiddleware()
}

func (engine *FiberEngine) Logger() *log.Logger {
	return engine.logger
}

func (engine *FiberEngine) App() *fiber.App {
	return engine.app
}

func (engine *FiberEngine) Address() string {
	ip := engine.cfg.BindAddr
	if ip == "" {
		ip = tools.GetLocalIpAddr()
	}
	return ip + ":" + strconv.Itoa(engine.cfg.Port)
}

func (engine *FiberEngine) Config() *Config {
	return engine.cfg
}

func (engine *FiberEngine) Run() error {
	go engine.handleSystemSignal()
	return engine.app.Listen(":" + strconv.Itoa(engine.cfg.Port))
}

func (engine *FiberEngine) StopGracefully() {
	engine.Shutdown()
}

func (engine *FiberEngine) Shutdown() {
	engine.stopOnce.Do(func() {
		engine.sysLogger.Info(engine.context, "server shutting down ...")
		engine.done.Store(true)
		engine.cancelFunc()
		defer engine.sysLogger.Info(engine.context, "server stopped.")

		if err := engine.app.Shutdown(); err != nil {
			engine.sysLogger.Errorf(engine.context, "server shutdown error: %v", err)
		}
		engine.wg.Wait()
	})
	return
}

func (engine *FiberEngine) Running() bool {
	return !engine.done.Load()
}

// HttpReady returns true if the http server is ready to serve traffic.
func (engine *FiberEngine) HttpReady() bool {
	if !engine.Running() {
		return false
	}
	if !engine.cfg.EnableHealthCheck {
		panic("health check is not enabled")
	}
	resp, err := req.R().Get("http://" + path.Join(engine.Address(), engine.cfg.ApiPrefix, engine.cfg.HealthCheckPath))
	if err != nil {
		engine.sysLogger.Errorf(engine.context, "health check error: %v", err)
		return false
	}
	if resp.StatusCode != 200 {
		engine.sysLogger.Errorf(engine.context, "health check error: %v", resp.String())
		return false
	}
	return true
}

func (engine *FiberEngine) Context() context.Context {
	return engine.context
}

func (engine *FiberEngine) handleSystemSignal() {
	ch := make(chan os.Signal, 1)
	signal.Notify(ch, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	for engine.Running() {
		select {
		case sig := <-ch:
			engine.sysLogger.Infof(engine.context, "fiber server receive signal: %v", sig)
			signal.Stop(ch)
			close(ch)
			engine.Shutdown()
		default:
		}
	}

	engine.sysLogger.Info(engine.context, "system signal handler stopped.")
}
