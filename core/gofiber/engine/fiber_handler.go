package engine

import (
	"path"

	"github.com/gofiber/fiber/v2"

	"git.haier.net/devops/hcms-task-center/core/gofiber/request"
	"git.haier.net/devops/hcms-task-center/core/gofiber/response"
)

type FiberHandler func(req *request.FiberReq) *response.FiberResp

type FiberGroupHandler func(router FiberRouter)

type FiberRouter struct {
	fiber.Router
}

func (engine *FiberEngine) Group(p string, handlers ...FiberGroupHandler) {
	g := FiberRouter{Router: engine.app.Group(path.Join(engine.cfg.ApiPrefix, p))}
	for _, handler := range handlers {
		handler(g)
	}
}

func (engine *FiberEngine) Register(method, path string, handler FiberHandler) {
	engine.app.Add(method, path, cover(handler))
}

func (group *FiberRouter) Group(path string, handlers ...FiberGroupHandler) {
	g := FiberRouter{Router: group.Router.Group(path)}
	for _, handler := range handlers {
		handler(g)
	}
}

func (group *FiberRouter) Register(method, path string, handler FiberHandler) {
	group.Add(method, path, cover(handler))
}

func cover(handler FiberHandler) fiber.Handler {
	return func(ctx *fiber.Ctx) error {
		req := &request.FiberReq{Ctx: ctx}
		resp := handler(req)
		if resp == nil {
			return nil
		}
		return resp.Result()
	}
}
