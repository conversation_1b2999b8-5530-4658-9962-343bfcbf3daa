//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"os"
	"os/exec"
	"reflect"
	"text/template"

	"git.haier.net/devops/hcms-task-center/core/gofiber/engine"
)

type fiberOption struct {
	Name  string
	Field string
	Type  string
}

func main() {
	filename := "fiber_config_options.go"
	fiberTyp := reflect.TypeOf(engine.Config{})
	options := generateOptions(fiberTyp)

	f, err := os.Create(filename)
	if err != nil {
		panic(err)
	}

	defer func() {
		closeErr := f.Close()
		if err == nil {
			err = closeErr
		} else if closeErr != nil {
			err = fmt.Errorf("close error: %v, original error: %w", closeErr, err)
		}
	}()

	if err := fiberOptionTpl.ExecuteTemplate(f, "options", options); err != nil {
		panic(err)
	}

	// 构造 gofmt 命令
	cmd := exec.Command("gofmt", "-l", "-s", "-w", filename)
	if _, err = cmd.Output(); err != nil {
		panic(err)
	}
}

func generateOptions(fiberTyp reflect.Type) []fiberOption {
	options := make([]fiberOption, 0)
	if fiberTyp.Kind() == reflect.Ptr {
		fiberTyp = fiberTyp.Elem()
	}

	for i := 0; i < fiberTyp.NumField(); i++ {
		field := fiberTyp.Field(i)
		if !field.IsExported() {
			continue
		}
		if field.Type.Kind() == reflect.Func {
			continue
		}
		if field.Anonymous {
			options = append(options, generateOptions(field.Type)...)
			continue
		}

		options = append(options, fiberOption{
			Name:  field.Name,
			Field: field.Name,
			Type:  field.Type.String(),
		})
	}
	return options
}

var fiberOptionTpl = template.Must(template.New("optTpl").Parse(`
{{- define "header" }}
// Code generated by gofiber/engine/generate.go DO NOT EDIT.
package engine

import (
	"time"

	"github.com/gofiber/fiber/v2"

	"git.haier.net/devops/hcms-task-center/core/gofiber/middleware/limiter"
)
{{- end }}

{{- define "option" }}
// With{{ $.Name }} sets the {{ $.Field }} field of the ConfigOption.
	{{- if eq $.Type "bool" }}
	func With{{ $.Name }}() ConfigOption {
		return func(config *Config) {
			config.{{ $.Field }} = true
		}
	}
	{{- else }}
	func With{{ $.Name }}(v {{ $.Type }}) ConfigOption {
		return func(config *Config) {
			config.{{ $.Field }} = v
		}
	}
	{{- end }}
{{- end }}

{{- define "options" }}
	{{ template "header" $ }}
	{{ range $_, $option := $ }}
		{{ template "option" $option }}
	{{- end }}
{{- end }}
`))
