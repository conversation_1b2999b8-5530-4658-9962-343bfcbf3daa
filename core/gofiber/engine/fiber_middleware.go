package engine

import (
	"path"
	"time"

	"github.com/ansrivas/fiberprometheus/v2"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/monitor"
	"github.com/gofiber/fiber/v2/middleware/requestid"

	"git.haier.net/devops/hcms-task-center/core/gofiber/middleware/limiter"
	"git.haier.net/devops/hcms-task-center/core/gofiber/middleware/recovery"
	"git.haier.net/devops/hcms-task-center/core/gofiber/middleware/reporter"
	"git.haier.net/devops/hcms-task-center/core/gofiber/request"
	"git.haier.net/devops/hcms-task-center/core/gofiber/response"
)

func (engine *FiberEngine) initMiddleware() *FiberEngine {
	var (
		healthCheckPath = path.Join(engine.cfg.ApiPrefix, engine.cfg.HealthCheckPath)
		readyCheckPath  = path.Join(engine.cfg.ApiPrefix, "/node/ready")
		monitorPath     = path.Join(engine.cfg.ApiPrefix, engine.cfg.MonitorPath)
		metricsPath     = path.Join(engine.cfg.ApiPrefix, engine.cfg.MetricsPath)
	)

	ignoredPath := []string{
		healthCheckPath,
		readyCheckPath,
		monitorPath,
		metricsPath,
	}

	if engine.cfg.EnableMetrics {
		prometheus := fiberprometheus.New(engine.cfg.AppName)
		prometheus.RegisterAt(engine.app, metricsPath)
		engine.app.Use(prometheus.Middleware)
	}

	engine.app.Use(
		reporter.NewReporter(engine.logger, ignoredPath...), //日志
		recovery.New(),                         //恢复
		cors.New(cors.ConfigDefault),           //跨域
		requestid.New(requestid.ConfigDefault), //请求id
	)

	if engine.cfg.EnableLimiter {
		engine.app.Use(limiter.NewLimiter(
			limiter.WithIgnoreLocalhost(true),
			limiter.WithLimitType(engine.cfg.LimitType),
			limiter.WithMax(engine.cfg.MaxReqInSec),
			limiter.WithDuration(time.Second),
			limiter.WithIgnoreUrls(ignoredPath...),
		))
	}

	if engine.cfg.EnableMonitor {
		engine.Group(engine.cfg.MonitorPath, func(router FiberRouter) {
			router.Add(fiber.MethodGet, "", monitor.New(monitor.Config{
				Title:   engine.cfg.AppName + " Monitor",
				Refresh: 3 * time.Second,
				APIOnly: engine.cfg.MonitorApiOnly,
				Next:    nil,
			}))
		})
	}

	if engine.cfg.EnableHealthCheck {
		engine.Group(engine.cfg.HealthCheckPath, func(router FiberRouter) {
			router.Register(fiber.MethodGet, "", func(req *request.FiberReq) *response.FiberResp {
				return response.Ok(req.Ctx)
			})
		})
	}

	return engine
}
