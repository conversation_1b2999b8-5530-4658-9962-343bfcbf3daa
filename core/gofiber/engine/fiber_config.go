package engine

import (
	"github.com/gofiber/fiber/v2"
	"github.com/mcuadros/go-defaults"

	"git.haier.net/devops/hcms-task-center/core/gofiber/middleware/limiter"
)

type ConfigOption func(config *Config)

type Config struct {
	BindAddr  string
	Port      int `default:"8000"`
	ApiPrefix string

	EnableHealthCheck bool
	HealthCheckPath   string `default:"/health"`

	EnableMonitor  bool
	MonitorPath    string `default:"/monitor"`
	MonitorApiOnly bool

	EnableMetrics bool
	MetricsPath   string `default:"/monitor/metrics"`

	EnableLimiter bool
	MaxReqInSec   int               `default:"100"`
	LimitType     limiter.LimitType `default:"token"`

	*fiber.Config
}

func NewFiberConfig(options ...ConfigOption) *Config {
	config := &Config{
		Config: &fiber.Config{},
	}
	defaults.SetDefaults(config)
	for _, option := range options {
		option(config)
	}

	return config
}
