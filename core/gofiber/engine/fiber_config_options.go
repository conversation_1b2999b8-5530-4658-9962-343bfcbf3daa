// Code generated by gofiber/engine/generate.go DO NOT EDIT.
package engine

import (
	"time"

	"github.com/gofiber/fiber/v2"

	"git.haier.net/devops/hcms-task-center/core/gofiber/middleware/limiter"
)

// WithBindAddr sets the BindAddr field of the ConfigOption.
func WithBindAddr(v string) ConfigOption {
	return func(config *Config) {
		config.BindAddr = v
	}
}

// WithPort sets the Port field of the ConfigOption.
func WithPort(v int) ConfigOption {
	return func(config *Config) {
		config.Port = v
	}
}

// WithApiPrefix sets the ApiPrefix field of the ConfigOption.
func WithApiPrefix(v string) ConfigOption {
	return func(config *Config) {
		config.ApiPrefix = v
	}
}

// WithEnableHealthCheck sets the EnableHealthCheck field of the ConfigOption.
func WithEnableHealthCheck() ConfigOption {
	return func(config *Config) {
		config.EnableHealthCheck = true
	}
}

// WithHealthCheckPath sets the HealthCheckPath field of the ConfigOption.
func WithHealthCheckPath(v string) ConfigOption {
	return func(config *Config) {
		config.HealthCheckPath = v
	}
}

// WithEnableMonitor sets the EnableMonitor field of the ConfigOption.
func WithEnableMonitor() ConfigOption {
	return func(config *Config) {
		config.EnableMonitor = true
	}
}

// WithMonitorPath sets the MonitorPath field of the ConfigOption.
func WithMonitorPath(v string) ConfigOption {
	return func(config *Config) {
		config.MonitorPath = v
	}
}

// WithMonitorApiOnly sets the MonitorApiOnly field of the ConfigOption.
func WithMonitorApiOnly() ConfigOption {
	return func(config *Config) {
		config.MonitorApiOnly = true
	}
}

// WithEnableMetrics sets the EnableMetrics field of the ConfigOption.
func WithEnableMetrics() ConfigOption {
	return func(config *Config) {
		config.EnableMetrics = true
	}
}

// WithMetricsPath sets the MetricsPath field of the ConfigOption.
func WithMetricsPath(v string) ConfigOption {
	return func(config *Config) {
		config.MetricsPath = v
	}
}

// WithEnableLimiter sets the EnableLimiter field of the ConfigOption.
func WithEnableLimiter() ConfigOption {
	return func(config *Config) {
		config.EnableLimiter = true
	}
}

// WithMaxReqInSec sets the MaxReqInSec field of the ConfigOption.
func WithMaxReqInSec(v int) ConfigOption {
	return func(config *Config) {
		config.MaxReqInSec = v
	}
}

// WithLimitType sets the LimitType field of the ConfigOption.
func WithLimitType(v limiter.LimitType) ConfigOption {
	return func(config *Config) {
		config.LimitType = v
	}
}

// WithPrefork sets the Prefork field of the ConfigOption.
func WithPrefork() ConfigOption {
	return func(config *Config) {
		config.Prefork = true
	}
}

// WithServerHeader sets the ServerHeader field of the ConfigOption.
func WithServerHeader(v string) ConfigOption {
	return func(config *Config) {
		config.ServerHeader = v
	}
}

// WithStrictRouting sets the StrictRouting field of the ConfigOption.
func WithStrictRouting() ConfigOption {
	return func(config *Config) {
		config.StrictRouting = true
	}
}

// WithCaseSensitive sets the CaseSensitive field of the ConfigOption.
func WithCaseSensitive() ConfigOption {
	return func(config *Config) {
		config.CaseSensitive = true
	}
}

// WithImmutable sets the Immutable field of the ConfigOption.
func WithImmutable() ConfigOption {
	return func(config *Config) {
		config.Immutable = true
	}
}

// WithUnescapePath sets the UnescapePath field of the ConfigOption.
func WithUnescapePath() ConfigOption {
	return func(config *Config) {
		config.UnescapePath = true
	}
}

// WithETag sets the ETag field of the ConfigOption.
func WithETag() ConfigOption {
	return func(config *Config) {
		config.ETag = true
	}
}

// WithBodyLimit sets the BodyLimit field of the ConfigOption.
func WithBodyLimit(v int) ConfigOption {
	return func(config *Config) {
		config.BodyLimit = v
	}
}

// WithConcurrency sets the Concurrency field of the ConfigOption.
func WithConcurrency(v int) ConfigOption {
	return func(config *Config) {
		config.Concurrency = v
	}
}

// WithViews sets the Views field of the ConfigOption.
func WithViews(v fiber.Views) ConfigOption {
	return func(config *Config) {
		config.Views = v
	}
}

// WithViewsLayout sets the ViewsLayout field of the ConfigOption.
func WithViewsLayout(v string) ConfigOption {
	return func(config *Config) {
		config.ViewsLayout = v
	}
}

// WithPassLocalsToViews sets the PassLocalsToViews field of the ConfigOption.
func WithPassLocalsToViews() ConfigOption {
	return func(config *Config) {
		config.PassLocalsToViews = true
	}
}

// WithReadTimeout sets the ReadTimeout field of the ConfigOption.
func WithReadTimeout(v time.Duration) ConfigOption {
	return func(config *Config) {
		config.ReadTimeout = v
	}
}

// WithWriteTimeout sets the WriteTimeout field of the ConfigOption.
func WithWriteTimeout(v time.Duration) ConfigOption {
	return func(config *Config) {
		config.WriteTimeout = v
	}
}

// WithIdleTimeout sets the IdleTimeout field of the ConfigOption.
func WithIdleTimeout(v time.Duration) ConfigOption {
	return func(config *Config) {
		config.IdleTimeout = v
	}
}

// WithReadBufferSize sets the ReadBufferSize field of the ConfigOption.
func WithReadBufferSize(v int) ConfigOption {
	return func(config *Config) {
		config.ReadBufferSize = v
	}
}

// WithWriteBufferSize sets the WriteBufferSize field of the ConfigOption.
func WithWriteBufferSize(v int) ConfigOption {
	return func(config *Config) {
		config.WriteBufferSize = v
	}
}

// WithCompressedFileSuffix sets the CompressedFileSuffix field of the ConfigOption.
func WithCompressedFileSuffix(v string) ConfigOption {
	return func(config *Config) {
		config.CompressedFileSuffix = v
	}
}

// WithProxyHeader sets the ProxyHeader field of the ConfigOption.
func WithProxyHeader(v string) ConfigOption {
	return func(config *Config) {
		config.ProxyHeader = v
	}
}

// WithGETOnly sets the GETOnly field of the ConfigOption.
func WithGETOnly() ConfigOption {
	return func(config *Config) {
		config.GETOnly = true
	}
}

// WithDisableKeepalive sets the DisableKeepalive field of the ConfigOption.
func WithDisableKeepalive() ConfigOption {
	return func(config *Config) {
		config.DisableKeepalive = true
	}
}

// WithDisableDefaultDate sets the DisableDefaultDate field of the ConfigOption.
func WithDisableDefaultDate() ConfigOption {
	return func(config *Config) {
		config.DisableDefaultDate = true
	}
}

// WithDisableDefaultContentType sets the DisableDefaultContentType field of the ConfigOption.
func WithDisableDefaultContentType() ConfigOption {
	return func(config *Config) {
		config.DisableDefaultContentType = true
	}
}

// WithDisableHeaderNormalizing sets the DisableHeaderNormalizing field of the ConfigOption.
func WithDisableHeaderNormalizing() ConfigOption {
	return func(config *Config) {
		config.DisableHeaderNormalizing = true
	}
}

// WithDisableStartupMessage sets the DisableStartupMessage field of the ConfigOption.
func WithDisableStartupMessage() ConfigOption {
	return func(config *Config) {
		config.DisableStartupMessage = true
	}
}

// WithAppName sets the AppName field of the ConfigOption.
func WithAppName(v string) ConfigOption {
	return func(config *Config) {
		config.AppName = v
	}
}

// WithStreamRequestBody sets the StreamRequestBody field of the ConfigOption.
func WithStreamRequestBody() ConfigOption {
	return func(config *Config) {
		config.StreamRequestBody = true
	}
}

// WithDisablePreParseMultipartForm sets the DisablePreParseMultipartForm field of the ConfigOption.
func WithDisablePreParseMultipartForm() ConfigOption {
	return func(config *Config) {
		config.DisablePreParseMultipartForm = true
	}
}

// WithReduceMemoryUsage sets the ReduceMemoryUsage field of the ConfigOption.
func WithReduceMemoryUsage() ConfigOption {
	return func(config *Config) {
		config.ReduceMemoryUsage = true
	}
}

// WithNetwork sets the Network field of the ConfigOption.
func WithNetwork(v string) ConfigOption {
	return func(config *Config) {
		config.Network = v
	}
}

// WithEnableTrustedProxyCheck sets the EnableTrustedProxyCheck field of the ConfigOption.
func WithEnableTrustedProxyCheck() ConfigOption {
	return func(config *Config) {
		config.EnableTrustedProxyCheck = true
	}
}

// WithTrustedProxies sets the TrustedProxies field of the ConfigOption.
func WithTrustedProxies(v []string) ConfigOption {
	return func(config *Config) {
		config.TrustedProxies = v
	}
}

// WithEnableIPValidation sets the EnableIPValidation field of the ConfigOption.
func WithEnableIPValidation() ConfigOption {
	return func(config *Config) {
		config.EnableIPValidation = true
	}
}

// WithEnablePrintRoutes sets the EnablePrintRoutes field of the ConfigOption.
func WithEnablePrintRoutes() ConfigOption {
	return func(config *Config) {
		config.EnablePrintRoutes = true
	}
}

// WithColorScheme sets the ColorScheme field of the ConfigOption.
func WithColorScheme(v fiber.Colors) ConfigOption {
	return func(config *Config) {
		config.ColorScheme = v
	}
}

// WithRequestMethods sets the RequestMethods field of the ConfigOption.
func WithRequestMethods(v []string) ConfigOption {
	return func(config *Config) {
		config.RequestMethods = v
	}
}
