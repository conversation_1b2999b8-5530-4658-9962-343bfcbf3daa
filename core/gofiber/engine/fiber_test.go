package engine

import (
	"io"
	"net/http/httptest"
	"testing"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/utils"

	"git.haier.net/devops/hcms-task-center/core/gofiber/request"
	"git.haier.net/devops/hcms-task-center/core/gofiber/response"
)

func TestNewFiber(t *testing.T) {
	app := NewFiber(
		WithEnableMonitor(),
		WithMonitorApiOnly(),
		WithEnablePrintRoutes(),
		WithEnableHealthCheck(),
		WithApiPrefix("/api/v1"),
	)

	go func() {
		for {
			if app.HttpReady() {
				app.Shutdown()
			}
		}
	}()

	if err := app.Run(); err != nil {
		t.Error(err)
	}
}

func TestFiberEngine_Register(t *testing.T) {
	t.<PERSON>()
	app := NewFiber(
		WithEnablePrintRoutes(),
	)

	app.Group("/api", func(router FiberRouter) {
		router.Group("/v1", func(router FiberRouter) {
			router.Register("GET", "/test", func(req *request.FiberReq) *response.FiberResp {
				return response.Ok(req.Ctx, "hello world")
			})
		})
	})

	req := httptest.NewRequest(fiber.MethodGet, "/api/v1/test", nil)
	req.Header.Set(fiber.HeaderXRequestID, "**********")
	resp, err := app.App().Test(req)
	utils.AssertEqual(t, nil, err)
	data, err := io.ReadAll(resp.Body)
	utils.AssertEqual(t, nil, err)
	respData := new(response.BaseResponse)
	err = json.Unmarshal(data, respData)
	utils.AssertEqual(t, nil, err)
	utils.AssertEqual(t, fiber.StatusOK, resp.StatusCode)
	utils.AssertEqual(t, "**********", resp.Header.Get(fiber.HeaderXRequestID))
	utils.AssertEqual(t, "**********", respData.RequestId)
}
