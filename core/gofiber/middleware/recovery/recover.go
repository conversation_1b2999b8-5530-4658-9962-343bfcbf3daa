package recovery

import (
	"fmt"
	"os"
	"runtime/debug"

	"github.com/gofiber/fiber/v2"

	"git.haier.net/devops/hcms-task-center/core/gofiber/response"
)

func defaultStackTraceHandler(_ *fiber.Ctx, e interface{}) {
	_, _ = os.Stderr.WriteString(fmt.Sprintf("panic: %v\n%s\n", e, debug.Stack())) //nolint:errcheck // This will never fail
}

// New creates a new middleware handler
func New() fiber.Handler {
	return func(c *fiber.Ctx) error {
		defer func() {
			if r := recover(); r != nil {
				if err, ok := r.(error); ok {
					_ = response.Fail(c, response.InternalErr, err.Error()).Result()
				} else {
					_ = response.Fail(c, response.InternalErr, fmt.Sprintf("%v", r)).Result()
				}
				defaultStackTraceHandler(c, r)
				return
			}
		}()
		return c.Next()
	}
}
