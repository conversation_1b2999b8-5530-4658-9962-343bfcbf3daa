package reporter

import (
	"fmt"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
	"github.com/gofiber/fiber/v2"

	"git.haier.net/devops/hcms-task-center/core/gofiber/futils"
)

type Reporter struct {
	logger     *log.Logger
	ignoreUrls []string
}

func NewReporter(logger *log.Logger, ignoreUrls ...string) fiber.Handler {
	return (&Reporter{
		logger:     logger,
		ignoreUrls: ignoreUrls,
	}).ReportRequest
}

func (r *Reporter) ReportRequest(ctx *fiber.Ctx) error {
	now := time.Now()
	for _, url := range r.ignoreUrls {
		if ctx.Path() == url {
			return ctx.Next()
		}
	}

	durationStart := time.Now()
	err := ctx.Next()
	duration := time.Since(durationStart)
	r.logger.Infoln(fmt.Sprintf("[%s] %s - %s [%d] %s - %s (%s)",
		futils.RequestID(ctx),
		ctx.IP(),
		ctx.Method(),
		ctx.Response().StatusCode(),
		ctx.Request().RequestURI(),
		duration,
		time.Since(now),
	))
	return err
}
