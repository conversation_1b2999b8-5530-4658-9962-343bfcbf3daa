package limiter

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/limiter"
)

type Config func(*Limiter)

type LimitType string

const (
	LimitByIP    LimitType = "ip"
	LimitByToken LimitType = "token"
)

type Limiter struct {
	Max             int
	IgnoreLocalhost bool
	LimitType       LimitType
	Duration        time.Duration
	IgnoreUrls      []string
}

func NewLimiter(c ...Config) fiber.Handler {
	l := &Limiter{
		Max:      5 * 60,
		Duration: 1 * time.Minute,
	}
	for _, v := range c {
		v(l)
	}

	var keyGenerator func(*fiber.Ctx) string
	switch l.LimitType {
	case LimitByToken:
		keyGenerator = func(c *fiber.Ctx) string {
			if token := c.Get("X-API-TOKEN"); token != "" {
				return token
			}
			return getRequestIp(c)
		}
	default:
		keyGenerator = func(c *fiber.Ctx) string {
			return getRequestIp(c)
		}
	}

	return limiter.New(limiter.Config{
		Next: func(c *fiber.Ctx) bool {
			for _, url := range l.IgnoreUrls {
				if c.Path() == url {
					return true
				}
			}
			return l.IgnoreLocalhost && c.IP() == "127.0.0.1"
		},
		Max:          l.Max,
		Expiration:   l.Duration,
		KeyGenerator: keyGenerator,
		LimitReached: func(ctx *fiber.Ctx) error {
			return ctx.SendStatus(fiber.StatusTooManyRequests)
		},
	})
}

func getRequestIp(c *fiber.Ctx) string {
	if ip := c.Get("x-forwarded-for"); ip != "" {
		return ip
	}
	return c.IP()
}

func WithMax(max int) Config {
	return func(l *Limiter) {
		l.Max = max
	}
}

func WithDuration(duration time.Duration) Config {
	return func(l *Limiter) {
		l.Duration = duration
	}
}

func WithLimitType(limitType LimitType) Config {
	return func(l *Limiter) {
		l.LimitType = limitType
	}
}

func WithIgnoreLocalhost(ignoreLocalhost bool) Config {
	return func(l *Limiter) {
		l.IgnoreLocalhost = ignoreLocalhost
	}
}

func WithIgnoreUrls(ignoreUrls ...string) Config {
	return func(l *Limiter) {
		l.IgnoreUrls = ignoreUrls
	}
}
