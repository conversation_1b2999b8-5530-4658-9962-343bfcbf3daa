package request

import (
	"context"

	myCtx "git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/utils"

	"git.haier.net/devops/hcms-task-center/core/gofiber/futils"
)

type FiberReq struct {
	*fiber.Ctx
}

func (req *FiberReq) FiberCtx() *fiber.Ctx {
	return req.Ctx
}

func (req *FiberReq) Context() context.Context {
	return myCtx.NewContextWithTraceId(futils.RequestID(req.FiberCtx()))
}

func (req *FiberReq) FiberParams(key string, defaultValue ...string) string {
	return utils.ImmutableString(req.FiberCtx().Params(key, defaultValue...))
}

func (req *FiberReq) FiberQuery(key string, defaultValue ...string) string {
	return utils.ImmutableString(req.FiberCtx().Query(key, defaultValue...))
}

func (req *FiberReq) FiberQueries() map[string]string {
	return req.FiberCtx().Queries()
}
