package config

import "git.haier.net/devops/ops-golang-common/sdk/zabbix"

type ZabbixConfig struct {
	DefaultCredential string `default:"default"`
	Config            map[string]*zabbix.Credential
}

func (c ZabbixConfig) GetDefaultCredential() *zabbix.Credential {
	return c.Config[c.DefaultCredential]
}

func (c ZabbixConfig) GetCredential(key string) *zabbix.Credential {
	return c.Config[key]
}

func (c ZabbixConfig) GetCredentials() []*zabbix.Credential {
	var credentials []*zabbix.Credential
	for _, credential := range c.Config {
		credentials = append(credentials, credential)
	}
	return credentials
}
