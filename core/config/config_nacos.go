package config

type NacosConfig struct {
	Schema      string `yaml:"schema" default:"http"`
	Host        string `yaml:"host"`
	Port        uint64 `yaml:"port"`
	NameSpaceId string `yaml:"namespace_id"`
	GroupPrefix string `yaml:"group_prefix"`
}

func (c NacosConfig) GetSchema() string {
	return c.Schema
}

func (c NacosConfig) GetHost() string {
	return c.Host
}

func (c NacosConfig) GetPort() uint64 {
	return c.Port
}

func (c NacosConfig) GetNameSpaceId() string {
	return c.NameSpaceId
}

func (c NacosConfig) GetGroupPrefix() string {
	return c.GroupPrefix
}
