package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
	"github.com/mcuadros/go-defaults"
	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v3"
)

var Version = "unknown"

// Config 系统配置
type Config struct {
	Env      string `default:"dev"`
	ConfPath string `default:"etc"`

	Mode               string        `yaml:"mode" default:"release"`
	ServerPort         int           `yaml:"server-port,omitempty" default:"8000"`
	RaftPort           int           `yaml:"raft-port" default:"9000"`
	ServerReadTimeout  time.Duration `yaml:"server-read-timout,omitempty" default:"1m"`
	ServerWriteTimeout time.Duration `yaml:"server-write-timeout,omitempty"  default:"1m"`
	MaxRequestPerSec   int           `yaml:"server-max-request-per-sec,omitempty" default:"50"`
	BatchSize          int           `yaml:"server-batch-size,omitempty" default:"4"`
	LogLevel           string        `yaml:"server-log-level,omitempty" default:"info"`
	LogPrentHeader     bool          `yaml:"server-log-print-header,omitempty" default:"false"`
	LogPrentBody       bool          `yaml:"server-log-print-body,omitempty" default:"false"`
	LogPrentResp       bool          `yaml:"server-log-print-resp,omitempty" default:"false"`
	DryRun             bool          `yaml:"dry-run,omitempty" default:"false"`
	DisableDispatcher  bool          `yaml:"disable-dispatcher,omitempty" default:"false"`
	SCode              string        `yaml:"s-code,omitempty" default:"S02166"`
	NotifyTemplateId   string        `yaml:"notify-template-id,omitempty"`

	// API 配置
	ApiConfig `yaml:"api"`
	// 账号资源配置
	AliyunAccountMapping map[string]string `yaml:"aliyun-account-mapping"`

	// 数据库配置
	DBConfig     `yaml:"database"`
	ZabbixConfig `yaml:"zabbix"`
	// 缓存配置
	RedisConfig `yaml:"redis"`
	// 飞书配置
	FeiShuConfig `yaml:"feishu"`
	// nacos配置
	NacosConfig `yaml:"nacos"`
	// 通用配置
	CommonConf `yaml:"common"`
	// 阿里hbr备份
	AliConfig `yaml:"ali"`
	// NCE-Campus配置
	NCECampusConfig *NCECampusConfig `yaml:"nce-campus,omitempty"`
	// NEO-Sight配置
	NEOSightConfig *NEOSightConfig `yaml:"neo-sight,omitempty"`
}

func (c Config) configPathFile() string {
	if !filepath.IsAbs(c.ConfPath) {
		prefix, _ := os.Getwd()
		c.ConfPath = filepath.Join(prefix, c.ConfPath)
	}
	return filepath.Join(c.ConfPath, fmt.Sprintf("tc.%s.yaml", c.Env))
}

func (c Config) TestEnv() string {
	e := strings.ToLower(strings.TrimSpace(c.Env))
	switch e {
	case "dev", "test", "local":
		return "TEST"
	default:
		return "PROD"
	}
}

func (c Config) IsTest() bool {
	e := strings.ToLower(strings.TrimSpace(c.Env))
	switch e {
	case "dev", "test", "local":
		return true
	default:
		return false
	}
}

// GetLogLevel 获取日志级别
func (c Config) GetLogLevel() logrus.Level {
	switch strings.ToLower(c.LogLevel) {
	case "debug":
		return logrus.DebugLevel
	case "info":
		return logrus.InfoLevel
	case "warn":
		return logrus.WarnLevel
	case "error":
		return logrus.ErrorLevel
	default:
		return logrus.InfoLevel
	}
}

// GetMode 获取运行模式
func (c Config) GetMode() string {
	return c.Mode
}

func NewConfig() *Config {
	c := &Config{
		Env:      os.Getenv(HcmsEnv),
		ConfPath: os.Getenv(HcmsConfigPath),
	}

	// 初始化环境信息
	if c.Env != "" {
		log.Systemf("==> $%s: %s", HcmsEnv, c.Env)
	}
	// 初始化配置文件
	if c.ConfPath != "" {
		log.Systemf("==> $%s: %s", HcmsConfigPath, c.ConfPath)
	}

	defaults.SetDefaults(c)
	log.Systemf("loading config: %s", c.configPathFile())
	bytes, err := os.ReadFile(c.configPathFile())
	if err != nil {
		log.SystemErrorf("load config failed: %s", err)
		panic(err)
	}
	if err := yaml.Unmarshal(bytes, c); err != nil {
		log.SystemErrorf("unmarshal config failed: %s", err)
		panic(err)
	}

	// 设置默认值
	for _, ds := range c.Store {
		defaults.SetDefaults(ds)
	}
	return c
}
