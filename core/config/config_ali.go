package config

func getDefAliConfig() *AliConfig {
	return &AliConfig{
		Account: "hr690n",
		Region:  "cn-qingdao",
		DMS: &DMSProductConfig{
			UserPrefix: []string{"dms-public/"},
		},
		RDS: &RDSProductConfig{},
		HDM: &HDMProductConfig{},
		HBR: &HBRProductConfig{},
	}
}

type HDMProductConfig struct {
	Endpoint string `koanf:"endpoint"`
}

type DMSProductConfig struct {
	Endpoint   string   `koanf:"endpoint"`
	UserPrefix []string `koanf:"user-prefix"`
}

type RDSProductConfig struct {
	Endpoint string   `koanf:"endpoint"`
	Regions  []string `koanf:"regions"`
}

type HBRProductConfig struct {
	AK       string   `koanf:"ak"`
	SK       string   `koanf:"sk"`
	Endpoint string   `koanf:"endpoint"`
	Regions  []string `koanf:"regions"`
	Engines  []string `koanf:"engines"`
}

type AliConfig struct {
	Account string            `koanf:"account"`
	Region  string            `koanf:"region"`
	Regions []string          `koanf:"regions"`
	DMS     *DMSProductConfig `koanf:"dms"`
	RDS     *RDSProductConfig `koanf:"rds"`
	HDM     *HDMProductConfig `koanf:"hdm"`
	HBR     *HBRProductConfig `koanf:"hbr"`

	allowRegionMap map[string]string
}

func (c AliConfig) IsRegionAllowed(region string) bool {
	if c.allowRegionMap == nil {
		c.allowRegionMap = map[string]string{}
		c.allowRegionMap[c.Region] = c.Region
		for _, r := range c.Regions {
			c.allowRegionMap[r] = r
		}
	}

	_, ok := c.allowRegionMap[region]
	return ok
}
