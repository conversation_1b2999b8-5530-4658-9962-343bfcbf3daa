package config

// NEOSightConfig NEO-Sight配置信息
type NEOSightConfig struct {
	Username    string `yaml:"username"`                          // API用户名
	Password    string `yaml:"password"`                          // API密码
	URL         string `yaml:"url,omitempty"`                     // API基础URL，默认为https://10.150.1.8:18002
	Timeout     int    `yaml:"timeout,omitempty" default:"30"`    // 请求超时时间（秒）
	Insecure    bool   `yaml:"insecure" default:"true"`           // 是否跳过SSL证书验证
	Concurrency int    `yaml:"concurrency,omitempty" default:"5"` // 并发获取设备接口的数量
}

// GetBaseURL 获取API基础URL
func (c *NEOSightConfig) GetBaseURL() string {
	if c.URL == "" {
		return "https://10.150.1.8:18002"
	}
	return c.URL
}
