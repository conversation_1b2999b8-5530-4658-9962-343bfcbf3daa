package config

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/go-redsync/redsync/v4/redis"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	goRedisLib "github.com/redis/go-redis/v9"
)

var (
	clientMap    = make(map[string]redis.Pool)
	rawClientMap = make(map[string]*goRedisLib.Client)
	mtx          = new(sync.Mutex)
)

type RedisConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port" default:"6379"`
	Password string `json:"password"`
	DB       int    `json:"db"`
	PoolSize int    `json:"pool-size" default:"10"`
}

func (c RedisConfig) RedisPool() redis.Pool {
	mtx.Lock()
	defer mtx.Unlock()

	key := fmt.Sprintf("%s:%d-%d", c.<PERSON>, c.<PERSON>, c.DB)
	if clientMap[key] != nil {
		return clientMap[key]
	}

	cli := c.newClient()

	rawClientMap[key] = cli
	clientMap[key] = goredis.NewPool(cli)
	return clientMap[key]
}

func (c RedisConfig) RedisConn() (redis.Conn, error) {
	return c.RedisPool().Get(context.TODO())
}

func (c RedisConfig) Redis() *goRedisLib.Client {
	mtx.Lock()
	defer mtx.Unlock()

	key := fmt.Sprintf("%s:%d-%d", c.Host, c.Port, c.DB)
	if rawClientMap[key] != nil {
		return rawClientMap[key]
	}

	rawClientMap[key] = c.newClient()
	return rawClientMap[key]
}

func (c RedisConfig) RedisRawConn() *goRedisLib.Client {
	return c.Redis()
}

func (c RedisConfig) newClient() *goRedisLib.Client {
	return goRedisLib.NewClient(&goRedisLib.Options{
		Addr:     c.Host + ":" + strconv.Itoa(c.Port),
		Password: c.Password,
		DB:       c.DB,
		PoolSize: c.PoolSize,

		MaxRetries:      3,                      // 最大重试次数（含首次请求）
		MinRetryBackoff: 100 * time.Millisecond, // 最小重试间隔
		MaxRetryBackoff: 1 * time.Second,        // 最大重试间隔
	})
}
