package config

import (
	"path"
)

type ApiConfig map[string]Application

func (c ApiConfig) GetApp(name string) Application {
	return c[name]
}

type Application map[string]string

func (a Application) GetUrl(name string) string {
	domain := a["domain"]
	schema := "http"
	if _, ok := a["schema"]; ok {
		schema = a["schema"]
	}
	return schema + "://" + path.Join(domain, a[name])
}

func (a Application) GetToken() string {
	if t, ok := a["token"]; ok {
		return t
	}
	return ""
}

func (a Application) GetAppId() string {
	if appId, ok := a["app-id"]; ok {
		return appId
	}

	return ""
}

func (a Application) GetSecretKey() string {
	if secretKey, ok := a["secret-key"]; ok {
		return secretKey
	}

	return ""
}
