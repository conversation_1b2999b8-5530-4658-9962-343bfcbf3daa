package config

type CommonConf map[string]interface{}

func (c CommonConf) GetString(key string) (string, bool) {
	return getValue[string](key, c)
}

func (c CommonConf) GetInt(key string) (int, bool) {
	return getValue[int](key, c)
}

func (c CommonConf) GetStringSlice(key string) ([]string, bool) {
	return getValue[[]string](key, c)
}

func (c CommonConf) GetIntSlice(key string) ([]int, bool) {
	return getValue[[]int](key, c)
}

func getValue[T any](key string, store map[string]interface{}) (T, bool) {
	if v, ok := store[key]; ok {
		if n, ok := v.(T); ok {
			return n, true
		}
	}
	return *new(T), false
}
