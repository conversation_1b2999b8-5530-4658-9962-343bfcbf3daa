package config

import (
	"git.haier.net/devops/ops-golang-common/models/models"
)

type DBConfig struct {
	EnableMigrate    bool                          `yaml:"enable-migrate"`
	DefaultStoreName string                        `yaml:"default-name" default:"hcms"`
	Store            map[string]*models.Datasource `yaml:"store"`
}

func (c DBConfig) GetStore(key string) *models.Datasource {
	if c, ok := c.Store[key]; ok {
		return c
	}
	return nil
}

func (c DBConfig) GetDefaultStore() *models.Datasource {
	return c.GetStore(c.DefaultStoreName)
}
