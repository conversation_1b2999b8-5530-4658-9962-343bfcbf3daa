package raftserver

import (
	"errors"
	"fmt"
	"path"
	"time"

	"github.com/hashicorp/raft"
)

// boot 启动Raft服务
func (n *Raft) boot() {
	if n.Server() == nil {
		panic(errors.New("raft server is nil"))
	}

	if n.raftBootstrapCluster != "" {
		n.bootAsClusterVoter()
	} else {
		n.bootAsSingleCluster()
	}
}

// bootAsSingleCluster 做为单节点集群启动
func (n *Raft) bootAsSingleCluster() {
	n.Info("raft server bootstrap as single cluster")
	err := n.Server().BootstrapCluster(raft.Configuration{
		Servers: []raft.Server{
			{
				ID:       n.raftServerId,
				Address:  n.raftTransport.LocalAddr(),
				Suffrage: raft.Voter,
			},
		},
	}).Error()

	if err != nil {
		n.Errorf("raft server bootstrap cluster failed: %s", err)
		panic(err)
	}
}

// bootAsClusterVoter 做为集群投票者启动
func (n *Raft) bootAsClusterVoter() {
	n.Infof("raft server bootstrap cluster: %s", n.raftBootstrapCluster)
	maxRetryTimes := 3
	for i := 0; i < maxRetryTimes; i++ {
		if err := n.joinCluster(); err != nil {
			n.Errorf("join cluster [%s] failed: %s", n.raftBootstrapCluster, err)
			n.Warnf("raft server join cluster failed, retry: %d", i+1)
			time.Sleep(time.Second)
			continue
		}
		n.Infof("raft server join cluster success")
		return
	}
}

func (n *Raft) joinCluster() error {
	obj := NodeActionReq{
		NodeId:   n.raftServerId,
		NodeAddr: n.raftTransport.LocalAddr(),
	}

	resp, err := n.httpClient.R().SetBody(obj).
		Post(n.raftBootstrapCluster + path.Join("/", n.raftApiJoinCluster))
	if err != nil {
		n.Errorf("join cluster failed: %s", err)
		return err
	}

	if resp.StatusCode != 200 {
		n.Errorf("join cluster failed: %s", resp.String())
		return fmt.Errorf("join cluster failed: %s", resp.String())
	}

	return nil
}

func (n *Raft) leaveCluster() error {
	if n.IsLeader() {
		return n.changeLeader()
	}

	obj := NodeActionReq{
		NodeId:   n.raftServerId,
		NodeAddr: n.raftTransport.LocalAddr(),
	}

	resp, err := n.httpClient.R().SetBody(obj).
		Delete(n.raftBootstrapCluster + path.Join("/", n.raftApiJoinCluster))
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return fmt.Errorf("leave cluster failed: %s", resp.String())
	}
	return nil
}

func (n *Raft) changeLeader() error {
	if !n.IsLeader() {
		return nil
	}
	if len(n.Server().GetConfiguration().Configuration().Servers) <= 1 {
		return nil
	}
	return n.Server().LeadershipTransfer().Error()
}
