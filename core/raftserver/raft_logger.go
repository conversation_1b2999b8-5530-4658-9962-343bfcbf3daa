package raftserver

func (n *Raft) Infof(fmt string, args ...interface{}) {
	n.logger.Infof(n.ctx, fmt, args...)
}

func (n *Raft) Debugf(fmt string, args ...interface{}) {
	n.logger.Debugf(n.ctx, fmt, args...)
}

func (n *Raft) Errorf(fmt string, args ...interface{}) {
	n.logger.Errorf(n.ctx, fmt, args...)
}

func (n *Raft) Warnf(fmt string, args ...interface{}) {
	n.logger.Warnf(n.ctx, fmt, args...)
}

func (n *Raft) Info(args ...interface{}) {
	n.logger.Info(n.ctx, args...)
}

func (n *Raft) Debug(args ...interface{}) {
	n.logger.Debug(n.ctx, args...)
}

func (n *Raft) Error(args ...interface{}) {
	n.logger.Error(n.ctx, args...)
}

func (n *Raft) Warn(args ...interface{}) {
	n.logger.Warn(n.ctx, args...)
}
