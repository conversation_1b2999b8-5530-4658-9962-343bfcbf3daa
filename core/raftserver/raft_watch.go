package raftserver

import (
	"os"
	"os/signal"
	"syscall"

	"git.haier.net/devops/hcms-task-center/core/tools"
)

// watchSystemSignal 监听系统信号
func (n *Raft) watchSystemSignal() {
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)
	defer signal.Stop(signals)
	for n.IsRunning() {
		select {
		case sig := <-signals:
			n.Infof("receive system signal: %v", sig)
			goto Exit
		default:
			tools.StandardSleep()
		}
	}

Exit:
	n.Infof("system signal watching thread quit.")
	n.Shutdown()
}

// watchRaftLeaderChange 监听raft leader变化
func (n *Raft) watchRaftLeaderChange() {
	n.Infof("start thread to watch raft leader change...")
	for n.IsRunning() {
		if n.Server() == nil {
			tools.StandardSleep()
			continue
		}
		select {
		case leader := <-n.Server().LeaderCh():
			if !n.IsRunning() {
				goto Exit
			}

			if leader {
				n.Warn("change to leader.")
				n.handlePromoteToLeader()
				continue
			}

			n.Warn("lost leadership.")
			n.handleLostLeadership()
		default:
			tools.StandardSleep()
		}
	}

Exit:
	n.Infof("leader change watching thread quit.")
	return
}

// watchRaftStateChange 监听raft状态变化
func (n *Raft) watchRaftStateChange() {
	n.Infof("start thread to watch raft state change...")
	lastState := "Unknown"
	this := "Unknown"
	for n.IsRunning() {
		if n.Server() == nil {
			continue
		}
		this = n.Server().State().String()
		if this != lastState {
			n.Warnf("node state change: [%s] ==> [%s]", lastState, this)
			lastState = this
		}
		tools.StandardSleep()
	}
	n.Infof("raft state change watching thread quit.")
}

// watchServerShutdown 监听服务关闭信号
func (n *Raft) watchServerShutdown() {
	n.Infof("start thread to watch server shutdown signal...")
	for {
		select {
		case <-n.ctx.Done():
			n.raftServerShutdown()
			goto Exit
		}
	}

Exit:
	n.Infof("server shutdown watching thread quit.")
	n.Shutdown()
}
