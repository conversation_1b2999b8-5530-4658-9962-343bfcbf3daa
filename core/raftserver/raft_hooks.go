package raftserver

import (
	"git.haier.net/devops/hcms-task-center/biz/notice"
	"git.haier.net/devops/hcms-task-center/core/config"
)

// handlePromoteToLeader handles the promote to leader event.
func (n *Raft) handlePromoteToLeader() {
	notice.SendInfoMessage("集群切换", "节点[**"+n.NodeName()+"**]成为集群[**"+config.Global().Env+"**]主节点")
}

// handleLostLeadership handles the lost leadership event.
func (n *Raft) handleLostLeadership() {
	notice.SendWarnMessage("集群切换", "节点[**"+n.NodeName()+"**]失去集群[**"+config.Global().Env+"**]领导权")
}

func (n *Raft) NodeName() string {
	if n.podName == "" {
		return n.podIP
	}
	return n.podName
}
