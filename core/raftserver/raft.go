package raftserver

import (
	"context"
	"os"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"git.haier.net/devops/ops-golang-common/log"
	myCtx "git.haier.net/devops/ops-golang-common/utils/context"
	"github.com/google/uuid"
	"github.com/hashicorp/raft"
	"github.com/imroc/req/v3"
	"github.com/sirupsen/logrus"

	"git.haier.net/devops/hcms-task-center/core/tools"
)

type Raft struct {
	id       string
	podIP    string
	podName  string
	nodeIp   string
	raftPort int

	raftConfig           *raft.Config
	raftServer           *raft.Raft
	raftServerId         raft.ServerID
	raftTransport        raft.Transport
	raftBootstrapCluster string
	raftApiJoinCluster   string
	raftApiLeaveCluster  string

	// LogStore is used to provide an interface for storing
	// and retrieving logs in a durable fashion.
	logStore raft.LogStore
	// StableStore is used to provide stable storage
	// of key configurations to ensure safety.
	stableStore raft.StableStore
	// SnapshotStore interface is used to allow for flexible implementations
	// of snapshot storage and retrieval. For example, a client could implement
	// a shared state store such as S3, allowing new nodes to restore snapshots
	// without streaming from the leader.
	snapshot raft.SnapshotStore

	// FSM is implemented by clients to make use of the replicated log.
	fsm raft.FSM

	ctx          context.Context
	cancel       context.CancelFunc
	logger       *log.Logger
	done         *atomic.Bool
	started      *atomic.Bool
	startOnce    *sync.Once
	shutdownOnce *sync.Once
	wg           *sync.WaitGroup
	httpClient   *req.Client
}

// NewNode returns a new raft Node.
func NewNode(port int, bootstrapCluster string, opts ...Option) *Raft {
	uuidStr := uuid.New().String()
	ctx, cancel := context.WithCancel(myCtx.NewContextWithTraceId(uuidStr))
	rft := &Raft{
		id:       uuidStr,
		podIP:    tools.GetLocalIpAddr(),
		podName:  os.Getenv(SysEnvPodName),
		nodeIp:   os.Getenv(SysEnvHostIp),
		raftPort: port,

		raftServerId:         raft.ServerID(uuidStr),
		raftConfig:           raft.DefaultConfig(),
		logStore:             raft.NewInmemStore(),
		stableStore:          raft.NewInmemStore(),
		snapshot:             raft.NewInmemSnapshotStore(),
		fsm:                  NewKeyValueStore(),
		raftBootstrapCluster: bootstrapCluster,

		ctx:          ctx,
		cancel:       cancel,
		logger:       log.NewWithOption("RAFT", log.WithLevel(logrus.InfoLevel)),
		done:         new(atomic.Bool),
		started:      new(atomic.Bool),
		startOnce:    new(sync.Once),
		shutdownOnce: new(sync.Once),
		wg:           new(sync.WaitGroup),
		httpClient:   req.NewClient(),
	}

	for _, opt := range opts {
		opt(rft)
	}

	return rft
}

// RaftBindIp returns the bind IP for the Raft server.
func (n *Raft) RaftBindIp() string {
	return n.podIP
}

// RaftBindAddr returns the bind address for the Raft server.
func (n *Raft) RaftBindAddr() string {
	return n.podIP + ":" + strconv.Itoa(n.raftPort)
}

// Server returns the Raft server.
func (n *Raft) Server() *raft.Raft {
	return n.raftServer
}

// RaftServers returns the Raft servers.
func (n *Raft) RaftServers() []raft.Server {
	c := n.raftServer.GetConfiguration().Configuration()
	return c.Clone().Servers
}

// LeaderAddr returns the leader address.
func (n *Raft) LeaderAddr() string {
	addr, _ := n.Server().LeaderWithID()
	return string(addr)
}

func (n *Raft) ID() string {
	return n.id
}

func (n *Raft) RaftID() raft.ServerID {
	return n.raftServerId
}

// LeaderID returns the leader ID.
func (n *Raft) LeaderID() string {
	_, id := n.Server().LeaderWithID()
	return string(id)
}

// IsLeader returns true if the node is the leader.
func (n *Raft) IsLeader() bool {
	if n.Server() == nil {
		return false
	}
	return n.Server().State() == raft.Leader
}

// IsRunning returns true if the node is running.
func (n *Raft) IsRunning() bool {
	return !n.done.Load()
}

// Closed returns true if the node is closed.
func (n *Raft) Closed() bool {
	return n.done.Load()
}

// Ready returns true if the node is ready to serve requests.
func (n *Raft) Ready() bool {
	if n.Server() == nil {
		n.Warnf("raft node %s not ready, raft server is nil", n.id)
		return false
	}
	state := n.Server().State()
	ready := state.String() == "Follower" || state.String() == "Leader"
	if !ready {
		n.Warnf("raft node %s not ready, state: %s", n.id, state)
	}
	return ready
}

func (n *Raft) SetRaftBootstrapCluster(bootstrapCluster string) {
	if n.started.Load() {
		panic("raft: already started")
	}
	n.raftBootstrapCluster = bootstrapCluster
}

// Shutdown stops the node.
func (n *Raft) Shutdown() {
	n.shutdownOnce.Do(func() {
		n.Infof("raft node %s shutting down...", n.id)
		if err := n.leaveCluster(); err != nil {
			n.Errorf("leave cluster failed: %s", err)
		}
		n.cancel()
		n.done.Store(true)
		n.Infof("raft node %s shutdown.", n.id)
	})
}

func (n *Raft) raftServerShutdown() {
	timer := time.NewTimer(20 * time.Second).C
	select {
	case <-timer:
		n.Warn("raft node shutdown timeout")
		return
	default:
		n.Warn("receive signal shutdown.")
		if n.Server() != nil {
			err := n.Server().Shutdown().Error()
			if err != nil {
				n.Warnf("raft node shutdown error: %s", err)
			}
		}
	}
}

// Start starts the node.
func (n *Raft) Start() {
	if n.started.Load() {
		panic("raft: already started")
	}
	n.startOnce.Do(func() {
		n.started.Store(true)
		n.initRaftConfig()
		n.initWatchers()
		n.raftTransport = n.initHttpTransPorter()
		n.raftServer = n.initRaftNode()
		n.boot()
	})
}

func (n *Raft) Wait() {
	n.wg.Wait()
}
