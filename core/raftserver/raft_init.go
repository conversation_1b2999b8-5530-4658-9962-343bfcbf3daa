package raftserver

import (
	"os"
	"time"

	"github.com/hashicorp/raft"
)

// initRaftConfig 初始化raft配置
func (n *Raft) initRaftConfig() {
	n.Infof("start initRaftConfig ...")
	n.raftConfig.LocalID = n.raftServerId
	n.raftConfig.LogLevel = "INFO"
	// n.raftConfig.NoSnapshotRestoreOnStart = true
	// n.raftConfig.SnapshotInterval = 365 * 24 * time.Hour
	// n.raftConfig.SnapshotThreshold = 100000
	// n.raftConfig.MaxAppendEntries = 64
	// n.raftConfig.TrailingLogs = 10
}

// initHttpTransPorter 初始化传输层
func (n *Raft) initHttpTransPorter() raft.Transport {
	n.Infof("start initHttpTransPorter ...")
	n.Infof("raft bind addr: %s", n.RaftBindAddr())
	transport, err := raft.NewTCPTransport(n.RaftBindAddr(), nil, 5, time.Second, os.Stdout)
	if err != nil {
		panic(err)
	}
	return transport
}

// initRaftNode 初始化raft节点
func (n *Raft) initRaftNode() *raft.Raft {
	raftNode, err := raft.NewRaft(n.raftConfig, n.fsm, n.logStore, n.stableStore, n.snapshot, n.raftTransport)
	if err != nil {
		panic(err)
	}
	return raftNode
}

func (n *Raft) initWatchers() {
	watchers := n.getWatcher()
	n.wg.Add(len(watchers))
	for _, watcher := range watchers {
		go func(w func()) {
			defer n.wg.Done()
			w()
		}(watcher)
	}
}

func (n *Raft) getWatcher() []func() {
	return []func(){
		n.watchSystemSignal,
		n.watchServerShutdown,
		n.watchRaftStateChange,
		n.watchRaftLeaderChange,
	}
}
