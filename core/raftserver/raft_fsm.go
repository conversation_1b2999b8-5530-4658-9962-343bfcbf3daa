package raftserver

import (
	"fmt"
	"io"
	"sync"

	"github.com/goccy/go-json"
	"github.com/hashicorp/raft"
)

func NewKeyValueStore() *KeyValueStore {
	return &KeyValueStore{
		mtx: new(sync.Mutex),
		kv:  make(map[string]string),
	}
}

// KeyValueCommand 定义一个命令结构体
type KeyValueCommand struct {
	Op    string `json:"op"`
	Key   string `json:"key"`
	Value string `json:"value"`
}

// KeyValueStore 实现Raft库的FSM接口
type KeyValueStore struct {
	mtx *sync.Mutex
	kv  map[string]string
}

func (s *KeyValueStore) Apply(log *raft.Log) interface{} {
	var cmd KeyValueCommand
	if err := json.Unmarshal(log.Data, &cmd); err != nil {
		return err
	}

	s.mtx.Lock()
	defer s.mtx.Unlock()

	switch cmd.Op {
	case "set":
		s.kv[cmd.Key] = cmd.Value
	case "delete":
		delete(s.kv, cmd.Key)
	default:
		return fmt.Errorf("unknown command type: %s", cmd.Op)
	}

	return nil
}

func (s *KeyValueStore) Snapshot() (raft.FSMSnapshot, error) {
	return &KeyValueStoreSnapshot{kv: s.kv}, nil
}

func (s *KeyValueStore) Restore(rc io.ReadCloser) error {
	defer rc.Close()
	snapshot := &KeyValueStoreSnapshot{}

	if err := json.NewDecoder(rc).Decode(&snapshot.kv); err != nil {
		return err
	}

	s.kv = snapshot.kv
	return nil
}

// KeyValueStoreSnapshot 实现Raft库的FSMSnapshot接口
type KeyValueStoreSnapshot struct {
	kv map[string]string
}

func (s *KeyValueStoreSnapshot) Persist(sink raft.SnapshotSink) error {
	if err := json.NewEncoder(sink).Encode(s.kv); err != nil {
		_ = sink.Cancel()
		return err
	}
	return sink.Close()
}

func (s *KeyValueStoreSnapshot) Release() {}
