package tools

import (
	"git.haier.net/devops/ops-golang-common/utils/pointer"
	"github.com/chyroc/lark"
)

func GetFeiShuRowString(col string, row ...lark.SheetContent) string {
	return pointer.Value(row[letterToNumber(col)].String)
}

func GetRowInt(col string, row ...lark.SheetContent) int64 {
	return pointer.Value(row[letterToNumber(col)].Int)
}

func letterToNumber(s string) int {
	var result int

	for _, c := range s {
		result *= 26
		result += int(c) - 'A' + 1
	}

	return result - 1
}
