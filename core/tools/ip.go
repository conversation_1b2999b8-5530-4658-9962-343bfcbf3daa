package tools

import (
	"fmt"
	"math/big"
	"net"
)

// GetLocalIpAddr 获取本机IP
func GetLocalIpAddr() string {
	interfaceAddress, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}
	for _, address := range interfaceAddress {
		if ipNet, ok := address.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				return ipNet.IP.String()
			}
		}
	}
	return ""
}

func InetAtoN(ip string) int64 {
	ret := big.NewInt(0)
	ip2v4 := net.ParseIP(ip).To4()
	if ip2v4 != nil {
		ret.SetBytes(ip2v4)
		return ret.Int64()
	}
	return 0
}

func InetNtoA(ip int64) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		byte(ip>>24), byte(ip>>16), byte(ip>>8), byte(ip))
}
