package tools

import (
	"fmt"
	"math/rand"
	"net"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestInetAtoN(t *testing.T) {
	a := assert.New(t)

	for i := 0; i < 2500; i++ {
		ip := generateRandomIP().String()
		nIp := InetAtoN(ip)
		oIp := InetNtoA(nIp)
		a.Equal(oIp, ip)
	}

}

func TestInetAtoN2(t *testing.T) {
	a := assert.New(t)
	ip := "*************"
	nIp := InetAtoN(ip)
	oIp := InetNtoA(nIp)
	a.Equal(oIp, ip)
	fmt.Println(nIp, oIp)
}

func generateRandomIP() net.IP {
	ip := make(net.IP, 4)
	for i := 0; i < 4; i++ {
		ip[i] = byte(rand.Intn(256))
	}
	return ip
}
