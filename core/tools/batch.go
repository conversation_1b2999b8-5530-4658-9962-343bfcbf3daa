package tools

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"git.haier.net/devops/ops-golang-common/batch"

	"git.haier.net/devops/hcms-task-center/core/config"
	"git.haier.net/devops/hcms-task-center/core/pool"
)

var cnf = config.Global()

func NewBatch[IN, OUT any](ctx context.Context, opt ...batch.Option) *batch.Batch[IN, OUT] {
	opts := append([]batch.Option{
		batch.WithShowLog(false),
		batch.WithPool(pool.Pool()),
		batch.WithIgnoreNil(true),
		batch.WithCtx(ctx),
		batch.WithBatchSize(cnf.BatchSize),
	}, opt...)

	return batch.NewBatch[IN, OUT](opts...)
}

func MergeData[T any](hostInfos ...[]T) []T {
	info := make([]T, 0)
	for _, infoList := range hostInfos {
		info = append(info, infoList...)
	}
	return info
}

func MergeErrors(errs []error) error {
	noEmptyErrors := make([]error, 0)
	if len(errs) > 0 {
		for _, err := range errs {
			if err != nil {
				noEmptyErrors = append(noEmptyErrors, err)
			}
		}
	}

	if len(noEmptyErrors) == 0 {
		return nil
	}

	sb := new(strings.Builder)
	sb.WriteString(fmt.Sprintf("%d errors: \n\t", len(errs)))
	for _, e := range errs {
		sb.WriteString(e.Error() + "\n\t")
	}
	return errors.New(sb.String())
}
