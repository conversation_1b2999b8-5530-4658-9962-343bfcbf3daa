package tools

// StringSet 定义一个字符串集合
type StringSet map[string]struct{}

// NewStringSet 创建一个新的 StringSet
func NewStringSet() StringSet {
	return make(StringSet)
}

// Add 向集合中添加元素
func (s StringSet) Add(value string) {
	s[value] = struct{}{}
}

// Remove 从集合中移除元素
func (s StringSet) Remove(value string) {
	delete(s, value)
}

// Contains 检查元素是否在集合中
func (s StringSet) Contains(value string) bool {
	_, exists := s[value]
	return exists
}

// Size 返回集合的大小
func (s StringSet) Size() int {
	return len(s)
}

// List 返回集合中的所有元素
func (s StringSet) List() []string {
	elements := make([]string, 0, len(s))
	for key := range s {
		elements = append(elements, key)
	}
	return elements
}
