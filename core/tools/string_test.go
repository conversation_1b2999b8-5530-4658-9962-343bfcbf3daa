package tools

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSubstringLastN(t *testing.T) {
	a := assert.New(t)
	a.Equal("123", SubstringLastN("123", 3))
	a.Equal("123", SubstringLastN("123", 4))
	a.Equal("23", SubstringLastN("123", 2))
	a.Equal("3", SubstringLastN("123", 1))
	a.Equal("", SubstringLastN("123", 0))
	a.Equal("", SubstringLastN("123", -1))
	a.Equal("", SubstringLastN("", 0))
	a.Equal("", SubstringLastN("", 1))
	a.Equal("", SubstringLastN("", -1))
	a.Equal("中文", SubstringLastN("中文", 2))
	a.Equal("文", SubstringLastN("中文", 1))
	a.Equal("", SubstringLastN("中文", 0))
	a.Equal("", SubstringLastN("中文", -1))
}
